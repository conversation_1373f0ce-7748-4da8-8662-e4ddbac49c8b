{"compilerOptions": {"baseUrl": ".", "paths": {"/imports/*": ["app/imports/*"], "/tests/*": ["app/tests/*"], "/client/*": ["app/client/*"], "/server/*": ["app/server/*"], "/public/*": ["app/public/*"], "/private/*": ["app/private/*"]}, "jsx": "react", "module": "esnext", "target": "es2017", "allowSyntheticDefaultImports": true, "moduleResolution": "node"}, "include": ["app/**/*"], "exclude": ["node_modules", "app/node_modules", "app/.meteor/local"]}