apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: springmath-prod-ingress
  annotations:
    # --- external DNS / CF (grey cloud - DNS only) ---
    external-dns.alpha.kubernetes.io/hostname: app.springmath.org
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "false"  # DNS-only mode

    # --- ingress class ---
    nginx.ingress.kubernetes.io/ingress.class: nginx

    # --- cert-manager annotations for Let's Encrypt ---
    cert-manager.io/cluster-issuer: "letsencrypt-production"

    # --- sticky‑session tuning ---
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/affinity-mode: "persistent"
    nginx.ingress.kubernetes.io/session-cookie-name: SPRINGMATH_STICKY_PROD
    nginx.ingress.kubernetes.io/session-cookie-path: "/"
    nginx.ingress.kubernetes.io/session-cookie-expires: "28800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "28800"

    # --- upstream timeouts ---
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - app.springmath.org
      secretName: prod-letsencrypt-production-tls
  rules:
    - host: app.springmath.org
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: springmath-prod-service
                port:
                  number: 80