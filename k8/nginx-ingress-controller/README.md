# Dual SSL Certificate Setup

This directory contains the configuration for supporting both Cloudflare Origin Certificates and Let's Encrypt certificates for the SpringMath application.

## Overview

The dual SSL setup allows switching between:
- **Cloudflare Origin Certificates**: Used when Cloudflare proxy is enabled (orange cloud)
- **Let's Encrypt Certificates**: Used when Cloudflare proxy is disabled (grey cloud/DNS-only)

## Files

### Certificate Issuers
- `letsencrypt-staging-issuer.yaml`: Let's Encrypt staging ClusterIssuer (for testing)
- `letsencrypt-production-issuer.yaml`: Let's Encrypt production ClusterIssuer (for live certificates)

### Ingress Configurations

#### Development Environment
- `dev-ingress-cloudflare.yaml`: Dev ingress using Cloudflare certificate
- `dev-ingress-letsencrypt.yaml`: Dev ingress using Let's Encrypt certificate

#### Production Environment
- `prod-ingress-cloudflare.yaml`: Production ingress using Cloudflare certificate
- `prod-ingress-letsencrypt.yaml`: Production ingress using Let's Encrypt certificate

### Deployment Script
- `deploy-ingress.sh`: Helper script to deploy the appropriate ingress based on SSL provider

## Prerequisites

1. **cert-manager** must be installed in the cluster:
   ```bash
   kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.3/cert-manager.yaml
   ```

2. **Cloudflare Origin Certificate** must be created and installed as a secret:
   ```bash
   kubectl create secret tls cloudflare-springmath-tls \
     --cert=path/to/cert.pem \
     --key=path/to/key.pem
   ```

## Usage

### Manual Deployment

#### Deploy with Let's Encrypt (DNS-only mode)
```bash
# Development
kubectl apply -f k8/nginx-ingress-controller/dev-ingress-letsencrypt.yaml

# Production
kubectl apply -f k8/nginx-ingress-controller/prod-ingress-letsencrypt.yaml
```

#### Deploy with Cloudflare (Proxy mode)
```bash
# Development
kubectl apply -f k8/nginx-ingress-controller/dev-ingress-cloudflare.yaml

# Production
kubectl apply -f k8/nginx-ingress-controller/prod-ingress-cloudflare.yaml
```

### Using the Deployment Script
```bash
# Deploy dev with Let's Encrypt
./k8/nginx-ingress-controller/deploy-ingress.sh dev letsencrypt

# Deploy dev with Cloudflare (default)
./k8/nginx-ingress-controller/deploy-ingress.sh dev cloudflare

# Deploy prod with Let's Encrypt
./k8/nginx-ingress-controller/deploy-ingress.sh prod letsencrypt
```

### GitHub Actions Integration

The deployment automatically uses environment-specific SSL provider variables:

```yaml
- name: Deploy Ingress
  run: |
    export DEV_SSL_PROVIDER="${{ vars.DEV_SSL_PROVIDER || 'cloudflare' }}"
    ./k8/nginx-ingress-controller/deploy-ingress.sh dev $DEV_SSL_PROVIDER
```

Add environment-specific variables in GitHub:
- Go to Settings → Secrets and variables → Actions → Variables
- Add the following variables:
  - `DEV_SSL_PROVIDER`: Set to `cloudflare` or `letsencrypt` for dev environment
  - `PROD_SSL_PROVIDER`: Set to `cloudflare` or `letsencrypt` for production
  - `DEV_SKIP_E2E_TESTS`: Set to `true` to skip E2E tests in dev (optional)

## Certificate Management

### Let's Encrypt Certificates

Certificates are automatically managed by cert-manager:
- Automatic renewal before expiration
- HTTP-01 challenge validation
- Staging certificates for testing (untrusted)
- Production certificates for live use (trusted)

Check certificate status:
```bash
kubectl get certificate
kubectl describe certificate <certificate-name>
```

### Cloudflare Origin Certificates

These are long-lived certificates (15 years) that only work when Cloudflare proxy is enabled.

## Troubleshooting

### Certificate Not Issuing
```bash
# Check certificate status
kubectl get certificate
kubectl describe certificate <cert-name>

# Check certificate request
kubectl get certificaterequest
kubectl describe certificaterequest <request-name>

# Check ACME order
kubectl get order
kubectl describe order <order-name>

# Check challenges
kubectl get challenge
kubectl describe challenge <challenge-name>
```

### Switching Between Providers

When switching from Cloudflare to Let's Encrypt:
1. Disable Cloudflare proxy (set to DNS-only/grey cloud)
2. Apply the Let's Encrypt ingress configuration
3. Wait for certificate to be issued (usually < 1 minute)

When switching from Let's Encrypt to Cloudflare:
1. Apply the Cloudflare ingress configuration
2. Enable Cloudflare proxy (orange cloud)

## Important Notes

- **Never use Let's Encrypt with Cloudflare proxy enabled** - the HTTP-01 challenge will fail
- **Cloudflare certificates only work with proxy enabled** - direct access will show certificate errors
- Let's Encrypt has rate limits - use staging for testing
- Production Let's Encrypt certificates are trusted by all browsers
- Staging Let's Encrypt certificates will show as untrusted (for testing only)