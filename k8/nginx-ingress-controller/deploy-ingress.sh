#!/bin/bash

# Deploy appropriate ingress based on environment-specific SSL_PROVIDER variable
# Usage: ./deploy-ingress.sh [dev|prod] [cloudflare|letsencrypt]

ENVIRONMENT="${1:-dev}"

# Use environment-specific SSL_PROVIDER variable
case "$ENVIRONMENT" in
  dev)
    SSL_PROVIDER="${2:-${DEV_SSL_PROVIDER:-cloudflare}}"
    ;;
  prod)
    SSL_PROVIDER="${2:-${PROD_SSL_PROVIDER:-cloudflare}}"
    ;;
  qa)
    SSL_PROVIDER="${2:-${QA_SSL_PROVIDER:-cloudflare}}"
    ;;
  stage)
    SSL_PROVIDER="${2:-${STAGE_SSL_PROVIDER:-cloudflare}}"
    ;;
  *)
    SSL_PROVIDER="${2:-cloudflare}"
    ;;
esac

echo "Deploying $ENVIRONMENT ingress with $SSL_PROVIDER SSL provider..."

case "$ENVIRONMENT" in
  dev)
    if [ "$SSL_PROVIDER" = "letsencrypt" ]; then
      echo "Applying Let's Encrypt ingress for dev..."
      kubectl apply -f k8/nginx-ingress-controller/dev-ingress-letsencrypt.yaml
    else
      echo "Applying Cloudflare ingress for dev..."
      kubectl apply -f k8/nginx-ingress-controller/dev-ingress-cloudflare.yaml
    fi
    ;;
  prod)
    if [ "$SSL_PROVIDER" = "letsencrypt" ]; then
      echo "Applying Let's Encrypt ingress for prod..."
      kubectl apply -f k8/nginx-ingress-controller/prod-ingress-letsencrypt.yaml
    else
      echo "Applying Cloudflare ingress for prod..."
      kubectl apply -f k8/nginx-ingress-controller/prod-ingress-cloudflare.yaml
    fi
    ;;
  *)
    echo "Error: Unknown environment '$ENVIRONMENT'. Use 'dev' or 'prod'."
    exit 1
    ;;
esac

echo "Ingress deployment complete."