# This section will create a secret in the Kubernetes cluster.
# We need this for private docker repos.
apiVersion: v1
kind: Secret
metadata:
  name: qa-springmathcreds
data:
  .dockerconfigjson: DOCKER_CONFIG
type: kubernetes.io/dockerconfigjson
---
apiVersion: v1
kind: Secret
metadata:
  name: qa-springmathenvs
data:
  QA_ROOT_URL: QA_ROOT_URL_SECRET
  QA_MONGO_URL: QA_MONGO_URL_SECRET
  QA_MONGO_OPLOG_URL: QA_MONGO_OPLOG_URL_SECRET
  QA_PORT: QA_PORT_SECRET
  QA_METEOR_SETTINGS: QA_METEOR_SETTINGS_SECRET
---
# This section will create a deployment in the Kubernetes cluster
apiVersion: apps/v1
kind: Deployment
metadata:
  name: springmath-qa
  labels:
    app: springmath-qa
spec:
  selector:
    matchLabels:
      app: springmath-qa
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: springmath-qa
    spec:
      containers:
        - env:
            - name: ROOT_URL
              valueFrom:
                secretKeyRef:
                  key: QA_ROOT_URL
                  name: qa-springmathenvs
            - name: MONGO_URL
              valueFrom:
                secretKeyRef:
                  key: QA_MONGO_URL
                  name: qa-springmathenvs
            - name: MONGO_OPLOG_URL
              valueFrom:
                secretKeyRef:
                  key: QA_MONGO_OPLOG_URL
                  name: qa-springmathenvs
            - name: PORT
              valueFrom:
                secretKeyRef:
                  key: QA_PORT
                  name: qa-springmathenvs
            - name: METEOR_SETTINGS
              valueFrom:
                secretKeyRef:
                  key: QA_METEOR_SETTINGS
                  name: qa-springmathenvs
            - name: NODE_OPTIONS
              value: --max-old-space-size=1024
          image: IMAGE_FULL_TAG
          imagePullPolicy: Always
          name: springmath-qa
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 1
              memory: 1Gi
      hostAliases:
        - ip: **********
          hostnames:
            - sm-mongo-qa
      imagePullSecrets:
        - name: qa-springmathcreds
---
# Ingress using Octavia controller for OVHcloud OpenStack
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: springmath-qa-ingress
  annotations:
    # --- external DNS / CF ---
    external-dns.alpha.kubernetes.io/hostname: app.qa.springmath.org
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "true"

    # --- ingress class ---
    kubernetes.io/ingress.class: nginx

    # --- sticky‑session tuning ---
    nginx.ingress.kubernetes.io/affinity: "cookie" # must literally be “cookie”
    nginx.ingress.kubernetes.io/affinity-mode: "persistent" # rewrites bad cookies; replaces your “balanced”
    nginx.ingress.kubernetes.io/session-cookie-name: SPRINGMATH_STICKY_QA
    nginx.ingress.kubernetes.io/session-cookie-path: "/"

    nginx.ingress.kubernetes.io/session-cookie-expires: "28800" # 8 hrs
    nginx.ingress.kubernetes.io/session-cookie-max-age: "28800"

    # --- upstream timeouts ---
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  tls:
    - hosts:
        - app.qa.springmath.org
      secretName: cloudflare-springmath-tls # Your existing Cloudflare SSL certificate
  rules:
    - host: app.qa.springmath.org
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: springmath-qa-service
                port:
                  number: 80
---
apiVersion: v1
kind: Service
metadata:
  name: springmath-qa-service
  labels:
    app: springmath-qa
spec:
  type: ClusterIP
  selector:
    app: springmath-qa
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
