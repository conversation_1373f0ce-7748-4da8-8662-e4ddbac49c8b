# This section will create a secret in the Kubernetes cluster.
# We need this for private docker repos.
apiVersion: v1
kind: Secret
metadata:
  name: demo-springmathcreds
data:
  .dockerconfigjson: DOCKER_CONFIG
type: kubernetes.io/dockerconfigjson
---
apiVersion: v1
kind: Secret
metadata:
  name: demo-springmathenvs
data:
  DEMO_ROOT_URL: DEMO_ROOT_URL_SECRET
  DEMO_MONGO_URL: DEMO_MONGO_URL_SECRET
  DEMO_MONGO_OPLOG_URL: DEMO_MONGO_OPLOG_URL_SECRET
  DEMO_PORT: DEMO_PORT_SECRET
  DEMO_METEOR_SETTINGS: DEMO_METEOR_SETTINGS_SECRET
---
# This section will create a deployment in the Kubernetes cluster
apiVersion: apps/v1
kind: Deployment
metadata:
  name: springmath-demo
  labels:
    app: springmath-demo
spec:
  selector:
    matchLabels:
      app: springmath-demo
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: springmath-demo
    spec:
      containers:
        - env:
            - name: ROOT_URL
              valueFrom:
                secretKeyRef:
                  key: DEMO_ROOT_URL
                  name: demo-springmathenvs
            - name: MONGO_URL
              valueFrom:
                secretKeyRef:
                  key: DEMO_MONGO_URL
                  name: demo-springmathenvs
            - name: MONGO_OPLOG_URL
              valueFrom:
                secretKeyRef:
                  key: DEMO_MONGO_OPLOG_URL
                  name: demo-springmathenvs
            - name: PORT
              valueFrom:
                secretKeyRef:
                  key: DEMO_PORT
                  name: demo-springmathenvs
            - name: METEOR_SETTINGS
              valueFrom:
                secretKeyRef:
                  key: DEMO_METEOR_SETTINGS
                  name: demo-springmathenvs
            - name: NODE_OPTIONS
              value: --max-old-space-size=1024
          image: IMAGE_FULL_TAG
          imagePullPolicy: Always
          name: springmath-demo
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 1
              memory: 1Gi
      hostAliases:
        - ip: ***********
          hostnames:
            - springmath-mongo-demo
      imagePullSecrets:
        - name: demo-springmathcreds
---
# Ingress using Octavia controller for OVHcloud OpenStack
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: springmath-demo-ingress
  annotations:
    # --- external DNS / CF ---
    external-dns.alpha.kubernetes.io/hostname: app.demo.springmath.org
    external-dns.alpha.kubernetes.io/cloudflare-proxied: "true"

    # --- ingress class ---
    kubernetes.io/ingress.class: nginx

    # --- sticky‑session tuning ---
    nginx.ingress.kubernetes.io/affinity: "cookie" # must literally be “cookie”
    nginx.ingress.kubernetes.io/affinity-mode: "persistent" # rewrites bad cookies; replaces your “balanced”
    nginx.ingress.kubernetes.io/session-cookie-name: SPRINGMATH_STICKY_DEMO
    nginx.ingress.kubernetes.io/session-cookie-path: "/"

    nginx.ingress.kubernetes.io/session-cookie-expires: "28800" # 8 hrs
    nginx.ingress.kubernetes.io/session-cookie-max-age: "28800"

    # --- upstream timeouts ---
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  tls:
    - hosts:
        - app.demo.springmath.org
      secretName: cloudflare-springmath-tls # Your existing Cloudflare SSL certificate
  rules:
    - host: app.demo.springmath.org
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: springmath-demo-service
                port:
                  number: 80
---
apiVersion: v1
kind: Service
metadata:
  name: springmath-demo-service
  labels:
    app: springmath-demo
spec:
  type: ClusterIP
  selector:
    app: springmath-demo
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
