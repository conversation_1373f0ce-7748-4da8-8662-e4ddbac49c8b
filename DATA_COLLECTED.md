# SpringMath Data Collection Overview

## Summary

SpringMath is a math assessment and intervention platform that collects essential educational data to identify students needing mathematical support and provide targeted interventions. This document outlines the data collected by SpringMath for its core educational functions.

## Student Information

**Basic Student Data:**

- Student first, middle, and last names
- Local student ID (district-assigned identifier)
- State student ID (state-assigned identifier)
- Birth date (when provided in roster imports)
- Current grade level
- School year enrollment

**Academic Context:**

- School/site assignment
- Class/group membership
- Teacher assignments
- Enrollment status and history

## Assessment and Performance Data

**Assessment Results:**

- Individual assessment scores and completion status
- Benchmark assessment results
- Progress monitoring data over time
- Intervention recommendations and assignments
- Student performance trends and growth metrics

**Educational Interventions:**

- Assigned intervention programs
- Intervention completion rates and effectiveness
- Individual vs. classwide intervention participation
- Progress monitoring frequency and results

## User Account Information

**Authentication Data:**

- User names (first, middle, last)
- Email addresses for system access
- User roles (teacher, admin, data admin, etc.)
- Site and organization access permissions
- Account creation and last modification dates
- Login activity and session data

**System Security:**

- Password change history (encrypted)
- Multi-factor authentication settings
- Single Sign-On (SSO) integration data when applicable

## Organizational Information

**Institution Data:**

- District/organization names and identifiers
- School names and identification numbers
- Site-specific information and settings
- School year boundaries and calendar information
- Administrative configuration settings

**Staff Information:**

- Teacher names and email addresses
- Staff role assignments and permissions
- Class assignments and student group ownership

## System Activity Data

**Audit and Security Logging:**

- All data changes with timestamps and user attribution
- System access events and authentication attempts
- Administrative actions and configuration changes
- Data import and export activities

**Roster Management:**

- Bulk data import processing and validation
- Student enrollment changes and updates
- Staff assignment modifications
- Data synchronization with student information systems

## External Data Integration

**Assessment Score Uploads:**

- External assessment results (state and district tests)
- Assessment names, scores, and proficiency levels
- Assessment years and testing periods

**Ed-Fi Data Exchange:**

- Student roster information
- Organizational data synchronization
- Assessment results sharing
- Enrollment and academic record updates

**Third-Party Services:**

- Document generation and educational materials
- Basic usage analytics for system improvement
- Customer support integration (when users request help)

## Technical and Infrastructure Data

**System Operations:**

- Database performance and optimization data
- File storage and document generation logs
- Email communication logs (system notifications)
- Error tracking and system reliability metrics

## Data Usage and Purpose

All data collected by SpringMath serves specific educational purposes:

- **Student Progress Monitoring:** Tracking individual academic growth and identifying students needing support
- **Intervention Management:** Assigning and monitoring effectiveness of educational interventions
- **Educational Reporting:** Providing teachers and administrators with actionable insights
- **System Administration:** Managing user access, roles, and organizational settings
- **Compliance:** Meeting educational data requirements and audit needs
- **System Improvement:** Optimizing platform performance and user experience

## Data Security and Privacy

SpringMath implements comprehensive security measures:

- All data encrypted in transit via SSL/TLS
- Application-level encryption for sensitive configuration data
- Database and storage encryption as configured in the deployment environment
- Role-based access controls limiting data visibility
- Complete audit trails for all data access and modifications
- Regular security monitoring and threat assessment
- Compliance with educational privacy regulations (FERPA)

## Data Retention

- Student academic data maintained for educational continuity across school years
- User account data retained while accounts remain active
- Audit logs preserved for security and compliance requirements
- Inactive data marked for appropriate retention per organizational policies

---

_This documentation reflects current data collection practices and is updated regularly to ensure accuracy as the system evolves._
