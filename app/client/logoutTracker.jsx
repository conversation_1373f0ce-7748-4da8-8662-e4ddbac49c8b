import React, { useContext, useEffect, Fragment } from "react";
import PropTypes from "prop-types";
import { useHistory } from "react-router-dom";
import layoutMethods from "../imports/ui/layouts/methods";
import { isOnExposedRoute } from "../imports/api/utilities/utilities";
import { UserContext } from "../imports/contexts/UserContext";

const LogoutTracker = ({ children }) => {
  const userContext = useContext(UserContext);
  const history = useHistory();

  useEffect(() => {
    // NOTE(fmazur) - if doesn't work add || getMeteorUserId()
    const currUserId = userContext.userId;
    const path = window.location.href;

    if (!currUserId && !localStorage.getItem("Meteor.loginToken") && !isOnExposedRoute(path)) {
      localStorage.removeItem("lastRouteWithGrades");
      localStorage.removeItem("lastSiteIdWithGrades");
      history.push("/login");
    }
  }, [userContext.userId]);

  return (
    <Fragment>
      {children}
      {layoutMethods.logoutWarningModal({ user: userContext.user })}
    </Fragment>
  );
};

LogoutTracker.propTypes = {
  children: PropTypes.node
};

export default LogoutTracker;
export { LogoutTracker as PureLogoutTracker };
