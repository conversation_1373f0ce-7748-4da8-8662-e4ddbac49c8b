import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { MemoryRouter } from "react-router-dom";
import { PureLogoutTracker } from "./logoutTracker";
import { UserContext } from "../imports/contexts/UserContext";

// Mock the utilities module
jest.mock("../imports/api/utilities/utilities", () => ({
  isOnExposedRoute: jest.fn(() => false)
}));

// Mock the layout methods
jest.mock("../imports/ui/layouts/methods", () => ({
  __esModule: true,
  default: {
    logoutWarningModal: jest.fn(({ user }) => {
      if (user?.warn) {
        return <div data-testid="inactivity-logout-warning-modal">Warning Modal</div>;
      }
      return null;
    })
  }
}));

// Mock UserContext
jest.mock("../imports/contexts/UserContext", () => ({
  UserContext: React.createContext({
    userId: null,
    userOrgId: null,
    userRoles: [],
    currentSiteAccess: {},
    firstName: "",
    lastName: "",
    warn: false,
    roleDefinitions: []
  })
}));

function SampleComponent() {
  return <div data-testid="child-component-testid">Hello There</div>;
}

// Mock useHistory at the module level
const mockPush = jest.fn();
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useHistory: () => ({ push: mockPush })
}));

// Helper function to render component with Router context
function renderWithRouter(component, userContext = {}) {
  const result = render(
    <MemoryRouter>
      <UserContext.Provider value={userContext}>{component}</UserContext.Provider>
    </MemoryRouter>
  );

  return { ...result, mockPush };
}

function warnUser(user) {
  return { ...user, warn: true };
}

function loginUser(userId) {
  localStorage.setItem("Meteor.userId", userId);
  return { _id: userId };
}

function logoutUser() {
  localStorage.removeItem("Meteor.userId");
  localStorage.removeItem("Meteor.loginToken");
  return {};
}

describe("LogoutTracker", () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    jest.clearAllMocks();
  });

  it("should render component", () => {
    const userContext = { userId: "someId" };
    const { getByTestId } = renderWithRouter(
      <PureLogoutTracker>
        <SampleComponent />
      </PureLogoutTracker>,
      userContext
    );
    expect(getByTestId("child-component-testid")).toBeVisible();
  });

  it("should test user inactivity", () => {
    // Test 1: User is logged in, should not redirect
    let user = loginUser("someId");
    let userContext = { userId: user._id };
    const { queryByTestId } = renderWithRouter(
      <PureLogoutTracker>
        <SampleComponent />
      </PureLogoutTracker>,
      userContext
    );

    expect(mockPush).not.toHaveBeenCalled();
    expect(queryByTestId("inactivity-logout-warning-modal")).toBe(null);

    // Test 2: User gets warning, should show modal
    user = warnUser(user);
    userContext = { userId: user._id, user: { warn: true } };
    const { queryByTestId: queryByTestId2 } = renderWithRouter(
      <PureLogoutTracker>
        <SampleComponent />
      </PureLogoutTracker>,
      userContext
    );
    expect(queryByTestId2("inactivity-logout-warning-modal")).toBeVisible();

    // Test 3: User is logged out, should redirect to login
    user = logoutUser();
    userContext = { userId: null };
    renderWithRouter(
      <PureLogoutTracker>
        <SampleComponent />
      </PureLogoutTracker>,
      userContext
    );
    expect(mockPush).toHaveBeenCalledWith("/login");
  });
});
