//base

.form-horizontal label {
    margin: 0;
}

.form-group {
    margin-bottom: 15px;
}

p, li{
    font-weight: 300;
    line-height: 1.65em;
    color: #4c5667;
}

p {
    margin-bottom: 10px;
}

label {
    font-weight: 700;
}

hr {
    opacity: 1;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

.btn-link-o{
    background: #fff;
    border: 1px solid #4991E6;
    border-radius: 3px;
    color: #4991E6;
    &:hover{
        background: #4991E6;
        color: #fff;
    }
}

.tiny-details{
    font-size: 0.9em;
    font-style: italic;
    margin: 0 20px;
}

.soft-hide{
    display: none;
}

//Components
.page-header{
    background: #fff;
    border-bottom: 1px solid #d7d7d7;
    margin: 0 0 25px;
    padding: 20px 25px 10px;
    text-align: center;
    h3{
        font-weight: 900;
        margin: 0 0 5px 0;
        text-transform: capitalize;
        font-size: 24px;
    }
}

//onboarding-navigation
#topnav .topbar-main.onboarding-nav{
    background-color: transparent;
    height: 60px;
}


//login
.conLogin {
  background: #ffffff;
  box-shadow: 0px 0px 6px #333333;
  margin: 7.5% auto;
  padding: 20px;
  width: 475px;

  .form-group {
    margin: 10px;
  }

  label {
    display: block;

    a {
      font-size: 0.8em;
      float: right;
    }
  }
}

.hidden-form{
    display: none;
}

.simple-details{
    color: #BBBBBB;
    display: block;
    font-size: 0.75em;
    font-weight: 200;
    text-transform: capitalize;
}

// Dashboard.html
.dashboard-header{
    padding: 0 15px;
    h4{
        margin: 0;
    }
}


.profile-stats{
    display: -webkit-flex; /* Safari */
    display: flex;
    // display: -webkit-inline-flex; /* Safari */
    // display: inline-flex;

    -webkit-flex-direction: row; /* Safari */
    flex-direction:         row;

    -webkit-flex-wrap: nowrap; /* Safari */
    flex-wrap:         nowrap;

    -webkit-justify-content: space-between; /* Safari */
    justify-content:         space-between;

    position: relative;
    .stat-item{
        text-align: center;
        h2{
            font-weight: 900;
            margin: 0;
        }
        label{
            font-size: 0.8em;
        }
    }
}

.dashboard-todos{
    .card-box-top{
        background: #376E86;
        border-radius: 3px 3px 0 0;
        height: 20px;
    }
    .card-box.flat-top{
        border-radius: 0 0 3px 3px;
        border-top: none;
    }
}

.task-item{
    border: 1px solid #ddd;
    border-radius: 3px;
    margin: 0 0 20px;
    padding: 0;
    overflow: hidden;
    .intervention-content{
        padding: 7px 10px;
        h4{
            margin: 0;
            line-height: 20px;
            span{
                font-size: 0.85em;
                font-weight: 500;
            }
        }
        .packet{
            small{
                color: #BDBDBD;
                display: block;
                font-size: 75%;
                font-weight: 300;
                vertical-align: super;
            }
        }
        .form-horizontal{
            margin-top: 6px;
        }
    }
}

// Screening
.screening-view{
    min-height: 170px;
    &.disabled{
        background: #ccc;
        color: #ddd;
        opacity: 0.5;
    }
    .intervention-recommendation input[type="checkbox"]{
        display: none;
    }
    .student-intervention-card h2{
        color: #D96060;
    }
    .student-intervention-card h4{
        color: #555;
    }
}

.screening-progress{
    padding: 0 10px 15px;
    // h4{
    //     line-height: 2em;
    // }
    // h4, p{
    //     margin: 0;
    // }
}

.highlighted-separator{
    background: #f4f4f4;
    padding: 3px 10px;
    font-weight: 700;
}






// screening-results.html

// Interventions
.interventions{
    margin-top: 1.5em;
}

.intervention-student{
    border-bottom: 1px solid #ddd;
    padding: 0 10px;
    small{
        font-style: italic;
        font-weight: 300;
        margin: 0 20px;
        .student-stat{
            font-size: 1.1em;
            font-style: normal;
            font-weight: 900;
        }
    }
}

.intervention-list{
    list-style: none;
    margin: 0;
    padding: 0;
}

a.intervention-item{
    border-bottom: 1px solid #ddd;
    h4{
        color: #337ab7;
        margin: 0 0 5px;
    }
    p{
        font-size: 0.8em;
        margin: 0;
        .intervention-started{
            display: inline-block;
            margin: 0 15px 0 0;
        }
        .intervention-status{
            display: inline-block;

        }
    }
    &.future{
        color: #B0B0B0;
        font-size: 0.8em;
        font-style: italic;
        padding: 4px 20px;
        small{
            float: right;
            margin-top: 3px;
        }
    }
}

.student-nav-options{
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 60%;
    a{
        display: block;
        margin: 0 0 10px;
    }
}

p.warningMessage,
p.warningMessage ul li {
    color: red;
}
