.container-item {
  overflow: hidden;
}

.container-item-full-border {
  border: 1px solid #d8d8d8;
  padding: 5px;
  border-radius: 6px;
}

.nav-container-item {
  position: relative;
  overflow: hidden;
  border: 1px solid #d8d8d8;
  border-bottom: 0px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.nav-link {
  padding-right: 23px !important;
}

.middle-sub-nav {
  margin: 0 0.5rem 0.5rem;

  &.middle-sub-nav-compact {
    margin-bottom: 20px;
  }

  &.nav-pills {
    border-bottom: 1px solid #ddd;

    > li {
      top: 1px;
      line-height: 2.6;

      > a {
        color: #888f95;
        font-size: 0.8em;
        font-weight: 500;
        padding: 12px 15px;

        &.active {
          border-bottom: 3px solid #00ccb3;
          color: #00ccb3;
        }
      }
    }
  } /*-- /.nav-pills --*/
} /*-- /.middle-sub-nav --*/

.nav-button-left {
  left: 5px;
  top: 5px;
}

.nav-button-right {
  right: 5px;
  top: 5px;
}

.sticky-menu {
  position: sticky !important;
  top: 52px;
  z-index: 100;
  background: white;
  margin: -1px 0 1px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);

  .middle-sub-nav {
    margin: 0;
    border: none;
  }
}

.scroll-margin-with-sticky-menu {
  scroll-margin-top: 105px;
}
