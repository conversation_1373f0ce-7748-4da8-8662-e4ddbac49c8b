// @import url(https://fonts.googleapis.com/css?family=Noto+Sans:400,700);
// @import url(http://fonts.googleapis.com/css?family=Roboto:400,500,700);
/*
Template Name: Minton Dashboard
Author: CoderThemes
Email: <EMAIL>
File: Responsive
*/
@media only screen and (max-width: 6000px) and (min-width: 700px) {
  .wrapper.right-bar-enabled .right-bar {
    right: 0;
    z-index: 99;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  body {
    overflow-x: hidden;
  }
}
@media (max-width: 767px) {
  body {
    overflow-x: hidden;
  }
  .mobile-sidebar {
    left: 0px;
  }
  .mobile-content {
    left: 250px;
    right: -250px;
  }
  .navbar-nav .show .dropdown-menu {
    background-color: #ffffff;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
    left: auto;
    position: absolute;
    right: 0;
  }
  .todo-send {
    margin-top: 10px;
    padding-left: 15px;
  }
  .chat-inputbar {
    padding-left: 15px;
  }
  .chat-send {
    margin-top: 10px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .fixedHeader-floating {
    top: 60px !important;
  }
  div#datatable-buttons_info {
    float: none;
  }
}
@media (max-width: 480px) {
  .side-menu {
    z-index: 10 !important;
  }
  .button-menu-mobile {
    display: block;
  }
  .search-bar {
    display: none !important;
  }
}
@media (max-width: 420px) {
  .hide-phone {
    display: none !important;
  }
}
/* Container-alt */
@media (min-width: 768px) {
  .container-alt {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container-alt {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container-alt {
    width: 1170px;
  }
}
