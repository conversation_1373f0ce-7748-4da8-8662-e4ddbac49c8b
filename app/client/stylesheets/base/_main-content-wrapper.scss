

div.conFullScreen {

  .container {
    background: #ffffff;
    border: 1px solid #d7d7d7;
    border-radius: 5px;
    box-shadow: 0 0 4px #d7d7d7;
    bottom: 30px;
    left: 20px;
    overflow: auto;
    padding: 20px;
    position: absolute;
    right: 20px;
    top: 130px;
    width: auto;
    max-width: 100%;

    &.add-user-screen {
      top: 20px;
    }

    &.report-screen {
      top: 40px
    }

    &.district-reporting-screen {
      top: 70px
    }
  }

  &.medium-width .container {
    width: 60%;
  }

  &.narrow .container {
    width: 45%;
  }

  &.short .container {
    bottom: auto;
  }

  &.password .container {
    top: auto;
  }
}/*-- /.conFullScreen --*/

.main-content {
  display: flex;
  min-height: min-content;
  flex-direction: column;
  overflow: auto;
}

.next-skill-button-container {
  padding-left: 1.9rem !important;
  padding-right: 0 !important;
  float: right;
  clear: right;
}