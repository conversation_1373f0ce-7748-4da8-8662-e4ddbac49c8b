
body {
    background-color: #DFEBF2;
    color: #4c5667;
    font-family: "Lato", sans-serif;
    font-size: 18px;
    overflow-x: hidden !important;
}
html {
    overflow-x: hidden;
    position: relative;
    min-height: 100%;
    background-color: #EFEDE7;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #505458;
    font-family: "Lato", sans-serif;
    margin: 10px 0;
}
h1 {
    line-height: 43px;

    small {
        font-size: 65%;
        font-weight: normal;
        color: #777;
    }
}
h2 {
    line-height: 35px;
}
h3 {
    line-height: 30px;
}
h3 small {
    color: #444444;
}
h4 {
    line-height: 22px;
}
h4 small {
    color: #444444;
}
h5 small {
    color: #444444;
}
p, li{
    font-weight: 200;
}
.m0{margin: 0;}
.w1{font-weight: 100;}
.w2{font-weight: 200;}
.w3{font-weight: 300;}
.w4{font-weight: 400;}
.w5{font-weight: 500;}
.w6{font-weight: 600;}
.w7{font-weight: 700;}
.w8{font-weight: 800;}
.w9{font-weight: 900;}

* {
    outline: none !important;
}
a:hover {
    outline: 0;
    text-decoration: none;
}
a:active {
    outline: 0;
    text-decoration: none;
}
a:focus {
    outline: 0;
    text-decoration: none;
}
.center-image{
  margin: auto;
  display: block;
}

.header-title {
    text-transform: uppercase;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 0.04em;
    line-height: 16px;
    margin-bottom: 8px;
}

.social-links li a {
    -webkit-border-radius: 50%;
    background: #EFF0F4;
    border-radius: 50%;
    color: #7A7676;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    text-align: center;
    width: 30px;
}

