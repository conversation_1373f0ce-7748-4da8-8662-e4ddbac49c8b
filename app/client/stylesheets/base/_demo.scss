/* Demo Only */
.grid-structure .grid-container {
    background-color: #f5f5f5;
    margin-bottom: 10px;
    padding: 10px 20px;
}
.icon-list-demo div {
    cursor: pointer;
    line-height: 45px;
    white-space: nowrap;
    color: #75798B;
}
.icon-list-demo div p {
    margin-bottom: 0px;
    line-height: inherit;
}
.icon-list-demo i {
    -webkit-transition: all 0.2s;
    display: inline-block;
    font-size: 18px;
    margin: 0;
    text-align: center;
    transition: all 0.2s;
    vertical-align: middle;
    width: 40px;
}
.icon-list-demo .col-md-4 {
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    background-clip: padding-box;
}
.icon-list-demo .col-md-4:hover {
    color: #f9cd48;
}
.icon-list-demo .col-md-4:hover i {
    -o-transform: scale(1.5);
    -webkit-transform: scale(1.5);
    moz-transform: scale(1.5);
    transform: scale(1.5);
}
.ionicon-list i {
    font-size: 16px;
}
.ionicon-list .col-md-3:hover i {
    -o-transform: scale(2);
    -webkit-transform: scale(2);
    moz-transform: scale(2);
    transform: scale(2);
}
.button-list {
    margin-left: -8px;
    margin-bottom: -12px;
}
.button-list .btn {
    margin-bottom: 12px;
    margin-left: 8px;
}
