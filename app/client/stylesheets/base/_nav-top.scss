// color: #0066CC !important;
// color: #00CCB3 !important;
#topnav.navbar-default {
  background-color: #005bb5;
  border: 0;
  border-radius: 0;
  margin: 0;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  min-height: 52px;
  position: fixed;
  width: 100%;
  z-index: 1000;
  // border-bottom: 1px solid #ddd;
  .navbar-brand.logo {
    background: transparent url("/images/SM_Logo_2.svg") no-repeat center left;
    image-rendering: auto;
    background-size: contain;
    color: transparent !important;
    height: 42px;
    line-height: 38px;
    margin-top: -4px;
    margin-right: 25px;
    min-width: 155px;

    &.user-guide {
      background-image: url("/images/SM_Logo_Guide.png");
    }
  }
  .navbar-school-year {
    color: rgba(255, 255, 255, 0.7);
    padding-left: 165px;
    font-size: 14px;
    vertical-align: sub;

    &:hover {
      color: white;
    }
  }
}

#topnav.navbar-default .navbar-nav {
  > li > a {
    border-bottom: 3px solid transparent;
    color: #d3d3d3;
    font-size: 0.9em;
    line-height: 20px;
    padding-bottom: 13px;
    &:hover {
      border-bottom: 3px solid #fff;
      color: #fff;
      background: none;
    }
  }
  .dropdown-toggle {
    padding-top: 14px !important;
    padding-bottom: 13px !important;
  }

  > .show {
    a.dropdown-toggle {
      background-color: #fff;
      color: #337ab7;
      padding-top: 15px !important;
      padding-bottom: 14px !important;
    }
  }
}

#topnav.navbar-default .navbar-nav > li {
  > a {
    border-bottom: 3px solid transparent;
    color: #d3d3d3;
    font-weight: 300;
    padding: 13px 15px !important;

    &.active {
      background: none;
      border-bottom-color: #ffffff;
    }
  }
}

#topnav .has-submenu.active a {
  color: #f9cd48;
  i {
    color: #f9cd48;
  }
  .submenu li.active a {
    color: #f9cd48;
  }
}

// #topnav .topbar-main .profile img {
//   border: 2px solid #edf0f0;
//   height: 36px;
//   width: 36px;
// }
#topnav .topbar-main .dropdown-menu-lg {
  width: 300px;
}
#topnav .topbar-main .dropdown-menu-lg .list-group {
  margin-bottom: 0px;
}
#topnav .topbar-main .dropdown-menu-lg .list-group-item {
  border: none;
  padding: 10px 20px;
}
#topnav .topbar-main .dropdown-menu-lg .media-heading {
  margin-bottom: 0px;
}
#topnav .topbar-main .dropdown-menu-lg .media-body p {
  color: #828282;
}
#topnav .topbar-main .notification-list {
  max-height: 230px;
}
#topnav .topbar-main .notification-list em {
  width: 34px;
  text-align: center;
}
#topnav .topbar-main .notification-list .media-body {
  display: inherit;
  width: auto;
  overflow: hidden;
  margin-left: 50px;
}
#topnav .topbar-main .notification-list .media-body h5 {
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
  font-weight: normal;
  overflow: hidden;
}
#topnav .topbar-main .notifi-title {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 11px 20px 15px;
  color: #4c5667;
  font-family: "Roboto", sans-serif;
}
#topnav .topbar-main .navbar-nav {
  margin: 0 0 0 15px;
}
#topnav .app-search {
  position: relative;
  // margin: 15px 20px 15px 10px;
  margin: 14px 20px 13px 10px;
}
#topnav .app-search a {
  position: absolute;
  top: 5px;
  right: 20px;
  color: rgba(255, 255, 255, 0.7);
}
#topnav .app-search .form-control,
#topnav .app-search .form-control:focus {
  border: none;
  font-size: 13px;
  color: #ffffff;
  padding-left: 20px;
  padding-right: 40px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: none;
  border-radius: 30px;
  height: 30px;
  width: 180px;
}
#topnav .app-search input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
}
#topnav .app-search input:-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}
#topnav .app-search input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}
#topnav .app-search input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}
#topnav .notifi-title {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #000000;
  font-size: 16px;
  font-weight: 400;
  padding: 5px 0px 10px;
}
#topnav .notification-list em {
  width: 30px;
  text-align: center;
  height: 30px;
  line-height: 28px;
  border-radius: 50%;
  margin-top: 4px;
}
#topnav .notification-list .list-group-item {
  padding: 12px 20px;
}
#topnav .notification-list .media-body {
  display: inherit;
  width: auto;
  overflow: hidden;
  margin-left: 50px;
}
#topnav .notification-list .media-body h5 {
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
  font-weight: normal;
  overflow: hidden;
}
#topnav .noti-primary {
  color: #f9cd48;
  border: 2px solid #f9cd48;
}
#topnav .noti-success {
  color: #009886;
  border: 2px solid #009886;
}
#topnav .noti-info {
  color: #3ddcf7;
  border: 2px solid #3ddcf7;
}
#topnav .noti-warning {
  color: #ffaa00;
  border: 2px solid #ffaa00;
}
#topnav .noti-danger {
  color: #ef5350;
  border: 2px solid #ef5350;
}
#topnav .noti-purple {
  color: #7266ba;
  border: 2px solid #7266ba;
}
#topnav .noti-pink {
  color: #f76397;
  border: 2px solid #f76397;
}
#topnav .noti-inverse {
  color: #4c5667;
  border: 2px solid #4c5667;
}
#topnav .navbar-custom {
  background-color: #ffffff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
#topnav .navbar-toggle {
  border: 0;
  position: relative;
  width: 60px;
  height: 60px;
  padding: 0;
  margin: 0;
  cursor: pointer;
}
#topnav .navbar-toggle:hover {
  background-color: transparent;
}
#topnav .navbar-toggle:hover span {
  background-color: #ffffff;
}
#topnav .navbar-toggle:focus {
  background-color: transparent;
}
#topnav .navbar-toggle:focus span {
  background-color: #f9cd48;
}
#topnav .navbar-toggle .lines {
  width: 25px;
  display: block;
  position: relative;
  margin: 23px auto 17px auto;
  height: 18px;
}
#topnav .navbar-toggle span {
  height: 2px;
  width: 100%;
  background-color: #fff;
  display: block;
  margin-bottom: 5px;
  -webkit-transition: -webkit-transform 0.5s ease;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
}
#topnav .navbar-toggle.show span {
  position: absolute;
}
#topnav .navbar-toggle.show span:first-child {
  top: 6px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
#topnav .navbar-toggle.show span:nth-child(2) {
  visibility: hidden;
}
#topnav .navbar-toggle.show span:last-child {
  width: 100%;
  top: 6px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
#topnav .navigation-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
#topnav .navigation-menu > li {
  float: left;
  display: block;
  position: relative;
}
#topnav .navigation-menu > li > a {
  display: block;
  color: #797979;
  font-weight: 500;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  line-height: 20px;
  padding-left: 20px;
  padding-right: 20px;
}
#topnav .navigation-menu > li > a:hover {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a:hover i {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a:focus {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a:focus i {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a:active {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a:active i {
  color: #f9cd48;
}
#topnav .navigation-menu > li > a i {
  font-size: 18px;
  margin-right: 5px;
  color: #98a6ad;
}

#topnav .dropdown-menu {
  max-height: calc(100vh - 60px);
  overflow: auto;
}

#topnav .navbar-right {
  margin-left: 0;
}

#topnav .navigation-menu > li > a:hover,
#topnav .navigation-menu > li > a:focus {
  background-color: transparent;
}
@media (min-width: 992px) {
  #topnav .navigation-menu > li > a {
    padding-top: 22px;
    padding-bottom: 22px;
  }
}
/*
Responsive Menu
*/
@media (min-width: 992px) {
  #topnav .navigation-menu > li.last-elements .submenu {
    left: auto;
    right: 0;
  }
  #topnav .navigation-menu > li.last-elements .submenu > li.has-submenu .submenu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: 10px;
  }
  #topnav .navigation-menu > li:first-of-type a {
    padding-left: 0px;
  }
  #topnav .navigation-menu > li:hover a {
    color: #f9cd48;
  }
  #topnav .navigation-menu > li:hover a i {
    color: #f9cd48;
  }
  #topnav .navigation-menu > li .submenu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    border: 1px solid #e7e7e7;
    padding: 15px 0;
    list-style: none;
    min-width: 200px;
    visibility: hidden;
    opacity: 0;
    margin-top: 10px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    background-color: #ffffff;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
  }
  #topnav .navigation-menu > li .submenu.megamenu {
    white-space: nowrap;
    width: auto;
  }
  #topnav .navigation-menu > li .submenu.megamenu > li {
    overflow: hidden;
    width: 200px;
    display: inline-block;
    vertical-align: top;
  }
  #topnav .navigation-menu > li .submenu > li.has-submenu > a:after {
    content: "\e649";
    font-family: "themify";
    position: absolute;
    right: 20px;
    font-size: 9px;
  }
  #topnav .navigation-menu > li .submenu > li .submenu {
    left: 100%;
    top: 0;
    margin-left: 10px;
    margin-top: -1px;
  }
  #topnav .navigation-menu > li .submenu li {
    position: relative;
  }
  #topnav .navigation-menu > li .submenu li ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
  }
  #topnav .navigation-menu > li .submenu li a {
    display: block;
    padding: 8px 25px;
    clear: both;
    white-space: nowrap;
    font-weight: 500;
    color: #494e53;
  }
  #topnav .navigation-menu > li .submenu li a:hover {
    color: #f9cd48;
  }
  #topnav .navigation-menu > li .submenu li span {
    display: block;
    padding: 8px 25px;
    clear: both;
    line-height: 1.42857143;
    white-space: nowrap;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    color: #949ba1;
  }
  #topnav .navbar-toggle {
    display: none;
  }
  #topnav #navigation {
    display: block !important;
  }
}
@media (max-width: 991px) {
  .wrapper {
  }
  #topnav .navigation-menu {
    float: none;
    max-height: 400px;
  }
  #topnav .navigation-menu > li {
    float: none;
  }
  #topnav .navigation-menu > li > a {
    color: #797979;
    padding: 15px;
  }
  #topnav .navigation-menu > li > a i {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 0px;
  }
  #topnav .navigation-menu > li > a:after {
    position: absolute;
    right: 15px;
  }
  #topnav .navigation-menu > li .submenu {
    display: none;
    list-style: none;
    padding-left: 20px;
    margin: 0;
  }
  #topnav .navigation-menu > li .submenu li a {
    display: block;
    position: relative;
    padding: 7px 20px;
    color: #797979;
  }
  #topnav .navigation-menu > li .submenu li a:hover {
    color: #f9cd48;
  }
  #topnav .navigation-menu > li .submenu li.has-submenu > a:after {
    content: "\e64b";
    font-family: "themify";
    position: absolute;
    right: 30px;
  }
  #topnav .navigation-menu > li .submenu.show {
    display: block;
  }
  #topnav .navigation-menu > li .submenu .submenu {
    display: none;
    list-style: none;
  }
  #topnav .navigation-menu > li .submenu .submenu.show {
    display: block;
  }
  #topnav .navigation-menu > li .submenu.megamenu > li > ul {
    list-style: none;
    padding-left: 0;
  }
  #topnav .navigation-menu > li .submenu.megamenu > li > ul > li > span {
    display: block;
    position: relative;
    padding: 15px;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 2px;
    color: #79818a;
  }
  #topnav .navigation-menu > li.has-submenu.show > a {
    color: #f9cd48;
  }
  #topnav .navbar-header {
    float: left;
  }
  #navigation {
    position: absolute;
    top: 60px;
    left: 0;
    width: 100%;
    display: none;
    height: auto;
    padding-bottom: 0;
    overflow: auto;
    border-top: 1px solid #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    background-color: #fff;
  }
  #navigation.show {
    display: block;
    overflow-y: auto;
  }
}
@media (min-width: 768px) {
  #topnav .navigation-menu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-top: 0;
  }
  #topnav .navigation-menu > li.has-submenu:hover > .submenu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-left: 0;
    margin-right: 0;
  }
  .navbar-toggle {
    display: block;
  }
}

.environmentType {
  position: absolute;
  top: 1px;
  padding-left: 165px;
  line-height: 1;
}
