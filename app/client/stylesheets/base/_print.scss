@import "{}/client/stylesheets/base/colors";

/* =============
Print css
============= */
#print-iframe {
  position: absolute;
}

.page-break-after {
  page-break-after: always;
}

.avoid-page-break-after {
  page-break-after: avoid;
}

.page-break-before {
  page-break-before: always;
}

.avoid-page-break-before {
  page-break-before: avoid;
}

.page-break-inside {
  page-break-inside: auto;
}

.avoid-page-break-inside {
  page-break-inside: avoid;
}

.page-break-avoid {
  break-inside: avoid;
}

.screening-page-graph:last-of-type {
  page-break-after: avoid;
  break-after: avoid;
}

@media print {
  .logo,
  .breadcrumb,
  .page-title,
  .footer,
  .topbar-main {
    display: none;
    margin: 0px;
    padding: 0px;
  }
  .left,
  .right-bar {
    display: none;
  }
  .wrapper {
    margin-top: 0px !important;
    padding-top: 0px;
  }
  .content-page {
    margin-left: 0px !important;
    margin-top: 0px;
    overflow-x: hidden;
  }
  .highcharts-root {
    position: absolute;
    left: 0;
  }

  .goal-skills {
    display: inline-block;
    margin-right: 2em;
    width: 100%;
  }

  // Fix for cutoff graph border
  .student-detail-pm-chart,
  .student-detail-chart {
    padding: 0;
    > .highcharts-container {
      width: 100% !important;
    }
  }

  .class-performance {
    border: 2px solid #edf0f0;

    .highcharts-container {
      width: 100% !important;
    }
  }

  .intervention-stat {
    margin: 0 !important;
    margin-right: 0.8em !important;

    label {
      margin: 0 !important;
      margin-left: 0.2em !important;
    }
  }

  .intervention-recommendation {
    border: 0;
    padding: 0;
  }

  .individual-interventions-group {
    border-bottom: 0 !important;
  }

  .measure-fail {
    color: red !important;
  }

  div.intervention-progress {
    .progress {
      position: relative;
    }
    .progress:before {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 0;
      border-bottom: 2rem solid #eeeeee;
    }

    .progress-bar {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      z-index: 1;
      border-bottom: 2rem solid #009886 !important;
    }
  }

  span > .progress {
    background-color: #ededed;
    border-radius: 3px;
    height: 25px;
    margin-bottom: 0;

    .progress-bar {
      background-color: #21cbb3;
      color: #505458;
      font-size: 16px;
      font-style: italic;
      line-height: 25px;
    }
  }

  .progress-bar-success {
    border-bottom-color: #009886 !important;
  }

  .growth-legend-item {
    &.fall-icon {
      background: $sw-orange !important;
    }
    &.winter-icon {
      background: $sw-dark-blue !important;
    }
    &.spring-icon {
      background: $sw-steel-blue-30 !important;
    }
    &.classwide-icon {
      background: $sw-violet !important;
    }
  }

  a[href]:after {
    content: none !important;
  }

  div.text-danger {
    color: #000 !important;
  }

  span.text-success {
    color: #000 !important;
  }

  small.text-danger {
    color: #000 !important;
  }

  .progress-table {
    font-size: 11px;

    th {
      font-size: 11px;
    }
  }
}

.custom-forced-print {
  .logo,
  .breadcrumb,
  .page-title,
  .footer,
  .topbar-main {
    display: none;
    margin: 0px;
    padding: 0px;
  }
  .left,
  .right-bar {
    display: none;
  }
  .wrapper {
    margin-top: 0px !important;
    padding-top: 0px;
  }
  .content-page {
    margin-left: 0px !important;
    margin-top: 0px;
    overflow-x: hidden;
  }

  .highcharts-root {
    position: absolute;
    left: 0;
  }

  .goal-skills {
    display: inline-block;
    margin-right: 2em;
  }

  .intervention-stat {
    margin: 0 !important;
    margin-right: 0.8em !important;

    label {
      margin: 0 !important;
      margin-left: 0.2em !important;
    }
  }

  .measure-fail {
    color: red !important;
  }

  a[href]:after {
    content: none !important;
  }

  .clickable-skill {
    color: black !important;
  }

  .progress-table {
    font-size: 11px;

    th {
      font-size: 11px;
    }
  }
}
