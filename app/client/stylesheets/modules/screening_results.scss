.screening-results {
  list-style: none;
  margin: 0;
  &.item {
    display: inline-block;
    width: 100%;
  }
  h2,
  h3,
  h4 {
    margin: 0;
    padding: 0;
  }
  .data-sub {
    color: #777;
    font-size: 0.8em;
    font-style: italic;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0px 5px;
  }
}

.left-result {
  min-height: 100%;
  float: left;
  padding: 20px;
  vertical-align: middle;
  width: 25%;
  h1 {
    border: 3px solid #fff;
    border-radius: 50%;
    color: #fff;
    font-size: 3.8vw;
    margin: 0;
    padding: 42px 3px;
    text-align: center;
    width: 180px;
    small {
      color: #fff;
      display: block;
      font-size: 25%;
      font-style: italic;
      font-weight: 300;
      line-height: 1.2em;
      margin-top: 5px;
      text-align: center;
    }
  }
}

.cw-underline {
  font-style: italic;
  text-decoration: underline;
}

.classwide-result {
  // background: #fafafa;
  // border: 1px solid #ccc;
  // padding: 15px 10px 0;
  p,
  h3 {
    margin: 0 10px 10px;
  }
}

.big-stats {
  // background: #fafafa;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: row; /* works with row or column */
  flex-direction: row;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  margin-top: 25px;
  li {
    cursor: pointer;
    display: inline-block;
    margin: 10px;
    padding: 1px;
    vertical-align: top;
    width: 25%;
    &:hover {
      opacity: 0.7;
    }
    &.active {
      border-top: 1px solid #ccc;
      border-right: 1px solid #ccc;
      border-left: 1px solid #ccc;
      background: #fff;
      cursor: default;
      padding: 0 0 10px;
      position: relative;
      opacity: 1;
      top: 5px;
      z-index: 4;
    }
    .fa-check {
      color: #009886;
    }
    .fa-exclamation {
      color: #555;
    }
    .fa-fail {
      color: red;
    }
  }
  .measure-fail {
    color: red !important;
  }
}

.intervention-recommendation {
  background: #fff;
  border: 1px solid #ccc;
  margin: 0 auto;
  padding: 15px 10px 5px;
  position: relative;
  top: -7px;
  z-index: 0;
  h5 {
    font-style: italic;
    font-weight: 500;
    margin: 30px 0 15px;
  }
}

.student-cards {
  margin: 0 0 20px;
  display: -webkit-flex;
  display: flex;
  flex-wrap: wrap;
  -webkit-flex-direction: row; /* works with row or column */
  flex-direction: row;
  -webkit-align-items: center;
  align-items: center;
  // -webkit-justify-content: center;
  // justify-content: center;
  .no-flow {
    display: block;
  }
}

.student-intervention-card {
  background: #fff;
  border-radius: 5px;
  border: 1px solid #ddd;
  cursor: pointer;
  display: inline-block;
  margin: 0 10px;
  padding: 10px;
  min-height: 210px;
  text-align: center;
  width: calc(100% * (1 / 4) - 10px - 10px);
  h2 {
    color: #a1a1a1;
    font-size: 55px;
    line-height: 42px;
    margin: 15px 0 10px;
  }
  h4 {
    border-bottom: 1px solid #ddd;
    color: #a1a1a1;
    font-weight: 200;
    height: 80px;
  }
  input {
    margin-top: 30px;
  }
  &:hover,
  &.active {
    h2 {
      color: #d96060;
    }
    h4 {
      color: #555;
    }
  }
  .student-name {
    font-weight: 400;
    padding: 7px 0;
  }
  .student-score {
    font-weight: 700;
  }
  .intervention-choice {
    label {
      display: block;
      padding: 7px;
    }
    &.invisible {
      visibility: hidden;
    }
  }
}

.screening-recommendation {
  min-height: 120px;
  padding: 1em;

  p {
    color: #828282;
    font-size: 0.9em;
    font-style: italic;
    text-align: right;
  }
}
