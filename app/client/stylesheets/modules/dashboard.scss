.modal .math-spring-tutorial-modal.modal-dialog{
  .modal-content{
    padding: 20px;
    .modal-header{
      margin-bottom: 15px;
    }
    .modal-body{
      padding: 0;
    }
  }
  h1{
    margin: 0;
  }
}

ul.math-spring-tutorial{
  list-style: none;
  margin: 15px auto 0;
  padding: 0;
  width: 98%;
  li{
    font-size: 0.9em;
    margin-bottom: 25px;
    .steps{
      background: #f3f3f3;
      border-radius: 50%;
      float: left;
      font-size: 1.4em;
      font-weight: 700;
      height: 50px;
      margin: 5px 15px 30px 5px;
      padding: 13px;
      text-align: center;
      width: 50px;
    }
    h4{
      margin: 0;
    }
    span{
      display: block;
    }
  }
}

div.conInterventionMessageNotice {
  background: #21cbb3; // Mint Green
  background-clip: padding-box;
  border: 2px solid #21cbb3; // Mint Green
  border-radius: 8px;
  margin: 10px 0;
  overflow: hidden;
  height: 36px;

  &.conInterventionMessageWarning {
    background: #ef5350;
    border-color: #ef5350;
    opacity: 1;
  }

  div.conInterventionMessageNotice-Heading {
    color: #ffffff;
    padding: 2px 2px 0 8px;

    h2 {
      color: #ffffff;
      float: left;
      font-size: 0.75em;
      font-weight: bold;
      line-height: 2;
      margin: 0 10px;
      width: 80%;
    }

    button.btnNoticeAction {
      background-color: #ffffff;
      color: #000000;
      opacity: .5;
      border: none;
      border-radius: 5px;
      float: right;
      font-size: 14px;
      line-height: 1.4;
      margin: 0 3px 0 0;
      padding: 4px 8px !important;
      text-align: left;
      //width: 150px;

      &:after {
        display: block;
        float: right;
        font: 15px 'FontAwesome';
        margin: 4px 3px 0 7px;
      }
    }/*-- /.btnNoticeAction --*/


    div.iconCallout {
      float: left;
      margin-top: 0;
    }

  }/*-- /conScreeningNotice-Heading --*/

}/*-- /.conScreeningNotice --*/

tr.mid-year-goal, tr.end-year-goal {
  border-bottom: 2px solid green;

  th:first-child {
    position: relative;

    &:after {
      position: absolute;
      display: block;
      white-space: nowrap;
      bottom: -9px;
      left: 50%;
      transform: translateX(-50%);
      content: "Mid-Year Goal";
      color: green;
      font-size: 13px;
      background: white;
      padding: 0 2px;
    }
  }
}

tr.end-year-goal th:first-child:after {
  content: "End of Year Goal";
}
