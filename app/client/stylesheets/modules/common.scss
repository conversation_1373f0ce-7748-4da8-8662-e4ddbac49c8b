@import "{}/client/stylesheets/base/colors";

div.information-box {
  border: 1px solid #dddddd;
  border-radius: 5px;
  margin: 0 auto 50px;
  padding: 15px 125px;
  position: relative;
  width: 75%;
  max-width: 750px;
  background-color: white;

  &.information-box-notice {
    padding: 15px 125px 15px 180px;
    &:before {
      content: "\f0a1";
      color: #005bb5;
      display: block;
      font: 3em "FontAwesome";
      left: 110px;
      position: absolute;
      top: 25px;
    }
  }
}

.relativeWrapper {
  position: relative;
}

.buttonInPageHeader {
  position: absolute;
  top: 50%;
  right: 0;
  margin-right: 20px;
  transform: translate(0, -50%);
}

div.printable-information-box {
  border: 1px solid #dddddd;
  border-radius: 5px;
  margin: 0 auto 50px;
  padding: 15px 25px;
  position: relative;
  width: 90%;
  background-color: white;
}

.text-special {
  color: $sw-orange !important;
}

.white-space-normal {
  white-space: normal;
}

.max-h-30 {
  overflow-y: auto;
  max-height: 30rem;
  overflow-x: hidden;
}

.printContainer {
  background-color: white;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
  print-color-adjust: exact;
  max-width: 190mm !important;

  .nav {
    display: none;
  }

  .print-clear {
    clear: both;
    width: 100% !important;
  }

  .print-display {
    display: block !important;
    width: 100% !important;
    .intervention-skill-list {
      overflow: auto;
    }
  }

  .skill-map-scroll {
    height: auto !important;
    overflow-y: visible !important;
  }

  .goal-skill-list {
    height: auto !important;
    overflow-y: visible !important;
  }

  p,
  li,
  small,
  .rowIndvSummaryHeading {
    color: #000 !important;
  }

  .district-reporting-school-card-group {
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
  }

  h4 {
    span.roi {
      float: right;
      font-size: 0.75em;
      font-weight: normal;
    }
  }
}

.addTeacherBackground {
  padding: 15px 15px;
  background-color: #f4f4f4;
  margin-bottom: 10px;
}
.addTeacherBackground h3 {
  font-size: 18px;
  font-weight: 700;
}
#treeContainer {
  width: 100%;
  overflow: scroll;
  .node {
    cursor: pointer;
  }

  .node circle {
    fill: #fff;
    stroke: steelblue;
    stroke-width: 3px;
  }

  .node circle.active {
    fill: #fff;
    stroke: black;
    stroke-width: 3px;
  }

  .node text {
    font: 12px sans-serif;
  }

  .link {
    fill: none;
    stroke: #ccc;
    stroke-width: 2px;
  }
}

.choose-rule-text {
  line-height: 30px;
}

.s-alert-wrapper,
.s-alert-box {
  z-index: 2000 !important;
  overflow-y: auto;
  max-height: calc(100% - 100px);
}

.header-action-button-container {
  position: absolute;
  right: 25px;
  top: 15px;
}

.input-no-arrows {
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}

.overflow-break {
  overflow-wrap: break-word;
}

.d-block {
  display: block;
}

.f-right {
  float: right;
}

.fuzzyBox {
  position: absolute;
  max-height: 200px;
  overflow: auto;
  padding: 0;
  width: 100%;
  background-color: white;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 3px 3px;
  z-index: 9001;

  .fuzzyBox-header {
    font-size: 85%;
    border-bottom: 1px solid #ccc;
    padding: 5px;
  }

  .fuzzyBox-item {
    font-size: 85%;
    padding: 5px;
    cursor: pointer;

    &:hover {
      background: #eee;
    }
  }
}

.text-black {
  color: #000;
}

.debug-small {
  position: absolute;
  font-size: 8pt;
  width: 100%;
  left: 0;
  top: -18px;
  color: #000;
}

.cursor-pointer {
  cursor: pointer;
}

.org-icon {
  width: 30px;
  height: 30px;
  background: #6254b2;
  color: white;
  border-radius: 6px;
  font-size: 0.7rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.org-icon--sso {
  background: #21cbb3;
}
.org-icon--sso-only {
  background: #ef5350;

  .small {
    display: block;
    font-size: 0.5rem;
  }
}

.org-icon--mfa {
  background: #1a63a2;
}

.cursor-not-allowed {
  cursor: not-allowed;
  pointer-events: all !important;
}

.text-grey {
  color: #4c5667;
}

.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  padding: 10px 0;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #ddd;
  z-index: 1;

  & + .individual-interventions-group {
    border-top: none !important;
  }
}

.text-overflow {
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  width: 80%;
  height: 1.1em;
  line-height: 1.1rem;
  vertical-align: text-bottom;
}

.tooltip-wrapper {
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.26);
  border-radius: 5px;
}

.tooltip-wrapper .tooltip-inner {
  max-width: 500px !important;
  white-space: normal;
  padding: 0 5px;
  color: #4c5667;
}

textarea.dynamic-size {
  resize: vertical;
  overflowY: auto;
}

textarea.dynamic-size[data-overflow="true"]:not(:focus) {
  box-shadow: inset 0px -5px 10px -5px rgba(0, 0, 0, 0.2);
}

.icon-outline {
  border: 1px solid black;
  width: 22px;
  height: 22px;
  border-radius: 4px;
}




