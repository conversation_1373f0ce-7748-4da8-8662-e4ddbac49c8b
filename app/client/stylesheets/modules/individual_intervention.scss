.skill-group-cell-overflow {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
}

.skill-group-cell-overflow:hover {
    overflow: visible;
    white-space: normal;
    height:auto
}

.skill-container.individual{
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    & + & {
        margin-top: 1em;
    }

    .skill-details{
        background: #fff;
        border-right: 1px solid #ddd;
        float: left;
        padding: 10px 15px 15px;
        width: 70%;
        small{
            color: #ababab;
            display: block;
            font-size: 80%;
            font-style: italic;
            font-weight: 300;
            margin: 0 0 5px;
            vertical-align: super;
            .profile-link{
                font-weight: 700;
                text-decoration: underline;
            }
        }
        &.individual-complete {
            border: none;
            width: 100%;
        }
    }
    .skill-score-entry{
        background: #fff;
        float: right;
        padding: 20px 15px 15px;
        width: 30%;
        list-style: none;
        label{
            font-size: 0.7em;
            font-weight: normal;
            margin-bottom: 3px;
            margin-top: 15px;
        }

        li {
            position: relative;

            .help-block{
                font-weight: 900;
                font-size: 10px;
                line-height: 1.3em;
                padding: 8px 20px 0 20px;
                position: absolute;
                text-decoration: none;
                top: 30px;
                vertical-align: middle;
                width: 100%;

                &.text-success {
                    font-weight: 300;
                    color: #0dd006;
                }
                &.text-warning {
                    white-space: nowrap;
                    padding: 8px 0 0 0;
                }
            }
        }


        label.last-entry{
            border-top: 1px solid #ededed;
            color: #afafaf;
            display: block;
            font-size: 0.7em;
            font-style: italic;
            font-weight: 300;
            padding: 10px;
            text-align: center;
        }
        .form-control{
            background: #fff;
            border: 1px solid #ccc;

        }

        .btn {
            font-size: .8em;
            margin-top: 20px;
            padding: 6px 16px;

            .fa {
                margin-right: 10px;
                margin-left: 0px;
            }
        }
    }
    .skill-graph{
        background: #f8f8f8;
        border-top: 1px solid #ddd;
        float: left;
        padding: 10px 15px 15px;
        width: 100%;
        h5{
          font-weight: 600;
          margin: 5px 0;
          padding: 2px 10px;
          small{
            float: left;
            font-size: 0.925rem;
            margin-top: 5px;
            margin-right: 20px;
            font-weight: normal;
            border-bottom: 1px dotted #444;
          }
          span.roi{
            float: right;
            font-size: 0.85em;
            margin-top: 5px;
          }
        }
        .chart{
            background: #cecece;
            margin-bottom: 25px;
            padding: 1px;
        }
    }
}
