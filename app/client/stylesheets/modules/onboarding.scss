

.conOnBoarding {
  background: #ffffff;
  margin: 50px 0 0;
  padding: 0 25px;

  h4 {
    color: #767676;
  }

  .form-group{
    overflow: hidden;

    label {
      font-size: 0.8em;
      font-weight: 400;
    }

    input.form-control {
      color: #454545;
      font-weight: 700;
    }

    .confirmName {
      cursor: not-allowed;
    }
  }
}

.next-steps{
  background-color: #BFDBF5;
  border-radius: 5px;
  padding: 0 30px 15px;
  margin-bottom: 20px;
  
  p {
    color: #fff;
    margin: 22px 0 10px;
    
    .btn {
      margin-left: 10px;
    }
  }
  
  a.prev {
    color: #ccc;
  }
}

.Checkout {
  align-content: center;
  max-width: 700px;
  box-sizing: border-box;
  margin: 10px;
  border: 1px solid lightgrey;
  padding: 0 5px;
  .card-element {
    //max-width: 500px;
  }
  &.Checkout-Errors {
    color: red;
    border: none;
    text-align: center;
  }

  .row{
    min-height: 35px;
  }

  > form {
    padding: 20px;
    > label {
      width: 600px;
    }
  }
  button{
    margin-top: 20px;
  }
}
