.intervention-dots{
  display: -webkit-flex; /* Safari */
  display: flex;
  // display: -webkit-inline-flex; /* Safari */
  // display: inline-flex;

  -webkit-flex-direction: column; /* Safari */
  flex-direction:         column;

  -webkit-flex-wrap: nowrap; /* Safari */
  flex-wrap:         nowrap;

  -webkit-justify-content: space-between; /* Safari */
  justify-content:         space-between;

  //height: 400px;
  .skill-list-title{
    margin-bottom: 10px;
    margin-top: 10px;
    label{
      font-size: 0.7em;
      font-weight: 400;
    }
  }

  .skill-item-mid-year-goal {
    margin-top: -25px;
    margin-bottom: -6px;
    position: relative;
    color: #3c763d;
    font-weight: bold;

    & > div {
      width: 100%;
      text-align: center;
      border-bottom: 1px solid #3c763d;
      line-height: 0.1em;
      margin: 10px 0 20px;
    }

    label {
      display: block;
      float: right;
      font-size: 0.7em;
      font-weight: 400;
      margin: 0;
      text-align: center;
      width: 100%;

      div {
        background: #fff;
        padding: 0 5px;
        display: inline;
      }
    }
  }

  .skill-item-end-year-goal {
    & > div {
      margin-bottom: 35px;

      label div {
        position: relative;
        top: -1px;
        display: inline-block;

        &.subtitle {
          top: 10px;
          width: 100%;
        }
      }
    }
  }

  .skill-item{
    border-left: 1px solid #ddd;
    position: relative;
    //padding-bottom: 20px;
    margin-left: 26px;
    opacity: 0.6;
    &:last-child {
      border-left: none;
    }
    &.active{
      opacity: 1;
    }
    label{
      display: block;
      float: right;
      font-size: 0.7em;
      font-weight: 400;
      margin: 0 0 30px;
      text-align: left;
      width: 90%;

      &.active {
        cursor: pointer;
      }
    }

    label.clickable-skill {
      cursor: pointer;
      color: #4991E6;
    }

    &.selected > label {
      color: #007EFF;
      font-weight: bold;
    }

    .skill-button {
      position: absolute;
      left: -44px;
      top: -5px;

      &:hover .fa {
        background: #337ab7;
      }
    }

    .fa{
      background: #fff;
      position: absolute;
      left: -8px;
    }

    .fa-video-camera {
      position: relative;
      left: auto;
      font-size: 0.8rem;
    }
    .fa-check, .fa-check:before {
      color: #009886 !important;
    }
    .fa-circle, .fa-circle:before {
      color: #00CCB3 !important;
    }
    .fa-circle-o.active, .fa-circle-o.active:before {
      color: #00CCB3 !important;
    }

    span {
      background-color: #00CCB3;
      line-height: 20px;
      float: right;
      color: white;
      border-radius: 3px;
      padding-left: 5px;
      padding-right: 5px;
    }
  }
  .skill-item.individual-skill-item{
    border-left:  none;
    min-height: 50px;
    position: relative;
    margin-left: 10px;
    //margin-top: 5px;
    //margin-bottom: 5px;
    padding: 0 2px 0 2px;
    opacity: 0.6;
  }
  .skill-item.individual-skill-item .selected{
    background-color: rgba(#00CCB3, 0.4);
    //border: 2px solid #00CCB3;
    opacity: .6;
    padding: 3px 3px 3px 3px;
    border-radius: 3px;
    color: black;
    font-weight: bold;
  }
  .skill-item.individual-skill-item-goal{
    border-left:  1px solid #ddd;
    min-height: 50px;
    position: relative;
    margin-left: 10px;
    //margin-top: 5px;
    //margin-bottom: 5px;
    padding: 0 2px 0 2px;
    opacity: 0.6;
  }
  .skill-item.individual-skill-item-goal .selected{
    background-color: rgba(#00CCB3, 0.4);
    //border: 2px solid #00CCB3;
    //border-right: none;
    opacity: .6;
    padding: 3px 3px 3px 3px;
    border-radius: 3px;
    color: black;
    font-weight: bold;
  }
  .skill-item.individual-skill-item-last-goal{
    border-left: none;
  }
}
.intervention-skill-list {
  //border: solid;
  //border-color: rgba(#00CCB3, 0.5);
  //border-radius: 7px;
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 350px;
}
.goal-skill-list {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 450px;
}

.skill-progress{
  // background: #efede6;
  // border-radius: 3px;
  padding: 10px 20px;
  h4{
    font-weight: 400;
    padding: 0 5px 3px;
  }
  .skill-map{
    padding: 0 10px 0 0;
    position: relative;
  }
  .skill-map-scroll{
    padding: 0 10px 0 0;
    position: relative;
    height: 450px;
    overflow-y: auto;
    margin-top: 20px;
  }
  .progress{
    height: 4px;
    .progress-bar{
      font-size: 16px;
      font-weight: 300;
      line-height: 20px;
    }
  }
}

.ends{
  background: #fff;
  border: 1px solid #ddd;
  display: inline-block;
  font-size: 0.85em;
  padding: 5px 35px;
  position: relative;
  left: -10px;
}
