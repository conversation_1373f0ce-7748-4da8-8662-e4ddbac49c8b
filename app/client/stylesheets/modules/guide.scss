.toc {
  display: -webkit-flex; /* Safari */
  display: flex;

  -webkit-flex-direction: row; /* Safari */
  flex-direction:         row;

  -webkit-flex-wrap: nowrap; /* Safari */
  flex-wrap:         nowrap;

  -webkit-justify-content: space-between; /* Safari */
  justify-content:         space-between;
  margin: auto;
  width: 90%;
}

.conGuide {
  $posSpacing: 20px;
  background: #ffffff;
  bottom: 20px;
  left: 0;
  overflow: auto;
  right: 0;
  top: $posSpacing;
  max-width: 1000px;
  padding-left: 20px;
  padding-right: 20px;

  .main-content & {
    margin: 0 auto;
    position: static;
    text-align: center;
    width: 100% !important;

    h1 {
      font-weight: 700;
      margin-bottom: 50px;
    }

    .guideBoxItem {
      text-align: left;
      & > h4 {
        white-space: nowrap;
      }
      & > p {
        font-size: 0.9em;
      }
    }

    @media (max-width: 1050px) {
      .guideNumBox {
        & > h4 {
          font-size: 1em;
        }
        & > p {
          font-size: 0.8em;
        }
      }
    }

    .btnStartScreening {
      display: block;
      margin: 0 auto;
      padding: 10px;
      width: 35%;
    }
  }

  header {
    border-bottom: 1px solid #ddd;
    margin-bottom: 50px;
  }

  .sticky {
    background: #fff;
    border-bottom: 1px solid red;
    box-shadow: 0 0 5px 10px #ccc;
    z-index: 10;
  }

  .nav.nav-pills {
    & > li {
      border-radius: 0;
      margin: 0;
      > a {
        color: #555;
      }
      &.active > a {
        background-color: #fff;
        border-bottom: 2px solid #005BB5;
        border-radius: 0;
        color: #555;
        font-weight: 500;
      }
    }
  }

  div.two-col {
    $colGap: 40px;
    -moz-column-count: 2;
    -moz-column-gap: $colGap;
    -webkit-column-count: 2;
    -webkit-column-gap: $colGap;
    column-count: 2;
    column-gap: $colGap;

    ul li {
      list-style: none;

      button.guideLink {
        margin: -40px;
        padding: 0;
        font-style: italic;
      }
    }
  }

  button.guideBackToTop {
    margin: 0;
    padding: 0;
  }


  .guideBoxList {
    list-style: none;
  }

  .guideBoxItem {
    border: 1px solid lightgrey;
    position: relative;
    padding: 20px;
    height: 150px;
    text-align: center;
    width: 30%;
    float: left;
    margin: 10px;

    .guideProcessNumber {
      // background-color: #F3F4F6;
      // border-radius: 30px;
      // color: #0961CD;
      // font-size: 36px;
      // font-weight: bold;
      // padding: 5px 15px;
      // position: relative;
      // top: -35px;
      $numSize: 60px;

      background-color: #F3F4F6;
      border-radius: 30px;
      color: #000;
      font-size: 36px;
      font-weight: bold;
      left: calc(50% - (#{$numSize} / 2));
      position: absolute;
      top: -30px;
      text-align: center;
      height: $numSize;
      width: $numSize;
      line-height: $numSize;
    }

    .guideProcessTitle {
      font-size: 1.1em;
      font-weight: bold;
      margin-top: 20px;
    }

  }

  .learnMoreItem {
    font-size: larger;
    padding-top: 40px;
  }

  .faqItem {
    font-size: larger;
    font-style: italic;
  }

  .guideNumBox {
    margin: 30px 10px;
    height: 240px;
  }



  ul li {
    margin-bottom: 15px;
  }
  .guide-img{
    border: 5px solid #efefef;
    margin: 0 40px 15px;
    padding: 5px;
  }
  .video{
    display: -webkit-flex; /* Safari */
    display: flex;

    -webkit-flex-direction: row; /* Safari */
    flex-direction:         row;

    -webkit-flex-wrap: nowrap; /* Safari */
    flex-wrap:         nowrap;

    -webkit-justify-content: center; /* Safari */
    justify-content:         center;
  }
  section{
    margin: 50px 0 100px;
  }
}

.map{
  margin: 75px 0 0;
}

.bar{
  background: #efefef;
  border-radius: 15px;
  height: 12px;
  width: 100%;
}
.label-container{
  display: -webkit-flex; /* Safari */
  display: flex;

  -webkit-flex-direction: row; /* Safari */
  flex-direction:         row;

  -webkit-flex-wrap: nowrap; /* Safari */
  flex-wrap:         nowrap;

  -webkit-justify-content: space-around; /* Safari */
  justify-content:         space-around;
  .step-label{
    text-align: center;
    width: 250px;
  }
}

.process{

  display: -webkit-flex; /* Safari */
  display: flex;

  -webkit-flex-direction: row; /* Safari */
  flex-direction:         row;

  -webkit-flex-wrap: nowrap; /* Safari */
  flex-wrap:         nowrap;

  -webkit-justify-content: space-around; /* Safari */
  justify-content:         space-around;

  .step{
    position: relative;
    max-width: 230px;
    z-index: 5;
    .placer{
      position: absolute;
      left: 0;
      right: 0;
      top: -50px;
      margin-left: auto;
      margin-right: auto;
      max-width: 230px;
      .fa-circle{
        color: #99CC33;
        font-size: 30px;
      }
      .marker{
        background: #efefef;
        border-top: 1px solid #ddd;
        border-right: 1px solid #ddd;
        height: 20px;
        width: 20px;
        margin: 7px auto;
        -ms-transform: rotate(-45deg); /* IE 9 */
        -webkit-transform: rotate(-45deg); /* Safari */
        transform: rotate(-45deg);
      }
    }
    .content{
      background: #efefef;
      border: 1px solid #ddd;
      font-size: 0.85em;
      padding: 10px 15px;
      top: 27px;
      position: relative;
      text-align: center;
    }
  }
}
