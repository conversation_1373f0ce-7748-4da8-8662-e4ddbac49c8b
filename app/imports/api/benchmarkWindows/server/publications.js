import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";

import { BenchmarkWindows } from "../benchmarkWindows";
import { getBenchmarkPeriodIdFromBenchmarkWindowWithSiteId } from "../methods";
import {
  getSiteIdFromStudentGroupWithStudentGroupId,
  getOrgidFromStudentGroupWithStudentGroupId
} from "../../studentGroups/methods";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("BenchmarkWindowsForSchool", function benchmarkSchedulesForSchool(siteId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(siteId, String);

  return BenchmarkWindows.find({ siteId });
});

Meteor.publish("BenchmarkWindowsForSchoolWithSchoolYear", function benchmarkWindowsForSchoolWithSchoolYear(
  siteId,
  schoolYear
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(siteId, String);
  check(schoolYear, Number);

  return BenchmarkWindows.find({ siteId, schoolYear }, { fields: { benchmarkPeriodId: 1, startDate: 1, endDate: 1 } });
});

Meteor.publish("BenchmarkWindowsForStudentGroupAndPeriod", async function benchmarkSchedulesPublication(
  studentGroupId
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);

  const siteId = await getSiteIdFromStudentGroupWithStudentGroupId(studentGroupId);
  const orgid = await getOrgidFromStudentGroupWithStudentGroupId(studentGroupId);

  const benchmarkPeriodId = await getBenchmarkPeriodIdFromBenchmarkWindowWithSiteId(orgid, siteId);
  if (!benchmarkPeriodId) return [];
  return BenchmarkWindows.find({ siteId, benchmarkPeriodId });
});
