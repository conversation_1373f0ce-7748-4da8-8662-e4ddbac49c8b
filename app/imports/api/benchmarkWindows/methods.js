import { check } from "meteor/check";
import moment from "moment";
import { get } from "lodash";
import { BenchmarkWindows } from "./benchmarkWindows.js";
import BenchmarkPeriodHelpers from "../benchmarkPeriods/methods.js";
import {
  getCurrentSchoolYear,
  getMeteorUser,
  getMeteorUserId,
  isDateInPreviousSchoolYear
} from "../utilities/utilities.js";
import { Organizations } from "../organizations/organizations";
import { getCurrentDate } from "../helpers/getCurrentDate";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { BenchmarkPeriods } from "../benchmarkPeriods/benchmarkPeriods";

export default class BenchmarkWindowHelpers {
  static async generateObj({ siteId, startDate, endDate, userId, schoolYear, benchmarkPeriodId, orgid }) {
    check(schoolYear, Number);
    check(benchmarkPeriodId, String);
    check(siteId, String);
    check(startDate, Date);
    check(endDate, Date);
    check(orgid, String);
    const byDateOn = await getTimestampInfo(userId || "TEST", orgid);
    return {
      schoolYear,
      benchmarkPeriodId,
      siteId,
      startDate,
      endDate,
      orgid,
      created: byDateOn,
      lastModified: byDateOn
    };
  }
}
export async function insert(benchmarkWindowDoc) {
  check(benchmarkWindowDoc, Object);
  BenchmarkWindows.validate(benchmarkWindowDoc);
  return BenchmarkWindows.insertAsync(benchmarkWindowDoc);
}
// TODO: move these into the helpers class
export async function getCurrentBenchmarkWindowWithSiteId({ orgid, siteId, schoolYear }) {
  // eslint-disable-next-line no-param-reassign
  schoolYear ??= await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const { customDate } = get(await getMeteorUser(), "profile", {});
  const currentDate = await getCurrentDate(customDate, orgid);
  return BenchmarkWindows.findOneAsync({
    // need to figure out if we're allowing overlaps, or adjustments...
    startDate: { $lte: currentDate },
    endDate: { $gte: currentDate },
    siteId,
    schoolYear
  });
}

export async function createCurrentBenchmarkWindowForSite({ orgid, siteId, userId }) {
  const benchmarkPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const benchmarkPeriodId = benchmarkPeriod._id;

  const user = await getMeteorUser();
  const schoolYear = await getCurrentSchoolYear(user, orgid);

  let bmpGroupId = "default";
  if (orgid) {
    const organization = (await Organizations.findOneAsync(orgid, { fields: { benchmarkPeriodsGroupId: 1 } })) || {};
    if (organization.benchmarkPeriodsGroupId) {
      bmpGroupId = organization.benchmarkPeriodsGroupId;
    }
  }

  const startDayOfMonth = benchmarkPeriod.startDate[bmpGroupId].day;
  const startMonth = benchmarkPeriod.startDate[bmpGroupId].month - 1;
  const startDate = moment.utc({
    y: moment().year(),
    M: startMonth,
    d: startDayOfMonth
  });

  const endDayOfMonth = benchmarkPeriod.endDate[bmpGroupId].day;
  const endMonth = benchmarkPeriod.endDate[bmpGroupId].month - 1;
  const endDate = moment
    .utc({
      y: moment().year(),
      M: endMonth,
      d: endDayOfMonth
    })
    .endOf("day");

  const newBMW = await BenchmarkWindowHelpers.generateObj({
    orgid,
    siteId,
    startDate: new Date(startDate.toISOString()),
    endDate: new Date(endDate.toISOString()),
    userId,
    schoolYear,
    benchmarkPeriodId
  });
  // insert benchmark window
  const bmwId = await insert(newBMW);
  return BenchmarkWindows.findOneAsync(bmwId);
}

export async function getBenchmarkPeriodIdFromBenchmarkWindowWithSiteId(orgid, siteId) {
  const bmw = await getCurrentBenchmarkWindowWithSiteId({ orgid, siteId });
  return bmw && bmw.benchmarkPeriodId;
}

export async function updateBenchmarkWindowsForOrganization({
  orgid,
  benchmarkPeriodsGroupId,
  schoolYear,
  schoolYearBoundary
}) {
  if (!(await BenchmarkWindows.findOneAsync({ orgid, schoolYear }))) {
    return false;
  }

  const benchmarkPeriods = await BenchmarkPeriods.find({ name: { $ne: "All" } }).fetchAsync();
  await Promise.all(
    benchmarkPeriods.map(async benchmarkPeriod => {
      const startDayOfMonth = benchmarkPeriod.startDate[benchmarkPeriodsGroupId].day;
      const startMonth = benchmarkPeriod.startDate[benchmarkPeriodsGroupId].month - 1;
      const isStartDatePreviousSchoolYear = isDateInPreviousSchoolYear({
        month: startMonth,
        day: startDayOfMonth,
        schoolYearBoundary
      });
      const startYear = schoolYear - (isStartDatePreviousSchoolYear ? 1 : 0);
      const startDate = new Date(
        moment
          .utc({
            y: startYear,
            M: startMonth,
            d: startDayOfMonth
          })
          .startOf("day")
          .toISOString()
      );

      const endDayOfMonth = benchmarkPeriod.endDate[benchmarkPeriodsGroupId].day;
      const endMonth = benchmarkPeriod.endDate[benchmarkPeriodsGroupId].month - 1;
      const isEndDatePreviousSchoolYear = isDateInPreviousSchoolYear({
        month: endMonth,
        day: endDayOfMonth,
        schoolYearBoundary
      });
      const endYear = schoolYear - (isEndDatePreviousSchoolYear ? 1 : 0);
      let endDateObject = moment.utc({
        y: endYear,
        M: endMonth,
        d: endDayOfMonth
      });

      // handle the 2/29 edge case for non-leap years
      if (endMonth === 1 && endDayOfMonth === 29) {
        endDateObject = moment
          .utc({
            y: 2000,
            M: endMonth,
            d: endDayOfMonth
          })
          .year(endYear);
      }
      const endDate = new Date(endDateObject.endOf("day").toISOString());

      const lastModified = await getTimestampInfo(
        this?.userId || getMeteorUserId() || "",
        orgid,
        "updateBenchmarkWindowsForOrganization"
      );
      await BenchmarkWindows.updateAsync(
        { orgid, schoolYear, benchmarkPeriodId: benchmarkPeriod._id },
        { $set: { startDate, endDate, lastModified } },
        { multi: true }
      );
    })
  );

  return true;
}
