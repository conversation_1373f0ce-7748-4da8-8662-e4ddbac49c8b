import { StudentGroups } from "./studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { getIndividualRuleOutcome, getPercent } from "../assessmentResults/utilities";

export default async function addStudentGroupWithClasswideIntervention({
  isFailing = true,
  isLastSkill = false,
  assessmentId,
  nextAssessmentId,
  assessmentResultId,
  studentGroupId,
  currentAssessmentResultId,
  studentScores = [30, isFailing ? 50 : 60, 99]
}) {
  const orgid = "testOrgId";
  const siteId = "testSiteId";
  const schoolYear = 2019;
  const testGroupName = "testStudentGroup";
  const mockedBMPeriodId = "currentBMPeriodId";
  const grade = "05";
  const openAssessmentResultIdForCurrentAssessment = currentAssessmentResultId || "currentAssessmentResultId";
  const openAssessmentResultIdForNextAssessment = "nextAssessmentResultId";

  const targets = [34, 54, 300];
  const cutoffTarget = targets[1];
  const totalStudentsAssessed = studentScores.filter(score => score !== "N/A").length;
  const studentIds = studentScores.map((val, index) => `testStudentId${index}`);

  const medianScore = studentScores[1];
  const numberMeetingTarget = studentScores.filter(score => score >= cutoffTarget).length;
  const percentMeetingTarget = getPercent(numberMeetingTarget, totalStudentsAssessed);
  const studentIdsNotMeetingTarget = studentIds.filter((val, index) => studentScores[index] < cutoffTarget);
  // eslint-disable-next-line no-param-reassign
  currentAssessmentResultId = isFailing
    ? openAssessmentResultIdForCurrentAssessment
    : openAssessmentResultIdForNextAssessment;
  const currentAssessmentResultIds = isLastSkill ? [] : [currentAssessmentResultId];
  const currentAssessmentId = isFailing ? assessmentId : nextAssessmentId;
  const currentAssessmentName = isFailing ? `assessmentName_${assessmentId}` : "Next Assessment Name";
  const currentTargets = isFailing ? targets : [28, 56, 300];
  await StudentGroups.insertAsync({
    _id: studentGroupId,
    orgid,
    siteId,
    schoolYear,
    name: testGroupName,
    isActive: true,
    grade,
    history: [
      {
        assessmentId,
        targets,
        assessmentResultId,
        whenEnded: {
          on: 1546902000000
        },
        assessmentResultMeasures: [
          {
            assessmentId,
            assessmentName: `assessmentName_${assessmentId}`,
            cutoffTarget,
            targetScores: targets,
            medianScore,
            studentScores,
            percentMeetingTarget,
            numberMeetingTarget,
            totalStudentsAssessed,
            studentResults: studentScores
              .filter(score => score !== "N/A")
              .map((score, index) => {
                const individualRuleOutcome = getIndividualRuleOutcome(score, targets);
                return {
                  studentId: studentIds[index],
                  status: "COMPLETE",
                  score: score.toString(),
                  meetsTarget: individualRuleOutcome === "above",
                  individualRuleOutcome
                };
              }),
            assessmentResultType: "classwide"
          }
        ],
        type: "classwide",
        enrolledStudentIds: studentIds
      }
    ],
    currentAssessmentResultIds
  });
  await StudentGroupEnrollments.insertAsync(
    studentIds.map(studentId => ({
      studentId,
      studentGroupId,
      isActive: true,
      schoolYear
    }))
  );
  const assessmentResults = [
    {
      _id: assessmentResultId,
      orgid,
      status: "COMPLETED",
      schoolYear,
      benchmarkPeriodId: mockedBMPeriodId,
      studentGroupId,
      grade,
      type: "classwide",
      created: {
        on: 1546902000000 // 2019-01-08
      },
      assessmentIds: [assessmentId],
      scores: studentScores.map((score, index) => {
        return {
          assessmentId,
          status: score !== "N/A" ? "COMPLETE" : "CANCELLED",
          studentId: studentIds[index],
          value: score.toString()
        };
      }),

      classwideResults: {
        percentMeetingTarget,
        percentAtRisk: 100 - percentMeetingTarget,
        totalStudentsMeetingAllTargets: numberMeetingTarget,
        totalStudentsAssessedOnAllMeasures: totalStudentsAssessed,
        studentIdsNotMeetingTarget
      },
      measures: [
        {
          assessmentId,
          assessmentName: "Mixed Addition/Subtraction 0-20",
          cutoffTarget,
          targetScores: targets,
          medianScore,
          studentScores,
          percentMeetingTarget,
          numberMeetingTarget,
          totalStudentsAssessed,
          studentResults: studentScores
            .filter(score => score !== "N/A")
            .map((score, index) => {
              const individualRuleOutcome = getIndividualRuleOutcome(score, targets);
              return {
                studentId: studentIds[index],
                status: "COMPLETE",
                firstName: `FirstName${index}`,
                lastName: `lastName${index}`,
                score: score.toString(),
                meetsTarget: individualRuleOutcome === "above",
                individualRuleOutcome
              };
            }),
          benchmarkPeriodId: mockedBMPeriodId,
          grade,
          assessmentResultType: "classwide"
        }
      ],
      ...(isLastSkill
        ? {}
        : {
            nextAssessmentResultId: currentAssessmentResultId,
            ruleResults: {
              passed: !isFailing,
              nextSkill: {
                assessmentId: currentAssessmentId,
                assessmentName: currentAssessmentName,
                interventions: [],
                targets: currentTargets
              }
            }
          })
    }
  ];
  if (!isLastSkill) {
    assessmentResults.push({
      _id: currentAssessmentResultId,
      benchmarkPeriodId: mockedBMPeriodId,
      orgid,
      created: {
        on: 1546902100000
      },
      schoolYear,
      status: "OPEN",
      studentGroupId,
      grade,
      type: "classwide",
      previousAssessmentResultId: assessmentResultId,
      assessmentIds: [currentAssessmentId],
      scores: studentIds.map(studentId => ({
        assessmentId: currentAssessmentId,
        status: "STARTED",
        studentId
      }))
    });
  }
  await AssessmentResults.insertAsync(assessmentResults);
}
