import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import { StudentGroups } from "./studentGroups.js";
import { studentGroup } from "../../test-helpers/data/studentGroups.js";

if (Meteor.isServer) {
  describe("StudentGroups", () => {
    describe("Should pass schema validation ", () => {
      it("this method is for inline testing", () => {
        assert.isUndefined(StudentGroups.validate(studentGroup({})));
      });
      it("this method returns a value", () => {
        assert.isTrue(StudentGroups.isValid(studentGroup({})));
      });
    });
    describe("Should fail schema validation ", () => {
      it("this method returns a value", () => {
        const studentGroupDoc = studentGroup({});
        studentGroupDoc._id = 1234;
        assert.isFalse(StudentGroups.isValid(studentGroupDoc));
      });
    });
  });
}
