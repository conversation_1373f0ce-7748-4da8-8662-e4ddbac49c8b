import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import some from "lodash/some";
import { StudentGroups } from "../studentGroups";
import * as auth from "../../authorization/server/methods";
import { getCurrentSchoolYear, getMeteorUser, isUserLoggedOut, ninjalog } from "../../utilities/utilities";

async function usersStudentGroupsPublication({ schoolYear, siteId, fields = {}, orgid }) {
  // if admin/coach get student groups for whole site

  if (
    await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    })
  ) {
    return StudentGroups.find(
      {
        schoolYear,
        siteId,
        isActive: true
      },
      {
        fields,
        sort: { _id: 1 }
      }
    );
  }

  if (await auth.hasAccess(["teacher"], { userId: this.userId })) {
    return StudentGroups.find(
      {
        $or: [{ ownerIds: { $in: [this.userId] } }, { secondaryTeachers: this.userId }],
        schoolYear,
        siteId,
        isActive: true
      },
      { fields }
    );
  }
  return this.ready();
}

Meteor.publish("StudentGroupById", function(studentGroupId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);

  return StudentGroups.find({ _id: studentGroupId });
});

Meteor.publish("StudentGroupsAssociatedWithUser", function(schoolYear, siteId, orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(schoolYear, Number);
  check(siteId, String);
  check(orgid, Match.Maybe(String));

  return usersStudentGroupsPublication.call(this, { schoolYear, siteId, orgid });
});

Meteor.publish("StudentGroupsAssociatedWithUser:BasicData", function(schoolYear, siteId, orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(schoolYear, Number);
  check(siteId, String);
  check(orgid, String);

  const fields = {
    _id: 1,
    siteId: 1,
    orgid: 1,
    name: 1,
    schoolYear: 1,
    isActive: 1
  };
  return usersStudentGroupsPublication.call(this, { schoolYear, siteId, fields, orgid });
});

Meteor.publish("StudentGroups:GetGroups", function(orgid, schoolYear) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  check(orgid, String);
  check(schoolYear, Number);

  return StudentGroups.find({ schoolYear, orgid });
});

Meteor.publish("StudentGroups:PerOrg", async function orgStudentGroupsPublication(orgid, schoolYear = null) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);
  check(schoolYear, Match.Maybe(Number));

  if (
    !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    }))
  ) {
    ninjalog.log({
      msg: "Publication Access Denied: StudentGroups:PerOrg"
    });
    return this.ready();
  }
  const query = { orgid, isActive: true };
  if (schoolYear) {
    query.schoolYear = schoolYear;
  }
  return StudentGroups.find(query, {
    fields: {
      orgid: 1,
      schoolYear: 1,
      siteId: 1,
      grade: 1,
      ownerIds: 1,
      secondaryTeachers: 1,
      name: 1,
      sectionId: 1
    }
  });
});

Meteor.publish("StudentGroups:NamesForStudentLog", async function(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);

  return StudentGroups.find(
    { orgid, schoolYear: await getCurrentSchoolYear(await getMeteorUser(), orgid) },
    { fields: { name: 1 } }
  );
});

Meteor.publish("StudentGroups:PerSite", async function siteStudentGroupsPublication(orgid, siteId, schoolYear) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!orgid || !siteId || !schoolYear) {
    ninjalog.log({
      msg: "Publication Access Denied: StudentGroups:PerSite - missing parameters"
    });
    return this.ready();
  }
  check(siteId, String);
  check(orgid, String);
  check(schoolYear, Number);
  if (
    !(await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    }))
  ) {
    ninjalog.log({
      msg: "Publication Access Denied: StudentGroups:PerSite"
    });
    return this.ready();
  }

  const query = { orgid, siteId, isActive: true, schoolYear };
  return StudentGroups.find(query, {
    fields: {
      currentClasswideSkill: 1,
      currentAssessmentResultIds: 1,
      grade: 1,
      history: 1,
      additionalHistory: 1,
      hasCompletedCWI: 1,
      individualInterventionQueue: 1,
      orgid: 1,
      ownerIds: 1,
      secondaryTeachers: 1,
      schoolYear: 1,
      siteId: 1,
      name: 1,
      classwideStatsAsOfDate: 1
    }
  });
});

Meteor.publish("StudentGroups:ScreeningResultsData", async function screeningResultsDataStudentGroupsPublication(
  studentGroupId,
  schoolYear
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(schoolYear, Number);

  const query = { _id: studentGroupId, isActive: true, schoolYear };
  const fields = {
    orgid: 1,
    schoolYear: 1,
    currentClasswideSkill: 1,
    history: 1,
    siteId: 1,
    name: 1,
    ownerIds: 1,
    secondaryTeachers: 1
  };
  const studentGroupCursor = StudentGroups.find(query, { fields });
  const studentGroup = (await studentGroupCursor.fetchAsync())[0];

  if (!studentGroup) {
    ninjalog.log({
      msg: "Publication Access Denied: StudentGroups:ScreeningResultsData"
    });
    return this.ready();
  }

  const { siteId } = studentGroup;
  if (
    some(studentGroup.ownerIds, oId => this.userId === oId) ||
    some(studentGroup.secondaryTeachers, scId => this.userId === scId) ||
    (await auth.hasAccess(["admin", "support", "universalCoach"], {
      userId: this.userId,
      siteId
    }))
  ) {
    return studentGroupCursor;
  }

  return this.ready();
});
