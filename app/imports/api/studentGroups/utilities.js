import { check } from "meteor/check";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Students } from "../students/students";

export async function getListOfStudentIdsFromStudentGroups(studentGroupIds) {
  check(studentGroupIds, Array);
  const sgEnrollments = await StudentGroupEnrollments.find(
    {
      studentGroupId: {
        $in: studentGroupIds
      },
      isActive: true
    },
    {
      fields: {
        studentId: 1
      }
    }
  ).fetchAsync();
  const studentIds = sgEnrollments.map(sge => sge.studentId);

  const students = await Students.find(
    {
      _id: {
        $in: studentIds
      }
    },
    {
      fields: {
        _id: 1
      }
    }
  ).fetchAsync();

  return students.map(student => student._id);
}
