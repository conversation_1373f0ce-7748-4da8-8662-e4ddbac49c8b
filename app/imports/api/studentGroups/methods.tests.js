import MockDate from "mockdate";
import moment from "moment";
import "moment-timezone";
import {
  getAveragePercentScreenedPerOrg,
  getAverageStatsPerOrg,
  updateGroupsData,
  updateStudentScoreInClasswideIntervention
} from "./methods";
import { StudentGroups } from "./studentGroups";
import { Organizations } from "../organizations/organizations";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Grades } from "../grades/grades";
import { Users } from "../users/users";
import { Rules } from "../rules/rules";
import { Assessments } from "../assessments/assessments";
import addStudentGroupWithClasswideIntervention from "./helpers";
import {
  averageStatsStudentGroups,
  averageStatsStudentGroupEnrollments
} from "../../test-helpers/data/getAverageStatsPerOrg.testData";

jest.mock("../benchmarkPeriods/methods");

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2019)
}));

describe("getAveragePercentScreenedPerOrg", () => {
  const orgid = "testOrgId";
  const testGroupName = "testStudentGroup";
  const schoolYearWithResults = 2017;
  const studentGroupId = "pureStudentGroupId";
  const mockedBMPeriodId = "currentBMPeriodId";

  beforeAll(async () => {
    await Grades.insertAsync([{ _id: "K" }, { _id: "HS" }]);
    await StudentGroups.insertAsync([
      {
        _id: studentGroupId,
        orgid,
        schoolYear: schoolYearWithResults,
        name: testGroupName,
        isActive: true,
        grade: "K"
      }
    ]);
    await AssessmentResults.insertAsync([
      {
        orgid,
        type: "benchmark",
        status: "COMPLETED",
        schoolYear: schoolYearWithResults,
        benchmarkPeriodId: mockedBMPeriodId,
        studentGroupId
      }
    ]);
  });
  afterAll(async () => {
    await StudentGroups.removeAsync({});
    await AssessmentResults.removeAsync({});
    await Grades.removeAsync({});
    jest.restoreAllMocks();
  });
  it("should return N/A if there are't any StudentGroups or AssessmentResults for given organization in provided schoolYear", async () => {
    const schoolYear = 2018;
    expect(await getAveragePercentScreenedPerOrg(orgid, schoolYear)).toEqual("N/A");
  });
  it("should return the percent of screened groups in the current benchmark period for given organization in provided schoolYear", async () => {
    expect(await getAveragePercentScreenedPerOrg(orgid, schoolYearWithResults)).toEqual(100);
  });
  it("should not take high school groups into consideration", async () => {
    await StudentGroups.insertAsync({
      orgid,
      schoolYear: schoolYearWithResults,
      name: "testGroup2",
      isActive: true,
      grade: "HS"
    });
    expect(await getAveragePercentScreenedPerOrg(orgid, schoolYearWithResults)).toEqual(100);
  });
});

describe("getAverageStatsPerOrg", () => {
  const orgid = "test_organization_id";
  const schoolYear = 2022;

  beforeAll(async () => {
    moment.tz.setDefault("UTC");
    MockDate.set("2022-06-22");
    await Organizations.insertAsync({
      _id: orgid,
      name: "DistrictName",
      schoolYearBoundary: {
        month: 6,
        day: 31
      }
    });
    await StudentGroups.insertAsync(averageStatsStudentGroups);
    await StudentGroupEnrollments.insertAsync(averageStatsStudentGroupEnrollments);
  });
  afterAll(async () => {
    MockDate.reset();
    moment.tz.setDefault();
    await Organizations.removeAsync({});
    await StudentGroups.removeAsync({});
    await StudentGroupEnrollments.removeAsync({});
  });
  it("should calculate stats for groups with classwide intervention history data", async () => {
    const { averageInterventionConsistency, averageWeeksPerSkill } = await getAverageStatsPerOrg(orgid, schoolYear);
    expect(averageInterventionConsistency).toEqual(6);
    expect(averageWeeksPerSkill).toEqual(4);
  });
});

describe("updateGroupsData", () => {
  // Using global mock for utilities
  const schoolYear = 2019;
  const orgid = "testOrgId";
  const newSiteId = "newSiteId";
  const teacherRole = "arbitraryIdteacher";
  const previousSiteId = "unrelevant";
  const newTeacherId = "teacherId";
  const newTeacher = {
    _id: newTeacherId,
    profile: {
      siteAccess: [
        {
          role: teacherRole,
          siteId: previousSiteId,
          schoolYear
        }
      ]
    }
  };
  const previousOwnerId = "previousOwnerId";
  const previousOwner = {
    _id: previousOwnerId,
    profile: {
      siteAccess: [
        {
          role: teacherRole,
          siteId: previousSiteId,
          schoolYear,
          isActive: true
        },
        {
          role: teacherRole,
          siteId: newSiteId,
          schoolYear: 2019,
          isActive: true
        }
      ]
    }
  };

  const studentGroup = {
    _id: "1",
    name: "name",
    ownerIds: [previousOwnerId]
  };

  beforeEach(async () => {
    await StudentGroups.removeAsync({});
    await Users.removeAsync({});
  });
  it("should update each provided group's ownerIds and name", async () => {
    const allGroups = [
      {
        _id: "1",
        name: "1",
        ownerIds: ["1"]
      },
      {
        _id: "2",
        name: "2",
        ownerIds: ["2"]
      }
    ];
    await StudentGroups.insertAsync(allGroups);
    const groupsWithUpdatedData = allGroups.map((group, index) => ({
      ...group,
      name: `${group._id}_${index}`,
      ownerIds: [`${group._id}_${index}`]
    }));

    await updateGroupsData({ updatedGroups: groupsWithUpdatedData, orgid, siteId: newSiteId });

    const studentGroupIds = allGroups.map(g => g._id);
    expect(await StudentGroups.find({ _id: { $in: studentGroupIds } }).fetchAsync()).toMatchObject(
      groupsWithUpdatedData
    );
  });
  it("should update user's site access if they don't have access to the provided site", async () => {
    await Users.insertAsync(newTeacher);
    await StudentGroups.insertAsync(studentGroup);
    const updatedGroup = {
      ...studentGroup,
      ownerIds: [newTeacherId]
    };

    await updateGroupsData({ updatedGroups: [updatedGroup], orgid, siteId: newSiteId });

    const updatedUser = await Users.findOneAsync(newTeacherId);
    expect(updatedUser.profile.siteAccess.find(sa => sa.siteId === newSiteId)).toBeTruthy();
  });
  it("should update removed owner's site access to deactivate the access to the provided site", async () => {
    await Users.insertAsync([previousOwner, newTeacher]);
    await StudentGroups.insertAsync(studentGroup);
    const updatedGroup = {
      ...studentGroup,
      ownerIds: [newTeacherId]
    };

    await updateGroupsData({
      updatedGroups: [updatedGroup],
      selectedUserIds: [previousOwnerId],
      orgid,
      siteId: newSiteId
    });

    const updatedPreviousOwner = await Users.findOneAsync(previousOwnerId);
    expect(
      updatedPreviousOwner.profile.siteAccess.find(sa => sa.siteId === newSiteId && sa.isActive === false)
    ).toBeTruthy();
  });
});

describe("updateStudentScoreInClasswideIntervention", () => {
  describe("should update a student score in a Classwide Intervention", () => {
    const studentGroupId = "testStudentGroupId";
    const assessmentId = "testAssessmentId";
    const assessmentResultId = "testAssessmentResultId";
    const assessmentName = "assessmentName";
    const masteryTarget = "54";
    const nextAssessmentId = "nextAssessmentId";
    const nextAssessmentResultId = "nextAssessmentResultId";
    const nextAssessmentName = "nextAssessmentName";
    const benchmarkPeriodId = "currentBMPeriodId";

    const addStudentGroupWithClasswideInterventionParams = {
      assessmentId,
      nextAssessmentId,
      assessmentResultId,
      studentGroupId
    };

    async function verifyAssessmentResult(providedAssessmentId) {
      const openAssessmentResult = await AssessmentResults.find({
        status: "OPEN",
        studentGroupId,
        type: "classwide"
      }).fetchAsync();
      expect(openAssessmentResult).toHaveLength(1);
      expect(openAssessmentResult[0].assessmentIds).toEqual([providedAssessmentId]);
      const studentScoreItem = openAssessmentResult[0].scores.find(scores => scores.studentId === "testStudentId1");
      expect(studentScoreItem).toMatchObject({
        assessmentId: providedAssessmentId,
        status: "STARTED",
        studentId: "testStudentId1"
      });
    }

    beforeAll(async () => {
      await Assessments.insertAsync([
        {
          _id: assessmentId,
          name: assessmentName,
          strands: [
            {
              name: "Overall",
              scores: [
                {
                  name: "Number Correct",
                  externalId: "number_correct",
                  targets: [
                    {
                      grade: "05",
                      periods: [
                        {
                          benchmarkPeriodId,
                          values: [34, 54, 300]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          _id: nextAssessmentId,
          name: nextAssessmentName,
          strands: [
            {
              name: "Overall",
              scores: [
                {
                  name: "Number Correct",
                  externalId: "number_correct",
                  targets: [
                    {
                      grade: "05",
                      periods: [
                        {
                          benchmarkPeriodId,
                          values: [28, 56, 300]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]);
      await Rules.insertAsync({
        grade: "05",
        skills: [
          {
            assessmentId,
            interventions: []
          },
          {
            assessmentId: nextAssessmentId,
            interventions: []
          }
        ]
      });
    });

    afterEach(async () => {
      await StudentGroups.removeAsync({});
      await AssessmentResults.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
      jest.restoreAllMocks();
    });
    afterAll(async () => {
      await Assessments.removeAsync({});
      await Rules.removeAsync({});
    });

    it("and move to next skill when at least 80% of students are at or above instructional target", async () => {
      await addStudentGroupWithClasswideIntervention({
        studentScores: [20, 36, 37],
        ...addStudentGroupWithClasswideInterventionParams
      });
      const studentId = "testStudentId0";
      const studentScore = "35";
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [35, 36, 37],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0
      });
      expect(studentResult).toMatchObject({
        score: "35",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("35");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 100,
        percentMeetingTarget: 0,
        studentIdsNotMeetingTarget: ["testStudentId0", "testStudentId1", "testStudentId2"],
        totalStudentsAssessedOnAllMeasures: 3,
        totalStudentsMeetingAllTargets: 0
      });
      expect(measure).toMatchObject({
        studentScores: [35, 36, 37],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 3
      });
      expect(result).toMatchObject({
        score: "35",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      await verifyAssessmentResult(nextAssessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe("currentAssessmentResultId");
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: true,
        nextSkill: {
          assessmentId: nextAssessmentId,
          assessmentName: nextAssessmentName,
          interventions: [],
          targets: [28, 56, 300]
        }
      });
    });

    it("and stays in the same skill when median is above mastery and less than 80% of students meet instructional target", async () => {
      await addStudentGroupWithClasswideIntervention({
        studentScores: [0, 99, 99, 99, 99],
        ...addStudentGroupWithClasswideInterventionParams
      });
      const studentId = "testStudentId1";
      const studentScore = "0";
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentGroup.currentAssessmentResultIds).toHaveLength(1);
      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [0, 0, 99, 99, 99],
        percentMeetingTarget: 60,
        numberMeetingTarget: 3
      });
      expect(studentResult).toMatchObject({
        score: "0",
        meetsTarget: false,
        individualRuleOutcome: "below"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("0");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 40,
        percentMeetingTarget: 60,
        studentIdsNotMeetingTarget: ["testStudentId0", "testStudentId1"],
        totalStudentsAssessedOnAllMeasures: 5,
        totalStudentsMeetingAllTargets: 3
      });
      expect(measure).toMatchObject({
        studentScores: [0, 0, 99, 99, 99],
        percentMeetingTarget: 60,
        numberMeetingTarget: 3,
        totalStudentsAssessed: 5
      });
      expect(result).toMatchObject({
        score: "0",
        meetsTarget: false,
        individualRuleOutcome: "below"
      });

      await verifyAssessmentResult(nextAssessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe("currentAssessmentResultId");
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: true,
        nextSkill: {
          assessmentId: nextAssessmentId,
          assessmentName: nextAssessmentName,
          interventions: [],
          targets: [28, 56, 300]
        }
      });
    });

    it("and not move to next skill when at least 80% of scores are at or above instructional target but group has more than 10 students enrolled", async () => {
      await addStudentGroupWithClasswideIntervention({
        studentScores: [20, 36, 37, "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"],
        ...addStudentGroupWithClasswideInterventionParams
      });

      const studentId = "testStudentId0";
      const studentScore = "35";
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [35, 36, 37, "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0
      });
      expect(studentResult).toMatchObject({
        score: "35",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("35");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 100,
        percentMeetingTarget: 0,
        studentIdsNotMeetingTarget: ["testStudentId0", "testStudentId1", "testStudentId2"],
        totalStudentsAssessedOnAllMeasures: 3,
        totalStudentsMeetingAllTargets: 0
      });
      expect(measure).toMatchObject({
        studentScores: [35, 36, 37, "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A"],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 3
      });
      expect(result).toMatchObject({
        score: "35",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      await verifyAssessmentResult(assessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe("currentAssessmentResultId");
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: false,
        nextSkill: {
          assessmentId,
          assessmentName: `assessmentName_${assessmentId}`,
          interventions: [],
          targets: [34, 54, 300]
        }
      });
    });

    it("when median score stays above mastery target then verifies AssessmentResult document", async () => {
      await addStudentGroupWithClasswideIntervention({
        isFailing: false,
        ...addStudentGroupWithClasswideInterventionParams,
        studentScores: [30, 30, 30, 30, 30, 60, 99, 99, 99, 99, 99]
      });
      const studentId = "testStudentId0";
      const studentScore = "20";
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [20, 30, 30, 30, 30, 60, 99, 99, 99, 99, 99],
        percentMeetingTarget: 55,
        numberMeetingTarget: 6
      });
      expect(studentResult).toMatchObject({
        score: "20",
        meetsTarget: false,
        individualRuleOutcome: "below"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("20");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 45,
        percentMeetingTarget: 55,
        studentIdsNotMeetingTarget: [
          "testStudentId0",
          "testStudentId1",
          "testStudentId2",
          "testStudentId3",
          "testStudentId4"
        ],
        totalStudentsAssessedOnAllMeasures: 11,
        totalStudentsMeetingAllTargets: 6
      });
      expect(measure).toMatchObject({
        studentScores: [20, 30, 30, 30, 30, 60, 99, 99, 99, 99, 99],
        percentMeetingTarget: 55,
        numberMeetingTarget: 6,
        totalStudentsAssessed: 11
      });
      expect(result).toMatchObject({
        score: "20",
        meetsTarget: false,
        individualRuleOutcome: "below"
      });

      await verifyAssessmentResult(nextAssessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe(nextAssessmentResultId);
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: true,
        nextSkill: {
          assessmentId: nextAssessmentId,
          assessmentName: nextAssessmentName,
          interventions: [],
          targets: [28, 56, 300]
        }
      });
    });

    it("when median score stays below mastery target then verifies AssessmentResult document", async () => {
      await addStudentGroupWithClasswideIntervention({
        ...addStudentGroupWithClasswideInterventionParams,
        studentScores: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
      });
      const studentId = "testStudentId1";
      const studentScore = "45";
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 45],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0
      });
      expect(studentResult).toMatchObject({
        score: "45",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("45");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 100,
        percentMeetingTarget: 0,
        studentIdsNotMeetingTarget: [
          "testStudentId0",
          "testStudentId1",
          "testStudentId2",
          "testStudentId3",
          "testStudentId4",
          "testStudentId5",
          "testStudentId6",
          "testStudentId7",
          "testStudentId8",
          "testStudentId9",
          "testStudentId10"
        ],
        totalStudentsAssessedOnAllMeasures: 11,
        totalStudentsMeetingAllTargets: 0
      });
      expect(measure).toMatchObject({
        studentScores: [1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 45],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 11
      });
      expect(result).toMatchObject({
        score: "45",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      await verifyAssessmentResult(assessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe("currentAssessmentResultId");
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: false,
        nextSkill: {
          assessmentId,
          assessmentName: `assessmentName_${assessmentId}`,
          interventions: [],
          targets: [34, 54, 300]
        }
      });
    });

    it("when median score goes below mastery target then verifies AssessmentResult document", async () => {
      await addStudentGroupWithClasswideIntervention({
        isFailing: false,
        ...addStudentGroupWithClasswideInterventionParams,
        studentScores: [30, 30, 30, 30, 30, 60, 99, 99, 99, 99, 99]
      });
      const studentId = "testStudentId5";
      const studentScore = "50";

      await verifyAssessmentResult(nextAssessmentId);
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [30, 30, 30, 30, 30, 50, 99, 99, 99, 99, 99],
        percentMeetingTarget: 45,
        numberMeetingTarget: 5
      });
      expect(studentResult).toMatchObject({
        score: "50",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("50");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 55,
        percentMeetingTarget: 45,
        studentIdsNotMeetingTarget: [
          "testStudentId0",
          "testStudentId1",
          "testStudentId2",
          "testStudentId3",
          "testStudentId4",
          "testStudentId5"
        ],
        totalStudentsAssessedOnAllMeasures: 11,
        totalStudentsMeetingAllTargets: 5
      });
      expect(measure).toMatchObject({
        studentScores: [30, 30, 30, 30, 30, 50, 99, 99, 99, 99, 99],
        percentMeetingTarget: 45,
        numberMeetingTarget: 5,
        totalStudentsAssessed: 11
      });
      expect(result).toMatchObject({
        score: "50",
        meetsTarget: false,
        individualRuleOutcome: "at"
      });
      await verifyAssessmentResult(assessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe(nextAssessmentResultId);
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: false,
        nextSkill: {
          assessmentId,
          assessmentName: `assessmentName_${assessmentId}`,
          interventions: [],
          targets: [34, 54, 300]
        }
      });
    });

    it("when median score goes above mastery target then verifies AssessmentResult document", async () => {
      await addStudentGroupWithClasswideIntervention({
        isFailing: true,
        ...addStudentGroupWithClasswideInterventionParams
      });
      const studentId = "testStudentId0";
      const studentScore = "70";
      await verifyAssessmentResult(assessmentId);
      await updateStudentScoreInClasswideIntervention({
        studentGroupId,
        assessmentId,
        studentId,
        studentScore,
        masteryTarget
      });
      const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      const assessmentResultMeasure = studentGroup.history[0].assessmentResultMeasures[0];
      const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);

      expect(assessmentResultMeasure).toMatchObject({
        studentScores: [50, 70, 99],
        percentMeetingTarget: 67,
        numberMeetingTarget: 2
      });
      expect(studentResult).toMatchObject({
        score: "70",
        meetsTarget: true,
        individualRuleOutcome: "above"
      });

      const assessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      const studentScoreItem = assessmentResult.scores.find(scores => scores.studentId === studentId);
      const measure = assessmentResult.measures[0];
      const result = measure.studentResults.find(sr => sr.studentId === studentId);

      expect(studentScoreItem.value).toBe("70");
      expect(assessmentResult.classwideResults).toEqual({
        percentAtRisk: 33,
        percentMeetingTarget: 67,
        studentIdsNotMeetingTarget: ["testStudentId1"],
        totalStudentsAssessedOnAllMeasures: 3,
        totalStudentsMeetingAllTargets: 2
      });
      expect(measure).toMatchObject({
        studentScores: [50, 70, 99],
        percentMeetingTarget: 67,
        numberMeetingTarget: 2,
        totalStudentsAssessed: 3
      });
      expect(result).toMatchObject({
        score: "70",
        meetsTarget: true,
        individualRuleOutcome: "above"
      });

      await verifyAssessmentResult(nextAssessmentId);
      const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
        _id: studentGroup.history[0].assessmentResultId
      });
      expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe("currentAssessmentResultId");
      expect(lastCompletedAssessmentResult.ruleResults).toEqual({
        passed: true,
        nextSkill: {
          assessmentId: nextAssessmentId,
          assessmentName: nextAssessmentName,
          interventions: [],
          targets: [28, 56, 300]
        }
      });
    });

    describe("For a student group with up to 10 students", () => {
      it("should remove an open assessment result when 80% of students meet instructional target on the last skill", async () => {
        await addStudentGroupWithClasswideIntervention({
          isFailing: true,
          ...addStudentGroupWithClasswideInterventionParams,
          studentScores: [30, 30, 40, 40, 40]
        });
        const studentId = "testStudentId1";
        const studentScore = "40";
        await verifyAssessmentResult(assessmentId);
        await updateStudentScoreInClasswideIntervention({
          studentGroupId,
          assessmentId,
          studentId,
          studentScore,
          masteryTarget,
          isLastSkill: true
        });

        const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(studentGroup.currentAssessmentResultIds).toHaveLength(0);
        expect(studentGroup.currentClasswideSkill.assessmentResultId).toBeUndefined();
        const openAssessmentResult = await AssessmentResults.findOneAsync({
          status: "OPEN",
          studentGroupId,
          type: "classwide"
        });
        expect(openAssessmentResult).toBe(null);
        const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
          _id: studentGroup.history[0].assessmentResultId
        });
        expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBeUndefined();
        expect(lastCompletedAssessmentResult.ruleResults).toEqual({
          passed: true
        });
      });

      it("should add an open assessment result when 80% of students don't meet instructional target anymore on the last skill", async () => {
        await addStudentGroupWithClasswideIntervention({
          isFailing: false,
          isLastSkill: true,
          ...addStudentGroupWithClasswideInterventionParams,
          studentScores: [30, 40, 40, 40, 40]
        });
        const openAssessmentResultQuery = {
          status: "OPEN",
          studentGroupId,
          type: "classwide"
        };
        let openAssessmentResult = await AssessmentResults.findOneAsync(openAssessmentResultQuery);
        expect(openAssessmentResult).toBe(null);

        const studentId = "testStudentId1";
        const studentScore = "30";
        await updateStudentScoreInClasswideIntervention({
          studentGroupId,
          assessmentId,
          studentId,
          studentScore,
          masteryTarget,
          isLastSkill: true
        });

        await verifyAssessmentResult(assessmentId);
        openAssessmentResult = await AssessmentResults.findOneAsync(openAssessmentResultQuery);
        expect(openAssessmentResult.scores.every(score => score._id)).toBe(true);
        const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(studentGroup.currentAssessmentResultIds).toEqual([openAssessmentResult._id]);
        expect(studentGroup.currentClasswideSkill.assessmentResultId).toEqual(openAssessmentResult._id);
        const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
          _id: openAssessmentResult.previousAssessmentResultId
        });
        expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe(openAssessmentResult._id);
        expect(lastCompletedAssessmentResult.ruleResults).toEqual({
          passed: false,
          nextSkill: {
            assessmentId,
            assessmentName: `assessmentName_${assessmentId}`,
            interventions: [],
            targets: [34, 54, 300]
          }
        });
      });
    });

    describe("For a student group with more than 10 students", () => {
      it("should remove an open assessment result when median score goes above mastery target and it's the last skill", async () => {
        await addStudentGroupWithClasswideIntervention({
          isFailing: true,
          ...addStudentGroupWithClasswideInterventionParams,
          studentScores: [30, 30, 30, 30, 30, 45, 99, 99, 99, 99, 99]
        });
        const studentId = "testStudentId5";
        const studentScore = "70";
        await verifyAssessmentResult(assessmentId);
        await updateStudentScoreInClasswideIntervention({
          studentGroupId,
          assessmentId,
          studentId,
          studentScore,
          masteryTarget,
          isLastSkill: true
        });

        const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(studentGroup.currentAssessmentResultIds).toHaveLength(0);
        expect(studentGroup.currentClasswideSkill.assessmentResultId).toBeUndefined();
        const openAssessmentResult = await AssessmentResults.findOneAsync({
          status: "OPEN",
          studentGroupId,
          type: "classwide"
        });
        expect(openAssessmentResult).toBe(null);
        const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
          _id: studentGroup.history[0].assessmentResultId
        });
        expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBeUndefined();
        expect(lastCompletedAssessmentResult.ruleResults).toEqual({
          passed: true
        });
      });

      it("should add an open assessment result when median score goes below mastery target and it's the last skill", async () => {
        await addStudentGroupWithClasswideIntervention({
          isFailing: false,
          isLastSkill: true,
          ...addStudentGroupWithClasswideInterventionParams,
          studentScores: [30, 30, 30, 30, 30, 60, 99, 99, 99, 99, 99]
        });
        const openAssessmentResultQuery = {
          status: "OPEN",
          studentGroupId,
          type: "classwide"
        };
        let openAssessmentResult = await AssessmentResults.findOneAsync(openAssessmentResultQuery);
        expect(openAssessmentResult).toBe(null);

        const studentId = "testStudentId5";
        const studentScore = "45";
        await updateStudentScoreInClasswideIntervention({
          studentGroupId,
          assessmentId,
          studentId,
          studentScore,
          masteryTarget,
          isLastSkill: true
        });

        await verifyAssessmentResult(assessmentId);
        openAssessmentResult = await AssessmentResults.findOneAsync(openAssessmentResultQuery);
        expect(openAssessmentResult.scores.every(score => score._id)).toBe(true);
        const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(studentGroup.currentAssessmentResultIds).toEqual([openAssessmentResult._id]);
        expect(studentGroup.currentClasswideSkill.assessmentResultId).toEqual(openAssessmentResult._id);
        const lastCompletedAssessmentResult = await AssessmentResults.findOneAsync({
          _id: openAssessmentResult.previousAssessmentResultId
        });
        expect(lastCompletedAssessmentResult.nextAssessmentResultId).toBe(openAssessmentResult._id);
        expect(lastCompletedAssessmentResult.ruleResults).toEqual({
          passed: false,
          nextSkill: {
            assessmentId,
            assessmentName: `assessmentName_${assessmentId}`,
            interventions: [],
            targets: [34, 54, 300]
          }
        });
      });
    });
  });
});
