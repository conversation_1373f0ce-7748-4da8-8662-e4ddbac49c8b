import React from "react";

export default function selectComponentToPrint(component) {
  let Component;

  switch (component) {
    case "StudentDetail":
      Component = require("/imports/ui/pages/student-detail/student-detail").default; // eslint-disable-line global-require
      break;
    case "AllStudentDetail":
      Component = require("/imports/ui/pages/student-detail/all-student-detail").default; // eslint-disable-line global-require
      break;
    case "AllStudentsClasswideInterventionGraphs":
      Component = require("/imports/ui/pages/student-detail/all-students-detail-merged").default; // eslint-disable-line global-require
      break;
    case "Screening":
      Component = require("/imports/ui/pages/screening/screening-results").default; // eslint-disable-line global-require
      break;
    case "ClasswideIntervention":
      Component = require("/imports/ui/pages/dashboard/dashboard").default; // eslint-disable-line global-require
      break;
    case "ProgramEvaluation":
      Component = require("/imports/ui/pages/program-evaluation/program-evaluation").default; // eslint-disable-line global-require
      break;
    case "DistrictReporting":
      Component = require("/imports/ui/pages/district-reporting/district-reporting").default; // eslint-disable-line global-require
      break;
    case "SchoolOverview":
      Component = require("/imports/ui/pages/admin-view/admin-view").AdminOverviewWithTracker; // eslint-disable-line global-require
      break;
    case "Growth":
      Component = require("/imports/ui/pages/dashboard/dashboard").default; // eslint-disable-line global-require
      break;
    default:
      Component = <div>Provide printable component.</div>;
      break;
  }

  return Component;
}
