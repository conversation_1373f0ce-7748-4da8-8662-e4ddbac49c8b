import { Meteor } from "meteor/meteor";
import { Interventions } from "../interventions";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("Interventions", function interventionsPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  return Interventions.find({});
});

Meteor.publish("Interventions:ProgressMonitoring", function() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Interventions.find({}, { fields: { name: 1, abbreviation: 1 } });
});
