import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import * as auth from "../../authorization/server/methods";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { Students } from "../../students/students";

Meteor.methods({
  async "Interventions:getNumberOfCompletedInterventionsInGroup"({ siteId, studentGroupId }) {
    check(siteId, String);
    check(studentGroupId, String);

    const { userId } = this;

    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    } else if (
      await auth.hasAccess(["teacher", "admin", "support", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return getNumberOfCompletedInterventionsInGroup({ siteId, studentGroupId });
    }
    throw new Meteor.Error(
      "Interventions:getNumberOfCompletedInterventionsInGroup",
      "Invalid permissions to call method Interventions:getNumberOfCompletedInterventionsInGroup"
    );
  }
});

export default async function getNumberOfCompletedInterventionsInGroup({ siteId, studentGroupId }) {
  const studentIdsInSite = (
    await StudentGroupEnrollments.find({ siteId, studentGroupId }, { fields: { studentId: 1 } }).fetchAsync()
  ).map(ss => ss.studentId);

  return (
    await Students.find(
      {
        _id: { $in: studentIdsInSite },
        "currentSkill.message.messageCode": "56"
      },
      { fields: { _id: 1 } }
    ).fetchAsync()
  ).length;
}
