import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import some from "lodash/some";
import { StudentGroupEnrollments } from "../studentGroupEnrollments";
import { StudentGroups } from "../../studentGroups/studentGroups";
import * as auth from "../../authorization/server/methods";
import * as utils from "../../utilities/utilities";
import { getMeteorUser, isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("StudentGroupEnrollments:PerSite", async function studentGroupEnrollmentsPublication({
  orgid,
  siteId,
  shouldPublishActive = true,
  schoolYear,
  projection = {}
}) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);
  check(siteId, String);

  if (
    !(await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    }))
  ) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: StudentGroupEnrollments:PerSite"
    });
    return this.ready();
  }
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const studentEnrollmentQuery = { siteId, schoolYear: schoolYear || currentSchoolYear };
  if (shouldPublishActive) {
    studentEnrollmentQuery.isActive = true;
  }
  return StudentGroupEnrollments.find(studentEnrollmentQuery, {
    fields: {
      orgid: 1,
      studentId: 1,
      studentGroupId: 1,
      siteId: 1,
      schoolYear: 1,
      rosterImportId: 1,
      isActive: 1,
      grade: 1,
      isInIndividualIntervention: 1,
      ...projection
    }
  });
});

Meteor.publish("StudentGroupEnrollments:PerOrg:Archived", async function getStudentGroupEnrollmentsForOrg(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);

  if (
    !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    }))
  ) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: StudentGroupEnrollments:PerOrg:Archived"
    });
    return this.ready();
  }
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const studentEnrollmentQuery = { orgid, schoolYear: currentSchoolYear };
  const archivedStudentIds = (
    await StudentGroupEnrollments.aggregate([
      {
        $match: studentEnrollmentQuery
      },
      {
        $group: {
          _id: "$studentId",
          isActiveList: { $addToSet: "$isActive" }
        }
      },
      {
        $project: {
          _id: 1,
          isAnyActive: { $anyElementTrue: "$isActiveList" }
        }
      },
      {
        $match: {
          isAnyActive: false
        }
      }
    ])
  ).map(a => a._id);
  return StudentGroupEnrollments.find(
    { studentId: { $in: archivedStudentIds } },
    { fields: { isActive: 1, orgid: 1, grade: 1, studentId: 1, siteId: 1, studentGroupId: 1 } }
  );
});

Meteor.publish("StudentGroupEnrollmentsInStudentGroup", async function studentGroupEnrollmentsPublication(
  studentGroupId
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);

  const sg = await StudentGroups.findOneAsync(
    { _id: studentGroupId },
    { fields: { ownerIds: 1, secondaryTeachers: 1, siteId: 1, orgid: 1 } }
  );
  if (!sg) {
    return this.ready();
  }
  if (
    some(sg.ownerIds, oId => this.userId === oId) ||
    some(sg.secondaryTeachers, teacherId => this.userId === teacherId) ||
    (await auth.hasAccess(["admin", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId: sg.siteId,
      orgid: sg.orgid
    }))
  ) {
    return StudentGroupEnrollments.find({ studentGroupId, isActive: true });
  }
  return this.ready();
});

Meteor.publish("StudentGroupEnrollmentsAssociatedWithUser", function({ studentId, schoolYear, user }) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentId, String);
  check(schoolYear, Number);
  check(user, Object);

  const { orgid, siteAccess } = user.profile;
  const siteIds = siteAccess.filter(sa => sa.isActive).map(sa => sa.siteId);
  return StudentGroupEnrollments.find({
    orgid,
    schoolYear,
    studentId,
    siteId: { $in: siteIds }
  });
});

Meteor.publish("StudentGroupEnrollmentsInStudentGroups", async function getStudentGroupEnrollmentsInStudentGroups(
  studentGroupIds
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupIds, [String]);

  const sgs = await StudentGroups.find(
    { _id: { $in: studentGroupIds } },
    { fields: { ownerIds: 1, secondaryTeachers: 1, siteId: 1, orgid: 1 } }
  ).fetchAsync();
  if (!sgs) {
    return this.ready();
  }

  const { userId } = this;

  // Check if user is owner or secondary teacher for all student groups
  const isOwnerForAll = sgs.every(sg => some(sg.ownerIds, oId => userId === oId));
  const isSecondaryTeacherForAll = sgs.every(sg => some(sg.secondaryTeachers, teacherId => userId === teacherId));

  if (isOwnerForAll || isSecondaryTeacherForAll) {
    return StudentGroupEnrollments.find({ studentGroupId: { $in: studentGroupIds }, isActive: true });
  }

  // Check if user has superAdmin or universalCoach access
  if (await auth.hasAccess(["superAdmin", "universalCoach"], { userId })) {
    return StudentGroupEnrollments.find({ studentGroupId: { $in: studentGroupIds }, isActive: true });
  }

  // Check if user has admin access for all student groups
  const adminAccessChecks = await Promise.all(sgs.map(sg => auth.hasAccess(["admin"], { userId, siteId: sg.siteId })));
  if (adminAccessChecks.every(hasAccess => hasAccess)) {
    return StudentGroupEnrollments.find({ studentGroupId: { $in: studentGroupIds }, isActive: true });
  }

  // Check if user has dataAdmin access for all student groups
  const dataAdminAccessChecks = await Promise.all(
    sgs.map(sg => auth.hasAccess(["dataAdmin"], { userId, orgid: sg.orgid }))
  );
  if (dataAdminAccessChecks.every(hasAccess => hasAccess)) {
    return StudentGroupEnrollments.find({ studentGroupId: { $in: studentGroupIds }, isActive: true });
  }

  // Check if user has support access for all student groups
  const supportAccessChecks = await Promise.all(
    sgs.map(sg => auth.hasAccess(["support"], { userId, orgid: sg.orgid }))
  );
  if (supportAccessChecks.every(hasAccess => hasAccess)) {
    return StudentGroupEnrollments.find({ studentGroupId: { $in: studentGroupIds }, isActive: true });
  }

  return this.ready();
});
