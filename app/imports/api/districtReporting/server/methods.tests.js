import { getIndividualInterventionImplementationForSchools } from "/imports/ui/pages/program-evaluation/reporting-data-methods";
import { getStudentIdsManuallyRecommendedForIndividualIntervention } from "../helpers";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";

const getAssessmentResult = ({
  assessmentResultId,
  studentId = "studentId",
  status = "COMPLETED",
  type = "individual",
  shouldHaveInterventions = true,
  assessmentId = "assessmentId1",
  benchmarkAssessmentId = assessmentId,
  meetsTarget = false,
  created,
  lastModified,
  studentGroupId = "studentGroupId",
  benchmarkPeriodId = "fall",
  nextAssessmentResultId
}) => {
  if (type === "benchmark") {
    return {
      _id: assessmentResultId,
      type,
      studentGroupId,
      benchmarkPeriodId,
      ...(nextAssessmentResultId ? { nextAssessmentResultId } : {}),
      created,
      lastModified
    };
  }

  const measureIds = type === "individual" ? [benchmarkAssessmentId, assessmentId] : [assessmentId];
  return {
    _id: assessmentResultId,
    ...(type === "individual" ? { studentId } : {}),
    status,
    type,
    studentGroupId,
    benchmarkPeriodId,
    ...(created ? { created } : {}),
    ...(lastModified ? { lastModified } : {}),
    ...(type === "classwide" ? { nextAssessmentResultId } : {}),
    individualSkills: {
      interventions: shouldHaveInterventions ? [{}] : [],
      benchmarkAssessmentId
    },
    measures: measureIds.filter(Boolean).map(aId => ({
      assessmentId: aId,
      studentResults: [{ meetsTarget }]
    })),
    ruleResults: {
      passed: meetsTarget
    }
  };
};

const getStudentGroup = ({ studentGroupId = "studentGroupId", ownerIds, secondaryTeachers = [] }) => {
  return {
    _id: studentGroupId,
    ownerIds,
    secondaryTeachers
  };
};

describe("getStudentIdsManuallyRecommendedForIndividualIntervention", () => {
  const teacherId = "teacherId";
  const coachId = "coachId";
  const externalUserId = "externalId";
  const universalCoachId = "universalCoachId";
  const userRolesById = {
    [teacherId]: [ROLE_IDS.teacher],
    [coachId]: [ROLE_IDS.admin],
    [externalUserId]: [ROLE_IDS.admin],
    [universalCoachId]: [ROLE_IDS.universalCoach]
  };

  test.each([
    {
      params: {
        assessmentResults: [],
        studentGroupsInSchool: [],
        userRolesById
      },
      expected: [],
      title: "student hasn't saved any individual intervention scores"
    },
    {
      params: {
        assessmentResults: [
          getAssessmentResult({
            assessmentResultId: "aId2",
            shouldHaveInterventions: false,
            created: { on: 10, by: externalUserId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId1",
            type: "benchmark",
            created: { on: 20, by: teacherId },
            lastModified: { on: 30, by: teacherId },
            nextAssessmentResultId: "aId3"
          })
        ],
        studentGroupsInSchool: [getStudentGroup({ ownerIds: [teacherId], secondaryTeachers: [coachId] })],
        userRolesById
      },
      expected: ["studentId"],
      title: "student was manually scheduled by having individual intervention score saved before screening"
    },
    {
      params: {
        assessmentResults: [
          getAssessmentResult({
            assessmentResultId: "aId9",
            shouldHaveInterventions: false,
            created: { on: 2419200001, by: externalUserId } // NOTE(fmazur) - 4 weeks
          }),
          getAssessmentResult({
            assessmentResultId: "aId1",
            type: "classwide",
            created: { on: 10, by: teacherId },
            lastModified: { on: 11, by: teacherId },
            nextAssessmentResultId: "aId2"
          }),
          getAssessmentResult({
            assessmentResultId: "aId2",
            type: "classwide",
            created: { on: 2419200011, by: teacherId }, // NOTE(fmazur) - 11 + 4weeks
            lastModified: { on: 2419200012, by: teacherId },
            nextAssessmentResultId: "aId3"
          })
        ],
        studentGroupsInSchool: [getStudentGroup({ ownerIds: [teacherId], secondaryTeachers: [coachId] })],
        userRolesById
      },
      expected: ["studentId"],
      title: "student was manually scheduled by having individual intervention score saved before 4 weeks of classwide"
    },
    {
      params: {
        assessmentResults: [
          getAssessmentResult({
            assessmentResultId: "aId5",
            type: "benchmark",
            created: { on: 20, by: teacherId },
            lastModified: { on: 20, by: teacherId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId1",
            studentId: "sId1",
            shouldHaveInterventions: false,
            created: { on: 30, by: universalCoachId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId2",
            studentId: "sId2",
            shouldHaveInterventions: false,
            created: { on: 30, by: externalUserId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId3",
            studentId: "sId3",
            shouldHaveInterventions: false,
            created: { on: 30, by: teacherId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId4",
            studentId: "sId4",
            shouldHaveInterventions: false,
            created: { on: 30, by: coachId }
          })
        ],
        studentGroupsInSchool: [getStudentGroup({ ownerIds: [teacherId], secondaryTeachers: [coachId] })],
        userRolesById
      },
      expected: ["sId1", "sId2"],
      title: "student was manually scheduled by any user that is not an owner or secondary teacher in the group"
    },
    {
      params: {
        assessmentResults: [
          getAssessmentResult({
            assessmentResultId: "aId1",
            shouldHaveInterventions: false,
            created: { on: 10, by: externalUserId }
          })
        ],
        studentGroupsInSchool: [getStudentGroup({ ownerIds: [teacherId], secondaryTeachers: [coachId] })],
        userRolesById
      },
      expected: ["studentId"],
      title: "student was manually scheduled when there is no screening or classwide in the group"
    },
    {
      params: {
        assessmentResults: [
          getAssessmentResult({
            assessmentResultId: "aId1",
            shouldHaveInterventions: false,
            created: { on: 10, by: coachId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId2",
            type: "benchmark",
            created: { on: 20, by: teacherId },
            lastModified: { on: 30, by: teacherId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId3",
            type: "benchmark",
            benchmarkPeriodId: "winter",
            created: { on: 50, by: coachId },
            lastModified: { on: 60, by: coachId }
          }),
          getAssessmentResult({
            assessmentResultId: "aId4",
            shouldHaveInterventions: false,
            benchmarkPeriodId: "winter",
            created: { on: 70, by: teacherId }
          })
        ],
        studentGroupsInSchool: [getStudentGroup({ ownerIds: [teacherId], secondaryTeachers: [coachId] })],
        userRolesById
      },
      expected: [],
      title:
        "student was manually scheduled by any case but was normally scheduled for an individual intervention in different benchmark period with screening"
    }
  ])("should return $expected when $title", ({ params, expected }) => {
    const manuallyScheduledStudentIds = getStudentIdsManuallyRecommendedForIndividualIntervention(params);
    expect(manuallyScheduledStudentIds).toEqual(expected);
  });
});

describe("DistrictReporting getIndividualInterventionImplementationForSchools", () => {
  const getStudent = ({ studentId, historyItems }) => {
    return {
      _id: studentId,
      history:
        historyItems.map(({ score, assessmentId = "assessmentId1", shouldHaveInterventions = true }) => {
          return {
            assessmentId,
            interventions: shouldHaveInterventions ? [{}] : [],
            type: "individual",
            assessmentResultMeasures: [
              {
                assessmentId,
                studentResults: [
                  {
                    studentId,
                    score
                  }
                ]
              }
            ]
          };
        }) || []
    };
  };
  describe("For each school:", () => {
    const studentId1 = "studentId1";
    const studentId2 = "studentId2";
    test.each([
      {
        schools: [
          {
            students: [],
            numberOfStudentsInIndividualInterventionQueue: 2,
            completedAssessmentResults: [],
            openAssessmentResults: []
          }
        ],
        expected: { numNeeding: 2, numGetting: 0, numCompleting: 0, averageScoresIncreasing: undefined },
        title: "there are 2 students in individual intervention queue only"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [{ score: 50 }, { score: 0 }, { score: 25, shouldHaveInterventions: false }]
              }),
              getStudent({
                studentId: studentId2,
                historyItems: [{ score: 50 }, { score: 0 }, { score: 25, shouldHaveInterventions: false }]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1, shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2, shouldHaveInterventions: false })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 2, numGetting: 2, numCompleting: 0, averageScoresIncreasing: 100 },
        title: "there are 2 students improving"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [{ score: 30 }, { score: 40 }, { score: 25, shouldHaveInterventions: false }]
              }),
              getStudent({
                studentId: studentId2,
                historyItems: [{ score: 50 }, { score: 0 }, { score: 25, shouldHaveInterventions: false }]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1, shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2, shouldHaveInterventions: false })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 2, numGetting: 2, numCompleting: 0, averageScoresIncreasing: 50 },
        title: "there is 1 student improving"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [{ score: 30 }, { score: 40 }, { score: 25, shouldHaveInterventions: false }]
              }),
              getStudent({
                studentId: studentId2,
                historyItems: [{ score: 20 }, { score: 20 }, { score: 25, shouldHaveInterventions: false }]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1 }),
              getAssessmentResult({ studentId: studentId1, shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2 }),
              getAssessmentResult({ studentId: studentId2, shouldHaveInterventions: false })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false }),
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 2, numGetting: 2, numCompleting: 0, averageScoresIncreasing: 0 },
        title: "there are 0 students improving"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [{ score: 25, shouldHaveInterventions: false }]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, shouldHaveInterventions: false })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 1, numGetting: 0, numCompleting: 0, averageScoresIncreasing: undefined },
        title: "there is a student with only drilldown score saved"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: []
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 1, numGetting: 0, numCompleting: 0, averageScoresIncreasing: undefined },
        title: "there is a student in the intervention without any score saved"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [
                  { score: 25, shouldHaveInterventions: true },
                  { score: 25, shouldHaveInterventions: false }
                ]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({
                studentId: studentId1,
                shouldHaveInterventions: true
              }),
              getAssessmentResult({
                studentId: studentId1,
                shouldHaveInterventions: false
              })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 1, numGetting: 1, numCompleting: 0, averageScoresIncreasing: undefined },
        title: "there is a student that hasn't completed at least one intervention skill tree"
      },
      {
        schools: [
          {
            students: [
              getStudent({
                studentId: studentId1,
                historyItems: [
                  { score: 25, shouldHaveInterventions: true },
                  { score: 25, shouldHaveInterventions: false }
                ]
              })
            ],
            numberOfStudentsInIndividualInterventionQueue: 0,
            completedAssessmentResults: [
              getAssessmentResult({
                studentId: studentId1,
                shouldHaveInterventions: true,
                meetsTarget: true
              }),
              getAssessmentResult({
                studentId: studentId1,
                shouldHaveInterventions: false
              })
            ],
            openAssessmentResults: [
              getAssessmentResult({ studentId: studentId1, status: "OPEN", shouldHaveInterventions: false })
            ]
          }
        ],
        expected: { numNeeding: 1, numGetting: 1, numCompleting: 1, averageScoresIncreasing: undefined },
        title: "there is a student that has completed at least one intervention skill tree"
      }
    ])("should return $expected when $title", ({ schools, expected }) => {
      const [schoolData] = getIndividualInterventionImplementationForSchools({ schools });
      expect(schoolData).toEqual(expect.objectContaining(expected));
    });
  });
});
