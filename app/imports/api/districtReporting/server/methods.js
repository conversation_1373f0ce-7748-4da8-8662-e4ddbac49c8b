import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { groupBy, keyBy, range, uniq } from "lodash";
import { Organizations } from "../../organizations/organizations";
import { Sites } from "../../sites/sites";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { BenchmarkPeriods } from "../../benchmarkPeriods/benchmarkPeriods";
import { Rules } from "../../rules/rules";
import { Assessments } from "../../assessments/assessments";
import { Students } from "../../students/students";
import { AssessmentGrowth } from "../../assessmentGrowth/assessmentGrowth";
import { AssessmentScoresUpload } from "../../assessmentScoresUpload/assessmentScoresUpload";
import { normalizeGrade } from "../../utilities/sortingHelpers/normalizeSortItem";
import { getLatestAvailableSchoolYear, getValuesByKey } from "../../utilities/utilities";
import {
  calculateExternalProficiencyDataForSchools,
  getGrowthDataBySchoolYearByGrade,
  getProficientOnExternalMeasureFallToSpringData,
  getProficientOnExternalMeasureSpringByYearData,
  getProficientOnExternalMeasureSpringData,
  getStudentIdsManuallyRecommendedForIndividualIntervention,
  getUserRolesByIdBySiteId
} from "../helpers";
import { calculateClasswideStatsForMultipleGroups } from "../../utilities/server/interventionStats";
import { Users } from "../../users/users";

async function getClasswideAssessmentIdsByGradeForSchoolYear(schoolYear) {
  const classwideAssessmentIdsForGrades = await AssessmentResults.aggregate([
    { $match: { schoolYear, type: "classwide", status: "COMPLETED" } },
    { $addFields: { assessmentId: { $arrayElemAt: ["$assessmentIds", 0] } } },
    { $project: { _id: 0, grade: 1, studentGroupId: 1, assessmentId: 1 } },
    { $group: { _id: "$studentGroupId", assessmentIds: { $addToSet: "$assessmentId" }, grade: { $first: "$grade" } } },
    { $addFields: { skillsCount: { $size: "$assessmentIds" } } },
    { $sort: { skillsCount: -1 } },
    {
      $group: {
        _id: "$grade",
        assessmentIds: { $first: "$assessmentIds" },
        studentGroupId: { $first: "$_id" }
      }
    }
  ]);
  const studentGroupIds = classwideAssessmentIdsForGrades.map(c => c.studentGroupId);
  const studentGroupByGrade = keyBy(
    await StudentGroups.find(
      { _id: { $in: studentGroupIds } },
      { fields: { "history.assessmentId": 1, "history.type": 1, grade: 1 } }
    ).fetchAsync(),
    "grade"
  );

  return classwideAssessmentIdsForGrades.reduce((a, c) => {
    const studentGroupHistory = studentGroupByGrade[c._id].history
      .filter(h => h.type === "classwide" && c.assessmentIds.includes(h.assessmentId))
      .reverse();
    a[c._id] =
      studentGroupHistory.length >= c.assessmentIds.length
        ? uniq(studentGroupHistory.map(h => h.assessmentId))
        : c.assessmentIds;
    return a;
  }, {});
}

async function getDistrictReportingDataSet(orgid, schoolYear) {
  const org = await Organizations.findOneAsync({ _id: orgid }, { fields: { name: 1, benchmarkPeriodsGroupId: 1 } });
  const schools = await Sites.find(
    { orgid },
    { fields: { name: 1 }, sort: { "lastModified.on": -1, name: 1 } }
  ).fetchAsync();
  const currentAndTwoPriorSchoolYears = range(schoolYear - 2, schoolYear + 1);
  const studentGroupProject = {
    grade: 1,
    schoolYear: 1,
    siteId: 1,
    "history.assessmentId": 1,
    "history.benchmarkPeriodId": 1,
    "history.enrolledStudentIds": 1,
    "history.targets": 1,
    "history.type": 1,
    "history.whenEnded.on": 1,
    "history.assessmentResultMeasures.assessmentId": 1,
    "history.assessmentResultMeasures.medianScore": 1,
    "history.assessmentResultMeasures.numberMeetingTarget": 1,
    "history.assessmentResultMeasures.studentScores": 1,
    "history.assessmentResultMeasures.targetScores": 1,
    "history.assessmentResultMeasures.totalStudentsAssessed": 1,
    "currentClasswideSkill.assessmentId": 1,
    hasCompletedCWI: 1,
    individualInterventionQueue: 1,
    ownerIds: 1,
    secondaryTeachers: 1
  };

  const studentGroupsAggregate = [];
  // Data structure for studentGroupsAggregate
  // [{ schoolYear: 2022, sites: [{ siteId: "siteId1", documents: ["student groups for siteId1"] }, ...] }, ...];
  // eslint-disable-next-line no-restricted-syntax
  for await (const year of currentAndTwoPriorSchoolYears) {
    const studentGroupsForSchoolYear = await StudentGroups.aggregate([
      { $match: { orgid, isActive: true, schoolYear: year } },
      { $project: studentGroupProject },
      { $group: { _id: "$siteId", documents: { $push: "$$ROOT" } } }
    ]);
    studentGroupsAggregate.push({ schoolYear: year, sites: studentGroupsForSchoolYear });
  }

  const studentGroupsBySiteIdBySchoolYear = {};
  currentAndTwoPriorSchoolYears.forEach(year => {
    studentGroupsBySiteIdBySchoolYear[year] = keyBy(
      studentGroupsAggregate.find(sg => sg.schoolYear === year)?.sites || {},
      "_id"
    );
  });

  const studentGroupsBySiteId = studentGroupsBySiteIdBySchoolYear[schoolYear];

  const studentGroupEnrollmentForSchoolYear = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const year of currentAndTwoPriorSchoolYears) {
    const studentGroupEnrollments = await StudentGroupEnrollments.aggregate([
      { $match: { orgid, schoolYear: year } },
      { $project: { siteId: 1, studentGroupId: 1, studentId: 1, isActive: 1, "created.on": 1 } },
      { $group: { _id: "$siteId", documents: { $push: "$$ROOT" } } }
    ]);
    studentGroupEnrollmentForSchoolYear.push({ schoolYear: year, sites: studentGroupEnrollments });
  }

  const studentGroupEnrollments = studentGroupEnrollmentForSchoolYear.find(sgSet => sgSet.schoolYear === schoolYear)
    ?.sites;

  const studentGroupEnrollmentBySiteId = keyBy(studentGroupEnrollments, "_id");

  const assessmentNameById = getValuesByKey(
    await Assessments.find({}, { fields: { name: 1 } }).fetchAsync(),
    "_id",
    "name"
  );
  const bmPeriods = await BenchmarkPeriods.find({}, { fields: { created: 0, lastModified: 0 } }).fetchAsync();
  const assessmentGrowth = await AssessmentGrowth.find().fetchAsync();

  const growthDataBySchoolYearByGrade = getGrowthDataBySchoolYearByGrade({
    studentGroupsAggregate,
    studentGroupsBySiteIdBySchoolYear,
    assessmentGrowth,
    assessmentNameById,
    bmPeriods
  });

  const assessmentScoresUpload = await AssessmentScoresUpload.find(
    { orgid, schoolYear: { $in: currentAndTwoPriorSchoolYears } },
    {
      fields: { data: 1, schoolYear: 1, grade: 1, studentId: 1 }
    }
  ).fetchAsync();
  const assessmentScoresUploadBySchoolYear = groupBy(assessmentScoresUpload, "schoolYear");

  const externalProficiencyDataForSchools = {
    springData: [], // getProficientOnExternalMeasureSpringData
    fallToSpringData: [], // getProficientOnExternalMeasureFallToSpringData
    springByYearData: [] // getProficientOnExternalMeasureSpringByYearData
  };

  const usersInOrg = await Users.find({ "profile.orgid": orgid }, { fields: { "profile.siteAccess": 1 } }).fetchAsync();
  const userRolesByIdBySiteId = getUserRolesByIdBySiteId(usersInOrg, schoolYear);

  const schoolNameById = getValuesByKey(schools, "_id", "name");
  const extendedSchools = await Promise.all(
    schools.map(async school => {
      const studentGroupsInSchool = studentGroupsBySiteId[school._id];
      const studentGroupIds = studentGroupsInSchool?.documents?.map(({ _id }) => _id) || [];
      const assessmentResultsForSite = await AssessmentResults.aggregate([
        {
          $match: {
            schoolYear,
            orgid,
            studentGroupId: { $in: studentGroupIds },
            isAdditional: { $ne: true }
          }
        },
        {
          $project: {
            "classwideResults.totalStudentsAssessedOnAllMeasures": 1,
            created: 1,
            lastModified: 1,
            "individualSkills.interventions": 1,
            "individualSkills.benchmarkAssessmentId": 1,
            "measures.assessmentId": 1,
            "measures.assessmentName": 1,
            "measures.medianScore": 1,
            "measures.studentResults.individualRuleOutcome": 1,
            "measures.studentResults.status": 1,
            "measures.studentResults.studentId": 1,
            "measures.studentResults.meetsTarget": 1,
            "measures.targetScores": 1,
            "ruleResults.passed": 1,
            "scores.status": 1,
            "scores.studentId": 1,
            "scores.value": 1,
            benchmarkPeriodId: 1,
            grade: 1,
            status: 1,
            studentGroupId: 1,
            studentId: 1,
            type: 1
          }
        }
      ]);
      const completedAssessmentResults = assessmentResultsForSite.filter(ar => ar.status === "COMPLETED") || [];
      const openAssessmentResults = assessmentResultsForSite.filter(ar => ar.status === "OPEN") || [];
      const allStudentIdsInSchool = uniq(
        studentGroupEnrollmentBySiteId[school._id]?.documents.map(sge => sge.studentId)
      );
      const studentIdsBySchoolYear = {};
      studentGroupEnrollmentForSchoolYear.forEach(set => {
        const studentIds = uniq(set.sites.find(s => s._id === school._id)?.documents.map(sge => sge.studentId));
        studentIdsBySchoolYear[set.schoolYear] = studentIds.flat(1);
      });
      const activeStudentIdsInSchool = uniq(
        studentGroupEnrollmentBySiteId[school._id]?.documents.filter(sge => sge.isActive).map(sge => sge.studentId)
      );
      const students = await Students.find(
        {
          _id: { $in: activeStudentIdsInSchool }
        },
        {
          fields: {
            "currentSkill.assessmentResultId": 1,
            "currentSkill.benchmarkAssessmentId": 1,
            "history.assessmentId": 1,
            "history.assessmentResultMeasures": 1,
            "history.type": 1,
            "history.interventions": 1
          }
        }
      ).fetchAsync();

      const numberOfGroups = studentGroupsInSchool?.documents?.length;
      const studentGroupsByGrade = groupBy(studentGroupsInSchool?.documents || [], "grade");

      const numberOfStudents = activeStudentIdsInSchool.length;
      const studentGroupEnrollmentsByGroupId = groupBy(
        studentGroupEnrollmentBySiteId[school._id]?.documents || [],
        "studentGroupId"
      );
      const gradesWithGroups = Object.keys(studentGroupsByGrade || {}).sort((a, b) => {
        return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
      });

      const { allClasswideResults: allClasswideStats = [] } = await calculateClasswideStatsForMultipleGroups(
        studentGroupIds
      );
      const gradesWithId = gradesWithGroups.map(grade => ({ _id: grade }));

      const externalAssessmentScoresForSchool = (assessmentScoresUploadBySchoolYear?.[schoolYear] || []).filter(asu =>
        (allStudentIdsInSchool || []).includes(asu.studentId)
      );

      externalProficiencyDataForSchools.springData.push({
        school: school._id,
        ...getProficientOnExternalMeasureSpringData({
          studentGroups: studentGroupsInSchool?.documents || [],
          assessmentResultsForSite,
          allClasswideStats,
          studentGroupEnrollmentsByGroupId,
          gradesWithStudentGroupsInSite: gradesWithGroups,
          schoolYear,
          assessmentScoresUpload: externalAssessmentScoresForSchool,
          availableGrades: gradesWithId
        })
      });
      externalProficiencyDataForSchools.fallToSpringData.push({
        school: school._id,
        ...getProficientOnExternalMeasureFallToSpringData({
          assessmentScoresUpload: externalAssessmentScoresForSchool,
          schoolYear,
          availableGrades: gradesWithId
        })
      });

      externalProficiencyDataForSchools.springByYearData.push({
        school: school._id,
        ...getProficientOnExternalMeasureSpringByYearData({
          schoolYears: currentAndTwoPriorSchoolYears,
          assessmentScoresUpload: Object.entries(assessmentScoresUploadBySchoolYear)
            .map(([year, scoresUpload]) => {
              return scoresUpload.filter(asu => (studentIdsBySchoolYear[year] || []).includes(asu.studentId));
            })
            .flat(1),
          availableGrades: gradesWithId
        })
      });

      const manuallyScheduledStudentIds = getStudentIdsManuallyRecommendedForIndividualIntervention({
        assessmentResults: assessmentResultsForSite,
        studentGroupsInSchool: studentGroupsInSchool?.documents || [],
        userRolesById: userRolesByIdBySiteId[school._id]
      });

      return {
        ...school,
        numberOfStudents,
        numberOfGroups,
        studentGroupEnrollmentsByGroupId,
        studentGroupsByGrade,
        gradesWithGroups,
        completedAssessmentResults,
        openAssessmentResults,
        allClasswideStats,
        numberOfStudentsInIndividualInterventionQueue: studentGroupsInSchool?.documents.reduce((a, c) => {
          return a + (c.individualInterventionQueue?.length || 0);
        }, 0),
        manuallyScheduledStudentIds,
        students
      };
    })
  );

  const parsedExternalProficiencyDataForSchools = calculateExternalProficiencyDataForSchools(
    externalProficiencyDataForSchools,
    schoolNameById
  );

  let ruleAssessmentIdsByGrade = {};
  let classwideRules = await Rules.find(
    { grade: { $exists: true } },
    { fields: { "skills.assessmentId": 1, "skills.assessmentName": 1, grade: 1 } }
  ).fetchAsync();

  const latestAvailableSchoolYear = await getLatestAvailableSchoolYear(null, orgid);
  if (schoolYear === latestAvailableSchoolYear) {
    classwideRules.forEach(rule => {
      ruleAssessmentIdsByGrade[rule.grade] = rule.skills.map(r => r.assessmentId);
    });
  } else {
    ruleAssessmentIdsByGrade = await getClasswideAssessmentIdsByGradeForSchoolYear(schoolYear);
    classwideRules.forEach(rule => {
      if (!ruleAssessmentIdsByGrade[rule.grade]) {
        ruleAssessmentIdsByGrade[rule.grade] = rule.skills.map(r => r.assessmentId);
      }
    });
    classwideRules = Object.entries(ruleAssessmentIdsByGrade).map(([grade, assessmentIds]) => ({
      grade,
      skills: assessmentIds.map(assessmentId => ({ assessmentId }))
    }));
  }

  return {
    orgName: org?.name || "N/A",
    externalProficiencyDataForSchools: parsedExternalProficiencyDataForSchools,
    benchmarkPeriodsGroupId: org.benchmarkPeriodsGroupId,
    bmPeriods,
    ruleAssessmentIdsByGrade,
    assessmentNameById,
    classwideRules,
    growthDataBySchoolYearByGrade,
    schools: extendedSchools
  };
}

Meteor.methods({
  async getDistrictReportingDataSet(orgid, schoolYear) {
    check(orgid, String);
    check(schoolYear, Number);

    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    return getDistrictReportingDataSet(orgid, schoolYear);
  }
});
