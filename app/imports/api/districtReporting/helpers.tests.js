import { getFallToSpringGrowthResultsForGrade } from "./helpers";

describe("getFallToSpringGrowthResultsForGrade", () => {
  const mockAssessments = [
    { _id: "assessment1", name: "Test Assessment 1" },
    { _id: "assessment2", name: "Test Assessment 2" }
  ];

  const mockBmPeriods = [
    { _id: "fall", name: "Fall" },
    { _id: "winter", name: "Winter" },
    { _id: "spring", name: "Spring" }
  ];

  it("should return null when assessmentGrowths is empty", () => {
    const result = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [] },
      assessmentGrowths: [],
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: mockBmPeriods
    });

    expect(result).toBeNull();
  });

  it("should return null when no assessment comparison map found for grade", () => {
    const result = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [] },
      assessmentGrowths: [{ grade: "2", winterToSpring: [] }],
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: mockBmPeriods
    });

    expect(result).toBeNull();
  });

  it("should return null when bmPeriods is empty (causing getGrowthResults to return false)", () => {
    const mockAssessmentGrowths = [
      {
        grade: "1",
        winterToSpring: [{ winter: "assessment1", spring: "assessment2" }],
        fallToWinter: []
      }
    ];

    const result = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [{ history: [] }] },
      assessmentGrowths: mockAssessmentGrowths,
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: [] // Empty bmPeriods will cause getGrowthResults to return false
    });

    expect(result).toBeNull();
  });

  it("should return null when bmPeriods is undefined", () => {
    const mockAssessmentGrowths = [
      {
        grade: "1",
        winterToSpring: [{ winter: "assessment1", spring: "assessment2" }],
        fallToWinter: []
      }
    ];

    const result = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [{ history: [] }] },
      assessmentGrowths: mockAssessmentGrowths,
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: undefined
    });

    expect(result).toBeNull();
  });

  it("should handle the case when studentGroupsByGrade[grade] is undefined", () => {
    const mockAssessmentGrowths = [
      {
        grade: "1",
        winterToSpring: [{ winter: "assessment1", spring: "assessment2" }],
        fallToWinter: []
      }
    ];

    const result = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: {}, // No grade "1" key
      assessmentGrowths: mockAssessmentGrowths,
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: mockBmPeriods
    });

    // Should return a valid structure with empty data, not null
    expect(result).toEqual({
      winterToSpring: expect.any(Array)
    });
    expect(result.winterToSpring.length).toBeGreaterThan(0);
  });

  it("should handle the case when getGrowthResults returns false due to missing bmPeriods", () => {
    const mockAssessmentGrowths = [
      {
        grade: "1",
        winterToSpring: [{ winter: "assessment1", spring: "assessment2" }],
        fallToWinter: []
      }
    ];

    // Test with undefined bmPeriods
    const resultUndefined = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [{ history: [] }] },
      assessmentGrowths: mockAssessmentGrowths,
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: undefined
    });

    expect(resultUndefined).toBeNull();

    // Test with null bmPeriods
    const resultNull = getFallToSpringGrowthResultsForGrade({
      studentGroupsByGrade: { "1": [{ history: [] }] },
      assessmentGrowths: mockAssessmentGrowths,
      assessments: mockAssessments,
      grade: "1",
      bmPeriods: null
    });

    expect(resultNull).toBeNull();
  });
});
