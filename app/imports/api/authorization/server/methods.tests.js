import { Meteor } from "meteor/meteor";
import { expect } from "@jest/globals";

import * as auth from "./methods";
import { Users } from "../../users/users";
import { Sites } from "../../sites/sites";
import { roles } from "../../roles/methods";

// Mock dependencies
jest.mock("../../users/users");
jest.mock("../../sites/sites");
jest.mock("../../roles/methods");

if (Meteor.isServer) {
  describe("Authorization Methods", () => {
    beforeEach(() => {
      jest.clearAllMocks();

      // Mock roles data
      roles.splice(0, roles.length); // Clear array
      roles.push(
        { _id: "arbitraryIdteacher", name: "teacher" },
        { _id: "arbitraryIdadmin", name: "admin" },
        { _id: "arbitraryIdsuperAdmin", name: "superAdmin" },
        { _id: "arbitraryIdsupport", name: "support" },
        { _id: "arbitraryIddataAdmin", name: "dataAdmin" },
        { _id: "arbitraryIduniversalCoach", name: "universalCoach" },
        { _id: "arbitraryIduniversalDataAdmin", name: "universalDataAdmin" }
      );
    });

    describe("getSiteAccess", () => {
      it("should return site access from user profile", () => {
        const user = {
          profile: {
            siteAccess: [{ role: "arbitraryIdteacher", siteId: "site1", isActive: true }]
          }
        };

        const result = auth.getSiteAccess(user);
        expect(result).toEqual(user.profile.siteAccess);
      });

      it("should return empty array if no site access", () => {
        const user = { profile: {} };
        const result = auth.getSiteAccess(user);
        expect(result).toEqual([]);
      });

      it("should return empty array if user is null", () => {
        const result = auth.getSiteAccess(null);
        expect(result).toEqual([]);
      });
    });

    describe("hasRoleInSiteAccess", () => {
      it("should return true when user has the specified role", () => {
        const siteAccesses = [
          { role: "arbitraryIdteacher", siteId: "site1" },
          { role: "arbitraryIdadmin", siteId: "site2" }
        ];

        const result = auth.hasRoleInSiteAccess(siteAccesses, "teacher");
        expect(result).toBe(true);
      });

      it("should return false when user does not have the specified role", () => {
        const siteAccesses = [{ role: "arbitraryIdteacher", siteId: "site1" }];

        const result = auth.hasRoleInSiteAccess(siteAccesses, "admin");
        expect(result).toBe(false);
      });

      it("should return false when siteAccesses is empty", () => {
        const result = auth.hasRoleInSiteAccess([], "teacher");
        expect(result).toBe(false);
      });

      it("should return false when role does not exist in roles array", () => {
        const siteAccesses = [{ role: "arbitraryIdteacher", siteId: "site1" }];

        const result = auth.hasRoleInSiteAccess(siteAccesses, "nonexistentRole");
        expect(result).toBe(false);
      });
    });

    describe("getSupportUserAccess", () => {
      it("should return organization access from user profile", () => {
        const user = {
          profile: {
            organizationAccess: ["org1", "org2"]
          }
        };

        const result = auth.getSupportUserAccess(user);
        expect(result).toEqual(["org1", "org2"]);
      });

      it("should return empty array if no organization access", () => {
        const user = { profile: {} };
        const result = auth.getSupportUserAccess(user);
        expect(result).toEqual([]);
      });
    });

    describe("hasAccess", () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdteacher", siteId: "site1", isActive: true }]
        }
      };

      beforeEach(() => {
        Users.findOneAsync.mockResolvedValue(mockUser);
      });

      it("should return true for teacher role when user has teacher access", async () => {
        const result = await auth.hasAccess(["teacher"], {
          userId: "user1",
          siteId: "site1"
        });
        expect(result).toBe(true);
      });

      it("should return false for admin role when user only has teacher access", async () => {
        const result = await auth.hasAccess(["admin"], {
          userId: "user1",
          siteId: "site1"
        });
        expect(result).toBe(false);
      });

      it("should return true when checking multiple roles and user has one of them", async () => {
        const result = await auth.hasAccess(["admin", "teacher"], {
          userId: "user1",
          siteId: "site1"
        });
        expect(result).toBe(true);
      });

      it("should return false when user has no matching roles", async () => {
        const result = await auth.hasAccess(["admin", "superAdmin"], {
          userId: "user1",
          siteId: "site1"
        });
        expect(result).toBe(false);
      });

      it("should use provided user object instead of fetching from database", async () => {
        const result = await auth.hasAccess(["teacher"], {
          user: mockUser,
          siteId: "site1"
        });
        expect(result).toBe(true);
        expect(Users.findOneAsync).not.toHaveBeenCalled();
      });

      describe("admin role", () => {
        const adminUser = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            siteAccess: [{ role: "arbitraryIdadmin", siteId: "site1", isActive: true }]
          }
        };

        it("should return true for admin with correct siteId", async () => {
          Users.findOneAsync.mockResolvedValue(adminUser);

          const result = await auth.hasAccess(["admin"], {
            userId: "user1",
            siteId: "site1"
          });
          expect(result).toBe(true);
        });

        it("should return false for admin without siteId", async () => {
          Users.findOneAsync.mockResolvedValue(adminUser);

          const result = await auth.hasAccess(["admin"], {
            userId: "user1"
          });
          expect(result).toBe(false);
        });
      });

      describe("dataAdmin role", () => {
        const dataAdminUser = {
          ...mockUser,
          profile: {
            orgid: "org1",
            siteAccess: [{ role: "arbitraryIddataAdmin", siteId: "site1", isActive: true }]
          }
        };

        it("should return true for dataAdmin with matching orgid", async () => {
          Users.findOneAsync.mockResolvedValue(dataAdminUser);

          const result = await auth.hasAccess(["dataAdmin"], {
            userId: "user1",
            orgid: "org1"
          });
          expect(result).toBe(true);
        });

        it("should return false for dataAdmin with non-matching orgid", async () => {
          Users.findOneAsync.mockResolvedValue(dataAdminUser);

          const result = await auth.hasAccess(["dataAdmin"], {
            userId: "user1",
            orgid: "org2"
          });
          expect(result).toBe(false);
        });
      });

      describe("support role", () => {
        const supportUser = {
          ...mockUser,
          profile: {
            orgid: "org1",
            siteAccess: [{ role: "arbitraryIdsupport", siteId: "site1", isActive: true }],
            organizationAccess: ["org1", "org2"]
          }
        };

        it("should return true for support with organization access", async () => {
          Users.findOneAsync.mockResolvedValue(supportUser);

          const result = await auth.hasAccess(["support"], {
            userId: "user1",
            orgid: "org1"
          });
          expect(result).toBe(true);
        });

        it("should return false for support without organization access", async () => {
          Users.findOneAsync.mockResolvedValue(supportUser);

          const result = await auth.hasAccess(["support"], {
            userId: "user1",
            orgid: "org3"
          });
          expect(result).toBe(false);
        });

        it("should check site access when shouldCheckOrgAccessForSupport is false", async () => {
          Users.findOneAsync.mockResolvedValue(supportUser);

          const result = await auth.hasAccess(["support"], {
            userId: "user1",
            shouldCheckOrgAccessForSupport: false
          });
          expect(result).toBe(true);
        });

        it("should get orgid from site when only siteId provided", async () => {
          Users.findOneAsync.mockResolvedValue(supportUser);
          Sites.findOneAsync.mockResolvedValue({ orgid: "org1" });

          const result = await auth.hasAccess(["support"], {
            userId: "user1",
            siteId: "site1"
          });
          expect(result).toBe(true);
          expect(Sites.findOneAsync).toHaveBeenCalledWith({ _id: "site1" }, { fields: { orgid: 1 } });
        });

        it("should handle missing site when looking up orgid", async () => {
          Users.findOneAsync.mockResolvedValue(supportUser);
          Sites.findOneAsync.mockResolvedValue(null);

          const result = await auth.hasAccess(["support"], {
            userId: "user1",
            siteId: "site1"
          });
          expect(result).toBe(false);
        });
      });

      it("should handle empty roleNames array", async () => {
        const result = await auth.hasAccess([], { userId: "user1" });
        expect(result).toBe(false);
      });

      it("should handle null user from database", async () => {
        Users.findOneAsync.mockResolvedValue(null);

        const result = await auth.hasAccess(["teacher"], { userId: "user1" });
        expect(result).toBe(false);
      });
    });
  });
}
