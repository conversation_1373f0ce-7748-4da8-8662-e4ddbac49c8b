import { expect } from "@jest/globals";

import * as auth from "./methods";
import { Users } from "../../users/users";
import { Sites } from "../../sites/sites";
import { roles } from "../../roles/methods";

describe("hasAccess", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock roles data
    roles.splice(0, roles.length);
    roles.push(
      { _id: "arbitraryIdteacher", name: "teacher" },
      { _id: "arbitraryIdadmin", name: "admin" },
      { _id: "arbitraryIdsuperAdmin", name: "superAdmin" },
      { _id: "arbitraryIdsupport", name: "support" },
      { _id: "arbitraryIddataAdmin", name: "dataAdmin" },
      { _id: "arbitraryIduniversalCoach", name: "universalCoach" },
      { _id: "arbitraryIduniversalDataAdmin", name: "universalDataAdmin" }
    );
  });

  describe("Sequential role checking", () => {
    it("should properly await each role check and return true for first matching role", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdadmin", siteId: "site1", isActive: true }]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);

      // Test that it finds admin role (second in the array) and returns true
      const result = await auth.hasAccess(["teacher", "admin", "superAdmin"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(true);
      expect(Users.findOneAsync).toHaveBeenCalledWith({ _id: "user1" });
    });

    it("should properly await each role check and return false when no roles match", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdteacher", siteId: "site1", isActive: true }]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);

      // Test that it checks all roles and returns false when none match
      const result = await auth.hasAccess(["admin", "superAdmin", "dataAdmin"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(false);
    });

    it("should stop checking roles after finding first match", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [
            { role: "arbitraryIdteacher", siteId: "site1", isActive: true },
            { role: "arbitraryIdadmin", siteId: "site1", isActive: true }
          ]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);

      // Should return true for teacher (first role) and not need to check admin
      const result = await auth.hasAccess(["teacher", "admin"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(true);
    });
  });

  describe("Support role with async site lookup", () => {
    it("should properly await site lookup when orgid is not provided", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdsupport", siteId: "site1", isActive: true }],
          organizationAccess: ["org1"]
        }
      };

      const mockSite = { orgid: "org1" };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);
      Sites.findOneAsync = jest.fn().mockResolvedValue(mockSite);

      const result = await auth.hasAccess(["support"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(true);
      expect(Sites.findOneAsync).toHaveBeenCalledWith({ _id: "site1" }, { fields: { orgid: 1 } });
    });

    it("should handle null site lookup result", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdsupport", siteId: "site1", isActive: true }],
          organizationAccess: ["org1"]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);
      Sites.findOneAsync = jest.fn().mockResolvedValue(null);

      const result = await auth.hasAccess(["support"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(false);
    });
  });

  describe("Multiple async operations", () => {
    it("should handle multiple roles with different async requirements", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org2", // Different from the one we'll check
          siteAccess: [{ role: "arbitraryIdsupport", siteId: "site1", isActive: true }],
          organizationAccess: ["org1"]
        }
      };

      const mockSite = { orgid: "org1" };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);
      Sites.findOneAsync = jest.fn().mockResolvedValue(mockSite);

      // Should check dataAdmin first (fails due to orgid mismatch), then support (succeeds)
      // Note: We don't provide orgid so support role will need to look up site
      const result = await auth.hasAccess(["dataAdmin", "support"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(true);
      expect(Sites.findOneAsync).toHaveBeenCalledWith({ _id: "site1" }, { fields: { orgid: 1 } });
    });
  });

  describe("Error handling in async operations", () => {
    it("should handle database errors gracefully", async () => {
      Users.findOneAsync = jest.fn().mockRejectedValue(new Error("Database error"));

      await expect(auth.hasAccess(["teacher"], { userId: "user1" })).rejects.toThrow("Database error");
    });

    it("should handle site lookup errors gracefully", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdsupport", siteId: "site1", isActive: true }],
          organizationAccess: ["org1"]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);
      Sites.findOneAsync = jest.fn().mockRejectedValue(new Error("Site lookup error"));

      await expect(
        auth.hasAccess(["support"], {
          userId: "user1",
          siteId: "site1"
        })
      ).rejects.toThrow("Site lookup error");
    });
  });

  describe("Performance considerations", () => {
    it("should not fetch user multiple times when user object is provided", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdteacher", siteId: "site1", isActive: true }]
        }
      };

      Users.findOneAsync = jest.fn();

      const result = await auth.hasAccess(["teacher"], {
        user: mockUser,
        siteId: "site1"
      });

      expect(result).toBe(true);
      expect(Users.findOneAsync).not.toHaveBeenCalled();
    });

    it("should fetch user only once even when checking multiple roles", async () => {
      const mockUser = {
        _id: "user1",
        profile: {
          orgid: "org1",
          siteAccess: [{ role: "arbitraryIdadmin", siteId: "site1", isActive: true }]
        }
      };

      Users.findOneAsync = jest.fn().mockResolvedValue(mockUser);

      const result = await auth.hasAccess(["teacher", "admin", "superAdmin"], {
        userId: "user1",
        siteId: "site1"
      });

      expect(result).toBe(true);
      expect(Users.findOneAsync).toHaveBeenCalledTimes(1);
    });
  });
});
