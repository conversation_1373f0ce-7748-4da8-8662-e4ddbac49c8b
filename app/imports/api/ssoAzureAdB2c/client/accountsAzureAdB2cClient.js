/* eslint-disable no-unused-expressions */
/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */
if (!Accounts.openIdConnect) {
  Accounts.openIdConnect = {};
}

/*
 This is very similar (read: copied) from the oauth method which calls the loginMethod with and oauth attribute.
 We change it to openIdConnect to bypass the loginHandler for oauth (accounts-oauth/oauth_server.js).
 This handle automatically tries to create the user for us which we don't want it to do.  Our azureAdB2c
 loginHandler picks it up instead.
 */

Accounts.openIdConnect.tryLoginAfterPopupClosed = function(credentialToken, callback) {
  const credentialSecret = OAuth._retrieveCredentialSecret(credentialToken) || null;
  console.log("[SSO-Client] After popup closed:", {
    credentialToken: `${credentialToken?.substring(0, 10)}...`,
    hasSecret: !!credentialSecret,
    secretPrefix: `${credentialSecret?.substring(0, 10)}...`
  });
  Accounts.callLoginMethod({
    methodArguments: [
      {
        openIdConnect: {
          credentialToken,
          credentialSecret
        }
      }
    ],
    userCallback:
      callback &&
      function(err) {
        callback(convertError(err));
      }
  });
};

Accounts.openIdConnect.credentialRequestCompleteHandler = function(callback) {
  return function(credentialTokenOrError) {
    if (credentialTokenOrError && credentialTokenOrError instanceof Error) {
      callback && callback(credentialTokenOrError);
    } else {
      Accounts.openIdConnect.tryLoginAfterPopupClosed(credentialTokenOrError, callback);
    }
  };
};

function convertError(err) {
  if (err && err instanceof Meteor.Error && err.error === Accounts.LoginCancelledError.numericError) {
    return new Accounts.LoginCancelledError(err.reason);
  }
  return err;
}
