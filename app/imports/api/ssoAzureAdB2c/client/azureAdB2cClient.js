/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */
import { Random } from "meteor/random";

Accounts.oauth.registerService("azureAdB2c");

export function loginWithazureAdB2c(options, callback) {
  const credentialRequestCompleteCallback = Accounts.openIdConnect.credentialRequestCompleteHandler(callback);
  requestCredential(options, credentialRequestCompleteCallback);
}

export function requestCredential(...args) {
  // support both (options, callback) and (callback).
  const isFirstArgFunction = typeof args[0] === "function";
  const options = isFirstArgFunction ? {} : args[0] || {};
  const callback = isFirstArgFunction ? args[0] : args[1];

  const config = options.config || ServiceConfiguration.configurations.findOne({ service: "azureAdB2c" });
  if (!config) {
    if (callback) {
      callback(new ServiceConfiguration.ConfigError());
    }
    return;
  }

  const { scope } = config;

  const loginStyle = OAuth._loginStyle("azureAdB2c", config, options);
  const credentialToken = Random.secret();
  console.log("[SSO-Client] Created credential token:", `${credentialToken.substring(0, 10)}...`);

  const queryParams = {
    client_id: config.clientId,
    response_type: "code",
    redirect_uri: OAuth._redirectUri("azureAdB2c", config),
    scope,
    response_mode: "query",
    // eslint-disable-next-line no-underscore-dangle
    state: OAuth._stateParam(loginStyle, credentialToken),
    prompt: "login"
  };

  if (options.login_hint) {
    queryParams.login_hint = options.login_hint;
  }
  if (options.domain_hint) {
    queryParams.domain_hint = options.domain_hint;
  }

  const queryParamsEncoded = Object.entries(queryParams).map(([key, value]) => `${key}=${encodeURIComponent(value)}`);

  const { baseUrl } = config;

  const loginUrl = baseUrl + queryParamsEncoded.join("&");

  // eslint-disable-next-line no-undef
  console.log("[SSO-Client] Launching OAuth with:", {
    loginService: "azureAdB2c",
    credentialToken: `${credentialToken.substring(0, 10)}...`,
    loginStyle
  });

  try {
    OAuth.launchLogin({
      loginStyle,
      loginUrl,
      credentialToken,
      loginService: "azureAdB2c",
      credentialRequestCompleteCallback: callback,
      popupOptions: { height: 600 }
    });
  } catch (e) {
    const err = new Error("Login Popup Failed to Launch");
    err.details = "Ensure popups are allowed for this site";
    callback(err);
  }
}
