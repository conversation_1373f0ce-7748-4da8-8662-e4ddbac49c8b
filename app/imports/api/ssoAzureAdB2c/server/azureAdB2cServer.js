/* eslint-disable max-statements */
/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */
import _ from "lodash";

const whitelistedFields = [
  "given_name",
  "family_name",
  "name",
  "sub",
  "email",
  "issuerUserId",
  "issuerOrgId",
  "issuerEmailVerified",
  "issuerLogoutRedirect"
];

export async function retrieveCredential(credentialToken, credentialSecret) {
  console.log("[SSO-Retrieve] Attempting to retrieve credential:", {
    tokenPrefix: `${credentialToken?.substring(0, 10)}...`,
    secretPrefix: `${credentialSecret?.substring(0, 10)}...`,
    tokenLength: credentialToken?.length,
    secretLength: credentialSecret?.length
  });

  // Debug: Check what's in pending credentials collection
  if (OAuth._pendingCredentials && typeof OAuth._pendingCredentials.findOneAsync === "function") {
    // It's a MongoDB collection, check for our credential
    console.log("[SSO-Retrieve-Debug] Checking MongoDB collection for token:", credentialToken);

    try {
      // Try to find the credential in the collection
      const doc = await OAuth._pendingCredentials.findOneAsync({ key: credentialToken });
      if (doc) {
        console.log("[SSO-Retrieve-Debug] Found credential in collection! Document:", {
          hasCredential: !!doc.credential,
          hasSecret: !!doc.credentialSecret,
          secretMatch: doc.credentialSecret === credentialSecret,
          createdAt: doc.createdAt
        });
      } else {
        console.log("[SSO-Retrieve-Debug] Token NOT found in collection");
      }

      // Also try to see all credentials (for debugging)
      const docs = await OAuth._pendingCredentials.find({}).fetchAsync();
      console.log("[SSO-Retrieve-Debug] Total credentials in collection:", docs.length);
      if (docs.length > 0) {
        console.log(
          "[SSO-Retrieve-Debug] Credential keys in collection:",
          docs.map(d => (d.key ? `${d.key.substring(0, 10)}...` : "no-key"))
        );
      }
    } catch (e) {
      console.log("[SSO-Retrieve-Debug] Error checking collection:", e.message);
    }
  } else if (OAuth._pendingCredentials) {
    // Fallback for non-collection (shouldn't happen in Meteor 3)
    console.log(
      "[SSO-Retrieve-Debug] _pendingCredentials is not a collection, keys:",
      Object.keys(OAuth._pendingCredentials).slice(0, 5)
    );
  }

  const result = await OAuth.retrieveCredential(credentialToken, credentialSecret);

  console.log("[SSO-Retrieve] OAuth.retrieveCredential returned:", {
    hasResult: !!result,
    hasServiceData: !!result?.serviceData,
    hasOptions: !!result?.options,
    serviceName: result?.serviceName,
    resultKeys: result ? Object.keys(result) : [],
    error: result?.error
  });

  return result;
}

async function adCall(method, url, options) {
  try {
    const fetchOptions = {
      method,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    };

    if (options.params) {
      if (method === "POST") {
        fetchOptions.body = new URLSearchParams(options.params).toString();
      } else {
        url += `?${new URLSearchParams(options.params).toString()}`;
      }
    }

    const response = await fetch(url, fetchOptions);
    const data = await response.json();

    if (!response.ok || data.error) {
      const reason = data.error || `HTTP ${response.status}`;
      const details = JSON.stringify({
        statusCode: response.status,
        url,
        options,
        method
      });
      throw new Meteor.Error("azure-active-directory:invalid HTTP response", `Url=${reason}`, details);
    }

    return data;
  } catch (err) {
    if (err instanceof Meteor.Error) {
      throw err;
    }
    const details = JSON.stringify({
      url,
      options,
      method
    });
    throw new Meteor.Error("azure-active-directory:failed HTTP request", err.message, details);
  }
}

async function getAccessTokensBase(additionalRequestParams, tenantId, rootUrlFromRequest) {
  console.log("[SSO-Config] Looking for service configuration with:", {
    service: "azureAdB2c",
    tenantIdProvided: tenantId
  });

  const config = await ServiceConfiguration.configurations.findOneAsync({
    service: "azureAdB2c"
    // Removed tenantId filter to allow config to be found regardless of tenantId value
  });

  console.log("[SSO-Config] Found configuration:", config ? "Yes" : "No");

  if (config) {
    console.log("[SSO-Config] Config details:", {
      hasClientId: !!config.clientId,
      hasSecret: !!config.secret,
      tokenUrl: config.tokenUrl,
      baseUrl: config.baseUrl,
      configTenantId: config.tenantId
    });
  }

  const url = config.tokenUrl;
  const baseParams = {
    client_id: config.clientId,
    client_secret: OAuth.openSecret(config.secret),
    redirect_uri: OAuth._redirectUri("azureAdB2c", config, null, null, rootUrlFromRequest)
  };
  const requestBody = { ...baseParams, ...additionalRequestParams };

  console.log("[SSO-Token] Token exchange request to:", url);
  console.log("[SSO-Token] Request params:", {
    ...requestBody,
    client_secret: "[REDACTED]",
    code: requestBody.code ? `${requestBody.code.substring(0, 10)}...` : undefined
  });

  const response = await adCall("POST", url, { params: requestBody });

  console.log("[SSO-Token] Token response received:", {
    hasAccessToken: !!response.access_token,
    hasIdToken: !!response.id_token,
    hasRefreshToken: !!response.refresh_token
  });

  return {
    accessToken: response.access_token,
    idToken: response.id_token,
    refreshToken: response.refresh_token,
    expiresIn: response.expires_in,
    expiresOn: response.expires_on,
    scope: response.scope,
    resource: response.resource
  };
}

async function getTokensFromCode({ code, tenantId, rootUrlFromRequest }) {
  return getAccessTokensBase(
    {
      grant_type: "authorization_code",
      code
    },
    tenantId,
    rootUrlFromRequest
  );
}

// eslint-disable-next-line no-undef
OAuth.registerService("azureAdB2c", 2, null, async requestData => {
  console.log("[SSO-OAuth] OAuth service handler START with requestData:", {
    hasTenantId: !!requestData.tenantId,
    hasCode: !!requestData.code,
    hasRootUrl: !!requestData.rootUrlFromRequest,
    hasState: !!requestData.state,
    tenantId: requestData.tenantId,
    codePrefix: requestData.code ? `${requestData.code.substring(0, 10)}...` : undefined,
    statePrefix: requestData.state ? `${requestData.state.substring(0, 20)}...` : undefined,
    requestDataKeys: Object.keys(requestData),
    stateLength: requestData.state?.length,
    rawState: requestData.state
  });

  const { tenantId } = requestData;

  const tokens = await getTokensFromCode(requestData);

  const { idToken } = tokens;
  const idTokenSplit = idToken.split(".");

  const idPart2 = idTokenSplit[1];

  const identity = JSON.parse(Buffer.from(idPart2, "base64").toString("binary"));

  console.log("[SSO-JWT] Decoded identity token:", identity);
  console.log("[SSO-JWT] Identity fields present:", Object.keys(identity));
  console.log("[SSO-JWT] Looking for custom claims:", {
    issuerUserId: identity.issuerUserId,
    issuerOrgId: identity.issuerOrgId,
    issuerEmailVerified: identity.issuerEmailVerified,
    sub: identity.sub,
    email: identity.email,
    name: identity.name
  });

  const serviceData = {
    accessToken: tokens.accessToken,
    idToken,
    expiresAt: new Date() + 1000 * identity.exp,
    identity
  };

  let emailAddress = identity && identity.email;
  if (!emailAddress) {
    emailAddress = identity && identity.emails && identity.emails[0];
  }

  const fields = _.pick(identity, whitelistedFields);

  console.log("[SSO-Identity] Whitelisted fields to extract:", whitelistedFields);
  console.log("[SSO-Identity] Extracted fields from identity:", fields);
  console.log(
    "[SSO-Identity] Fields with values:",
    Object.keys(fields).filter(key => fields[key] !== undefined)
  );

  _.extend(serviceData, fields);

  // only set the token in serviceData if it's there. this ensures
  // that we don't lose old ones (since we only get this on the first
  // log in attempt)
  if (tokens.refreshToken) serviceData.refreshToken = tokens.refreshToken;

  const options = {
    tenantId,
    profile: {
      name: identity.name
    }
  };

  if (emailAddress) {
    options.emails = [
      {
        address: emailAddress,
        verified: true
      }
    ];
  }

  console.log("[SSO-ServiceData] Final serviceData object:", {
    hasAccessToken: !!serviceData.accessToken,
    hasIdToken: !!serviceData.idToken,
    hasIdentity: !!serviceData.identity,
    issuerUserId: serviceData.issuerUserId,
    issuerOrgId: serviceData.issuerOrgId,
    issuerEmailVerified: serviceData.issuerEmailVerified,
    email: serviceData.email,
    name: serviceData.name,
    sub: serviceData.sub,
    serviceDataKeys: Object.keys(serviceData)
  });

  console.log("[SSO-ServiceData] Returning options:", options);

  console.log("[SSO-OAuth] Handler COMPLETE - about to return:", {
    returningServiceData: !!serviceData,
    returningOptions: !!options,
    serviceDataKeys: Object.keys(serviceData),
    optionsKeys: Object.keys(options)
  });

  // IMPORTANT: The OAuth middleware will handle credential storage and secret generation
  // We should NOT manually store credentials here - that's the middleware's job
  // The middleware will:
  // 1. Extract the credential token from the state
  // 2. Generate a credential secret using Random.secret()
  // 3. Store the pending credential
  // 4. Send the response back to the client with the secret

  const result = { serviceData, options };
  console.log("[SSO-OAuth] Returning to OAuth middleware:", {
    hasServiceData: !!result.serviceData,
    hasOptions: !!result.options
  });

  // The OAuth2 handler expects this exact format
  return result;
});
