/* ============================================================================ */
/* Keeping ths around for when we build up a bootstrapper that clones           */
/*  non-sensitive production collections instead of manually bootstrapping them */
/* ============================================================================ */

// import _ from 'underscore';
// import { Meteor } from 'meteor/meteor';
// import MeteorDBProxy from './dbProxy.js';

// const db = {};
// const SETUP_MN = Meteor.settings.USE_MN && Meteor.settings.MN_MONGO;
// const SETUP_MONITOR = Meteor.settings.USE_MONITOR && Meteor.settings.MONITOR_MONGO;
// if (Meteor.isServer && SETUP_MN) {
//   db.mnDB = null;

//   // console.log('Starting to connect to databases...');

//   // Connect to edSpring collections
//   const mnOpts = {
//     collections: [
//       { db: 'assessments', name: 'Assessments' },
//       { db: 'interventions', name: 'Interventions' },
//       { db: 'ruleDefinitions', name: 'RuleDefinitions' },
//       { db: 'rules', name: 'Rules' },
//       // { db: 'ruleConditionDefinitions', name: 'RuleConditionDefinitions' },
//       // { db: 'ruleConditions', name: 'RuleConditions' },
//       // { db: 'ruleOutcomes', name: 'RuleOutcomes' },
//     ],
//     bindables: ['find', 'findOne'],
//   };

//   let mnURL = Meteor.settings.MN_MONGO;
//   let mnOPLOG = Meteor.settings.MN_MONGO_OPLOG;

//   if (Meteor.settings.public.ENVIRONMENT === 'LOCAL') {
//     mnURL = 'mongodb:// localhost:3001';

//   }

//   _.extend(mnOpts, {
//     mongoUrl: mnURL,
//     // oplogUrl: mnOPLOG,
//   });
//   db.mnDB = new MeteorDBProxy(mnOpts);
// }

// if (Meteor.isServer && SETUP_MONITOR) {
//   db.monitorDB = null;
//   const monitorOpts = {
//     collections: [
//       { db: 'math_intervention_protocols', name: 'MathInterventionProtocols' },
//     ],
//     bindables: ['find', 'findOne'],
//   };
//   let monitorURL = Meteor.settings.MONITOR_MONGO;
//   let monitorOPLOG = Meteor.settings.MONITOR_MONGO_OPLOG;
//   if (Meteor.settings.public.ENVIRONMENT === 'LOCAL') {
//     monitorURL = 'mongodb:// localhost:3001';
//   }
//   _.extend(monitorOpts, {
//     mongoUrl: monitorURL,
//     // oplogUrl: monitorOPLOG,
//   });
//   db.monitorDB = new MeteorDBProxy(monitorOpts);
// }
// export { db as default };
