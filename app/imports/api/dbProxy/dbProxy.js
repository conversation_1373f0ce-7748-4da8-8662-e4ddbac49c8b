/* ============================================================================ */
/* Keeping ths around for when we build up a bootstrapper that clones           */
/*  non-sensitive production collections instead of manually bootstrapping them */
/* ============================================================================ */

// export default function MeteorDBProxy(opts) {
// const _self = this;
// if (!(_self instanceof MeteorDBProxy))
// return new MeteorDBProxy(opts);

// //collection name
// //[{ db: "dbCollectionName", name: "MeteorCollectionName" }]

// function _init() {
//   let _opts = {};
//   if (Meteor.isServer) {
//     const _args = {};
//     if (opts.oplogUrl) {
//       _.extend(_args, { oplogUrl: opts.oplogUrl });
//     }
//     //using RemoteCollectionDriver returns a _driver that can be used to hook
//     //into the Meteor.Collection api and then do all the mongo stuff (insert, upsert, etc)
//     //as well as leverage allow/deny rules on the collection itself
//     _opts = { _driver: new MongoInternals.RemoteCollectionDriver(opts.mongoUrl, _args) }
//   }

//   _.each(opts.collections, function(c) {
//     const _name = c.name || c.db;
//     // console.log('_opts: ', _opts);
//     _self[_name] = new Meteor.Collection(c.db, _opts);
//   });
// }

// _init();

// return _self;
// }
