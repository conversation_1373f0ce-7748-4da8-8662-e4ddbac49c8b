import SimpleSchema from "simpl-schema";
import * as yup from "yup";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";
import { AuditedCollection } from "../auditLogs/AuditedCollection";

export const Organizations = new AuditedCollection("Organizations");

Organizations.schema = new SimpleSchema({
  _id: {
    type: String,
    optional: true
  },
  classwideEnabled: {
    type: Boolean,
    optional: true
  },
  name: {
    type: String
  },
  isDistrict: {
    type: Boolean
  },
  details: {
    type: Object,
    optional: true,
    blackbox: true
  },
  schoolYearBoundary: {
    type: Object,
    optional: false
  },
  "schoolYearBoundary.month": { type: Number },
  "schoolYearBoundary.day": { type: Number },
  springMathLite: {
    type: Object,
    blackbox: true,
    optional: true
  },
  isActive: {
    type: Boolean
  },
  created: {
    type: ByDateOn
  },
  lastModified: {
    type: ByDateOn
  },
  isSelfEnrollee: {
    type: Boolean,
    optional: true
  },
  isTestOrg: {
    type: Boolean,
    optional: true
  },
  allowRosterImports: {
    type: Boolean,
    optional: true
  },
  rostering: {
    type: String,
    optional: true
  },
  rosteringSettings: {
    type: Object,
    blackbox: true,
    optional: true
  },
  benchmarkPeriodsGroupId: {
    type: String,
    optional: true
  },
  ssoIssuerOrgId: {
    type: String,
    optional: true
  },
  accessReminderSentOn: {
    type: Date,
    optional: true
  }
});

Organizations.validate = organizations => {
  Organizations.schema.validate(organizations);
};
Organizations.isValid = organizations => Organizations.schema.namedContext("testContext").validate(organizations);

export const districtSettingsSchema = yup.object().shape({
  name: yup
    .string()
    .label("Name")
    .required(),
  city: yup
    .string()
    .label("City")
    .required(),
  state: yup
    .string()
    .label("State")
    .required(),
  isTestOrg: yup.bool().label("Is Test Organization"),
  firstName: yup
    .string()
    .label("First Name")
    .required(),
  lastName: yup
    .string()
    .label("Last Name")
    .required(),
  email: yup
    .string()
    .optional()
    .label("Email")
    .email(),
  phone: yup
    .string()
    .notRequired()
    .label("Phone")
    .transform(value => value || null)
    .min(8),
  benchmarkPeriodsGroupId: yup
    .string()
    .label("Benchmark Periods Group")
    .required()
});

export const schoolBreaksSchema = yup.object().shape({
  winter: yup.object().shape({
    startMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Winter Start Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    startDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Winter Start Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Winter End Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Winter End Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null))
  }),
  spring: yup.object().shape({
    startMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Spring Start Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    startDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Spring Start Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Spring End Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Spring End Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null))
  }),
  other: yup.object().shape({
    startMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Other Start Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    startDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Other Start Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endMonth: yup
      .number()
      .integer()
      .min(1)
      .max(12)
      .label("Other End Month")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null)),
    endDay: yup
      .number()
      .integer()
      .min(1)
      .max(31)
      .label("Other End Day")
      .notRequired()
      .transform(value => (Number.isInteger(value) ? value : null))
  })
});
