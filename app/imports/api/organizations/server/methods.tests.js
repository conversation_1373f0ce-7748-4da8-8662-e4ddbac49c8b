import { assert } from "chai";
import sinon from "sinon";
import { Accounts } from "meteor/accounts-base";
import { insertDistrict, insertSelfEnrollment, removeRosterSyncSchedule } from "./methods";
import { Organizations } from "../organizations";
import { Roles } from "../../roles/roles";
import { Sites } from "../../sites/sites";
import { Settings } from "../../settings/settings";
import stubUtils from "../../../test-helpers/methods/stubUtils";

// Mock getTimestampInfo to provide proper timestamp objects with required 'by' field
jest.mock("../../helpers/getTimestampInfo", () => ({
  getTimestampInfo: jest.fn(() => ({
    by: "testUserId",
    on: new Date().getTime(),
    date: new Date()
  }))
}));

describe("imports/api/organizations/server/methods.js tests", () => {
  describe("insertSelfEnrollment", () => {
    describe("with valid parameters", () => {
      const testClientName = "testClientName";
      const testOrgFields = {
        clientName: testClientName
      };
      const testUserFields = {};
      const testOrgid = "testOrgid";
      let organizationsFindOneStub;
      let organizationsInsertStub;
      let rolesStub;
      let sitesInsertSpy;
      let settingsFindOneStub;
      let accountsCreateUserSpy;
      let accountsSendEnrollmentEmailSpy;
      beforeEach(async () => {
        organizationsInsertStub = sinon.stub(Organizations, "insertAsync");
        organizationsInsertStub.callsFake(() => testOrgid);

        organizationsFindOneStub = sinon.stub(Organizations, "findOneAsync");

        rolesStub = sinon.stub(Roles, "findOneAsync");
        rolesStub.callsFake(() => ({ _id: "testTeacherRole " }));
        sitesInsertSpy = sinon.spy(Sites, "insertAsync");

        settingsFindOneStub = sinon.stub(Settings, "findOneAsync");
        settingsFindOneStub.callsFake(() => ({ defaults: "testDefaults " }));

        accountsCreateUserSpy = sinon.spy(Accounts, "createUserAsync");
        accountsSendEnrollmentEmailSpy = sinon.spy(Accounts, "sendEnrollmentEmail");
      });

      afterEach(async () => {
        stubUtils.safeRestore(Organizations.insertAsync);
        stubUtils.safeRestore(Organizations.findOneAsync);
        stubUtils.safeRestore(Sites.insertAsync);
        stubUtils.safeRestore(Roles.findOneAsync);
        stubUtils.safeRestore(Settings.findOneAsync);
        stubUtils.safeRestore(Accounts.createUserAsync);
        stubUtils.safeRestore(Accounts.sendEnrollmentEmail);
      });
      it("should call Organizations.insert", async () => {
        await insertSelfEnrollment(testOrgFields, testUserFields);
        assert.isTrue(organizationsInsertStub.calledOnce);
      });
      it("should throw an exception if there already exists an organization with the clientName provided", async () => {
        organizationsFindOneStub.callsFake(({ name }) => {
          if (name === `${testClientName}_same`) {
            return {
              _id: `${testOrgid}_exists`,
              name: `${testClientName}_same`
            };
          }
          return undefined;
        });
        await expect(
          insertSelfEnrollment({ ...testOrgFields, clientName: `${testClientName}_same` }, testUserFields)
        ).rejects.toThrow("already exists");
      });
      it("should call Sites.insert with the new orgid that was inserted", async () => {
        await insertSelfEnrollment(testOrgFields, testUserFields);
        assert.isTrue(sitesInsertSpy.firstCall && sitesInsertSpy.firstCall.args[0].orgid === testOrgid);
      });
      it("should call users insert with the orgid set to the organization that was inserted", async () => {
        await insertSelfEnrollment(testOrgFields, testUserFields);
        assert.isTrue(
          !!accountsCreateUserSpy.firstCall && accountsCreateUserSpy.firstCall.args[0].profile.orgid === testOrgid
        );
      });
      it("should try to send an enrollment link to the user that was created", async () => {
        const userId = await insertSelfEnrollment(testOrgFields, testUserFields);
        assert.isTrue(accountsSendEnrollmentEmailSpy.calledWith(userId));
      });
    });
  });

  describe("insertDistrict", () => {
    beforeAll(async () => {
      await Settings.insertAsync({
        defaults: {
          schoolYearBoundary: {
            month: 6,
            day: 31
          }
        }
      });
    });
    afterEach(async () => {
      await Organizations.removeAsync({});
    });
    it("should discard leading and trailing whitespaces in district name", async () => {
      await insertDistrict({
        clientName: "  District Name  ",
        city: "Wonderland",
        street: "",
        state: "Arizona",
        firstName: "FirstName",
        lastName: "LastName",
        email: "",
        phone: "",
        ssoIssuerOrgId: "",
        isTestOrg: false,
        rostering: "rosterImport"
      });
      assert.isTrue((await Organizations.findOneAsync({}))?.name === "District Name");
    });
  });

  describe("removeRosterSyncSchedule", () => {
    beforeAll(async () => {
      await Organizations.insertAsync({
        _id: "thisIsArbitrary",
        name: "One Roster Chichester",
        isDistrict: true,
        isSelfEnrollee: false,
        details: {
          city: "Boston",
          street: "",
          state: "Massachusetts",
          primaryContact: {
            firstName: "Jon",
            lastName: "Doe",
            email: "",
            phone: ""
          }
        },
        schoolYearBoundary: {
          month: 6,
          day: 31
        },
        isActive: true,
        created: {
          by: "super_admin_user_id",
          on: new Date(),
          date: new Date().toISOString()
        },
        lastModified: {
          by: "super_admin_user_id",
          on: new Date(),
          date: new Date().toISOString()
        },
        classwideEnabled: true,
        isTestOrg: false,
        rostering: "rosterOR",
        rosteringSettings: {
          apiUrl: "this",
          clientId: "doesn't",
          clientSecret: "matter",
          schoolYear: "",
          filters: {
            schools: ["a"],
            teachers: ["b"],
            classes: ["c"]
          },
          syncSchedule: {
            startDate: "2022-08-15",
            endDate: "2023-07-31",
            time: "02:00",
            timeZone: "Europe/Warsaw",
            frequency: "weekly",
            schoolYear: 2023
          }
        }
      });
    });
    afterEach(async () => {
      await Organizations.removeAsync({});
    });
    it("should clear syncSchedule for organization", async () => {
      await removeRosterSyncSchedule("thisIsArbitrary");
      assert.isTrue(
        (await Organizations.findOneAsync({ _id: "thisIsArbitrary" })).rosteringSettings.syncSchedule === undefined
      );
    });
  });
});
