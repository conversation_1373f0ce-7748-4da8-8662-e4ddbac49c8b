import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Accounts } from "meteor/accounts-base";

import cronManager from "/imports/api/cron/cronManager";
import { runSchoolScrubber } from "/imports/scripts/schoolScrubber/schoolScrubber";

import { trim } from "lodash";
import { districtSettingsSchema, Organizations, schoolBreaksSchema } from "../organizations";
import { Roles } from "../../roles/roles";
import { insert as insertSite } from "../../sites/methods";
import * as auth from "../../authorization/server/methods";
import * as utils from "../../utilities/utilities";
import { encrypt, getCurrentSchoolYear, getMeteorUser, getMeteorUserId } from "../../utilities/utilities";
import { Settings } from "../../settings/settings";
import { normalizeEmail } from "../../users/server/methods";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import { testExternalRosteringAPIConnection } from "../../utilities/methods";
import { Users } from "../../users/users";
import { updateBenchmarkWindowsForOrganization } from "../../benchmarkWindows/methods";

export async function insert(OrganizationDoc) {
  Organizations.validate(OrganizationDoc);
  return Organizations.insertAsync(OrganizationDoc);
}

export async function insertSelfEnrollment(orgFields, userFields) {
  const orgid = await insertDistrict(orgFields, true);
  const timestampInfo = await getTimestampInfo(getMeteorUserId(), orgid);
  const schoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const newSite = {
    orgid,
    created: timestampInfo,
    lastModified: timestampInfo,
    name: orgFields.clientName,
    schoolYear,
    gradeLevels: [], // todo
    stateInformation: {},
    isVisible: true
  };
  const siteId = await insertSite(newSite);
  const teacherRole = await Roles.findOneAsync({ name: "teacher" });
  // build a user now
  const newUser = {
    email: userFields.email,
    profile: {
      onboarded: false,
      orgid,
      isSelfEnrollee: true,
      siteAccess: [
        {
          role: teacherRole._id,
          siteId,
          schoolYear,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: userFields.firstName,
        last: userFields.lastName,
        middle: userFields.middleName
      },
      created: timestampInfo,
      lastModified: await getTimestampInfo("ObserverOnServer", orgid)
    }
  };
  const userId = await Accounts.createUserAsync(newUser);
  await Accounts.sendEnrollmentEmail(userId);
  return userId;
}

export async function insertDistrict(payload, isSelfEnrollee = false) {
  // check existence of name:
  const existingOrg = await Organizations.findOneAsync({ name: payload.clientName });
  if (existingOrg) {
    throw new Meteor.Error(403, "Organization or district with that name already exists!");
  }
  const timestampInfo = await getTimestampInfo(getMeteorUserId());
  const settings = await Settings.findOneAsync({});
  const schoolYearBoundary = settings.defaults.schoolYearBoundary || {
    month: new Date().getMonth(),
    day: new Date().getDate()
  };
  const newOrgDoc = {
    name: trim(payload.clientName),
    isDistrict: true,
    isSelfEnrollee,
    details: {
      city: payload.city,
      street: payload.street,
      state: payload.state,
      zip: payload.zip,
      primaryContact: {
        firstName: payload.firstName,
        lastName: payload.lastName,
        email: payload.email,
        phone: payload.phone
      }
    },
    schoolYearBoundary,
    isActive: true,
    created: timestampInfo,
    lastModified: timestampInfo,
    classwideEnabled: !isSelfEnrollee, // SpringMathLite should not use classwideEnabled
    isTestOrg: payload.isTestOrg,
    rostering: payload.rostering,
    ...(payload.rostering === "rosterOR"
      ? { rosteringSettings: { userIdentifiers: { teacher: ["administrator", "aide"] } } }
      : {}),
    ssoIssuerOrgId: payload.ssoIssuerOrgId,
    accessReminderSentOn: timestampInfo.date
  };
  if (isSelfEnrollee) {
    newOrgDoc.springMathLite = settings.defaults.springMathLite || {};
    newOrgDoc.springMathLite.numberOfStudentsPurchased = 0;
  }
  Organizations.validate(newOrgDoc);
  return Organizations.insertAsync(newOrgDoc);
}

async function getOrganizationFieldValues(orgid = "", keys) {
  return (
    (await Organizations.findOneAsync(
      {
        _id: orgid
      },
      {
        fields: {
          _id: 0,
          ...keys.reduce((a, key) => {
            // eslint-disable-next-line no-param-reassign
            a[key] = 1;
            return a;
          }, {})
        }
      }
    )) || {}
  );
}

async function areInterventionsWithoutScreeningsAllowed(orgid = "") {
  const org = await Organizations.findOneAsync({
    _id: orgid
  });
  if (!org) {
    return false;
  }
  return org.allowClasswideWithoutScreening && org.canOrgStartClasswidePriorToScreening;
}

async function updateOrganizationFieldValue(orgid = "", keyToSet = "", value) {
  if (!orgid) {
    throw new Meteor.Error(403, "No Organization id provided in updateOrganizationFieldValue");
  }
  const setExpression = {
    [keyToSet]: value
  };
  if (keyToSet === "allowClasswideWithoutScreening" && value === false) {
    setExpression.canOrgStartClasswidePriorToScreening = false;
  }
  const orgIdsWithMFADisabled = Meteor.settings.public.ORG_IDS_WITH_MFA_DISABLED || [];
  if (keyToSet === "useSSOOnly" && value === true && !orgIdsWithMFADisabled.includes(orgid)) {
    setExpression.isMFARequired = true;
  }
  if (keyToSet === "rostering" && value === "rosterOR") {
    const org = await Organizations.findOneAsync(
      {
        _id: orgid
      },
      { fields: { "rosteringSettings.userIdentifiers": 1 } }
    );
    if (!org?.rosteringSettings?.userIdentifiers) {
      setExpression["rosteringSettings.userIdentifiers"] = { teacher: ["administrator", "aide"] };
    }
  }
  setExpression.lastModified = await getTimestampInfo(
    this?.userId || getMeteorUserId() || "",
    orgid,
    "updateOrganizationFieldValue"
  );
  return Organizations.updateAsync(
    {
      _id: orgid
    },
    {
      $set: setExpression
    }
  );
}

async function saveRosteringSettings({
  orgid,
  apiUrl,
  authUrl,
  clientId,
  clientSecret,
  shouldUseScopes = false,
  shouldUseSequentialRequests = false,
  limit = 500,
  schoolYear = ""
}) {
  return await Organizations.updateAndLog(
    {
      type: "rosteringSetting",
      timestampInfo: await getTimestampInfo(getMeteorUserId(), orgid),
      findOptions: {
        fields: {
          rosteringSettings: 1
        }
      },
      orgid
    },
    {
      _id: orgid
    },
    {
      $set: {
        "rosteringSettings.apiUrl": encrypt(apiUrl),
        "rosteringSettings.authUrl": encrypt(authUrl),
        "rosteringSettings.clientId": encrypt(clientId),
        "rosteringSettings.clientSecret": encrypt(clientSecret),
        "rosteringSettings.shouldUseScopes": shouldUseScopes,
        "rosteringSettings.shouldUseSequentialRequests": shouldUseSequentialRequests,
        "rosteringSettings.limit": limit,
        "rosteringSettings.schoolYear": schoolYear
      }
    }
  );
}

async function saveRosterFilters({ orgid, filters }) {
  return await Organizations.updateAndLog(
    {
      type: "rosteringFilter",
      timestampInfo: await getTimestampInfo(getMeteorUserId(), orgid),
      findOptions: {
        fields: {
          "rosteringSettings.filters": 1
        }
      },
      orgid
    },
    {
      _id: orgid
    },
    {
      $set: { "rosteringSettings.filters": filters }
    }
  );
}

async function saveRosterSyncSchedule({ orgid, syncSchedule }) {
  const result = await Organizations.updateAndLog(
    {
      type: "rosteringSchedule",
      timestampInfo: await getTimestampInfo(getMeteorUserId(), orgid),
      findOptions: {
        fields: {
          "rosteringSettings.syncSchedule": 1
        }
      },
      orgid
    },
    {
      _id: orgid
    },
    {
      $set: { "rosteringSettings.syncSchedule": syncSchedule }
    }
  );
  await cronManager.setRosteringJob({ orgid, syncSchedule, shouldListJobs: true });

  return result;
}

export async function removeRosterSyncSchedule(orgid) {
  const result = await Organizations.updateAndLog(
    {
      type: "rosteringSchedule",
      timestampInfo: await getTimestampInfo(getMeteorUserId(), orgid),
      findOptions: {
        fields: {
          "rosteringSettings.syncSchedule": 1
        }
      },
      orgid
    },
    {
      _id: orgid
    },
    {
      $unset: { "rosteringSettings.syncSchedule": "" }
    }
  );
  cronManager.removeRosteringJob(orgid);

  return result;
}

async function getOrganizations(orgid, query) {
  const organizationsQuery = orgid ? { _id: orgid } : query;
  return await Organizations.find(organizationsQuery, {
    fields: {
      benchmarkPeriodsGroupId: 1,
      classwideEnabled: 1,
      created: 1,
      details: 1,
      isActive: 1,
      isDistrict: 1,
      isSelfEnrollee: 1,
      isTestOrg: 1,
      name: 1,
      rostering: 1,
      rosteringSettings: 1,
      schoolYearBoundary: 1,
      schoolBreaks: 1,
      allowMultipleGradeLevels: 1,
      ssoIssuerOrgId: 1,
      useSSOOnly: 1,
      isMFARequired: 1
    }
  }).fetchAsync();
}

Meteor.methods({
  async "Organizations:insertDistrict"(payload) {
    check(
      payload,
      Match.ObjectIncluding({
        city: String,
        clientName: String,
        email: Match.Maybe(String),
        firstName: String,
        lastName: String,
        phone: Match.Maybe(String),
        street: Match.Maybe(String),
        state: String,
        zip: Match.Maybe(String),
        isTestOrg: Boolean,
        rostering: String,
        ssoIssuerOrgId: String
      })
    );
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
        userId: this.userId
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use this method");
    }
    const orgid = await insertDistrict(payload);

    const sourceOrgId = Meteor.settings.public.DATA_COPY_SOURCE_ORG_ID;
    if (sourceOrgId && (await Organizations.findOneAsync({ _id: sourceOrgId }, { fields: { _id: 1 } }))) {
      try {
        await runSchoolScrubber({ targetOrgId: orgid, schoolYear: 2024 });
      } catch (e) {
        throw new Meteor.Error(e.message);
      }
    }
    return orgid;
  },
  async "Organizations:insertSelfEnrollmentDistrict"({
    clientName,
    email,
    firstName,
    lastName,
    middleName,
    city,
    street,
    state,
    zip,
    phone,
    ssoIssuerOrgId
  }) {
    if (this.userId) {
      throw new Meteor.Error(403, "Logged in users cannot use this method.");
    }
    check(clientName, String);
    check(email, String);
    check(firstName, String);
    check(lastName, String);
    check(middleName, Match.Maybe(String));
    check(city, String);
    check(street, String);
    check(state, String);
    check(zip, Match.Maybe(String));
    check(phone, Match.Maybe(String));
    check(ssoIssuerOrgId, Match.Maybe(String));

    const userFields = { firstName, lastName, middleName, email };
    const orgFields = {
      clientName,
      city,
      street,
      state,
      zip,
      firstName,
      lastName,
      email: normalizeEmail(email),
      phone,
      ssoIssuerOrgId
    };

    return insertSelfEnrollment(orgFields, userFields);
  },
  async "Organizations:getOrganizationFieldValues"(orgid, keys) {
    const userId = getMeteorUserId();
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(orgid, String);
    check(keys, Array);

    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      return getOrganizationFieldValues(orgid, keys);
    }
    throw new Meteor.Error(
      "Organizations:getOrganizationFieldValues",
      "User not authorized to use getOrganizationFieldValues"
    );
  },
  async "Organizations:updateOrganizationFieldValue"(orgid, keyToSet, value) {
    check(orgid, String);
    check(keyToSet, String);
    check(value, Match.OneOf(String, Number, Boolean));

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      const orgIdsWithMFADisabled = Meteor.settings.public.ORG_IDS_WITH_MFA_DISABLED || [];
      if (keyToSet === "isMFARequired" && orgIdsWithMFADisabled.includes(orgid)) {
        throw new Meteor.Error(
          "Organizations:updateOrganizationFieldValue",
          "Organization has the MFA feature disabled"
        );
      } else {
        return updateOrganizationFieldValue(orgid, keyToSet, value);
      }
    }
    throw new Meteor.Error(
      "Organizations:updateOrganizationFieldValue",
      "User not authorized to use updateOrganizationFieldValue"
    );
  },
  async "Organizations:getOrganizationById"(orgid) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(orgid, String);

    return Organizations.findOneAsync({ _id: orgid });
  },
  async "Organizations:getAllOrganizations"(orgid) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    // TODO(fmazur) - remove publication that's a duplicate of this
    check(orgid, Match.Maybe(String));
    const user = await getMeteorUser();

    if (!user) {
      return [];
    }

    if (
      await auth.hasAccess(["universalCoach", "superAdmin", "universalDataAdmin", "downloader"], {
        user
      })
    ) {
      const query = {};
      return getOrganizations(orgid, query);
    }

    if (auth.hasRoleInSiteAccess(await auth.getSiteAccess(user), "support")) {
      const query = { _id: { $in: await auth.getSupportUserAccess(user) } };
      return getOrganizations(orgid, query);
    }

    return getOrganizations(user.profile.orgid);
  },
  async "Organizations:areInterventionsWithoutScreeningsAllowed"(orgid) {
    check(orgid, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    return areInterventionsWithoutScreeningsAllowed(orgid);
  },
  async "Organizations:saveRosteringSettings"({
    orgid,
    apiUrl,
    authUrl,
    clientId,
    clientSecret,
    shouldUseScopes = false,
    shouldUseSequentialRequests = false,
    limit = 500,
    schoolYear
  }) {
    check(orgid, String);
    check(apiUrl, String);
    check(authUrl, String);
    check(clientId, String);
    check(clientSecret, String);
    check(shouldUseScopes, Boolean);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveRosteringSettings");
    }
    const testResult = await testExternalRosteringAPIConnection({
      orgid,
      apiUrl,
      authUrl,
      clientId,
      clientSecret,
      shouldUseScopes,
      shouldUseSequentialRequests,
      limit,
      schoolYear
    }).catch(() => {});

    return {
      ...testResult,
      saveResult: await saveRosteringSettings({
        orgid,
        apiUrl,
        authUrl,
        clientId,
        clientSecret,
        shouldUseScopes,
        shouldUseSequentialRequests,
        limit,
        schoolYear
      })
    };
  },
  async "Organizations:saveGradeTranslation"({ orgid, grade, translations }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(orgid, String);
    check(grade, String);
    check(translations, Array);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveGradeTranslation");
    }

    const modifier = {
      [translations.length ? "$set" : "$unset"]: {
        [`rosteringSettings.translations.${grade}`]: translations
      }
    };

    const lastModified = await getTimestampInfo(this?.userId, orgid, "Organizations:saveGradeTranslation");
    if (translations.length) {
      modifier.$set.lastModifed = lastModified;
    } else {
      modifier.$set = { lastModified };
    }

    return Organizations.updateAsync({ _id: orgid }, modifier);
  },
  async "Organizations:saveUserRoleIdentifiers"({ orgid, role, identifiers }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(orgid, String);
    check(role, String);
    check(identifiers, Array);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveUserRoleIdentifiers");
    }

    const modifier = {
      [identifiers.length ? "$set" : "$unset"]: {
        [`rosteringSettings.userIdentifiers.${role}`]: identifiers
      }
    };

    const lastModified = await getTimestampInfo(this?.userId, orgid, "Organizations:saveUserRoleIdentifiers");
    if (identifiers.length) {
      modifier.$set.lastModifed = lastModified;
    } else {
      modifier.$set = { lastModified };
    }

    return Organizations.updateAsync({ _id: orgid }, modifier);
  },
  async "Organizations:saveRosterFilters"({ orgid, filters }) {
    check(orgid, String);
    check(filters, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveRosterFilters");
    }

    return saveRosterFilters({ orgid, filters });
  },
  async "Organizations:saveRosterSyncSchedule"({ orgid, syncSchedule }) {
    check(orgid, String);
    check(syncSchedule, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveRosterSyncSchedule");
    }
    const user = await Users.findOneAsync({ _id: this.userId });
    return saveRosterSyncSchedule({
      orgid,
      syncSchedule: { ...syncSchedule, schoolYear: await getCurrentSchoolYear(user, orgid) }
    });
  },
  async "Organizations:removeRosterSyncSchedule"(orgid) {
    check(orgid, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:removeRosterSyncSchedule");
    }

    return removeRosterSyncSchedule(orgid);
  },
  async "Organizations:saveDistrictSettings"({ orgid, settings, schoolBreaks }) {
    check(orgid, String);
    check(settings, Object);
    check(schoolBreaks, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use Organizations:saveDistrictSettings");
    }

    const timestampInfo = await getTimestampInfo(getMeteorUserId(), orgid, "Organizations:saveDistrictSettings");
    const isSuperAdminOrUniversalDataAdmin = await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
      userId: this.userId
    });

    try {
      districtSettingsSchema.validateSync(settings);
    } catch (e) {
      throw new Meteor.Error(403, `There was an error while validating District Settings: ${e.errors[0]}`);
    }

    try {
      schoolBreaksSchema.validateSync(schoolBreaks);
    } catch (e) {
      throw new Meteor.Error(403, `There was an error while validating School Breaks: ${e.errors[0]}`);
    }

    const {
      name,
      city,
      state,
      firstName,
      lastName,
      email,
      phone,
      benchmarkPeriodsGroupId,
      schoolYearBoundary,
      isTestOrg,
      contractBeginDate,
      contractEndDate,
      postContractDeleteDays,
      emergencyContacts,
      studentInformationSystem,
      allowsDeIdentifiedDataResearch = false
    } = settings;

    const data = {
      name,
      details: {
        city,
        state,
        primaryContact: {
          firstName,
          lastName,
          email,
          phone
        },
        contractBeginDate,
        contractEndDate,
        postContractDeleteDays,
        emergencyContacts,
        studentInformationSystem,
        allowsDeIdentifiedDataResearch
      },
      ...(isSuperAdminOrUniversalDataAdmin
        ? {
            schoolYearBoundary: {
              month: schoolYearBoundary.month - 1,
              day: schoolYearBoundary.day
            },
            benchmarkPeriodsGroupId,
            isTestOrg
          }
        : {})
    };

    const user = await Users.findOneAsync({ _id: this.userId });
    const schoolYear = await getCurrentSchoolYear(user, orgid);

    if (isSuperAdminOrUniversalDataAdmin && data.schoolYearBoundary) {
      await updateBenchmarkWindowsForOrganization({
        orgid,
        benchmarkPeriodsGroupId,
        schoolYear,
        schoolYearBoundary: data.schoolYearBoundary
      });
    }

    return Organizations.updateAndLog(
      {
        type: "districtSettings",
        timestampInfo: await getTimestampInfo(user._id, orgid),
        findOptions: {
          fields: {
            benchmarkPeriodsGroupId: 1,
            details: 1,
            isTestOrg: 1,
            name: 1,
            schoolBreaks: 1
          }
        },
        orgid
      },
      {
        _id: orgid
      },
      {
        $set: { ...data, schoolBreaks, lastModified: timestampInfo }
      }
    );
  }
});
