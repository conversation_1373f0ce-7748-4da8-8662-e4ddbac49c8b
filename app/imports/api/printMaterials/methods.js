import { Meteor } from "meteor/meteor";
import { fetch } from "meteor/fetch";
import { check, Match } from "meteor/check";
import moment from "moment";
import CryptoJS from "crypto-js";
import { Base64 } from "js-base64";
import { Assessments } from "../assessments/assessments";
import { Users } from "../users/users";
import * as assessmentsMethods from "../assessments/methods";
import { StudentGroups } from "../studentGroups/studentGroups";
import { base64PdfString } from "./testPdf";

const getAuthDigest = (key, _sharedSecret, prefix, encryptionType) => {
  let usedEncryptionType = encryptionType || "HMACSHA256_ASMT ";
  if (usedEncryptionType.slice(-1) !== " ") {
    usedEncryptionType += " ";
  }
  const hash = CryptoJS.HmacSHA256(key, _sharedSecret);
  const encoded = Base64.encode(`${prefix}:${hash}`); // eslint-disable-line new-cap
  const digest = usedEncryptionType + encoded;
  return digest;
};

function buildInterventionPacketPayload({
  timestamp,
  digest,
  studentGrade,
  studentName,
  teacherName,
  protocolTarget,
  benchmarkTarget,
  protocolType,
  assessmentMeasure,
  protocolMeasure,
  materialsPart,
  groupName
}) {
  const newPrintPayload = {
    auth_timestamp: timestamp,
    auth_token: digest,
    vendor_system_name: "edSpring",
    student_id: "0000",
    student_name: studentName,
    assessment_grade_level: studentGrade,
    teacher_name: teacherName,
    weekly_goal: protocolTarget,
    protocol_measure: protocolMeasure,
    protocol_type: protocolType,
    assessment_measure: assessmentMeasure,
    protocol_target: protocolTarget,
    benchmark_target: benchmarkTarget,
    group_name: groupName,
    materials_part: materialsPart
  };
  return newPrintPayload;
}

async function buildAssessmentPayload({
  timestamp,
  digest,
  studentGrade,
  studentName,
  teacherName,
  assessmentIds,
  protocolTypeToUse,
  assessmentMeasures,
  benchmarkPeriodId
}) {
  // quick hack =>  assessmentId : assessmentMeasure : masteryTarget*1.2
  const hackedAssessmentMeasures = await Promise.all(
    assessmentMeasures.map(async (a, index) => {
      const target = assessmentsMethods
        .getScoreTargets({
          assessment: await Assessments.findOneAsync({ _id: assessmentIds[index] }),
          grade: studentGrade,
          benchmarkPeriodId,
          assessmentType: "benchmark"
        })[1]
        .toString();
      return `${a}:1000:${target}`;
    })
  );
  const newPrintPayload = {
    auth_timestamp: timestamp,
    auth_token: digest,
    vendor_system_name: "edSpring",
    student_id: "0000",
    student_name: studentName,
    teacher_name: teacherName,
    response_url: Meteor.settings.PRINT_PAYLOAD_RESPONSE_URL,
    assessment_type: protocolTypeToUse === "SCREENING" ? 1 : 2,
    assessment_grade_level: studentGrade,
    child_assessment_ids: hackedAssessmentMeasures.join(","),
    assessment_measure: "1000",
    versions: 1, // TODO,
    includeKey: true
  };
  return newPrintPayload;
}

async function sendPrintRequest(endpoint, params) {
  const url = new URL(endpoint);
  Object.entries(params || {}).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  let response;
  try {
    response = await fetch(url.toString(), {
      method: "GET",
      signal: AbortSignal.timeout(120000)
    });
  } catch (err) {
    throw new Meteor.Error("No response", "Server did not respond");
  }

  if (response.status === 401) {
    const content = await response.text();
    throw new Meteor.Error("Unauthorized", content);
  }

  if (response.status !== 200 && response.status !== 303) {
    throw new Meteor.Error("Server responded with an error", `Server Status Code: ${response.status}`);
  }

  return response.text();
}

const buildPayload = async params => {
  const {
    payloadType,
    timestamp,
    digest,
    studentGrade,
    studentName,
    teacherName,
    protocolTarget,
    benchmarkTarget,
    protocolTypeToUse,
    assessmentMeasures,
    protocolMeasures,
    materialsPart,
    groupName,
    assessmentIds,
    benchmarkPeriodId
  } = params;

  const packetPayload = {
    timestamp,
    digest,
    studentGrade,
    studentName,
    teacherName,
    protocolTarget,
    benchmarkTarget,
    protocolType: protocolTypeToUse,
    assessmentMeasure: assessmentMeasures[0],
    protocolMeasure: protocolMeasures[0],
    materialsPart,
    groupName
  };

  if (
    payloadType === "intervention-packet" ||
    payloadType === "documentation-materials" ||
    payloadType === "practice-materials"
  ) {
    return buildInterventionPacketPayload(packetPayload);
  }

  return buildAssessmentPayload({
    timestamp,
    digest,
    studentGrade,
    studentName,
    teacherName,
    assessmentIds,
    protocolTypeToUse,
    assessmentMeasures,
    benchmarkPeriodId
  });
};

export async function triggerGenerateAndGetLink(
  protocolType,
  assessmentIds,
  assessmentMeasures,
  protocolMeasures,
  studentGrade,
  studentName,
  studentGroupId,
  payloadType,
  materialsPart,
  benchmarkPeriodId,
  groupName
) {
  const protocolTypeToUse = protocolType;
  try {
    const teacherId = (await StudentGroups.findOneAsync({ _id: studentGroupId }, { fields: { ownerIds: 1 } }))
      ?.ownerIds[0];
    const teacher = await Users.findOneAsync({ _id: teacherId });
    let teacherName = "";
    if (teacher) {
      const { name } = teacher.profile;
      teacherName = `${name.first} ${name.last}`;
    }
    // Meteor.settings.CAN_RESET_ALL_DATA
    // Default: Stream PDF Data from Monitor
    const payloadEndpoints = {
      "intervention-packet": Meteor.settings.PRINT_MATERIALS_STREAM_PDF_FROM_MONITOR_INTERVENTION_PACKET,
      "documentation-materials": Meteor.settings.PRINT_MATERIALS_STREAM_PDF_FROM_MONITOR_DOCUMENTATION_MATERIALS,
      "practice-materials": Meteor.settings.PRINT_MATERIALS_STREAM_PDF_FROM_MONITOR_PRACTICE_MATERIALS,
      assessment: Meteor.settings.PRINT_MATERIALS_STREAM_PDF_FROM_MONITOR_ASSESSMENT
    };
    const endpoint = payloadEndpoints[payloadType];
    if (!endpoint) {
      throw new Meteor.Error(`no endpoint defined for payload type: ${payloadType}`);
    }

    const timestamp = moment()
      .utc()
      .format("YYYY-MM-DDTHH:mm:ss\\Z");
    const digestString = `${timestamp}${payloadType === "assessment" ? "1000" : assessmentMeasures[0]}0000`;
    const monitorSharedSecret = Meteor.settings.MONITOR_SHARED_SECRET; // TODO move to db/config
    let protocolTarget = "";
    let benchmarkTarget = "";
    if (protocolTypeToUse !== "SCREENING") {
      const assessmentType = studentName.length > 0 ? "individual" : "classwide";
      protocolTarget = assessmentsMethods
        .getScoreTargets({
          assessment: await Assessments.findOneAsync({ _id: assessmentIds[0] }),
          grade: studentGrade,
          benchmarkPeriodId,
          assessmentType
        })[1]
        .toString();
      // SPRIN-1306 Weekly goal should be mastery target for the assessment.
      // protocolTarget = Math.ceil(1.2 * protocolTarget);
      if (assessmentIds.length >= 2) {
        benchmarkTarget = assessmentsMethods
          .getScoreTargets({
            assessment: await Assessments.findOneAsync({ _id: assessmentIds[1] }),
            grade: studentGrade,
            benchmarkPeriodId,
            assessmentType
          })[1]
          .toString();
        // SPRIN-1306 Weekly goal should be mastery target for the assessment.
        // benchmarkTarget = Math.ceil(1.2 * benchmarkTarget);
      }
    }
    const digest = getAuthDigest(digestString, monitorSharedSecret, "edSpringMonitor", "HMACSHA256_ASMT");
    const newPrintPayload = await buildPayload({
      payloadType,
      timestamp,
      digest,
      studentGrade,
      studentName,
      teacherName,
      protocolTarget,
      benchmarkTarget,
      protocolTypeToUse,
      assessmentMeasures,
      protocolMeasures,
      materialsPart,
      groupName,
      assessmentIds,
      benchmarkPeriodId
    });

    // Attempt the Print
    if (Meteor.settings.USE_TEST_PRINT && process.env.METEOR_ENVIRONMENT === "TEST") {
      return base64PdfString;
    }
    return sendPrintRequest(endpoint, newPrintPayload);
  } catch (e) {
    throw new Meteor.Error("Unable to generate print materials", e.message);
  }
}

Meteor.methods({
  async "printMaterials:triggerGenerateAndGetLink"({
    protocolType,
    assessmentIds,
    assessmentMeasureIds,
    protocolMeasureIds,
    studentGrade,
    studentName,
    studentGroupId,
    payloadType,
    benchmarkPeriodId,
    groupName,
    materialsPart = ""
  }) {
    this.unblock();
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(protocolType, String);
    check(assessmentIds, Array);
    check(assessmentMeasureIds, Array);
    check(protocolMeasureIds, Array);
    check(studentGrade, String);
    check(studentName, String);
    check(studentGroupId, String);
    check(payloadType, String);
    check(benchmarkPeriodId, String);
    check(groupName, Match.Maybe(String));
    check(materialsPart, String);

    return triggerGenerateAndGetLink(
      protocolType,
      assessmentIds,
      assessmentMeasureIds,
      protocolMeasureIds,
      studentGrade,
      studentName,
      studentGroupId,
      payloadType,
      materialsPart,
      benchmarkPeriodId,
      groupName
    );
  }
});
