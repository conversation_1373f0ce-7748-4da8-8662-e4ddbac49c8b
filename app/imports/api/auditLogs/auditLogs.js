import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const AuditLogs = new Mongo.Collection("AuditLogs");

AuditLogs.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String },
  created: { type: ByDateOn },
  type: { type: String },
  outdated: { type: Object },
  updated: { type: Object }
});

AuditLogs.validate = logs => {
  AuditLogs.schema.validate(logs);
};

AuditLogs.isValid = logs => AuditLogs.schema.namedContext("testContext").validate(logs);
