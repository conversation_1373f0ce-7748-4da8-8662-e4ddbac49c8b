import { replacePropertiesInObject, simpleReplacer } from "./utilities";

describe("obscureNestedPropertiesInObject", () => {
  it("should not alter original object if keys to be replaced don't exist", () => {
    const obj = {
      property1: "test",
      secret: "secret",
      nestedObjLvl1: {
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "secret"
        }
      }
    };
    replacePropertiesInObject({
      object: obj,
      propertyNamesToReplace: ["doesNotExist"],
      replacerFunc: simpleReplacer
    });
    expect(obj).toEqual({
      property1: "test",
      secret: "secret",
      nestedObjLvl1: {
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "secret"
        }
      }
    });
  });

  it("should replace properties in original object if the specified key exists", () => {
    const obj = {
      property1: "test",
      secret: "secret"
    };
    replacePropertiesInObject({ object: obj, propertyNamesToReplace: ["secret"], replacerFunc: simpleReplacer });
    expect(obj).toEqual({
      property1: "test",
      secret: "***"
    });
  });

  it("should replace properties in original object if the specified keys exist and some of them are on different depths", () => {
    const obj = {
      property1: "test",
      secret: "secret",
      nestedObjLvl1: {
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "secret"
        }
      }
    };
    replacePropertiesInObject({ object: obj, propertyNamesToReplace: ["secret"], replacerFunc: simpleReplacer });
    expect(obj).toEqual({
      property1: "test",
      secret: "***",
      nestedObjLvl1: {
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "***"
        }
      }
    });
  });

  it("should replace properties in original object when there are multiple keys to be replaced provided", () => {
    const obj = {
      property1: "test",
      secret: "secret",
      secret2: "secret",
      nestedObjLvl1: {
        secret3: "secret",
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "secret",
          secret2: "secret"
        }
      }
    };
    replacePropertiesInObject({
      object: obj,
      propertyNamesToReplace: ["secret", "secret2", "secret3"],
      replacerFunc: simpleReplacer
    });
    expect(obj).toEqual({
      property1: "test",
      secret: "***",
      secret2: "***",
      nestedObjLvl1: {
        secret3: "***",
        nestedObjLvl2: {
          property1: "test",
          property2: 21,
          secret: "***",
          secret2: "***"
        }
      }
    });
  });

  it("should replace properties of objects within array of objects inside the root object", () => {
    const obj = {
      property1: "test",
      secret: "secret",
      secret2: "secret",
      nestedObjLvl1: [
        {
          secret3: "secret",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "secret",
            secret2: "secret"
          }
        },
        {
          secret3: "secret",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "secret",
            secret2: "secret"
          }
        },
        {
          secret3: "secret",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "secret",
            secret2: "secret"
          }
        }
      ]
    };
    replacePropertiesInObject({
      object: obj,
      propertyNamesToReplace: ["secret", "secret2", "secret3"],
      replacerFunc: simpleReplacer
    });
    expect(obj).toEqual({
      property1: "test",
      secret: "***",
      secret2: "***",
      nestedObjLvl1: [
        {
          secret3: "***",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "***",
            secret2: "***"
          }
        },
        {
          secret3: "***",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "***",
            secret2: "***"
          }
        },
        {
          secret3: "***",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "***",
            secret2: "***"
          }
        }
      ]
    });
  });
  it("should replace properties of deeply nested objects within array of objects inside the root object", () => {
    const obj = {
      property1: "test",
      secret: "secret",
      secret2: "secret",
      nestedObjLvl1: [
        {
          secret3: "secret",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "secret",
            secret2: "secret",
            arr: [
              {
                nestedObjLvl3: {
                  secret: "secret",
                  secretArr: [null]
                }
              }
            ]
          }
        }
      ],
      secret3: "secret"
    };
    replacePropertiesInObject({
      object: obj,
      propertyNamesToReplace: ["secret", "secret2", "secret3", "secretArr"],
      replacerFunc: simpleReplacer
    });
    expect(obj).toEqual({
      property1: "test",
      secret: "***",
      secret2: "***",
      nestedObjLvl1: [
        {
          secret3: "***",
          nestedObjLvl2: {
            property1: "test",
            property2: 21,
            secret: "***",
            secret2: "***",
            arr: [
              {
                nestedObjLvl3: {
                  secret: "***",
                  secretArr: "***"
                }
              }
            ]
          }
        }
      ],
      secret3: "***"
    });
  });
});
