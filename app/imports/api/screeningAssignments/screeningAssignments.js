import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";

export const ScreeningAssignments = new Mongo.Collection("ScreeningAssignments");

ScreeningAssignments.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  grade: { type: String },
  benchmarkPeriodId: { type: String },
  assessmentIds: [String],
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn }
});

ScreeningAssignments.validate = assessmentScreening => {
  ScreeningAssignments.schema.validate(assessmentScreening);
};
ScreeningAssignments.isValid = assessmentScreening =>
  ScreeningAssignments.schema.namedContext("assessmentScreeningContext").validate(assessmentScreening);
