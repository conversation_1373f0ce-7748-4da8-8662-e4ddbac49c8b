import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { ScreeningAssignments } from "./screeningAssignments.js";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getMeteorUserId } from "../utilities/utilities";

export async function insert(screeningAssignmentDoc) {
  check(screeningAssignmentDoc, Object);
  ScreeningAssignments.validate(screeningAssignmentDoc);
  return ScreeningAssignments.insertAsync(screeningAssignmentDoc);
}

export async function addAssessmentId(screeningAssignmentId, assessmentId) {
  check(screeningAssignmentId, String);
  check(assessmentId, String);
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "addAssessmentId");
  return ScreeningAssignments.updateAsync(screeningAssignmentId, {
    $push: { assessmentIds: assessmentId },
    $set: { lastModified }
  });
}

export async function removeAssessmentId(screeningAssignmentId, assessmentId) {
  check(screeningAssignmentId, String);
  check(assessmentId, String);
  const screeningAssignment = await ScreeningAssignments.findOneAsync(screeningAssignmentId);
  const { assessmentIds } = screeningAssignment;
  const index = assessmentIds.indexOf(assessmentId);
  const removed = assessmentIds.splice(index, 1);
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "removeAssessmentId");
  if (removed[0] === assessmentId) {
    return ScreeningAssignments.updateAsync(screeningAssignmentId, {
      $set: { assessmentIds, lastModified }
    });
  }
  return false;
}

Meteor.methods({
  async "ScreeningAssignments:insert"(screeningAssignmentDoc) {
    check(screeningAssignmentDoc, Object);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return insert(screeningAssignmentDoc);
  },
  async "ScreeningAssignments:addAssessmentId"(screeningAssignmentId, assessmentId) {
    check(screeningAssignmentId, String);
    check(assessmentId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return addAssessmentId(screeningAssignmentId, assessmentId);
  },
  async "ScreeningAssignments:removeAssessmentId"(screeningAssignmentId, assessmentId) {
    check(screeningAssignmentId, String);
    check(assessmentId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return removeAssessmentId(screeningAssignmentId, assessmentId);
  }
});

// TODO: add helper methods here
// export default class ScreeningAssignmentHelpers {
//   static getCurrentPeriod() {
//   }
// }
