import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { ScreeningAssignments } from "../screeningAssignments";
import { isUserLoggedOut } from "../../utilities/utilities";

const fields = { grade: 1, benchmarkPeriodId: 1, assessmentIds: 1 };
Meteor.publish("ScreeningAssignments", function screeningAssignmentsPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return ScreeningAssignments.find({}, { fields });
});

Meteor.publish("ScreeningAssignmentsByGrade", function(grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(grade, String);

  return ScreeningAssignments.find({ grade }, { fields });
});
