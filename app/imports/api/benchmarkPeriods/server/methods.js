import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";

import { BenchmarkPeriods, createCustomBenchmarkPeriodsSchema } from "../benchmarkPeriods";

import * as auth from "../../authorization/server/methods";
import { getPreviousDayObject } from "../../utilities/utilities";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";

Meteor.methods({
  async "BenchmarkPeriods:addCustom"(data) {
    check(data, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
        userId: this.userId
      }))
    ) {
      throw new Meteor.Error(403, "User not authorized to use BenchmarkPeriods:addCustom");
    }

    const availableBenchmarkPeriodGroupIds = Object.keys(
      (await BenchmarkPeriods.findOneAsync({ name: "Fall" })).startDate
    );

    const restrictedNames = [...availableBenchmarkPeriodGroupIds];
    const customBenchmarkPeriodsSchema = createCustomBenchmarkPeriodsSchema(restrictedNames);

    try {
      customBenchmarkPeriodsSchema.validateSync(data);
    } catch (e) {
      throw new Meteor.Error(403, `There was an error while validating custom Benchmark Periods: ${e.errors[0]}`);
    }

    const {
      name,
      fallStartMonth,
      fallStartDay,
      winterStartMonth,
      winterStartDay,
      springStartMonth,
      springStartDay
    } = data;

    const { month: fallEndMonth, day: fallEndDay } = getPreviousDayObject(winterStartMonth, winterStartDay);
    const { month: winterEndMonth, day: winterEndDay } = getPreviousDayObject(springStartMonth, springStartDay);
    const { month: springEndMonth, day: springEndDay } = getPreviousDayObject(fallStartMonth, fallStartDay);

    const lastModified = await getTimestampInfo(this?.userId, null, "BenchmarkPeriods:addCustom");
    await BenchmarkPeriods.updateAsync(
      { name: "Fall" },
      {
        $set: {
          [`startDate.${name}`]: { month: fallStartMonth, day: fallStartDay },
          [`endDate.${name}`]: { month: fallEndMonth, day: fallEndDay },
          lastModified
        }
      }
    );
    await BenchmarkPeriods.updateAsync(
      { name: "Winter" },
      {
        $set: {
          [`startDate.${name}`]: { month: winterStartMonth, day: winterStartDay },
          [`endDate.${name}`]: { month: winterEndMonth, day: winterEndDay },
          lastModified
        }
      }
    );
    await BenchmarkPeriods.updateAsync(
      { name: "Spring" },
      {
        $set: {
          [`startDate.${name}`]: { month: springStartMonth, day: springStartDay },
          [`endDate.${name}`]: { month: springEndMonth, day: springEndDay },
          lastModified
        }
      }
    );
    await BenchmarkPeriods.updateAsync(
      { name: "All" },
      {
        $set: {
          [`startDate.${name}`]: { month: springEndMonth, day: springEndDay },
          [`endDate.${name}`]: { month: springEndMonth, day: springEndDay },
          lastModified
        }
      }
    );

    return true;
  }
});
