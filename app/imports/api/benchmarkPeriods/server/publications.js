import { Meteor } from "meteor/meteor";
import { BenchmarkPeriods } from "../benchmarkPeriods";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("BenchmarkPeriods", function bmPeriodsPub() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  return BenchmarkPeriods.find(
    { name: { $exists: true } },
    {
      sort: { sortOrder: 1 },
      fields: {
        name: 1,
        label: 1,
        sortOrder: 1,
        startDate: 1,
        endDate: 1
      }
    }
  );
});
