import sinon from "sinon";
import { assert, expect } from "chai";
import uuid from "uuid";
import jwt from "jwt-simple";

import stubUtils from "../../../../test-helpers/methods/stubUtils";
import { createJWTForCurrentUser } from "./methods";

describe("imports/api/authentication/zendesk/server/methods.js tests", () => {
  describe("createJWTForCurrentUser", () => {
    const testUserId = "testUserId";
    const testEmail = "<EMAIL>";
    it("should call the v4 function of uuid", () => {
      stubUtils.safeRestore(uuid.v4);
      const uuidv4Spy = sinon.spy(uuid, "v4");
      createJWTForCurrentUser(testUserId, testEmail);
      assert.isTrue(uuidv4Spy.calledOnce);
      stubUtils.safeRestore(uuid.v4);
    });
    it("should encode a jwt", () => {
      stubUtils.safeRestore(jwt.encode);
      const jwtEncodeSpy = sinon.spy(jwt, "encode");
      createJWTForCurrentUser(testUserId, testEmail);
      assert.isTrue(jwtEncodeSpy.calledOnce);
    });
    it("should throw an exception if there is no logged in user in the context", () => {
      expect(() => {
        createJWTForCurrentUser();
      }).to.throw(/no logged in user/i);
    });
  });
});
