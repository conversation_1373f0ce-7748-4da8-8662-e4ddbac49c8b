import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import uuid from "uuid";
import jwt from "jwt-simple";
import { Users } from "../../../users/users";
import { getMeteorUserId } from "../../../utilities/utilities";

const sharedKey = Meteor.settings.ZENDESK_SHARED_SECRET || "fakeKey";
const springmathSubdomain = Meteor.settings.ZENDESK_SPRINGMATH_SUBDOMAIN || "fakeDomain";
const zendeskEnabled = Meteor.settings.public.ZENDESK_SUPPORT_PORTAL_LINK_ENABLED || false;
const zendeskWidgetSecretKey = Meteor.settings.ZDWidgetSharedSecret || "fakeSecret";

async function createJWTForZendeskWidget() {
  const userId = getMeteorUserId();
  if (!userId) {
    throw new Error("no logged in user found");
  }
  const user = await Users.findOneAsync({ _id: userId }, { fields: { history: 0 } });
  const timestamp = Math.floor(Date.now() / 1000);
  const payload = {
    name: `${user.profile.name.first} ${user.profile.name.last}`,
    email: user.emails[0].address,
    external_id: userId,
    iat: timestamp,
    exp: timestamp + 4 * 60
  };

  return jwt.encode(payload, zendeskWidgetSecretKey);
}

export function createJWTForCurrentUser(userId, email, userName) {
  if (!userId) {
    throw new Error("no logged in user found");
  }
  const payload = {
    iat: new Date().getTime() / 1000,
    jti: uuid.v4(),
    name: userName,
    email
  };
  return jwt.encode(payload, sharedKey);
}

function createRedirectFromTokenAndSubdomain(token, subdomain, returnTo = `https://${subdomain}.zendesk.com`) {
  return `https://${subdomain}.zendesk.com/access/jwt?jwt=${token}&return_to=${encodeURIComponent(returnTo)}`;
}

function createZendeskRedirect({ userName, userId, email, subdomain, returnToUrl }) {
  const token = createJWTForCurrentUser(userId, email, userName);
  return createRedirectFromTokenAndSubdomain(token, subdomain, returnToUrl);
}

Meteor.methods({
  "zendesk:getAuthenticatedRedirectUrl": async function getAuthenticatedRedirectUrl(returnToUrl) {
    check(returnToUrl, Match.Maybe(String));
    if (!(this.userId && zendeskEnabled)) {
      return null;
    }
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return createZendeskRedirect({
        userName: `${user.profile.name.first} ${user.profile.name.last}`,
        userId: user._id,
        email: user.emails[0].address,
        subdomain: springmathSubdomain,
        returnToUrl
      });
    }
    return null;
  },
  "zendesk:getCurrentUserJWTToken": async function getCurrentUserJWTToken() {
    if (!this.userId) return null;
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      return createJWTForCurrentUser(
        user._id,
        user.emails[0].address,
        `${user.profile.name.first} ${user.profile.name.last}`
      );
    }
    return null;
  },
  "zendesk:getJWTTokenForWidget": async function getJWTTokenForWidget() {
    if (!this.userId) return null;
    return createJWTForZendeskWidget();
  }
});

export default { createJWTForCurrentUser };
