// sets up interval checks for checking user inactivity

import { Meteor } from "meteor/meteor";
import { getMeteorUserId } from "../../utilities/utilities";

const postponeKillInterval = Meteor.settings?.public?.INACTIVITY_KILL_POSTPONE_INTERVAL || 3 * 60 * 1000;

const postponeKillEventsConfig = Meteor.settings?.public?.INACTIVITY_KILL_POSTPONE_EVENTS || "click keydown";

let activityDetected = false;

Meteor.startup(() => {
  Meteor.setInterval(() => {
    if (getMeteorUserId() && activityDetected) {
      Meteor.call("inactivity:imStillBreathing");
      activityDetected = false;
    }
  }, postponeKillInterval);

  // set things that could detect activity from user
  const postponeKillEvents = postponeKillEventsConfig.split(" ");
  postponeKillEvents.forEach(ev => {
    document.addEventListener(ev, () => {
      activityDetected = true;
    });
  });
});
