import { Meteor } from "meteor/meteor";

const killInterval = Meteor?.settings?.public?.INACTIVITY_KILL_INTERVAL || 60 * 1000;
const inactivityTimeout = Meteor?.settings?.public?.INACTIVITY_TIMEOUT || 8 * 60 * 60 * 1000; // 8 hours
const forceLogout = Meteor?.settings?.public?.INACTIVITY_FORCE_LOGOUT;
const warningTime = Number(inactivityTimeout) * 0.9;

Meteor.methods({
  "inactivity:imStillBreathing": async function imStillBreathing() {
    if (!this.userId) return;
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      await Meteor.users.updateAsync(user._id, {
        $set: { activityStamp: new Date() },
        $unset: { warn: 1 }
      });
    } else {
      throw new Meteor.Error(`inactivity:imStillBreathing - no user found for userId: ${this.userId}`);
    }
  },
  "inactivity:defibNow": async function defibNow() {
    if (!this.userId) return;
    const user = await Meteor.users.findOneAsync(this.userId);
    if (user) {
      await Meteor.users.updateAsync(user._id, {
        $set: { activityStamp: new Date() },
        $unset: { warn: 1 }
      });
    } else {
      throw new Meteor.Error(`inactivity:defibNow - no user found for userId: ${this.userId}`);
    }
  }
});

if (forceLogout) {
  // create a function that runs on an interval that kills old tokens
  Meteor.setInterval(async () => {
    const now = new Date();
    const killThreshold = new Date(now - inactivityTimeout);
    const warnThreshold = new Date(now - warningTime);

    await Meteor.users.updateAsync(
      {
        "services.resume.loginTokens.0": { $exists: true },
        activityStamp: { $lt: warnThreshold },
        warn: { $ne: true }
      },
      {
        $set: { warn: true }
      },
      {
        multi: true
      }
    );
    await Meteor.users.updateAsync(
      {
        activityStamp: { $lt: killThreshold },
        warn: true
      },
      {
        $set: { "services.resume.loginTokens": [] },
        $unset: { warn: 1 }
      },
      {
        multi: true
      }
    );
  }, killInterval);
}
