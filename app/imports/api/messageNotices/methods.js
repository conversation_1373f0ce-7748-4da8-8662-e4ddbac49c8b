import { MessageNotices } from "./messageNotices";

export async function addMessageNotice({ noticeLocation, component }) {
  // new minimongo version does not allow react component type key ($$typeof) inside a document...
  const { $$typeof: reactTypeOfKeyValue, _owner, ...restOfComponent } = component;
  await MessageNotices.removeAsync({ noticeLocation });
  await MessageNotices.insertAsync({
    noticeLocation,
    restOfComponent,
    reactTypeOfKeyValue
  });
}

export async function getMessageNoticeByLocation(noticeLocation) {
  return MessageNotices.findOneAsync({ noticeLocation });
}

export async function clearLocationMessages(noticeLocation) {
  await MessageNotices.removeAsync({ noticeLocation });
}
