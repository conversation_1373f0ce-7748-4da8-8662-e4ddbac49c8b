import moment from "moment";
import { Meteor } from "meteor/meteor";

import { Organizations } from "/imports/api/organizations/organizations";
import {
  FORCE_CHANGE_PASSWORD_INTERVAL_IN_DAYS,
  FORCE_CHANGE_PASSWORD_INTERVAL_WITH_MFA_IN_DAYS
} from "/imports/api/constants";

function getSiteAccessObject(futureUser, siteId, schoolYear) {
  return {
    role: futureUser.role,
    siteId,
    schoolYear,
    isActive: futureUser.isActive,
    isDefault: true
  };
}

export function getUserSiteAccess(futureUser) {
  return futureUser.siteIds.map(siteId => getSiteAccessObject(futureUser, siteId, futureUser.schoolYear));
}

export function isMissingSiteAccess(siteAccess, siteId, currentSchoolYear) {
  return !siteAccess.find(sa => sa.siteId === siteId && sa.schoolYear === currentSchoolYear);
}

export function getUpdatedCoachProfile(futureUser, currentProfile, currentSchoolYear, lastModified) {
  const updatedProfile = {};
  // update missing site access and add localId for admins
  const missingSiteAccess = [];
  futureUser.siteIds.forEach(siteId => {
    if (isMissingSiteAccess(currentProfile.siteAccess, siteId, currentSchoolYear)) {
      missingSiteAccess.push(getSiteAccessObject(futureUser, siteId, currentSchoolYear));
    }
  });
  updatedProfile.localId = futureUser.localId;
  if (missingSiteAccess.length) {
    updatedProfile.siteAccess = [...missingSiteAccess, ...currentProfile.siteAccess];
    updatedProfile.lastModified = lastModified;
  }
  return { ...currentProfile, ...updatedProfile };
}

export function getUpdatedTeacherProfile(futureUser, existingUser, currentSchoolYear, lastModified) {
  const updatedProfile = {};
  const currentYearSiteAccess = getUserSiteAccess(futureUser);
  const previousYearsSiteAccess = existingUser.profile.siteAccess.filter(
    access => access.schoolYear !== currentSchoolYear
  );
  updatedProfile.siteAccess = [...currentYearSiteAccess, ...previousYearsSiteAccess];
  updatedProfile.name = futureUser.name;
  updatedProfile.lastModified = lastModified;
  return { ...existingUser.profile, ...updatedProfile };
}

export async function shouldForceUserToChangePassword(user) {
  const organization = await Organizations.findOneAsync({ _id: user.profile.orgid }, { fields: { isMFARequired: 1 } });
  const isMFARequired = organization?.isMFARequired || false;
  const forceChangePasswordIntervalWithMFAInDays =
    Meteor.settings.public.FORCE_CHANGE_PASSWORD_INTERVAL_WITH_MFA_IN_DAYS ||
    FORCE_CHANGE_PASSWORD_INTERVAL_WITH_MFA_IN_DAYS;
  const forceChangePasswordIntervalInDays =
    Meteor.settings.public.FORCE_CHANGE_PASSWORD_INTERVAL_IN_DAYS || FORCE_CHANGE_PASSWORD_INTERVAL_IN_DAYS;
  const intervalInDays = isMFARequired ? forceChangePasswordIntervalWithMFAInDays : forceChangePasswordIntervalInDays;

  return moment(user.profile.lastPasswordChange) < moment().subtract(intervalInDays, "days");
}

export function isUserConfiguringMFA(user) {
  return !!(user?.services?.twoFactorAuthentication?.secret && !user.services.twoFactorAuthentication.type);
}
