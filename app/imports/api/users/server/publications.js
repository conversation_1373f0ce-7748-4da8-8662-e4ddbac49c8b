import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { getMeteorUser, isUserLoggedOut } from "../../utilities/utilities";
import * as auth from "../../authorization/server/methods";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";

Meteor.publish("Users", async function usersPublication({ orgid, siteId }) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, Match.Maybe(String));
  check(siteId, Match.Maybe(String));

  const options = {
    fields: { emails: 1, profile: 1, warn: 1 }
  };

  const user = await getMeteorUser();

  let query = {};
  const organizationUsersQuery = { "profile.orgid": orgid };
  if (
    await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      user,
      orgid
    })
  ) {
    if (orgid) {
      query = organizationUsersQuery;
    }
    return Meteor.users.find(query, options);
  }

  const siteUsersQuery = { "profile.siteAccess.siteId": siteId };
  if (
    await auth.hasAccess(["support", "universalCoach"], {
      user,
      shouldCheckOrgAccessForSupport: false
    })
  ) {
    if (siteId) {
      query = siteUsersQuery;
    } else if (orgid) {
      query = organizationUsersQuery;
    }
    return Meteor.users.find(query, options);
  }

  if (siteId && (await auth.hasAccess(["admin"], { user, siteId }))) {
    return Meteor.users.find(siteUsersQuery, options);
  }

  if (await auth.hasAccess(["teacher"], { user })) {
    return Meteor.users.find({ _id: this.userId }, options);
  }

  return this.ready();
});

Meteor.publish("Users:DataAdminsInOrganization", async function dataAdminsInOrganizationPublication(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);

  if (
    await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    })
  ) {
    return Meteor.users.find(
      { "profile.orgid": orgid, "profile.siteAccess.role": ROLE_IDS.dataAdmin },
      {
        fields: {
          "emails.address": 1,
          "profile.orgid": 1,
          "profile.siteAccess": 1,
          "profile.name": 1
        }
      }
    );
  }
  return this.ready();
});

Meteor.publish("Users:DataAdminsInOrganizationFromUser", async function dataAdminsInOrganizationFromUserPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  const user = await getMeteorUser();

  if (
    await auth.hasAccess(["teacher"], {
      userId: this.userId,
      orgid: user?.profile?.orgid
    })
  ) {
    return Meteor.users.find(
      { "profile.orgid": user?.profile?.orgid, "profile.siteAccess.role": ROLE_IDS.dataAdmin },
      {
        fields: {
          "emails.address": 1,
          "profile.siteAccess.role": 1,
          "profile.name": 1
        }
      }
    );
  }
  return this.ready();
});

Meteor.publish("Users:DataAdminsInAllOrganizations", async function dataAdminsInAllOrganizationsPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (
    await auth.hasAccess(["support", "universalCoach", "superAdmin", "universalDataAdmin"], {
      userId: this.userId,
      shouldCheckOrgAccessForSupport: false
    })
  ) {
    return Meteor.users.find(
      { "profile.siteAccess.role": "arbitraryIddataAdmin" },
      {
        fields: {
          "profile.orgid": 1,
          "profile.name": 1
        }
      }
    );
  }
  return this.ready();
});

Meteor.publish("Users:InAllOrganizations", async function usersInAllOrganizationsPublication(role) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(role, Match.Maybe(String));

  if (!role) {
    return this.ready();
  }
  if (await auth.hasAccess(["superAdmin"], { userId: this.userId })) {
    return Meteor.users.find(
      {
        "profile.siteAccess.role": `arbitraryId${role}`
      },
      {
        fields: {
          emails: 1,
          "profile.name": 1,
          "profile.siteAccess.role": 1,
          "profile.organizationAccess": 1
        }
      }
    );
  }
  return this.ready();
});

Meteor.publish("Users:InOrganization", async function usersInOrganizationPublication({ orgid }) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, Match.Maybe(String));
  if (
    await auth.hasAccess(["support", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    })
  ) {
    return Meteor.users.find(
      { "profile.orgid": orgid },
      {
        fields: {
          _id: 1,
          emails: 1,
          "profile.name": 1,
          "profile.orgid": 1,
          "profile.siteAccess": 1
        }
      }
    );
  }
  return this.ready();
});

Meteor.publish("userData", function() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  return Meteor.users.find(
    { _id: this.userId },
    {
      fields: { username: 1, emails: 1, profile: 1, warn: 1, "services.twoFactorAuthentication": 1 }
    }
  );
});

Meteor.publish("UserNames", async function userNamesPublication(userIds = []) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(userIds, [String]);
  if (
    await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId
    })
  ) {
    return Meteor.users.find(
      { _id: { $in: [this.userId, ...userIds] } },
      {
        fields: {
          _id: 1,
          "profile.name": 1
        }
      }
    );
  }
  return this.ready();
});
