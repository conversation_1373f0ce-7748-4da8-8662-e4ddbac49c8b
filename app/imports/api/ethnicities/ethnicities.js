import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";

export const Ethnicities = new Mongo.Collection("Ethnicities");

Ethnicities.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  label: { type: String },
  name: { type: String }
});

Ethnicities.validate = ethnicity => {
  Ethnicities.schema.validate(ethnicity);
};
Ethnicities.isValid = ethnicity => {
  return Ethnicities.schema.namedContext("checkEthnicity").validate(ethnicity);
};
