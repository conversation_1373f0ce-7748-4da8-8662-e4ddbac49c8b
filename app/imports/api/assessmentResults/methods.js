import { extend, find, get, includes, inRange, intersection, isEmpty, keyBy, uniq } from "lodash";
import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Random } from "meteor/random";
import { AssessmentResults } from "./assessmentResults";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Students } from "../students/students";
import { Assessments } from "../assessments/assessments";
import { Organizations } from "../organizations/organizations";
import { Rules } from "../rules/rules";
import { Users } from "../users/users";
import * as rulesMethods from "../rules/server/methods";
import { processIndividualRule } from "../utilities/server/individualRuleProcessing";
import {
  getCurrentSchoolYear,
  getHistoryFieldName,
  getMedianNumber,
  getMeteorUser,
  getMeteorUserId,
  ninjalog
} from "../utilities/utilities";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import * as auth from "../authorization/server/methods";
import { getScoreTargets } from "../assessments/methods";
import {
  assignToStudentsOrGroup,
  canForceGroupToClasswideIntervention,
  getIdsOfHSStudentsWithCompletedAllInterventions,
  getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening,
  getRecommendationsForIndividualIntervention,
  insertAssessmentsAndScores
} from "./helpers";
import BenchmarkPeriodHelpers from "../benchmarkPeriods/methods";
import { Sites } from "../sites/sites";
import { getCurrentDate } from "../helpers/getCurrentDate";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getEnrolledStudentIds, getIndividualRuleOutcome, getPercent } from "./utilities";
import { getListOfStudentIdsFromStudentGroups } from "../studentGroups/utilities";
import { getIndividualInterventionRecommendationList } from "./getIndividualInterventionRecommendationList";
import { updateIndividualInterventionRecommendationFromManageScoreChanges } from "../studentGroups/methods";

export function checkForAdditionsToIndividualInterventionQueue(
  previousIndividualInterventionQueue = [],
  individualInterventionQueueUpdate = []
) {
  let changesWereMade = false;
  individualInterventionQueueUpdate.forEach(studentId => {
    if (!previousIndividualInterventionQueue.includes(studentId)) {
      changesWereMade = true;
    }
  });
  return changesWereMade;
}

export function getClasswideResultsMessage(
  currentClasswideSkill,
  ruleResults,
  assessmentResult,
  previousIndividualInterventionQueue,
  individualInterventionQueueUpdate
) {
  // Note, the currentClasswideSkill is BEFORE ruleProcessing is finalized...the previous currentSkill
  // See message codes utilities.js
  let mostRecentClasswideMedianScore;
  if (
    currentClasswideSkill &&
    currentClasswideSkill.benchmarkPeriodId === assessmentResult.benchmarkPeriodId &&
    assessmentResult.type === "classwide"
  ) {
    mostRecentClasswideMedianScore = assessmentResult.measures.find(
      assessmentResultMeas => assessmentResultMeas.assessmentId === currentClasswideSkill.assessmentId
    ).medianScore;
  }
  let messageCode = "100"; // default is an empty string
  // newly assigned classwide interventions
  if (!currentClasswideSkill && ruleResults.nextSkill && ruleResults.nextSkill.assessmentId) {
    messageCode = "1";
    // completed screening in a new period but continuing classwide
  } else if (
    currentClasswideSkill &&
    currentClasswideSkill.benchmarkPeriodId !== assessmentResult.benchmarkPeriodId &&
    ruleResults.nextSkill
  ) {
    messageCode = "7";
    // done with classwide
  } else if (!ruleResults.nextSkill) {
    messageCode = "5";
    // new classwide assessment assigned for the first time
  } else if (currentClasswideSkill.assessmentId !== ruleResults.nextSkill.assessmentId) {
    messageCode = "4";
    // same assessment with previous scores in acquisition range
  } else if (
    currentClasswideSkill.assessmentId === ruleResults.nextSkill.assessmentId &&
    mostRecentClasswideMedianScore < currentClasswideSkill.targets[0]
  ) {
    messageCode = "2";
    // same assessment with scores in fluency range
  } else if (
    currentClasswideSkill.assessmentId === ruleResults.nextSkill.assessmentId &&
    mostRecentClasswideMedianScore < currentClasswideSkill.targets[1]
  ) {
    messageCode = "3";
  }
  return {
    additionalStudentsAddedToInterventionQueue: checkForAdditionsToIndividualInterventionQueue(
      previousIndividualInterventionQueue,
      individualInterventionQueueUpdate
    ),
    messageCode,
    dismissed: false
  };
}

async function createClasswideInterventionForStudentGroup({ studentGroupId, benchmarkPeriodId }) {
  const studentGroup = await StudentGroups.findOneAsync(studentGroupId);
  const classwideRule = await Rules.findOneAsync({ grade: studentGroup.grade });
  const currentSkill = classwideRule.skills[0];
  const assessment = await Assessments.findOneAsync(currentSkill.assessmentId);
  const assessmentTarget = assessment.strands[0].scores[0].targets.find(target => target.grade === studentGroup.grade);
  const targets = assessmentTarget.periods[0].values;
  const { orgid } = studentGroup;
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);

  if (studentGroup.currentClasswideSkill) {
    throw new Meteor.Error(403, "Class already has an open classwide intervention!");
  }

  const ruleResult = {
    assessmentId: assessment._id,
    assessmentName: assessment.name,
    interventions: [],
    targets
  };

  const assessmentResult = {
    benchmarkPeriodId,
    orgid,
    created: bdo,
    schoolYear: studentGroup.schoolYear,
    status: "OPEN",
    studentGroupId,
    type: "classwide",
    previousAssessmentResultId: "",
    grade: studentGroup.grade
  };
  const assessmentIds = [ruleResult.assessmentId];
  const id = await AssessmentResults.insertAsync(assessmentResult);

  const studentIdsInThisStudentGroup = await getListOfStudentIdsFromStudentGroups([studentGroupId]);
  const scores = assessmentIds.reduce((scoreList, assessmentId) => {
    const studentScores = studentIdsInThisStudentGroup.map(studentId => ({
      _id: Random.id(),
      assessmentId,
      orgid,
      siteId: studentGroup.siteId,
      status: "STARTED",
      studentId
    }));
    return [...scoreList, ...studentScores];
  }, []);
  await insertAssessmentsAndScores({
    assessmentResultId: id,
    assessmentIds,
    scores
  });
  const newAssessmentResult = await AssessmentResults.findOneAsync(id);
  const currentClasswideSkill = {
    assessmentId: ruleResult.assessmentId,
    assessmentName: ruleResult.assessmentName,
    interventions: ruleResult.interventions || [],
    targets: ruleResult.targets,
    whenStarted: bdo,
    assessmentResultId: id,
    benchmarkPeriodId: newAssessmentResult.benchmarkPeriodId,
    message: {
      additionalStudentsAddedToInterventionQueue: false,
      messageCode: "1",
      dismissed: false
    }
  };
  return assignToStudentsOrGroup({
    assessmentResult: newAssessmentResult,
    currentClasswideSkill
  });
}

// returns id of new AssessmentResult
async function createNewClasswideInterventionAssessmentResultForStudentGroup({
  previousAssessmentResult,
  ruleResults,
  resultsMessage,
  isAdditional = false,
  isNextSkillAdditional = false
}) {
  const { orgid } = previousAssessmentResult;
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);

  const assessmentResult = {
    benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId,
    orgid,
    created: bdo,
    schoolYear: previousAssessmentResult.schoolYear,
    status: "OPEN",
    studentGroupId: previousAssessmentResult.studentGroupId,
    type: "classwide",
    previousAssessmentResultId: previousAssessmentResult._id,
    grade: previousAssessmentResult.grade
  };
  if (isNextSkillAdditional) {
    assessmentResult.isAdditional = true;
  }
  // insert it and get the id
  const id = await AssessmentResults.insertAsync(assessmentResult);
  const { siteId } = previousAssessmentResult.scores[0];
  const assessmentIds = [ruleResults.nextSkill.assessmentId];
  const studentIdsInThisStudentGroup = await getListOfStudentIdsFromStudentGroups([
    previousAssessmentResult.studentGroupId
  ]);
  const scores = assessmentIds.reduce((scoreList, assessmentId) => {
    const studentScores = studentIdsInThisStudentGroup.map(studentId => ({
      _id: Random.id(),
      assessmentId,
      orgid,
      siteId,
      status: "STARTED",
      studentId
    }));
    return [...scoreList, ...studentScores];
  }, []);
  // inserts/overwrites scores on the assessmentResult.. not sure if this is the best
  // idea yet, but we're going to leave it.  There should only be one assessmentResult
  // per studentGroup for benchmarking per benchmark period.
  await insertAssessmentsAndScores({
    assessmentResultId: id,
    assessmentIds,
    scores
  });
  const newAssessmentResult = await AssessmentResults.findOneAsync(id);
  const currentClasswideSkill = {
    assessmentId: ruleResults.nextSkill.assessmentId,
    assessmentName: ruleResults.nextSkill.assessmentName,
    interventions: ruleResults.nextSkill.interventions || [],
    targets: ruleResults.nextSkill.targets,
    whenStarted: bdo,
    assessmentResultId: id,
    benchmarkPeriodId: newAssessmentResult.benchmarkPeriodId,
    message: resultsMessage
  };
  return assignToStudentsOrGroup({
    assessmentResult: newAssessmentResult,
    currentClasswideSkill,
    previousAssessmentResult,
    isAdditional,
    hasCompletedCWI: isNextSkillAdditional
  });
}

export async function getIndividualInterventionQueueBasedOnFourWeekRule({
  studentGroup,
  assessmentResult,
  timestampInfo
}) {
  // eslint-disable-next-line no-param-reassign
  timestampInfo ??= await getTimestampInfo(getMeteorUserId(), studentGroup.orgid);
  const targetHistoryPlaceholder = {
    type: assessmentResult.type,
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
    assessmentId: assessmentResult.assessmentIds[0], // NOTE(fmazur) - classwide always has only 1 assessmentId
    assessmentResultId: assessmentResult._id,
    whenEnded: timestampInfo,
    whenStarted: timestampInfo,
    assessmentResultMeasures: assessmentResult.measures,
    enrolledStudentIds: getEnrolledStudentIds(assessmentResult)
  };

  const history = [targetHistoryPlaceholder, ...studentGroup.history];
  const currentDate = timestampInfo.date;

  const recommendations = getRecommendationsForIndividualIntervention({ history, currentDate });

  if (
    !recommendations ||
    !Object.keys(recommendations).length ||
    (!recommendations.idsOfStudentsInFrustrationalRangeInPast &&
      !recommendations.missingScoresRecommendationsResult?.twoAnySkillsAbsentWhenMovedUpStudentIds.length)
  ) {
    return undefined;
  }

  return uniq([
    ...recommendations.missingScoresRecommendationsResult.missingScoresRecommendations,
    ...(recommendations.idsOfStudentsInFrustrationalRangeInPast || [])
  ]);
}

// todo figure out why this is different from the assignToGroup subroutines
async function updateStudentGroupHistoryWithoutAssignment(
  studentGroup,
  assessmentResult,
  byDateOnObj,
  ruleResults,
  resultsMessage,
  isAdditional = false
) {
  const localStudentGroup = { ...studentGroup };
  const historyFieldName = getHistoryFieldName(isAdditional);
  // update the student group history
  if (!localStudentGroup[historyFieldName]) {
    localStudentGroup[historyFieldName] = [];
  }
  const enrolledStudentIds = getEnrolledStudentIds(assessmentResult);
  if (localStudentGroup.currentClasswideSkill && assessmentResult.type !== "benchmark") {
    localStudentGroup.currentClasswideSkill.assessmentResultMeasures = assessmentResult.measures;
    localStudentGroup.currentClasswideSkill.type = assessmentResult.type;
    localStudentGroup.currentClasswideSkill.enrolledStudentIds = enrolledStudentIds;
    localStudentGroup.currentClasswideSkill.whenEnded = byDateOnObj;
    localStudentGroup[historyFieldName].unshift(localStudentGroup.currentClasswideSkill);
  } else if (assessmentResult.type === "benchmark") {
    // store history here in case we don't need intervention
    localStudentGroup[historyFieldName].unshift({
      type: assessmentResult.type,
      benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
      assessmentResultId: assessmentResult._id,
      whenEnded: byDateOnObj,
      whenStarted: assessmentResult.whenStarted,
      assessmentResultMeasures: assessmentResult.measures,
      enrolledStudentIds
    });
  }
  await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
    $set: {
      [historyFieldName]: localStudentGroup[historyFieldName],
      currentAssessmentResultIds: localStudentGroup.currentAssessmentResultIds.filter(
        arid => arid !== assessmentResult._id
      ),
      lastModified: { ...byDateOnObj, context: "updateStudentGroupHistoryWithoutAssignment" }
    }
  });
  // clear out if we're done with classwide
  if (assessmentResult.type === "classwide" && !ruleResults.nextSkill) {
    // await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
    //   $unset: { currentClasswideSkill: 1 },
    // });
    await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
      $set: {
        currentClasswideSkill: {
          whenStarted: byDateOnObj,
          benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
          message: resultsMessage
        },
        lastModified: { ...byDateOnObj, context: "updateStudentGroupHistoryWithoutAssignment" }
      }
    });
  }
}

function canGenerateClasswideIntervention({
  classwideEnabled,
  ruleResults,
  assessmentResultType,
  hasClasswideSkill,
  numberOfStudents
}) {
  return (
    classwideEnabled &&
    ruleResults.nextSkill &&
    (assessmentResultType === "classwide" ||
      (assessmentResultType === "benchmark" && !hasClasswideSkill && numberOfStudents > 1))
  );
}

export async function getIdsOfStudentsInIndividualIntervention(studentIds, schoolYear) {
  return (
    await AssessmentResults.find(
      {
        studentId: { $in: studentIds },
        type: "individual",
        status: "OPEN",
        schoolYear
      },
      { fields: { studentId: 1 } }
    ).fetchAsync()
  ).map(assessmentResult => assessmentResult.studentId);
}

async function processClasswideResults({
  assessmentResult,
  studentGroup,
  assessmentResultUpdate,
  timestampInfo,
  shouldForceClasswide = false,
  isAdditional = false
}) {
  // process the results
  const updateDocument = { ...assessmentResultUpdate };
  const ruleResults = await rulesMethods.processClasswideRules(assessmentResult, shouldForceClasswide);
  const isNextSkillAdditional = ruleResults.nextSkill?.isAdditional || false;
  const previousIndividualInterventionQueue = [...(studentGroup.individualInterventionQueue || [])];
  // Get just the classwideEnabled value if it exists, otherwise assume true.
  const org =
    (await Organizations.findOneAsync({
      _id: studentGroup.orgid
    })) || {};
  const classwideEnabled = typeof org.classwideEnabled !== "undefined" ? org.classwideEnabled : true;
  // 4 WEEK RULE
  let individualInterventionQueue;
  if (assessmentResult.type === "classwide" && !isAdditional) {
    individualInterventionQueue = await getIndividualInterventionQueueBasedOnFourWeekRule({
      studentGroup,
      assessmentResult,
      timestampInfo
    });

    if (individualInterventionQueue) {
      const idsOfStudentsInIndividualIntervention = await getIdsOfStudentsInIndividualIntervention(
        individualInterventionQueue,
        assessmentResult.schoolYear
      );

      const studentIdsWithCompletedIndividualInterventionForMostRecentScreening =
        assessmentResult.grade !== "HS"
          ? await getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening({
              studentIds: individualInterventionQueue,
              schoolYear: assessmentResult.schoolYear
            })
          : await getIdsOfHSStudentsWithCompletedAllInterventions(individualInterventionQueue);

      individualInterventionQueue = individualInterventionQueue.filter(
        studentId =>
          !idsOfStudentsInIndividualIntervention.includes(studentId) &&
          !studentIdsWithCompletedIndividualInterventionForMostRecentScreening.includes(studentId)
      );
      await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
        $set: {
          individualInterventionQueue,
          lastModified: { ...timestampInfo, context: "processClasswideResults" }
        }
      });
    }
  }
  const resultsMessage = getClasswideResultsMessage(
    studentGroup.currentClasswideSkill,
    ruleResults,
    assessmentResult,
    previousIndividualInterventionQueue,
    individualInterventionQueue
  );
  const assessmentResultType = assessmentResult.type;
  const hasClasswideSkill = !!studentGroup.currentClasswideSkill;
  const numberOfStudents = assessmentResult.classwideResults.totalStudentsAssessedOnAllMeasures;
  if (
    canGenerateClasswideIntervention({
      classwideEnabled,
      ruleResults,
      assessmentResultType,
      hasClasswideSkill,
      numberOfStudents
    })
  ) {
    const nextAssessmentResult = await createNewClasswideInterventionAssessmentResultForStudentGroup({
      previousAssessmentResult: assessmentResult,
      ruleResults,
      resultsMessage,
      isAdditional,
      isNextSkillAdditional
    });
    // add the next assessmentResultId to the current one
    extend(updateDocument, {
      nextAssessmentResultId: nextAssessmentResult._id
    });
  } else {
    await updateStudentGroupHistoryWithoutAssignment(
      studentGroup,
      assessmentResult,
      timestampInfo,
      ruleResults,
      resultsMessage,
      isAdditional
    );
  }
  // add the results of processing to the assessment result update query
  return extend(updateDocument, { ruleResults });
}

async function completeAssessmentResult({
  assessmentResult,
  classwideResults,
  measures,
  isResuming,
  isAdditional = false
}) {
  const studentGroup = await StudentGroups.findOneAsync(assessmentResult.studentGroupId);
  const timestampInfo = await getTimestampInfo(getMeteorUserId(), studentGroup.orgid, "completeAssessmentResult");
  let assessmentResultUpdate = {
    status: "COMPLETED",
    classwideResults,
    measures,
    lastModified: timestampInfo
  };
  extend(assessmentResult, { classwideResults, measures });
  // process rules
  if (["benchmark", "classwide"].includes(assessmentResult.type)) {
    assessmentResultUpdate = await processClasswideResults({
      assessmentResult,
      studentGroup,
      assessmentResultUpdate,
      timestampInfo,
      isAdditional
    });
  } else {
    const ruleResults = await processIndividualRule({
      assessmentResult,
      studentId: assessmentResult.studentId,
      allowNoScore: false,
      isResuming
    });
    extend(assessmentResultUpdate, {
      ruleResults,
      nextAssessmentResultId: ruleResults.nextAssessmentResultId
    });
  }
  // update the current assessment result
  await AssessmentResults.updateAsync(assessmentResult._id, {
    $set: assessmentResultUpdate
  });
  return {
    resultHasUpcomingInterventions: get(assessmentResultUpdate, "ruleResults.nextSkill.interventions.length") > 0,
    hasPassed: get(assessmentResultUpdate, "ruleResults.passed")
  };
}

export async function createNewBenchmarkAssessmentResultForStudentGroup({
  studentGroupId,
  userId,
  schoolYear,
  benchmarkPeriodId,
  grade,
  orgid
}) {
  if (!studentGroupId) {
    throw new Meteor.Error("403", "studentGroupId is undefined");
  }
  if (!userId) {
    throw new Meteor.Error("403", "userId is undefined");
  }
  if (!schoolYear) {
    throw new Meteor.Error("403", "schoolYear is undefined");
  }
  if (!benchmarkPeriodId) {
    throw new Meteor.Error("403", "benchmarkPeriodId is undefined");
  }
  if (!orgid) {
    throw new Meteor.Error("403", "orgid is undefined");
  }

  const bdo = await getTimestampInfo(userId, orgid);

  const assessmentResult = {
    benchmarkPeriodId,
    created: bdo,
    schoolYear,
    status: "OPEN",
    studentGroupId,
    type: "benchmark",
    grade,
    orgid
  };
  const id = await AssessmentResults.insertAsync(assessmentResult);
  extend(assessmentResult, { _id: id });
  return assignToStudentsOrGroup({ assessmentResult });
}

export async function mostRecentOpenBenchmarkAssessmentResultForStudentGroup({
  studentGroupId,
  schoolYear,
  benchmarkPeriodId
}) {
  if (!studentGroupId) {
    throw new Meteor.Error("403", "studentGroupId is undefined");
  }
  check(studentGroupId, String);
  check(schoolYear, Number);
  check(benchmarkPeriodId, String);

  const q = {
    benchmarkPeriodId,
    schoolYear,
    status: { $in: ["OPEN", "STARTED"] },
    studentGroupId,
    type: "benchmark"
  };
  const qSort = { sort: { "created.on": -1 } };

  return AssessmentResults.findOneAsync(q, qSort);
}

function getAssessmentTargets(measures, assessments, studentGroup, assessmentResult, targetIndex) {
  return assessments.map(assessment => {
    const targetScores = getScoreTargets({
      assessment,
      grade: studentGroup.grade,
      benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
      assessmentType: assessmentResult.type
    });
    const assCutoffTarget = targetScores[targetIndex];
    measures.push({
      assessmentId: assessment._id,
      assessmentName: assessment.name,
      cutoffTarget: assCutoffTarget,
      targetScores,
      medianScore: 0,
      studentScores: [],
      percentMeetingTarget: 0,
      numberMeetingTarget: 0,
      totalStudentsAssessed: 0,
      studentResults: []
    });

    return {
      assessmentId: assessment._id,
      assessmentName: assessment.name,
      cutoffTarget: assCutoffTarget,
      targetScores
    };
  });
}

export async function calculateAssessmentResults({ assessmentResult }) {
  check(assessmentResult, Object);

  let userId = "";
  try {
    userId = getMeteorUserId();
  } catch (error) {
    ({ userId } = this);
  }

  // get the student group
  const studentGroup = await StudentGroups.findOneAsync({
    _id: assessmentResult.studentGroupId
  });
  const assessmentIsNotCompleted = assessmentResult.scores.some(score => score.status === "STARTED");
  const isBenchmarkOrClasswide = assessmentResult.type === "benchmark" || assessmentResult.type === "classwide";
  if (assessmentIsNotCompleted && isBenchmarkOrClasswide) {
    throw new Meteor.Error(
      "calculateAssessmentResults",
      "Assessment is not completed. There are some scores with status STARTED. There should be only scores with statuses CANCELED or COMPLETED"
    );
  }

  const studentIds = assessmentResult.scores.reduce((a, c) => {
    if (c.status === "COMPLETE") {
      if (a.indexOf(c.studentId) < 0) a.push(c.studentId);
    }
    return a;
  }, []);

  const assessmentIds =
    assessmentResult.type === "individual" && isEmpty(assessmentResult.individualSkills.interventions)
      ? [assessmentResult.individualSkills.assessmentId]
      : uniq(assessmentResult.assessmentIds);

  const assessments = (await Assessments.find({ _id: { $in: assessmentIds } }).fetchAsync()).sort(
    (a, b) => assessmentResult.assessmentIds.indexOf(a._id) - assessmentResult.assessmentIds.indexOf(b._id)
  );
  let measures = [];
  const classwideResults = {
    percentMeetingTarget: 0,
    percentAtRisk: 0,
    totalStudentsMeetingAllTargets: 0,
    totalStudentsAssessedOnAllMeasures: 0,
    studentIdsNotMeetingTarget: []
  };

  if (assessmentIds.length !== assessments.length) {
    throw new Meteor.Error("calculateAssessmentResults", `Cannot find one of assessments: ${assessmentIds.join(", ")}`);
  }

  // Get the assessment target cutoffs
  // If we are working on an individual use the mastery target (index 0), otherwise use the instructional (index 1)
  let assessmentTargets = [];
  const targetIndex = assessmentResult.type.toLowerCase() !== "individual" && assessments.length > 1 ? 0 : 1;
  assessmentTargets = getAssessmentTargets(measures, assessments, studentGroup, assessmentResult, targetIndex);
  const sortedMeasures = [];
  if (assessmentResult.type === "individual" && assessmentResult.individualSkills) {
    if (
      assessmentResult.individualSkills.interventions &&
      assessmentResult.individualSkills.interventions.length > 0 &&
      measures.length > 1
    ) {
      sortedMeasures.push(
        measures.find(m => m.assessmentId === assessmentResult.individualSkills.benchmarkAssessmentId)
      );
    }
    sortedMeasures.push(measures.find(m => m.assessmentId === assessmentResult.individualSkills.assessmentId));
    measures = sortedMeasures;
  }
  // Loop through each student score for each assessment they
  // took and see if their number_correct is above or below the cutoff target
  // get students
  const studentDocs = await Students.find({ _id: { $in: studentIds } }).fetchAsync();
  studentIds.forEach(stuId => {
    const studentInfo = find(studentDocs, student => student._id === stuId);
    let numOfMeasuresAssessed = 0;
    let numOfMeasureTargetsMet = 0;
    assessmentResult.scores.forEach(assScore => {
      if (assScore.studentId === stuId) {
        const currAssTarget =
          assessmentTargets.length === 1
            ? assessmentTargets[0]
            : assessmentTargets.find(at => at.assessmentId === assScore.assessmentId);

        const stuScore = assScore.value;
        if (stuScore !== "" && stuScore !== "N/A") {
          numOfMeasuresAssessed += 1;
          // Let's put the students not meeting target in a
          // special place for now...we'll get to them later...
          if (Number(stuScore) < currAssTarget.cutoffTarget) {
            if (!includes(classwideResults.studentIdsNotMeetingTarget, stuId)) {
              classwideResults.studentIdsNotMeetingTarget.push(stuId);
            }
          }
        }
        measures.forEach(m => {
          const measure = m;
          if (measure.assessmentId === currAssTarget.assessmentId) {
            if (!measure.studentResults.find(sr => sr.studentId === studentInfo._id)) {
              const studentResult = {
                studentId: studentInfo._id,
                status: assScore.status,
                firstName: studentInfo.identity.name.firstName,
                lastName: studentInfo.identity.name.lastName,
                score: stuScore,
                meetsTarget: stuScore === "" ? null : Number(stuScore) >= currAssTarget.cutoffTarget,
                individualRuleOutcome: getIndividualRuleOutcome(stuScore, currAssTarget.targetScores)
              };
              if (assScore.status === "COMPLETE") {
                measure.totalStudentsAssessed += 1;

                if (stuScore) {
                  measure.studentScores.push(Number(stuScore));
                }

                if (studentResult.meetsTarget) {
                  measure.numberMeetingTarget += 1;
                  numOfMeasureTargetsMet += 1;
                }
              }
              measure.studentResults.push(studentResult);
            }
          }
        });
      }
    });

    if (numOfMeasuresAssessed === measures.length) {
      classwideResults.totalStudentsAssessedOnAllMeasures += 1;
      ninjalog.trace({
        msg: "inside if...classwideResults.totalStudentsAssessedOnAllMeasures",
        val: classwideResults.totalStudentsAssessedOnAllMeasures,
        context: "screening"
      });
    }

    const isPassingIndividualInterventionGoalSkill =
      assessmentResult.type === "individual" &&
      measures.find(m => m.assessmentId === assessmentResult.individualSkills.benchmarkAssessmentId)
        ?.numberMeetingTarget > 0;

    if (numOfMeasureTargetsMet === measures.length || isPassingIndividualInterventionGoalSkill) {
      classwideResults.totalStudentsMeetingAllTargets += 1;
      ninjalog.trace({
        msg: "Inside if...classwideResults.totalStudentsMeetingAllTargets",
        val: classwideResults.totalStudentsMeetingAllTargets,
        context: "screening"
      });
    }
    if (isPassingIndividualInterventionGoalSkill) {
      classwideResults.studentIdsNotMeetingTarget = [];
    }
  });

  // Calculate percentages for Class Wide results
  ninjalog.trace({
    msg: "classwideResults.totalStudentsMeetingAllTargets",
    val: classwideResults.totalStudentsMeetingAllTargets,
    context: "screening"
  });
  ninjalog.trace({
    msg: "classwideResults.totalStudentsAssessedOnAllMeasures",
    val: classwideResults.totalStudentsAssessedOnAllMeasures,
    context: "screening"
  });
  const cwPercentMeetingTarget = getPercent(
    classwideResults.totalStudentsMeetingAllTargets,
    classwideResults.totalStudentsAssessedOnAllMeasures
  );

  classwideResults.percentMeetingTarget = cwPercentMeetingTarget;
  classwideResults.percentAtRisk = 100 - cwPercentMeetingTarget;

  // Calculate individual percentage & median score of targets met per measure
  measures.forEach(m => {
    const measure = m;
    measure.percentMeetingTarget = getPercent(measure.numberMeetingTarget, measure.totalStudentsAssessed);
    measure.medianScore = getMedianNumber(measure.studentScores, "roundUp");
  });

  const bdo = await getTimestampInfo(userId, assessmentResult.orgid);

  // Compile results
  return extend(
    {},
    {
      classwideResults,
      measures,
      status: "PENDING",
      created: bdo
    }
  );
}

async function calculateScoreResult({ assessmentResultId, doSave, isResuming = false, isAdditional = false }) {
  check(assessmentResultId, String);
  const assessmentResultQuery = { _id: assessmentResultId };
  if (!isResuming && doSave) {
    assessmentResultQuery.status = "OPEN";
  }
  const assessmentResult = await AssessmentResults.findOneAsync(assessmentResultQuery);
  if (!assessmentResult) {
    return {};
  }
  let resultsPkg = await calculateAssessmentResults({
    assessmentResult
  });
  if (doSave && resultsPkg) {
    const { resultHasUpcomingInterventions, hasPassed } = await completeAssessmentResult({
      assessmentResult,
      classwideResults: resultsPkg.classwideResults,
      measures: resultsPkg.measures,
      type: assessmentResult.type,
      isResuming,
      isAdditional
    });
    resultsPkg = { ...resultsPkg, hasFollowUpPhaseEnded: resultHasUpcomingInterventions, hasPassed };
  }
  return { ...resultsPkg, scores: assessmentResult.scores };
}

async function shouldRecommendIndividualInterventions({ studentGroupId, orgid }) {
  return !(await AssessmentResults.findOneAsync(
    {
      studentGroupId,
      type: "classwide",
      schoolYear: await getCurrentSchoolYear(await getMeteorUser(), orgid)
    },
    { fields: { _id: 1 } }
  ));
}

async function getNewIndividualInterventionRecommendations({ studentGroup, classwideResults, schoolYear }) {
  const { totalStudentsAssessedOnAllMeasures, percentAtRisk, studentIdsNotMeetingTarget } = classwideResults;
  const isSingleStudentWithFailedBenchmark = totalStudentsAssessedOnAllMeasures === 1 && percentAtRisk === 100;
  const idsOfStudentsInIndividualIntervention = await getIdsOfStudentsInIndividualIntervention(
    studentIdsNotMeetingTarget,
    schoolYear
  );
  const classwideCompleteMessageCode = "5";
  let newIndividualInterventionRecommendations = [];
  if (
    percentAtRisk <= 50 ||
    studentGroup?.currentClasswideSkill?.message.messageCode === classwideCompleteMessageCode ||
    isSingleStudentWithFailedBenchmark
  ) {
    newIndividualInterventionRecommendations =
      studentIdsNotMeetingTarget.filter(studentId => !idsOfStudentsInIndividualIntervention.includes(studentId)) || [];
  }
  return newIndividualInterventionRecommendations;
}

export async function calculateClasswideScreeningResults(assessmentResultId) {
  const assessmentResult = await AssessmentResults.findOneAsync(assessmentResultId);
  if (!assessmentResult) {
    return null;
  }
  const studentGroup = await StudentGroups.findOneAsync(assessmentResult.studentGroupId);
  if (!studentGroup) {
    throw new Meteor.Error("403", `No student group found matching id: ${assessmentResult.studentGroupId}`);
  }
  const resultsPkg = await calculateAssessmentResults({ assessmentResult });
  if (!resultsPkg) {
    return null;
  }
  await completeAssessmentResult({
    assessmentResult,
    classwideResults: resultsPkg.classwideResults,
    measures: resultsPkg.measures,
    type: "benchmark"
  });

  const shouldRecommendNewIndividualInterventions = await shouldRecommendIndividualInterventions({
    studentGroupId: assessmentResult.studentGroupId,
    orgid: assessmentResult.orgid
  });

  if (shouldRecommendNewIndividualInterventions) {
    const newIndividualInterventionRecommendations = await getNewIndividualInterventionRecommendations({
      studentGroup,
      classwideResults: resultsPkg.classwideResults,
      schoolYear: assessmentResult.schoolYear
    });

    const lastModified = await getTimestampInfo(
      getMeteorUserId(),
      assessmentResult.orgid,
      "calculateClasswideScreeningResults"
    );
    await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
      $addToSet: {
        individualInterventionQueue: {
          $each: newIndividualInterventionRecommendations
        }
      },
      $set: { lastModified }
    });
  }
  return resultsPkg;
}

export async function updateScores(scoresPkg) {
  if (scoresPkg.length < 1) {
    throw new Meteor.Error("403", "scores package was empty");
  }
  let docsIdsUpdated = [];
  const ar = await AssessmentResults.findOneAsync(
    {
      _id: scoresPkg[0].assessmentResultId
    },
    { fields: { orgid: 1, scores: 1 } }
  );
  ar.scores = ar.scores.reduce((scores, s) => {
    const newScore = s;
    const scoreUpdate = scoresPkg.find(sp => sp.scoreId === s._id);
    if (scoreUpdate) {
      newScore.value = scoreUpdate.number_correct;
      newScore.status = scoreUpdate.status;
    }
    scores.push(newScore);
    return scores;
  }, []);

  const customDate = get(await getMeteorUser(), "profile.customDate");
  const currentDate = await getCurrentDate(customDate, ar.orgid);
  const lastModified = await getTimestampInfo(getMeteorUserId(), ar?.orgid, "updateScores");

  const updated = await AssessmentResults.updateAsync(
    {
      _id: ar._id
    },
    {
      $set: {
        scores: ar.scores,
        lastScoreUpdatedAt: currentDate.getTime(),
        lastModified
      }
    }
  );
  if (updated) {
    docsIdsUpdated = scoresPkg.map(s => s.scoreId);
  }
  return docsIdsUpdated;
}

export async function clearScreeningScores(studentGroupId, benchmarkPeriodId, schoolYear) {
  const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId }, { transform: null });
  const screeningAssessments = await AssessmentResults.find({
    studentGroupId,
    type: "benchmark",
    benchmarkPeriodId,
    schoolYear
  }).fetchAsync();

  if (!studentGroup) {
    throw new Meteor.Error("clearScreeningScores", `Student group: ${studentGroupId} not found.`);
  }

  if (!screeningAssessments.length) {
    throw new Meteor.Error(
      "clearScreeningScores",
      "No screening assessment was found in the provided benchmark period."
    );
  }

  if (screeningAssessments.length > 1) {
    throw new Meteor.Error(
      "clearScreeningScores",
      "More than one screening assessment found in the provided benchmark period"
    );
  }

  const screeningAssessment = screeningAssessments[0];
  if (!screeningAssessment.scores || !screeningAssessment.scores.length) {
    throw new Meteor.Error("clearScreeningScores", "AssessmentResults doesn't have any scores.");
  }
  const idsOfCurrentlyEnrolledStudents = (
    await StudentGroupEnrollments.find(
      { studentGroupId, schoolYear, isActive: true },
      { fields: { studentId: 1 } }
    ).fetchAsync()
  ).map(enrollment => enrollment.studentId);

  if (!idsOfCurrentlyEnrolledStudents || !idsOfCurrentlyEnrolledStudents.length) {
    throw new Meteor.Error("clearScreeningScores", "The group does not have any actively enrolled student.");
  }

  const { assessmentIds } = screeningAssessment;
  const clearedScores = [];
  const { orgid, siteId } = studentGroup;

  assessmentIds.forEach(assessmentId => {
    idsOfCurrentlyEnrolledStudents.forEach(studentId => {
      clearedScores.push({
        _id: Random.id(),
        assessmentId,
        orgid,
        siteId,
        status: "STARTED",
        studentId
      });
    });
  });

  let classwideInterventionToRemove = null;
  const unsetInScreeningAssessment = {
    measures: 1,
    classwideResults: 1,
    ruleResults: 1
  };
  if (screeningAssessment.nextAssessmentResultId) {
    const classwideInterventionResults = await AssessmentResults.findOneAsync({
      _id: screeningAssessment.nextAssessmentResultId
    });
    if (classwideInterventionResults && classwideInterventionResults.status !== "COMPLETED") {
      classwideInterventionToRemove = classwideInterventionResults._id;
      await AssessmentResults.removeAsync({ _id: classwideInterventionToRemove });
      unsetInScreeningAssessment.nextAssessmentResultId = 1;
    }
  }

  const userId = getMeteorUserId();
  const lastModified = await getTimestampInfo(userId, orgid, "clearScreeningScores");
  await AssessmentResults.updateAsync(
    { _id: screeningAssessment._id },
    {
      $set: {
        scores: clearedScores,
        status: "OPEN",
        lastModified
      },
      $unset: unsetInScreeningAssessment
    }
  );

  if (!studentGroup.history || !studentGroup.history.length) {
    return true;
  }
  const pullFromStudentGroup = {
    history: {
      assessmentResultId: {
        $in: [screeningAssessment._id, classwideInterventionToRemove]
      }
    }
  };

  if (
    screeningAssessment.status === "COMPLETED" &&
    screeningAssessment.ruleResults.passed === true &&
    studentGroup.individualInterventionQueue
  ) {
    const idsOfStudentsToRemoveInterventionEligibility = get(
      screeningAssessment,
      "classwideResults.studentIdsNotMeetingTarget",
      []
    );
    pullFromStudentGroup.individualInterventionQueue = {
      $in: idsOfStudentsToRemoveInterventionEligibility
    };
  }

  const studentGroupUpdateObject = {
    $pull: pullFromStudentGroup,
    $addToSet: {
      currentAssessmentResultIds: screeningAssessment._id
    },
    $set: {
      lastModified
    }
  };

  if (classwideInterventionToRemove) {
    studentGroupUpdateObject.$unset = {
      currentClasswideSkill: ""
    };
  }

  await StudentGroups.updateAsync({ _id: studentGroupId }, studentGroupUpdateObject);

  if (classwideInterventionToRemove) {
    await StudentGroups.updateAsync(
      { _id: studentGroupId },
      {
        $pull: {
          currentAssessmentResultIds: classwideInterventionToRemove
        }
      }
    );
  }
  return true;
}

export async function forceClasswideInterventionFor({ studentGroupId, benchmarkAssessmentResultId }) {
  const benchmarkAssessmentResult = await AssessmentResults.findOneAsync(benchmarkAssessmentResultId);
  const studentGroup = await StudentGroups.findOneAsync(studentGroupId);
  const { orgid } = studentGroup;
  const currentBenchmarkPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const isInCurrentBenchmarkPeriod = benchmarkAssessmentResult.benchmarkPeriodId === currentBenchmarkPeriod._id;
  if (!canForceGroupToClasswideIntervention(studentGroup) || !isInCurrentBenchmarkPeriod) {
    throw new Meteor.Error(
      "forceClasswideInterventionFor",
      `Cannot force classwide intervention for the studentGroupId: ${studentGroupId}`
    );
  }
  const shouldForceClasswide = true;
  const timestampInfo = await getTimestampInfo(getMeteorUserId(), orgid);
  const assessmentResultUpdate = await processClasswideResults({
    assessmentResult: benchmarkAssessmentResult,
    studentGroup,
    assessmentResultUpdate: {},
    timestampInfo,
    shouldForceClasswide
  });
  return AssessmentResults.updateAsync(benchmarkAssessmentResultId, {
    $set: assessmentResultUpdate
  });
}

export async function hasGroupOpenIndividualIntervention(studentGroupId, orgid) {
  const currentSchoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const openIndividualInterventions = await AssessmentResults.findOneAsync(
    {
      studentGroupId,
      schoolYear: currentSchoolYear,
      status: "OPEN",
      type: "individual"
    },
    { fields: { _id: 1 } }
  );
  return !!openIndividualInterventions;
}

export async function hasGroupOpenClasswideIntervention(studentGroupId, orgid) {
  const currentSchoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const openClasswideInterventions = await AssessmentResults.findOneAsync(
    {
      studentGroupId,
      schoolYear: currentSchoolYear,
      status: "OPEN",
      type: "classwide"
    },
    { fields: { _id: 1 } }
  );
  return !!openClasswideInterventions;
}

export async function hasAnyClasswideInterventionsScores(studentGroupId, orgid) {
  const currentSchoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const openClasswideInterventions = await AssessmentResults.find(
    {
      studentGroupId,
      schoolYear: currentSchoolYear,
      type: "classwide"
    },
    { fields: { scores: 1 } }
  ).fetchAsync();
  /* eslint-disable-next-line no-prototype-builtins */
  return openClasswideInterventions.every(cw => cw.scores.every(score => !score.hasOwnProperty("value")));
}

async function clearAssessmentResultScores(lastClasswideAssessmentResult) {
  const scores = [];
  const { schoolYear, studentGroupId } = lastClasswideAssessmentResult;
  const studentIdsCurrentlyActiveInStudentGroup = (
    await StudentGroupEnrollments.find({
      studentGroupId,
      schoolYear,
      isActive: true
    }).fetchAsync()
  ).map(sge => sge.studentId);

  lastClasswideAssessmentResult.scores.forEach(score => {
    if (studentIdsCurrentlyActiveInStudentGroup.includes(score.studentId)) {
      const updatedScore = {
        ...score,
        status: "STARTED"
      };
      delete updatedScore.value;
      scores.push(updatedScore);
    }
  });

  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    lastClasswideAssessmentResult.orgid,
    "clearAssessmentResultScores"
  );

  await AssessmentResults.updateAsync(
    { _id: lastClasswideAssessmentResult._id },
    {
      $set: { scores, lastModified, status: "OPEN" },
      $unset: { classwideResults: "", measures: "", nextAssessmentResultId: "", ruleResults: "" }
    }
  );
}

export async function setCurrentAssessmentResultForStudentGroup({
  studentGroup,
  assessmentResult,
  assessmentId,
  shouldUpdateHistory = true,
  currentAssessmentResultId = assessmentResult._id
}) {
  const assessmentResultId = assessmentResult._id;

  const additionalHistory = studentGroup.additionalHistory || [];
  let isAdditionalSkill = false;
  let classwideAssessmentHistoryItemIndex = studentGroup.history.findIndex(
    item => item.assessmentResultId === assessmentResultId
  );
  if (classwideAssessmentHistoryItemIndex < 0) {
    classwideAssessmentHistoryItemIndex = additionalHistory.findIndex(
      item => item.assessmentResultId === assessmentResultId
    );
    if (classwideAssessmentHistoryItemIndex >= 0) {
      isAdditionalSkill = true;
    }
  }
  const historyFieldName = getHistoryFieldName(isAdditionalSkill);
  const classwideAssessmentHistoryItem = studentGroup[historyFieldName][classwideAssessmentHistoryItemIndex];
  studentGroup[historyFieldName].splice(classwideAssessmentHistoryItemIndex, 1);

  const {
    assessmentId: currentAssessmentId,
    assessmentName,
    whenStarted,
    benchmarkPeriodId,
    interventions,
    targets
  } = classwideAssessmentHistoryItem;

  const oldCurrentClasswideSkill = {
    assessmentId: currentAssessmentId,
    assessmentName,
    interventions,
    targets,
    whenStarted,
    assessmentResultId: currentAssessmentResultId,
    benchmarkPeriodId
  };

  const resultsMessage = getClasswideResultsMessage(
    oldCurrentClasswideSkill,
    assessmentResult.ruleResults,
    assessmentResult,
    studentGroup.individualInterventionQueue,
    []
  );

  const isSameCurrentAssessmentSkill = assessmentId === currentAssessmentId;

  const {
    assessmentId: nextAssessmentId,
    assessmentName: nextAssessmentName,
    interventions: nextInterventions,
    targets: nextTargets
  } = isSameCurrentAssessmentSkill ? oldCurrentClasswideSkill : assessmentResult.ruleResults.nextSkill;

  const currentClasswideSkill = {
    assessmentId: nextAssessmentId,
    assessmentName: nextAssessmentName,
    interventions: nextInterventions,
    targets: nextTargets,
    whenStarted,
    assessmentResultId: currentAssessmentResultId,
    benchmarkPeriodId,
    message: resultsMessage
  };

  const currentAssessmentResultIds = studentGroup.currentAssessmentResultIds.filter(
    asrId => asrId !== assessmentResult.nextAssessmentResultId
  );
  currentAssessmentResultIds.push(currentAssessmentResultId);

  const studentGroupUpdateQuery = {
    currentAssessmentResultIds,
    currentClasswideSkill
  };

  if (shouldUpdateHistory) {
    studentGroupUpdateQuery[historyFieldName] = studentGroup[historyFieldName];
  }

  if (
    (!assessmentResult.isAdditional && assessmentResult.ruleResults?.nextSkill?.assessmentId) ||
    assessmentResult.isAdditional
  ) {
    studentGroupUpdateQuery.hasCompletedCWI =
      !!(
        assessmentResult.ruleResults?.nextSkill?.isAdditional &&
        assessmentResult.ruleResults.passed &&
        !isSameCurrentAssessmentSkill
      ) || !!assessmentResult.isAdditional;
  }

  studentGroup.lastModified = await getTimestampInfo(
    getMeteorUserId(),
    assessmentResult?.orgid || studentGroup?.orgid,
    "setCurrentAssessmentResultForStudentGroup"
  );
  await StudentGroups.updateAsync(
    {
      _id: studentGroup._id
    },
    {
      $set: studentGroupUpdateQuery
    }
  );
}

async function clearLastClasswideInterventionScores({ studentGroupId, assessmentId }) {
  const studentGroup = await StudentGroups.findOneAsync({
    _id: studentGroupId,
    "currentClasswideSkill.benchmarkPeriodId": { $exists: true }
  });

  if (!studentGroup) {
    throw new Meteor.Error("403", `No student group with id: ${studentGroupId} found`);
  }

  const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];
  const lastCompletedAssessment = studentGroupHistory.find(historyItem => historyItem.assessmentId === assessmentId);

  const lastClasswideAssessmentResult = await AssessmentResults.findOneAsync({
    _id: lastCompletedAssessment.assessmentResultId
  });

  if (!lastClasswideAssessmentResult) {
    throw new Meteor.Error("403", `No classwide assessment result with id: ${assessmentId} found`);
  }
  await clearAssessmentResultScores(lastClasswideAssessmentResult);

  if (lastClasswideAssessmentResult.status === "COMPLETED") {
    await setCurrentAssessmentResultForStudentGroup({
      studentGroup,
      assessmentResult: lastClasswideAssessmentResult,
      assessmentId
    });
  }

  if (lastClasswideAssessmentResult.nextAssessmentResultId) {
    await AssessmentResults.removeAsync({ _id: lastClasswideAssessmentResult.nextAssessmentResultId });
  }

  const mostRecentClasswideAssessmentResult = await AssessmentResults.findOneAsync(
    {
      type: "classwide",
      status: "COMPLETED",
      studentGroupId,
      schoolYear: studentGroup.schoolYear
    },
    { sort: { "created.on": -1 } }
  );
  if (mostRecentClasswideAssessmentResult) {
    const lastModified = await getTimestampInfo(
      getMeteorUserId(),
      studentGroup.orgid,
      "clearLastClasswideInterventionScores"
    );
    await updateIndividualInterventionRecommendationFromManageScoreChanges({
      studentGroupId,
      assessmentResult: mostRecentClasswideAssessmentResult,
      lastModified
    });
  }
}

export async function clearLastIndividualInterventionScores({ studentGroupId, studentId }) {
  const studentGroup = await StudentGroups.findOneAsync({
    _id: studentGroupId
  });
  if (!studentGroup) {
    throw new Meteor.Error("403", `No student group with id: ${studentGroupId} found`);
  }
  const studentDoc = await Students.findOneAsync({ _id: studentId }, { history: 1 });
  if (!(studentDoc && studentDoc.history && studentDoc.history.length)) {
    return false;
  }

  const { assessmentResultId } = studentDoc.history.find(({ type }) => type === "individual");

  if (!assessmentResultId) {
    return {
      student: await Students.findOneAsync({ _id: studentId }),
      studentGroup: await StudentGroups.findOneAsync({ _id: studentGroupId })
    };
  }

  const { _id: currentAssessmentResultId, nextAssessmentResultId } = await AssessmentResults.findOneAsync(
    assessmentResultId
  );

  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    studentGroup?.orgid,
    "clearLastIndividualInterventionScores"
  );
  if (nextAssessmentResultId) {
    await AssessmentResults.removeAsync({
      _id: nextAssessmentResultId
    });
    await StudentGroups.updateAsync(
      { _id: studentGroupId },
      {
        $pull: { currentAssessmentResultIds: nextAssessmentResultId }
      }
    );
  }
  if (currentAssessmentResultId) {
    await StudentGroups.updateAsync(
      { _id: studentGroupId },
      {
        $push: { currentAssessmentResultIds: currentAssessmentResultId },
        $set: { lastModified }
      }
    );
  }
  const currentAssessmentResult = await AssessmentResults.findOneAsync({ _id: currentAssessmentResultId });
  const newScores = currentAssessmentResult.scores.map(({ value, ...score }) => ({
    ...score,
    status: "STARTED"
  }));

  await AssessmentResults.updateAsync(
    { _id: currentAssessmentResultId },
    {
      $set: {
        status: "OPEN",
        scores: newScores,
        lastModified
      },
      $unset: {
        measures: "",
        nextAssessmentResultId: "",
        ruleResults: "",
        lastScoreUpdatedAt: "",
        classwideResults: ""
      }
    }
  );

  const { history } = studentDoc;
  const { assessmentResultMeasures, ...currentSkill } = history.shift();
  const { type, whenEnded, ...filteredCurrentSkill } = currentSkill;
  await Students.updateAsync(
    { _id: studentId },
    {
      $set: {
        history,
        currentSkill: filteredCurrentSkill,
        lastModified
      }
    }
  );
  return {
    student: await Students.findOneAsync({ _id: studentId }),
    studentGroup: await StudentGroups.findOneAsync({ _id: studentGroupId })
  };
}

export const areScoresInSameTargetRange = (oldScore, newScore, targetScores) => {
  const [instructionalTarget, masterTarget, maxTarget] = targetScores;
  if (inRange(oldScore, instructionalTarget) && inRange(newScore, instructionalTarget)) {
    return true;
  }
  if (inRange(oldScore, instructionalTarget, masterTarget) && inRange(newScore, instructionalTarget, masterTarget)) {
    return true;
  }
  return inRange(oldScore, masterTarget, maxTarget) && inRange(newScore, masterTarget, maxTarget);
};

async function editLastIndividualInterventionScores({
  studentGroupId,
  orgid,
  studentId,
  newGoalScoreValue,
  newInterventionScoreValue
}) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId }, { fields: { _id: 1 } });

  if (!studentGroup) {
    throw new Meteor.Error("403", `No student group with id: ${studentGroupId} found`);
  }

  const latestCompletedIndividualAssessmentResult = await AssessmentResults.findOneAsync(
    {
      studentGroupId,
      studentId,
      schoolYear,
      status: "COMPLETED",
      type: "individual"
    },
    { sort: { lastScoreUpdatedAt: -1 } }
  );
  const { lastScoreUpdatedAt, lastModified } = latestCompletedIndividualAssessmentResult;

  const studentDoc = await Students.findOneAsync({ _id: studentId }, { history: 1 });
  if (!(studentDoc && studentDoc.history && studentDoc.history.length)) {
    return false;
  }
  const goalSkillId = latestCompletedIndividualAssessmentResult.individualSkills.benchmarkAssessmentId;
  const interventionSkillId = latestCompletedIndividualAssessmentResult.individualSkills.assessmentId;
  let newScores = [];

  // ensure proper skill order intervention then goal
  const newScoreValues = [];
  if (newInterventionScoreValue >= 0) {
    newScores.push(latestCompletedIndividualAssessmentResult.scores.find(s => s.assessmentId === interventionSkillId));
    newScoreValues.push(newInterventionScoreValue);
  }
  if (newGoalScoreValue >= 0) {
    newScores.push(latestCompletedIndividualAssessmentResult.scores.find(s => s.assessmentId === goalSkillId));
    newScoreValues.push(newGoalScoreValue);
  }
  newScores = newScores.filter(score => score.value); // remove undefined scores

  const scorePkg = newScores.map((elem, index) => ({
    assessmentResultId: latestCompletedIndividualAssessmentResult._id,
    scoreId: newScores[index]._id,
    number_correct: newScoreValues[index].toString(),
    status: "COMPLETE"
  }));

  await clearLastIndividualInterventionScores({ studentGroupId, studentId });
  await updateScores(scorePkg);
  await calculateScoreResult({
    assessmentResultId: latestCompletedIndividualAssessmentResult._id,
    doSave: true
  });

  // Fix date after updating scores
  await AssessmentResults.updateAsync(
    {
      _id: latestCompletedIndividualAssessmentResult._id
    },
    {
      $set: {
        lastScoreUpdatedAt,
        lastModified
      }
    }
  );

  await Students.updateAsync(
    {
      _id: latestCompletedIndividualAssessmentResult.studentId
    },
    {
      $set: {
        "history.0.whenEnded": lastModified,
        lastModified: { ...lastModified, context: "editLastIndividualInterventionScores" }
      }
    }
  );

  return {
    wereScoresEdited: true,
    student: await Students.findOneAsync({ _id: latestCompletedIndividualAssessmentResult.studentId }),
    studentGroup: await StudentGroups.findOneAsync({ _id: latestCompletedIndividualAssessmentResult.studentGroupId })
  };
}

async function shouldDisplayStatistics(siteId, schoolYear) {
  const studentGroups = await StudentGroups.find({ siteId }, { fields: { _id: 1, schoolYear: 1 } }).fetchAsync();
  const studentGroupIds = studentGroups.map(sg => sg._id);
  const availableSchoolYears = uniq(studentGroups.map(sg => sg.schoolYear)).sort();
  let schoolYearData = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const schoolYearIteratee of availableSchoolYears) {
    schoolYearData.push(
      await AssessmentResults.findOneAsync(
        { studentGroupId: { $in: studentGroupIds }, schoolYear: schoolYearIteratee, status: "COMPLETED" },
        { fields: { _id: 1, schoolYear: 1 } }
      )
    );
  }
  schoolYearData = schoolYearData.filter(f => f);
  const matchingSchoolYears = intersection(
    availableSchoolYears,
    schoolYearData.map(sy => sy.schoolYear)
  );
  if (matchingSchoolYears.length) {
    const currentSite = await Sites.findOneAsync({ _id: siteId }, { fields: { isHighSchool: 1 } });
    return {
      siteId,
      schoolYearsWithData: matchingSchoolYears,
      hasDataForSelectedSchoolYear: !!(matchingSchoolYears.find(msy => msy === schoolYear) && currentSite)
    };
  }
  return false;
}

Meteor.methods({
  async "AssessmentResults:updateScores"(scoresPkg, siteId) {
    check(scoresPkg, Array);
    check(siteId, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return updateScores(scoresPkg);
    }
    throw new Meteor.Error("403", "You are not authorized to update scores");
  },
  async clearLastClasswideInterventionScores({ studentGroupId, assessmentId, orgid }) {
    check(studentGroupId, String);
    check(assessmentId, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error("403", "User is not authorized to use clear classwide intervention scores");
    }
    return clearLastClasswideInterventionScores({ studentGroupId, assessmentId });
  },
  async clearLastIndividualInterventionScores({ studentGroupId, studentId, orgid }) {
    check(studentGroupId, String);
    check(studentId, String);
    check(orgid, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error("403", "User is not authorized to use clear individual intervention scores");
    }
    return clearLastIndividualInterventionScores({ studentGroupId, studentId });
  },
  async editLastIndividualInterventionScores({
    studentGroupId,
    orgid,
    studentId,
    newGoalScoreValue,
    newInterventionScoreValue
  }) {
    check(studentGroupId, String);
    check(orgid, String);
    check(studentId, String);
    check(newGoalScoreValue, Number);
    check(newInterventionScoreValue, Number);

    if (newGoalScoreValue < 0 && newInterventionScoreValue < 0) {
      throw new Meteor.Error("403", "Incorrect new score!");
    }

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error("403", "User is not authorized to use edit individual intervention scores");
    }
    return editLastIndividualInterventionScores({
      studentGroupId,
      orgid,
      studentId,
      newGoalScoreValue,
      newInterventionScoreValue
    });
  },
  async calculateScoreResult({ assessmentResultId }) {
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    check(assessmentResultId, String);

    return calculateScoreResult({
      assessmentResultId,
      doSave: false
    });
  },
  async saveScoreResult({ assessmentResultId, isAdditional = false }) {
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    check(assessmentResultId, String);
    return calculateScoreResult({
      assessmentResultId,
      doSave: true,
      isAdditional
    });
  },
  async resumeIndividualIntervention(studentId, siteId) {
    check(studentId, String);
    check(siteId, String);
    if (
      !this.userId ||
      !(await auth.hasAccess(["admin", "universalCoach"], {
        userId: this.userId,
        siteId
      }))
    ) {
      throw new Meteor.Error("403", "You are not authorized to manage individual interventions");
    }
    const studentDocument = await Students.findOneAsync(studentId, {
      fields: { currentSkill: 1, history: 1 }
    });
    const completedBenchmarkGoalSkillsMessageCode = "56";
    if (get(studentDocument, "currentSkill.message.messageCode") === completedBenchmarkGoalSkillsMessageCode) {
      throw new Meteor.Error("403", "The completed individual intervention cannot be resumed.");
    }
    console.log("[resumeIndividualIntervention] Student document:", { studentId, studentDocument });

    // First check for active individual intervention
    const activeIntervention = await AssessmentResults.findOneAsync(
      {
        studentId,
        type: "individual",
        status: "OPEN"
      },
      {
        sort: { "created.on": -1 } // Most recent first
      }
    );

    if (activeIntervention) {
      console.log("[resumeIndividualIntervention] Found active intervention:", activeIntervention._id);
      return calculateScoreResult({
        assessmentResultId: activeIntervention._id,
        doSave: true,
        isResuming: true
      });
    }

    // Fallback to history for completed interventions
    if (!studentDocument.history || studentDocument.history.length === 0) {
      throw new Meteor.Error(
        "no-resumable-intervention",
        "No active or completed individual interventions found for this student"
      );
    }

    const idOfLastCompletedIndividualAssessmentResult = studentDocument.history[0].assessmentResultId;
    console.log("[resumeIndividualIntervention] Resuming from history:", idOfLastCompletedIndividualAssessmentResult);
    return calculateScoreResult({
      assessmentResultId: idOfLastCompletedIndividualAssessmentResult,
      doSave: true,
      isResuming: true
    });
  },
  async calculateClasswideScreeningResults(assessmentResultId) {
    check(assessmentResultId, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    return calculateClasswideScreeningResults(assessmentResultId);
  },
  async clearScreeningScores({ studentGroupId, benchmarkPeriodId, schoolYear }) {
    check(studentGroupId, String);
    check(benchmarkPeriodId, String);
    check(schoolYear, Number);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }

    const curUser = await Users.findOneAsync(this.userId);
    const { orgid } = curUser.profile;

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error("403", "User is not authorized to use clearScreeningScores for this organization");
    }

    return clearScreeningScores(studentGroupId, benchmarkPeriodId, schoolYear);
  },
  async startClasswideIntervention({ studentGroupId, benchmarkPeriodId, siteId }) {
    check(studentGroupId, String);
    check(benchmarkPeriodId, String);
    check(siteId, String);

    if (!this.userId) {
      throw new Meteor.Error("403", "You are not authorized to manage classwide interventions");
    }
    await createClasswideInterventionForStudentGroup({
      studentGroupId,
      benchmarkPeriodId
    });
  },
  async forceClasswideInterventionFor({ studentGroupId, benchmarkAssessmentResultId, siteId }) {
    check(studentGroupId, String);
    check(benchmarkAssessmentResultId, String);
    check(siteId, String);
    if (
      !this.userId ||
      !(await auth.hasAccess(["admin", "universalCoach"], {
        userId: this.userId,
        siteId
      }))
    ) {
      throw new Meteor.Error("forceClasswideIntervention", "You are not authorized to force classwide interventions");
    }
    return forceClasswideInterventionFor({
      studentGroupId,
      benchmarkAssessmentResultId
    });
  },
  async "AssessmentResult:hasGroupOpenIndividualIntervention"(studentGroupId, siteId, orgid) {
    check(studentGroupId, String);
    check(siteId, String);
    check(orgid, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "User is not logged in");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return hasGroupOpenIndividualIntervention(studentGroupId, orgid);
    }
    throw new Meteor.Error(
      "hasGroupOpenIndividualIntervention",
      "You are not authorized to check for active Individual Interventions"
    );
  },
  async "AssessmentResult:hasGroupOpenClasswideIntervention"(studentGroupId, siteId, orgid) {
    check(studentGroupId, String);
    check(siteId, String);
    check(orgid, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "User is not logged in");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return hasGroupOpenClasswideIntervention(studentGroupId, orgid);
    }
    throw new Meteor.Error(
      "hasGroupOpenClasswideIntervention",
      "You are not authorized to check for active Classwide Interventions"
    );
  },
  async "StudentGroup:hasAnyClasswideInterventionsScores"(studentGroupId, siteId, orgid) {
    check(studentGroupId, String);
    check(siteId, String);
    check(orgid, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "User is not logged in");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return hasAnyClasswideInterventionsScores(studentGroupId, orgid);
    }
    throw new Meteor.Error(
      "hasAnyClasswideInterventionsScores",
      "You are not authorized to check the status of the Classwide Intervention"
    );
  },
  async "AssessmentResults:shouldDisplayStatistics"(siteId, orgid, schoolYear) {
    check(siteId, Match.Maybe(String));
    check(orgid, Match.Maybe(String));
    check(schoolYear, Number);

    if (!this.userId) {
      throw new Meteor.Error("403", "User is not logged in");
    }
    if (orgid) {
      const sites = await Sites.find({ orgid }, { fields: { _id: 1 } }).fetchAsync();
      const sitesWithData = [];
      sites.filter(async site => {
        const stats = await shouldDisplayStatistics(site._id, schoolYear);
        if (stats.hasDataForSelectedSchoolYear) {
          sitesWithData.push(stats);
        }
        return stats?.hasDataForSelectedSchoolYear;
      });
      if (sites.length < 2) {
        return {
          schoolYearsWithData: [],
          hasDataForSelectedSchoolYear: false
        };
      }
      return (
        sitesWithData.find(s => s.schoolYearsWithData.length) || {
          schoolYearsWithData: [schoolYear],
          hasDataForSelectedSchoolYear: true
        }
      );
    }
    return shouldDisplayStatistics(siteId, schoolYear);
  },
  async "AssessmentResults:getAssessmentResultsForStudentGroupIds"(studentGroupIds, schoolYear) {
    check(studentGroupIds, Array);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    return AssessmentResults.find(
      { studentGroupId: { $in: studentGroupIds }, schoolYear, isAdditional: { $ne: true } },
      {
        fields: {
          benchmarkPeriodId: 1,
          classwideResults: 1,
          created: 1,
          grade: 1,
          ruleResults: 1,
          status: 1,
          studentGroupId: 1,
          studentId: 1,
          type: 1,
          individualSkills: 1,
          "measures.assessmentId": 1,
          "measures.studentResults": 1,
          "measures.targetScores": 1,
          "measures.medianScore": 1,
          lastModified: 1,
          nextAssessmentResultId: 1,
          scores: 1
        }
      }
    ).fetchAsync();
  },
  async getIndividualRecommendationList(params) {
    check(params, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    return getIndividualInterventionRecommendationList(params);
  },
  async "ManageScores:AssessmentResultsAbsentScores"(studentGroupId, orgid) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(studentGroupId, String);
    check(orgid, String);

    const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
    const assessmentResults = await AssessmentResults.find(
      { studentGroupId, type: "classwide", schoolYear, status: "COMPLETED" },
      { fields: { scores: 1 } }
    ).fetchAsync();
    const studentIds = uniq(
      assessmentResults.map(ar => ar.scores.filter(ars => ars.status === "CANCELLED").map(ars => ars.studentId)).flat(1)
    );
    const studentsById = keyBy(
      (await Students.find({ _id: { $in: studentIds } }, { fields: { "identity.name": 1 } }).fetchAsync()) || [],
      "_id"
    );
    return assessmentResults.map(ar => ({
      _id: ar._id,
      scores: ar.scores.map(ars => {
        const { firstName, lastName } = studentsById[ars.studentId]?.identity.name || {};
        return { ...ars, firstName, lastName };
      })
    }));
  }
});

export default calculateAssessmentResults;
