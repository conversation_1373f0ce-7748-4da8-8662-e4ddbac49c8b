import { keyBy, orderBy, uniq } from "lodash";
import { getStudentName, sortStudentsByRecommendationData } from "../../ui/components/student-groups/helperFunction";
import {
  getRecommendationsForIndividualIntervention,
  getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening,
  getIdsOfHSStudentsWithCompletedAllInterventions
} from "./helpers";

const getStudentResult = ({ student, measure, index, individualInterventionQueueTransferredStudentScores }) => {
  let studentResult = measure.studentResults && measure.studentResults.find(sr => sr.studentId === student._id);
  if (!studentResult && individualInterventionQueueTransferredStudentScores.length) {
    individualInterventionQueueTransferredStudentScores.find(ss => {
      studentResult = ss.assessmentResultMeasures[index].studentResults.find(sr => sr.studentId === student._id);
      return studentResult;
    });
  }
  return studentResult;
};

const getRecommendationReasonData = ({
  student,
  individualInterventionQueueTransferredStudentScores,
  isHighSchoolGroup,
  studentGroupHistory
}) => {
  const mostRecentStudentResults = studentGroupHistory?.[0];
  return isHighSchoolGroup || !mostRecentStudentResults
    ? []
    : mostRecentStudentResults.assessmentResultMeasures.map((m, i) => {
        const studentResult = getStudentResult({
          student,
          measure: m,
          index: i,
          individualInterventionQueueTransferredStudentScores
        });
        const reasonTitle = m.assessmentName || `Measure ${i + 1}`;
        return {
          reasonTitle,
          columnData: {
            Score: (studentResult && studentResult.score) || "N/A",
            Target: m.targetScores[0]
          },
          showRed: !studentResult || !studentResult.score || studentResult.score < m.targetScores[0]
        };
      });
};

const getStudentRecommendationData = ({
  student,
  studentRecommendations,
  individualInterventionQueueTransferredStudentScores,
  isHighSchoolGroup,
  studentGroupHistory
}) => ({
  studentName: getStudentName(student),
  studentId: student._id,
  grade: student.grade,
  recommendationReasonData: getRecommendationReasonData({
    student,
    individualInterventionQueueTransferredStudentScores,
    isHighSchoolGroup,
    studentGroupHistory
  }),
  numberOfRecommendations: studentRecommendations[student._id]
});

export const getIndividualInterventionRecommendationList = async ({
  history,
  grade,
  schoolYear,
  students,
  isHighSchoolGroup,
  individualInterventionStudentIds,
  individualInterventionQueueStudentIds,
  individualInterventionQueueTransferredStudentScores
}) => {
  const classwideInterventionHistoryItems = history?.filter(item => item.type === "classwide");
  const currentHistory = [];
  const recommendationHistory = [];
  let missingScores = [];
  classwideInterventionHistoryItems?.reverse().forEach(historyItem => {
    currentHistory.unshift(historyItem);
    const recommendations = getRecommendationsForIndividualIntervention({
      history: currentHistory,
      currentDate: new Date(historyItem.whenEnded.date)
    });
    const missingScoresCountStudentList = [];
    Object.entries(
      recommendations?.missingScoresRecommendationsResult?.absentWhenMovedUpStudentCountByStudentId || {}
    ).forEach(([studentId, { totalAbsentCount }]) => {
      if (
        !individualInterventionStudentIds.includes(studentId) &&
        recommendations.missingScoresRecommendationsResult.missingScoresRecommendations.includes(studentId)
      ) {
        missingScoresCountStudentList.push({ studentId, totalAbsentCount });
      }
    });
    recommendationHistory.push(
      recommendations?.idsOfStudentsInFrustrationalRangeInPast
        ? recommendations.idsOfStudentsInFrustrationalRangeInPast
        : []
    );
    // NOTE(fmazur) - We only need last iteration result for missing scores recommendations
    missingScores = missingScoresCountStudentList || [];
  });

  const totalRecommendationHistory = recommendationHistory.flat();
  const studentIdsWithCompletedIndividualInterventionForMostRecentScreening =
    grade !== "HS"
      ? await getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening({
          studentIds: uniq(totalRecommendationHistory),
          schoolYear
        })
      : [];

  const idsOfStudentsWithMissingScores = missingScores.map(ms => ms.studentId);
  const studentsRecommendedInGroup = students.filter(
    s =>
      !individualInterventionStudentIds.includes(s._id) &&
      !studentIdsWithCompletedIndividualInterventionForMostRecentScreening.includes(s._id) &&
      (individualInterventionQueueStudentIds.includes(s._id) || idsOfStudentsWithMissingScores.includes(s._id))
  );

  const studentRecommendations = totalRecommendationHistory.reduce((a, v) => {
    if (v) {
      // eslint-disable-next-line no-param-reassign
      a[v] = (a[v] || 0) + 1;
    }
    return a;
  }, {});

  const studentRecommendationData = studentsRecommendedInGroup.map(student =>
    getStudentRecommendationData({
      student,
      studentRecommendations,
      individualInterventionQueueTransferredStudentScores,
      isHighSchoolGroup,
      studentGroupHistory: history
    })
  );
  const studentDataByStudentId = keyBy(studentRecommendationData, "studentId");

  const extendedStudentMissingScores = missingScores.map(ms => {
    const studentData = studentDataByStudentId[ms.studentId];
    return {
      ...studentData,
      ...ms
    };
  });
  missingScores = orderBy(extendedStudentMissingScores, ["totalAbsentCount", "studentName"], ["desc", "asc"]).filter(
    ms => !studentIdsWithCompletedIndividualInterventionForMostRecentScreening.includes(ms.studentId)
  );
  const studentIds = students.map(({ _id }) => _id);
  const hsStudentsThatCompletedAllSkillTrees = await getIdsOfHSStudentsWithCompletedAllInterventions(studentIds);

  return [
    ...sortStudentsByRecommendationData(
      studentRecommendationData.filter(s => !missingScores.map(ms => ms.studentId).includes(s.studentId))
    ),
    ...missingScores
  ].filter(s => studentIds.includes(s.studentId) && !hsStudentsThatCompletedAllSkillTrees.includes(s.studentId));
};
