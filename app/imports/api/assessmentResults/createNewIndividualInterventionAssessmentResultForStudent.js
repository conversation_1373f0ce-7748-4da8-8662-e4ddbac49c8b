import { Random } from "meteor/random";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { assignToStudentsOrGroup, insertAssessmentsAndScores } from "./helpers";
import { AssessmentResults } from "./assessmentResults";
import { getMeteorUserId } from "../utilities/utilities";

export async function createNewIndividualInterventionAssessmentResultForStudent({
  previousAssessmentResult,
  studentId,
  skills,
  rootRuleId
}) {
  const { orgid } = previousAssessmentResult;
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);
  const individualSkills = skills;
  const assessmentResult = {
    benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId,
    orgid,
    created: bdo,
    schoolYear: previousAssessmentResult.schoolYear,
    status: "OPEN",
    studentGroupId: previousAssessmentResult.studentGroupId,
    studentId,
    type: "individual",
    previousAssessmentResultId: previousAssessmentResult._id,
    grade: previousAssessmentResult.grade,
    rootRuleId
  };

  const id = await AssessmentResults.insertAsync(assessmentResult);
  const { siteId } = previousAssessmentResult.scores[0];
  const assessmentIds = [];
  if (individualSkills.assessmentId) assessmentIds.push(individualSkills.assessmentId);
  if (individualSkills.benchmarkAssessmentId) assessmentIds.push(individualSkills.benchmarkAssessmentId);
  const scores = assessmentIds.map(assessmentId => ({
    _id: Random.id(),
    assessmentId,
    orgid,
    siteId,
    status: "STARTED",
    studentId
  }));
  individualSkills.assessmentResultId = id;
  if (!individualSkills.interventions) {
    individualSkills.interventions = [];
  }

  await insertAssessmentsAndScores({
    assessmentResultId: id,
    assessmentIds,
    scores,
    individualSkills
  });
  const newAssessmentResult = await AssessmentResults.findOneAsync(id);

  return assignToStudentsOrGroup({
    assessmentResult: newAssessmentResult,
    previousAssessmentResult,
    individualSkills
  });
}
