import { assert } from "chai";
import MockDate from "mockdate";
import { take } from "lodash";
import {
  areScoresInSameTargetRange,
  calculateClasswideScreeningResults,
  checkForAdditionsToIndividualInterventionQueue,
  clearScreeningScores,
  forceClasswideInterventionFor,
  getIndividualInterventionQueueBasedOnFourWeekRule,
  getClasswideResultsMessage,
  clearLastIndividualInterventionScores,
  hasGroupOpenIndividualIntervention,
  updateScores
} from "./methods";
import { Rules } from "../rules/rules";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { StudentGroups } from "../studentGroups/studentGroups";
import { AssessmentResults } from "./assessmentResults";
import { Assessments } from "../assessments/assessments";
import {
  activeScreeningAssessment,
  assessmentResultWithNoScores,
  benchmarkPeriodId,
  classwideFailedScreeningAssessment,
  classwideInterventionId,
  classwideInterventionStarted,
  classwideInterventionWithScores,
  completeScreeningAssessment,
  faultyAssessmentResult,
  firstSkillIdForGrade1,
  firstSkillIdForGrade1Assessment,
  getFakeClasswideAssessmentResults,
  getFakeClasswideRuleResults,
  getFakeCurrentClasswideSkill,
  getFakeIndividualsUpdateSet,
  individuallyFailedScreeningAssessment,
  passedScreeningResult,
  rulesForGrade,
  schoolYear,
  studentGroupInClasswideIntervention,
  studentGroupWithActiveScreening,
  studentGroupWithCompleteScreening,
  studentGroupWithInterventionEligibility,
  studentGroupWithProgressedClasswideIntervention,
  studentWithIndividualInterventionEligibility,
  testAssessmentResultId,
  testStudentGroupId,
  assessmentIds,
  generateAssessment,
  generateAssessmentResult
} from "../../test-helpers/data/assessmentResults.testData";
import { buildAssessmentResult, buildHistory } from "../../test-helpers/data/fourWeekRuleDataGenerators";
import { getIndividualResultsMessage, getPercent } from "./utilities";
import { BenchmarkPeriods } from "../benchmarkPeriods/benchmarkPeriods";
import { getBenchmarkPeriods } from "../../test-helpers/data/benchmarkPeriods";
import { Students } from "../students/students";
import BenchmarkPeriodHelpers from "../benchmarkPeriods/methods";

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => schoolYear),
  idValidation: { regex: /.*/, description: "" }
}));

describe("imports/api/assessmentResults/methods.js tests", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("getPercent", () => {
    it("Should return 50 when the numerator is 10 and the denominator is 20", () => {
      assert.equal(getPercent(10, 20), 50);
    });

    it("Should return 0 when the numerator is 0 and the denominator is 20", () => {
      assert.equal(getPercent(0, 20), 0);
    });

    it("Should return null when the numerator is null", () => {
      assert.equal(getPercent(null, 20), null);
    });

    it("Should return null when the denominator is null", () => {
      assert.equal(getPercent(10, null), null);
    });

    it("Should return null when the numerator is 10 and the denominator is -10", () => {
      assert.equal(getPercent(10, -10), null);
    });

    it("Should return null when the numerator is -10 and the denominator is 10", () => {
      assert.equal(getPercent(-10, 10), null);
    });
  });

  describe("getClasswideResultsMessage", () => {
    it("Should return an object", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        getFakeClasswideRuleResults("newAssessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 12)
      );
      assert.equal("object", typeof message);
    });

    it("When a class is first scheduled and has no currentSkill it should return a code of 1", () => {
      const message = getClasswideResultsMessage(
        null,
        getFakeClasswideRuleResults("newAssessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 12)
      );
      assert.equal(message.messageCode, "1", "Should be code 1");
    });

    it("When a class completes a new screening new window and continues classwide interventions it should return a code of 7", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        getFakeClasswideRuleResults("newAssessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 12, "spring")
      );
      assert.equal(message.messageCode, "7", "Should be code 7");
    });

    it("When a class receives a score in the acquisition range it should return a code of 2", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        getFakeClasswideRuleResults("assessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 8)
      );
      assert.equal(message.messageCode, "2", "Should be code 2");
    });

    it("When a class receives a score in the fluency range it should return a code of 3", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        getFakeClasswideRuleResults("assessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 12)
      );
      assert.equal(message.messageCode, "3", "Should be code 3");
    });

    it("When a class moves on to a new intervention skill it should return a code 4", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        getFakeClasswideRuleResults("newAssessmentId"),
        getFakeClasswideAssessmentResults("assessmentId", 12)
      );
      assert.equal(message.messageCode, "4", "Should be code 4");
    });

    it("When a class finishes all classwide interventions it should return a code 5", () => {
      const message = getClasswideResultsMessage(
        getFakeCurrentClasswideSkill("assessmentId", 10, 20),
        { nextSkill: null },
        getFakeClasswideAssessmentResults("assessmentId", 12)
      );
      assert.equal(message.messageCode, "5", "Should be code 5");
    });
  });

  describe("getIndividualResultsMessage", () => {
    it("Should return an object", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "newAssessmentId",
          [], // new interventions
          "oldAssessmentId",
          "oldBMID",
          [], // old interventions
          10,
          20,
          13
        )
      );
      assert.equal("object", typeof message);
    });

    it("When a student is assigned a drilldown and has not done interventions in this season yet it should return a code 51", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "newAssessmentId",
          [], // new interventions
          null,
          null,
          [], // old interventions
          10,
          20,
          13
        )
      );
      assert.equal(message.messageCode, "51", "Should be code 51");
    });

    it(
      "When a student is assigned the same assessment for interventions and their" +
        "previous score was in the acquisition range it it should return a code 52",
      () => {
        const message = getIndividualResultsMessage(
          getFakeIndividualsUpdateSet(
            "assessmentId",
            "assessmentId",
            ["int"], // new interventions
            "assessmentId",
            "assessmentId",
            ["int"], // old interventions
            9,
            10,
            20
          )
        );
        assert.equal(message.messageCode, "52", "Should be code 52");
      }
    );

    it(
      "When a student is assigned the same assessment for interventions and their" +
        "previous score was in the fluency range it it should return a code 53",
      () => {
        const message = getIndividualResultsMessage(
          getFakeIndividualsUpdateSet(
            "assessmentId",
            "assessmentId",
            ["int"], // new interventions
            "assessmentId",
            "assessmentId",
            ["int"], // old interventions
            12,
            10,
            20
          )
        );
        assert.equal(message.messageCode, "53", "Should be code 53");
      }
    );

    it("When a student is assigned a new assessment for interventions but their goal skill is the same it should return a code 54", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "assessmentId",
          "bmAssessmentId",
          ["int"], // new interventions
          "newAssessmentId",
          "bmAssessmentId",
          ["int"], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "54", "Should be code 54");
    });

    it("When a student is assigned a new goal skill in drilldown it should return a code 55", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newBMAssessmentId",
          "newBMAssessmentId",
          [], // new interventions
          "assessmentId",
          "oldBMAssessmentId",
          ["int"], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "55", "Should be code 55");
    });

    it("When a student has completed all goal skills and is done with the season it should return a code 56", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          null,
          null,
          null, // new interventions
          "assessmentId",
          "oldBMAssessmentId",
          ["int"], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "56", "Should be code 56");
    });

    it("When a student has been assigned a drilldown and is coming from an intervention it should return a code 57", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "oldBMAssessmentId",
          [], // new interventions
          "assessmentId",
          "oldBMAssessmentId",
          ["int"], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "57", "Should be code 57");
    });

    it("When a student has been assigned interventions and has not done anything in this season yet it should return a code of 58", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "newBMAssessmentId",
          ["int"], // new interventions
          null,
          null,
          null, // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "58", "Should be code 58");
    });

    it("When a student has been assigned interventions after a drilldown it should return a code 59", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "oldBMAssessmentId",
          ["int"], // new interventions
          "assessmentId",
          "oldBMAssessmentId",
          [], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "59", "Should be code 59");
    });

    it("When a student is assigned the the goal skill as their intervention it should return a code 60", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "newAssessmentId",
          ["int"], // new interventions
          "assessmentId",
          "newAssessmentId",
          ["int"], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "60", "Should be code 60");
    });

    it("When a student is assigned a drilldown and is coming from another drilldown it should return code 61", () => {
      const message = getIndividualResultsMessage(
        getFakeIndividualsUpdateSet(
          "newAssessmentId",
          "newAssessmentId",
          [], // new interventions
          "assessmentId",
          "newAssessmentId",
          [], // old interventions
          9,
          10,
          20
        )
      );
      assert.equal(message.messageCode, "61", "Should be code 61");
    });
  });

  describe("checkForAdditionsToIndividualInterventionQueue", () => {
    it("should return a boolean", () => {
      const result = checkForAdditionsToIndividualInterventionQueue();
      assert.equal(typeof result, "boolean", "Should be boolean");
    });

    it("should return true when a student has been added to a empty queue", () => {
      const previousIndividualInterventionQueue = [];
      const newIndividualInterventionQueue = ["someStudentId"];
      const result = checkForAdditionsToIndividualInterventionQueue(
        previousIndividualInterventionQueue,
        newIndividualInterventionQueue
      );
      assert.equal(result, true, "Should be true");
    });

    it("should return true when a new student has been added to a queue", () => {
      const previousIndividualInterventionQueue = ["studentId1"];
      const newIndividualInterventionQueue = ["studentId1", "studentId2"];
      const result = checkForAdditionsToIndividualInterventionQueue(
        previousIndividualInterventionQueue,
        newIndividualInterventionQueue
      );
      assert.equal(result, true, "Should be true");
    });

    it("should return false when no students have been added to a previously empty queue", () => {
      const previousIndividualInterventionQueue = [];
      const newIndividualInterventionQueue = [];
      const result = checkForAdditionsToIndividualInterventionQueue(
        previousIndividualInterventionQueue,
        newIndividualInterventionQueue
      );
      assert.equal(result, false, "Should be false");
    });

    it("should return false when the new queue contains the same students as the previous queue", () => {
      const previousIndividualInterventionQueue = ["studentId1", "studentId2"];
      const newIndividualInterventionQueue = ["studentId1", "studentId2"];
      const result = checkForAdditionsToIndividualInterventionQueue(
        previousIndividualInterventionQueue,
        newIndividualInterventionQueue
      );
      assert.equal(result, false, "Should be false");
    });
  });

  describe("getIndividualInterventionQueueBasedOnFourWeekRule", () => {
    const defaultTestStudentGroup = {
      _id: testStudentGroupId,
      history: []
    };

    describe("when there are no histories for the student group", () => {
      it("should not modify the intervention queue and it should return undefined", async () => {
        const assessmentResult = buildAssessmentResult({
          numberOfStudents: 5,
          failingIndexes: [1],
          assessmentId: assessmentIds[0]
        });
        const studentGroup = {
          ...defaultTestStudentGroup
        };
        const testResult = await getIndividualInterventionQueueBasedOnFourWeekRule({
          studentGroup,
          assessmentResult
        });
        assert.isUndefined(testResult);
      });
    });
    describe("when there are histories for the student group", () => {
      // 3 weeks worth of history which is not enough to trigger the fourWeekRule
      describe("when there are under the minimum weeks required for the four week rule", () => {
        it("should not modify the intervention queue and it should return undefined", async () => {
          const numberOfWeeks = 2;
          const assessmentIdsQueue = [...assessmentIds];
          const studentGroup = {
            ...defaultTestStudentGroup,
            history: buildHistory({
              numberOfWeeks,
              failingIndexes: [2],
              numberOfStudents: 5,
              assessments: take(assessmentIdsQueue, numberOfWeeks)
            })
          };
          const assessmentResult = buildAssessmentResult({
            numberOfStudents: 5,
            failingIndexes: [2],
            assessmentId: take(assessmentIdsQueue)[0]
          });
          const testResult = await getIndividualInterventionQueueBasedOnFourWeekRule({
            studentGroup,
            assessmentResult
          });
          assert.isUndefined(testResult);
        });
      });

      describe("when there are over the minimum weeks required for the four week rule", () => {
        describe("when the class isn't moving up use most recent passing history item for recommendations", () => {
          it("should return intervention queue based on most recent passing history item", async () => {
            const numberOfWeeks = 5;
            const assessmentIdsQueue = [...assessmentIds];
            const studentGroup = {
              ...defaultTestStudentGroup,
              history: buildHistory({
                numberOfWeeks,
                failingIndexes: [2],
                numberOfStudents: 5,
                assessments: take(assessmentIdsQueue, numberOfWeeks)
              })
            };
            const assessmentResult = buildAssessmentResult({
              numberOfStudents: 5,
              failingIndexes: [1, 2, 3],
              assessmentId: take(assessmentIdsQueue)[0]
            });
            const testResult = await getIndividualInterventionQueueBasedOnFourWeekRule({
              studentGroup,
              assessmentResult
            });
            expect(testResult).toEqual(["studentId1", "studentId2", "studentId3"]);
          });
        });

        describe("when the class is moving up for the current skill", () => {
          it("should modify the intervention queue and it should return an array with an id of a student recommended for an individual intervention", async () => {
            const numberOfWeeks = 5;
            const assessmentIdsQueue = [...assessmentIds];
            const studentGroup = {
              ...defaultTestStudentGroup,
              history: buildHistory({
                numberOfWeeks,
                failingIndexes: [2],
                numberOfStudents: 5,
                assessments: take(assessmentIdsQueue, numberOfWeeks)
              })
            };
            const assessmentResult = buildAssessmentResult({
              numberOfStudents: 5,
              failingIndexes: [2],
              assessmentId: take(assessmentIdsQueue)[0]
            });
            const testResult = await getIndividualInterventionQueueBasedOnFourWeekRule({
              studentGroup,
              assessmentResult
            });
            assert.isArray(testResult);
            assert.lengthOf(testResult, 1);
          });

          it("should return an empty array when student was absent when the class moved up a skill and the most recent score was in the frustrational range but 4 weeks haven't passed yet for that score", async () => {
            const numberOfWeeks = 4;
            const assessmentIdsQueue = [...assessmentIds];
            const [assessmentId] = take(assessmentIdsQueue);
            const studentGroup = {
              ...defaultTestStudentGroup,
              history: buildHistory({
                numberOfWeeks,
                failingIndexes: [2, 3, 4],
                numberOfStudents: 5,
                assessments: assessmentId
              })
            };
            const assessmentResult = buildAssessmentResult({
              numberOfStudents: 4,
              failingIndexes: [],
              assessmentId
            });
            const testResult = await getIndividualInterventionQueueBasedOnFourWeekRule({
              studentGroup,
              assessmentResult
            });
            assert.isArray(testResult);
            assert.lengthOf(testResult, 0);
          });
        });
      });
    });
  });

  describe("clearScreeningScores", () => {
    beforeAll(async () => {
      await StudentGroupEnrollments.insertAsync([
        {
          studentGroupId: testStudentGroupId,
          studentId: "firstStudentId",
          schoolYear,
          isActive: true
        },
        {
          studentGroupId: testStudentGroupId,
          studentId: "secondStudentId",
          schoolYear,
          isActive: true
        }
      ]);
    });

    afterEach(async () => {
      await StudentGroups.removeAsync({});
      await AssessmentResults.removeAsync({});
    });

    afterAll(async () => {
      await StudentGroupEnrollments.removeAsync({});
    });

    it("should clear screening scores from assessment results if benchmark is in progress", async () => {
      await StudentGroups.insertAsync(studentGroupWithActiveScreening);
      await AssessmentResults.insertAsync(activeScreeningAssessment);

      await clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear);

      const screeningAssessment = await AssessmentResults.findOneAsync(activeScreeningAssessment._id);
      const studentGroupData = await StudentGroups.findOneAsync(testStudentGroupId);
      const hasNoCompleteScores =
        screeningAssessment.scores && screeningAssessment.scores.every(score => score.status === "STARTED");
      assert.isTrue(hasNoCompleteScores);
      assert.equal(screeningAssessment.status, "OPEN", "Screening assessment should keep OPEN status");
      assert.equal(screeningAssessment.scores.length, 6, "Expected 6 scores - 2 for each student");
      assert.include(
        studentGroupData.currentAssessmentResultIds,
        testAssessmentResultId,
        "The cleared screening id should still appear in group's currentAssessmentResultIds"
      );
    });

    it("should move the completed benchmark back to open status and remove the benchmark from the group's history", async () => {
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);
      await AssessmentResults.insertAsync(completeScreeningAssessment);

      await clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear);

      const screeningAssessment = await AssessmentResults.findOneAsync(completeScreeningAssessment._id);
      const studentGroupData = await StudentGroups.findOneAsync({
        _id: testStudentGroupId
      });
      const hasNoCompleteScores = screeningAssessment.scores.every(score => score.status === "STARTED");
      assert.isTrue(hasNoCompleteScores);
      assert.equal(screeningAssessment.status, "OPEN", "Screening assessment should have OPEN status");
      assert.include(
        studentGroupData.currentAssessmentResultIds,
        testAssessmentResultId,
        "The cleared screening should reappear in group's currentAssessmentResultIds"
      );
      assert.notInclude(
        studentGroupData.history,
        { assessmentResultId: testAssessmentResultId },
        "The studentGroup should not have history for cleared screening assessment"
      );
      assert.notProperty(screeningAssessment, "measures", "Active screening should not have measures property");
      assert.notProperty(
        screeningAssessment,
        "classwideResults",
        "Active screening  should not have classwideResults property"
      );
      assert.notProperty(screeningAssessment, "ruleResults", "Active screening  should not have ruleResults property");
    });

    it("should remove Classwide Intervention based on the cleared screening result if there were no scores entered", async () => {
      await StudentGroups.insertAsync(studentGroupInClasswideIntervention);
      await AssessmentResults.insertAsync(classwideFailedScreeningAssessment);
      await AssessmentResults.insertAsync(classwideInterventionStarted);

      await clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear);

      const studentGroupData = await StudentGroups.findOneAsync({
        _id: testStudentGroupId
      });
      const classwideResult = await AssessmentResults.findOneAsync(classwideInterventionStarted._id);
      const screeningResult = await AssessmentResults.findOneAsync(classwideFailedScreeningAssessment._id);
      assert.equal(classwideResult, null, "Classwide Intervention should be removed");
      assert.notProperty(
        screeningResult,
        "nextAssessmentResultId",
        "Screening assessment should have nextAssessmentResultId property removed if the classwide intervention is removed"
      );
      assert.notInclude(
        studentGroupData.currentAssessmentResultIds,
        classwideInterventionStarted._id,
        "StudentGroup should have removed classwide intervention id from currentAssessmentResultIds"
      );
      assert.notProperty(
        studentGroupData,
        "currentClasswideSkill",
        "Student group should have currentClasswideSkill property removed if the classwide intervention is removed"
      );
    });

    it("should not remove Classwide Intervention based on the cleared screening if the classwide intervention was progressed", async () => {
      await StudentGroups.insertAsync(studentGroupWithProgressedClasswideIntervention);
      await AssessmentResults.insertAsync(classwideFailedScreeningAssessment);
      await AssessmentResults.insertAsync(classwideInterventionWithScores);

      await clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear);

      const studentGroupData = await StudentGroups.findOneAsync(studentGroupWithProgressedClasswideIntervention._id);
      const classwideResult = await AssessmentResults.findOneAsync(classwideInterventionWithScores._id);
      const assessmentResult = await AssessmentResults.findOneAsync(classwideFailedScreeningAssessment._id);
      assert.deepEqual(
        classwideResult,
        classwideInterventionWithScores,
        "The classwide intervention should not be removed"
      );
      assert.equal(
        studentGroupData.currentAssessmentResultIds.length,
        2,
        "The studentGroup should have both the classwide intervention and the benchmark assessment in currentAssessmentResultIds"
      );
      assert.include(
        studentGroupData.currentAssessmentResultIds,
        "nextClasswideIntervention",
        "The active classwide intervention should stay in currentAssessmentResultIds if the classwide intervention is not getting removed"
      );
      assert.equal(
        assessmentResult.nextAssessmentResultId,
        classwideInterventionId,
        "The screening assessment should keep nextAssessmentResultId property pointing to the completed classwide intervention"
      );
      assert.include(
        studentGroupData.history,
        { assessmentResultId: classwideInterventionId, type: "classwide" },
        "The studentGroup should keep history of the classwide skill progressed"
      );
      assert.property(
        studentGroupData,
        "currentClasswideSkill",
        "Student group should keep currentClasswideSkill property if the classwide intervention was not removed"
      );
    });

    it("should remove individual intervention eligibility for students that failed the cleared screening", async () => {
      await StudentGroups.insertAsync(studentGroupWithInterventionEligibility);
      await AssessmentResults.insertAsync(individuallyFailedScreeningAssessment);

      await clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear);

      const studentGroupData = await StudentGroups.findOneAsync(studentGroupWithInterventionEligibility._id);
      assert.notInclude(
        studentGroupData.individualInterventionQueue,
        studentWithIndividualInterventionEligibility,
        "Student scheduled for individual intervention after cleared screening is still scheduled for intervention"
      );
      assert.equal(
        studentGroupData.individualInterventionQueue.length,
        1,
        "Individual intervention queue array should contain one student id"
      );
    });

    it("should throw an error when there is no StudentGroup with given ID", async () => {
      const expectedErrorMessage = "Student group: TEST_STUDENT_GROUP_1 not found.";

      await expect(clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear)).rejects.toThrow(
        expectedErrorMessage
      );
    });

    it("should throw an error when there is no AssessmentResult for this studentGroupId", async () => {
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);

      const expectedErrorMessage = "No screening assessment was found in the provided benchmark period";

      await expect(clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear)).rejects.toThrow(
        expectedErrorMessage
      );
    });

    it("should throw an error when there is no scores in AssessmentResult", async () => {
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);
      await AssessmentResults.insertAsync(assessmentResultWithNoScores);

      const expectedErrorMessage = "AssessmentResults doesn't have any scores.";

      await expect(clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear)).rejects.toThrow(
        expectedErrorMessage
      );
    });

    it("should throw an error if there is more than one screening assessment in the provided benchmark period", async () => {
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);
      await AssessmentResults.insertAsync([assessmentResultWithNoScores, faultyAssessmentResult]);

      const expectedErrorMessage = "More than one screening assessment found in the provided benchmark period";

      await expect(clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear)).rejects.toThrow(
        expectedErrorMessage
      );
    });

    it("should throw an error if the group does not have any active enrollment", async () => {
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);
      await AssessmentResults.insertAsync(individuallyFailedScreeningAssessment);
      await StudentGroupEnrollments.removeAsync({});

      const expectedErrorMessage = "The group does not have any actively enrolled student.";

      await expect(clearScreeningScores(testStudentGroupId, benchmarkPeriodId, schoolYear)).rejects.toThrow(
        expectedErrorMessage
      );
    });
  });

  describe("forceClasswideInterventionFor", () => {
    beforeAll(async () => {
      await Rules.insertAsync(rulesForGrade);
      await BenchmarkPeriods.insertAsync(getBenchmarkPeriods());
      jest.spyOn(BenchmarkPeriodHelpers, "getBenchmarkPeriodByDate").mockResolvedValue({
        _id: "nEsbWokBWutTZFkTh",
        name: "Winter",
        label: "winter-period",
        sortOrder: 2
      });
    });
    afterEach(async () => {
      await StudentGroups.removeAsync({});
      await AssessmentResults.removeAsync({});
    });
    afterAll(async () => {
      await Rules.removeAsync({});
      await Assessments.removeAsync({});
      await BenchmarkPeriods.removeAsync({});
      jest.restoreAllMocks();
    });
    it("should throw an error if group has already taken some classwide assessments", async () => {
      await StudentGroups.insertAsync(studentGroupWithProgressedClasswideIntervention);
      await AssessmentResults.insertAsync(completeScreeningAssessment);

      await expect(
        forceClasswideInterventionFor({
          studentGroupId: testStudentGroupId,
          benchmarkAssessmentResultId: testAssessmentResultId
        })
      ).rejects.toThrow(/Cannot force classwide intervention for the studentGroupId:/);
    });

    it("should make overriding rules and forcing classwide assessment on groups with at least 2 students possible", async () => {
      await Assessments.insertAsync(firstSkillIdForGrade1Assessment);
      await StudentGroups.insertAsync(studentGroupWithCompleteScreening);
      await AssessmentResults.insertAsync(passedScreeningResult);
      const studentGroupHistoryLength = studentGroupWithCompleteScreening.history.length;
      const studentGroupId = studentGroupWithCompleteScreening._id;
      const benchmarkAssessmentResultId = passedScreeningResult._id;

      await forceClasswideInterventionFor({
        studentGroupId,
        benchmarkAssessmentResultId
      });

      const assessmentResult = await AssessmentResults.findOneAsync({
        type: "classwide",
        status: "OPEN",
        studentGroupId
      });
      const newlyAddedResultId = assessmentResult._id;
      expect(newlyAddedResultId).toBeTruthy();

      const modifiedStudentGroup = await StudentGroups.findOneAsync(studentGroupId);
      expect(modifiedStudentGroup.currentAssessmentResultIds[0]).toEqual(newlyAddedResultId);
      expect(modifiedStudentGroup.currentClasswideSkill.assessmentResultId).toEqual(newlyAddedResultId);
      expect(modifiedStudentGroup.currentClasswideSkill.assessmentId).toEqual(firstSkillIdForGrade1);
      expect(modifiedStudentGroup.history.length).toEqual(studentGroupHistoryLength);

      const modifiedBenchmarkAssessment = await AssessmentResults.findOneAsync(benchmarkAssessmentResultId);
      expect(modifiedBenchmarkAssessment.nextAssessmentResultId).toEqual(newlyAddedResultId);
      expect(modifiedBenchmarkAssessment.ruleResults.passed).toEqual(false);
      expect(modifiedBenchmarkAssessment.ruleResults.nextSkill.assessmentId).toEqual(firstSkillIdForGrade1);
    });
  });

  describe("hasGroupOpenIndividualIntervention", () => {
    const studentGroupId = "studentGroupId";
    const otherStudentGroupId = "otherStudentGroupId";
    const assessmentResults = [
      {
        schoolYear,
        status: "OPEN",
        studentGroupId,
        type: "individual"
      },
      {
        schoolYear,
        status: "COMPLETED",
        studentGroupId,
        type: "individual"
      }
    ];

    beforeEach(async () => {
      await AssessmentResults.insertAsync(assessmentResults);
    });
    afterEach(async () => {
      await AssessmentResults.removeAsync({});
    });
    it("should return true if there are AssessmentResults with open individual interventions", async () => {
      expect(await hasGroupOpenIndividualIntervention(studentGroupId)).toEqual(true);
    });
    it("should return false if there are no AssessmentResults with open individual interventions", async () => {
      expect(await hasGroupOpenIndividualIntervention(otherStudentGroupId)).toEqual(false);
    });
  });

  describe("areScoresInSameTargetRange", () => {
    it("should return true if both scores are below the instructional target", () => {
      expect(areScoresInSameTargetRange(0, 19, [20, 40, 200])).toEqual(true);
    });

    it("should return true if both scores are between the instructional and mastery targets", () => {
      expect(areScoresInSameTargetRange(20, 39, [20, 40, 200])).toEqual(true);
    });

    it("should return true if both scores are above the mastery target", () => {
      expect(areScoresInSameTargetRange(40, 150, [20, 40, 200])).toEqual(true);
    });

    it("should return false if one score is in a different range than the other one", () => {
      expect(areScoresInSameTargetRange(5, 30, [20, 40, 200])).toEqual(false);
      expect(areScoresInSameTargetRange(5, 80, [20, 40, 200])).toEqual(false);
      expect(areScoresInSameTargetRange(30, 5, [20, 40, 200])).toEqual(false);
      expect(areScoresInSameTargetRange(80, 5, [20, 40, 200])).toEqual(false);
      expect(areScoresInSameTargetRange(30, 80, [20, 40, 200])).toEqual(false);
      expect(areScoresInSameTargetRange(80, 30, [20, 40, 200])).toEqual(false);
    });
  });

  describe("calculateClasswideScreeningResults", () => {
    const assessmentId = firstSkillIdForGrade1Assessment._id;
    const assessmentResultId = "assessmentResultId";
    const grade = "01";
    const studentGroupId = "studentGroupId";
    const studentId = "studentId";
    const studentId2 = "studentId2";
    const studentId3 = "studentId3";
    const fallBenchmarkPeriodId = "8S52Gz5o85hRkECgq";

    const studentGroup = {
      _id: studentGroupId,
      grade,
      currentAssessmentResultIds: [],
      individualInterventionQueue: [studentId]
    };
    const student = {
      _id: studentId,
      identity: {
        name: { firstName: "F", lastName: "L" }
      }
    };
    const student2 = {
      _id: studentId2,
      identity: {
        name: { firstName: "F2", lastName: "L2" }
      }
    };
    const student3 = {
      _id: studentId3,
      identity: {
        name: { firstName: "F3", lastName: "L3" }
      }
    };

    beforeAll(async () => {
      await BenchmarkPeriods.insertAsync(getBenchmarkPeriods());
      await Students.insertAsync([student, student2, student3]);
    });
    afterAll(async () => {
      await BenchmarkPeriods.removeAsync({});
      await Students.removeAsync({});
    });

    beforeEach(async () => {
      await StudentGroups.insertAsync(studentGroup);
    });

    afterEach(async () => {
      await Assessments.removeAsync({});
      await AssessmentResults.removeAsync({});
      await StudentGroups.removeAsync({});
    });

    it("should return null if specified assessment result doesn't exist", async () => {
      expect(await calculateClasswideScreeningResults(assessmentResultId)).toEqual(null);
    });
    describe("when a student group hasn't got any classwide intervention", () => {
      it("should keep previous Individual Intervention recommendations when processing current screening", async () => {
        const assessmentResult = generateAssessmentResult({
          _id: assessmentResultId,
          studentGroupId,
          type: "benchmark",
          assessmentIds: [assessmentId],
          scores: [
            { studentId, value: 3 },
            { studentId: studentId2, value: 4 },
            { studentId: studentId3, value: 5 }
          ],
          benchmarkPeriodId: fallBenchmarkPeriodId
        });
        const assessment = generateAssessment({ benchmarkPeriodId: fallBenchmarkPeriodId });
        await Assessments.insertAsync(assessment);
        await AssessmentResults.insertAsync(assessmentResult);

        let updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);

        await calculateClasswideScreeningResults(assessmentResultId);
        updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);
      });
      it("should add new Individual Intervention recommendations when processing screening", async () => {
        const assessmentResult = generateAssessmentResult({
          _id: assessmentResultId,
          studentGroupId,
          type: "benchmark",
          assessmentIds: [assessmentId],
          scores: [
            { studentId, value: 5 },
            { studentId: studentId2, value: 4 },
            { studentId: studentId3, value: 1 }
          ],
          benchmarkPeriodId: fallBenchmarkPeriodId
        });
        const assessment = generateAssessment({ benchmarkPeriodId: fallBenchmarkPeriodId });
        await Assessments.insertAsync(assessment);
        await AssessmentResults.insertAsync(assessmentResult);

        let updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);

        const result = await calculateClasswideScreeningResults(assessmentResultId);
        updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(result.classwideResults.studentIdsNotMeetingTarget).toEqual([studentId3]);
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId, studentId3]);
      });
    });
    describe("when a student group has current classwide interventions", () => {
      const openAssessmentResultId = "openAssessmentResultId";
      const openAssessmentResult = generateAssessmentResult({
        _id: openAssessmentResultId,
        studentGroupId,
        status: "OPEN",
        type: "classwide",
        assessmentIds: [assessmentId],
        benchmarkPeriodId: fallBenchmarkPeriodId
      });
      it("should keep previous and not add new Individual Intervention recommendations when processing current screening", async () => {
        const assessmentResult = generateAssessmentResult({
          _id: assessmentResultId,
          studentGroupId,
          type: "benchmark",
          assessmentIds: [assessmentId],
          scores: [
            { studentId, value: 5 },
            { studentId: studentId2, value: 4 },
            { studentId: studentId3, value: 1 }
          ],
          benchmarkPeriodId: fallBenchmarkPeriodId
        });
        const assessment = generateAssessment({ benchmarkPeriodId: fallBenchmarkPeriodId });
        await Assessments.insertAsync(assessment);
        await AssessmentResults.insertAsync([openAssessmentResult, assessmentResult]);

        let updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);

        await calculateClasswideScreeningResults(assessmentResultId);
        updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);
      });
    });
    describe("when a student group has completed classwide interventions", () => {
      const completedAssessmentResultId = "completedAssessmentResultId";
      const completedAssessmentResult = generateAssessmentResult({
        _id: completedAssessmentResultId,
        studentGroupId,
        status: "COMPLETED",
        type: "classwide",
        assessmentIds: [assessmentId],
        benchmarkPeriodId: fallBenchmarkPeriodId
      });
      it("should keep previous and not add new Individual Intervention recommendations when processing current screening", async () => {
        const assessmentResult = generateAssessmentResult({
          _id: assessmentResultId,
          studentGroupId,
          type: "benchmark",
          assessmentIds: [assessmentId],
          scores: [
            { studentId, value: 5 },
            { studentId: studentId2, value: 4 },
            { studentId: studentId3, value: 1 }
          ],
          benchmarkPeriodId: fallBenchmarkPeriodId
        });
        const assessment = generateAssessment({ benchmarkPeriodId: fallBenchmarkPeriodId });
        await Assessments.insertAsync(assessment);
        await AssessmentResults.insertAsync([completedAssessmentResult, assessmentResult]);

        let updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);

        await calculateClasswideScreeningResults(assessmentResultId);
        updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
        expect(updatedStudentGroup.individualInterventionQueue).toEqual([studentId]);
      });
    });
  });

  describe("clearLastIndividualInterventionScores", () => {
    const studentGroupId = "studentGroupId";
    const studentId = "studentId";
    const orgid = "testOrgId";

    afterEach(async () => {
      await AssessmentResults.removeAsync({});
      await StudentGroups.removeAsync({});
      await Students.removeAsync({});
    });

    it("should throw error if no student group found", async () => {
      // Clear the collections to ensure no student group exists
      await StudentGroups.removeAsync({});

      await expect(clearLastIndividualInterventionScores({ studentGroupId, studentId })).rejects.toThrow(
        "No student group with id"
      );
    });

    it("should return false if no student history found", async () => {
      const studentGroup = {
        _id: studentGroupId
      };

      const student = {
        _id: studentId,
        history: []
      };

      await StudentGroups.insertAsync(studentGroup);
      await Students.insertAsync(student);

      const result = await clearLastIndividualInterventionScores({ studentGroupId, studentId });

      expect(result).toBe(false);
    });

    it("should return student and studentGroup if no assessmentResultId is found in the latest student history item", async () => {
      const studentGroup = {
        _id: studentGroupId
      };

      const student = {
        _id: studentId,
        history: [
          {
            type: "individual"
          }
        ]
      };

      await StudentGroups.insertAsync(studentGroup);
      await Students.insertAsync(student);

      const result = await clearLastIndividualInterventionScores({ studentGroupId, studentId });

      expect(result).toEqual({
        student,
        studentGroup
      });
    });

    it("should remove next assessment result and properly update student group when next assessment result exists", async () => {
      const currentAssessmentResultId = "currentAssessmentResultId";
      const nextAssessmentResultId = "nextAssessmentResultId";

      const studentGroup = {
        _id: studentGroupId,
        orgid,
        currentAssessmentResultIds: [nextAssessmentResultId]
      };

      const student = {
        _id: studentId,
        history: [
          {
            type: "individual",
            assessmentResultId: currentAssessmentResultId
          }
        ]
      };

      const currentAssessmentResult = {
        _id: currentAssessmentResultId,
        scores: [{ value: 10 }],
        nextAssessmentResultId
      };

      const nextAssessmentResult = {
        _id: nextAssessmentResultId
      };

      await StudentGroups.insertAsync(studentGroup);
      await Students.insertAsync(student);
      await AssessmentResults.insertAsync([currentAssessmentResult, nextAssessmentResult]);

      await clearLastIndividualInterventionScores({ studentGroupId, studentId });

      expect(await AssessmentResults.findOneAsync({ _id: nextAssessmentResultId })).toBeNull();
      const updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
      expect(updatedStudentGroup.currentAssessmentResultIds).not.toContain(nextAssessmentResultId);
      expect(updatedStudentGroup.currentAssessmentResultIds).toContain(currentAssessmentResultId);
      expect(updatedStudentGroup.lastModified).toBeDefined();
    });

    it("should properly update current assessment result and student", async () => {
      const lastAssessmentResultId = "lastAssessmentResultId";
      const firstAssessmentResultId = "firstAssessmentResultId";

      const studentGroup = {
        _id: studentGroupId,
        orgid,
        currentAssessmentResultIds: []
      };

      const student = {
        _id: studentId,
        history: [
          {
            type: "individual",
            assessmentResultId: lastAssessmentResultId
          },
          {
            type: "individual",
            assessmentResultId: firstAssessmentResultId
          }
        ]
      };

      const lastAssessmentResult = {
        _id: lastAssessmentResultId,
        scores: [{ value: 10, studentId }]
      };

      const firstAssessmentResult = {
        _id: firstAssessmentResultId,
        scores: [{ value: 10, studentId }]
      };

      await StudentGroups.insertAsync(studentGroup);
      await Students.insertAsync(student);
      await AssessmentResults.insertAsync([lastAssessmentResult, firstAssessmentResult]);

      await clearLastIndividualInterventionScores({ studentGroupId, studentId });

      const updatedAssessmentResult = await AssessmentResults.findOneAsync({ _id: lastAssessmentResultId });
      expect(updatedAssessmentResult.status).toBe("OPEN");
      expect(updatedAssessmentResult.scores).toEqual([{ studentId, status: "STARTED" }]);
      expect(updatedAssessmentResult.lastModified).toBeDefined();
      expect(updatedAssessmentResult.nextAssessmentResultId).toBeUndefined();

      const updatedStudent = await Students.findOneAsync({ _id: studentId });
      expect(updatedStudent.history).toEqual([{ type: "individual", assessmentResultId: firstAssessmentResultId }]);
      expect(updatedStudent.currentSkill).toEqual({ assessmentResultId: lastAssessmentResultId });
      expect(updatedStudent.lastModified).toBeDefined();
    });
  });

  describe("updateScores", () => {
    const assessmentResultId = "assessmentResultId";
    const orgid = "testOrgId";

    afterEach(async () => {
      await AssessmentResults.removeAsync({});
    });

    it("should throw an error if scores package is empty", async () => {
      await expect(updateScores([])).rejects.toThrow(expect.objectContaining({ reason: "scores package was empty" }));
    });

    it("should properly update scores in the scores package", async () => {
      const assessmentResult = {
        _id: assessmentResultId,
        orgid,
        scores: [
          { _id: "score1", value: 0, status: "STARTED" },
          { _id: "score2", value: 0, status: "STARTED" }
        ]
      };
      await AssessmentResults.insertAsync(assessmentResult);

      const scoresPkg = [
        { assessmentResultId, scoreId: "score1", number_correct: 5, status: "COMPLETE" },
        { assessmentResultId, scoreId: "score2", number_correct: 8, status: "COMPLETE" }
      ];

      await updateScores(scoresPkg);

      const updatedAssessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      expect(updatedAssessmentResult.scores.length).toBe(2);
      expect(updatedAssessmentResult.scores[0].value).toBe(5);
      expect(updatedAssessmentResult.scores[0].status).toBe("COMPLETE");
      expect(updatedAssessmentResult.scores[1].value).toBe(8);
      expect(updatedAssessmentResult.scores[1].status).toBe("COMPLETE");
      expect(updatedAssessmentResult.lastScoreUpdatedAt).toBeDefined();
      expect(updatedAssessmentResult.lastModified).toBeDefined();
    });

    it("should not update scores that are not in the scores package", async () => {
      const assessmentResult = {
        _id: assessmentResultId,
        orgid,
        scores: [
          { _id: "score1", value: 0, status: "STARTED" },
          { _id: "score2", value: 0, status: "STARTED" },
          { _id: "score3", value: 0, status: "STARTED" }
        ]
      };
      await AssessmentResults.insertAsync(assessmentResult);

      const scoresPkg = [
        { assessmentResultId, scoreId: "score2", number_correct: 5, status: "COMPLETE" },
        { assessmentResultId, scoreId: "score3", number_correct: 8, status: "COMPLETE" }
      ];

      await updateScores(scoresPkg);

      const updatedAssessmentResult = await AssessmentResults.findOneAsync({ _id: assessmentResultId });
      expect(updatedAssessmentResult.scores.length).toBe(3);
      expect(updatedAssessmentResult.scores[0].value).toBe(0);
      expect(updatedAssessmentResult.scores[0].status).toBe("STARTED");
      expect(updatedAssessmentResult.scores[1].value).toBe(5);
      expect(updatedAssessmentResult.scores[1].status).toBe("COMPLETE");
      expect(updatedAssessmentResult.scores[2].value).toBe(8);
      expect(updatedAssessmentResult.scores[2].status).toBe("COMPLETE");
      expect(updatedAssessmentResult.lastScoreUpdatedAt).toBeDefined();
      expect(updatedAssessmentResult.lastModified).toBeDefined();
    });
  });
});
