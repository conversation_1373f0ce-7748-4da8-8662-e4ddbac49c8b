import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const AssessmentResults = new Mongo.Collection("AssessmentResults");

AssessmentResults.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  assessmentIds: { type: Array, optional: true },
  "assessmentIds.$": String,
  benchmarkPeriodId: { type: String },
  classwideResults: { type: Object, optional: true }, // TODO: change classwide to
  created: { type: ByDateOn },
  grade: { type: String },
  individualSkills: { type: Object, optional: true },
  "individualSkills.assessmentId": { type: String },
  "individualSkills.assessmentName": { type: String },
  "individualSkills.benchmarkAssessmentId": { type: String },
  "individualSkills.benchmarkAssessmentName": { type: String },
  "individualSkills.interventions": [Object],
  "individualSkills.assessmentResultId": { type: String, optional: true },
  lastModified: { type: ByDateOn, optional: true },
  lastScoreUpdatedAt: { type: Number, optional: true },
  nextAssessmentResultId: { type: String, optional: true },
  measures: { type: Array, optional: true },
  "measures.$": { type: Object, optional: true },
  "measures.$.assessmentId": { type: String },
  "measures.$.assessmentName": { type: String },
  "measures.$.benchmarkScheduleId": { type: String, optional: true },
  "measures.$.preAssessmentResultId": { type: String, optional: true },
  "measures.$.scheduleId": { type: String, optional: true },
  "measures.$.cutoffTarget": { type: Number },
  "measures.$.targets": [Number],
  "measures.$.medianScore": { type: Number },
  "measures.$.studentScores": [Number],
  "measures.$.percentMeetingTarget": { type: Number },
  "measures.$.numberMeetingTarget": { type: Number },
  "measures.$.totalStudentsAssessed": { type: Number },
  "measures.$.studentResults": [Object],
  "measures.$.studentResults.$.studentId": { type: String },
  "measures.$.studentResults.$.status": { type: String },
  "measures.$.studentResults.$.firstName": { type: String },
  "measures.$.studentResults.$.lastName": { type: String },
  "measures.$.studentResults.$.score": { type: String },
  "measures.$.studentResults.$.meetsTarget": { type: Boolean },
  "measures.$.studentResults.$.individualRuleOutcome": { type: String }, // above, at, below
  orgid: { type: String },
  previousAssessmentResultId: { type: String, optional: true },
  ruleResults: { type: Object, optional: true, blackbox: true },
  schoolYear: { type: Number },
  scores: { type: Array, optional: true },
  "scores.$": { type: Object, optional: true },
  "scores.$._id": { type: String },
  "scores.$.assessmentId": { type: String },
  "scores.$.orgid": { type: String },
  "scores.$.siteId": { type: String },
  "scores.$.status": {
    type: String,
    allowedValues: ["CANCELLED", "STARTED", "COMPLETE"]
  },
  "scores.$.studentId": { type: String },
  "scores.$.value": { type: Number, optional: true },
  siteId: { type: String },
  status: { type: String, allowedValues: ["OPEN", "STARTED", "COMPLETED"] },
  studentGroupId: { type: String },
  studentId: { type: String, optional: true },
  type: {
    type: String,
    allowedValues: ["classwide", "individual", "benchmark"]
  }
});

AssessmentResults.validate = organizations => {
  AssessmentResults.schema.validate(organizations);
};

AssessmentResults.isValid = organizations =>
  AssessmentResults.schema.namedContext("testContext").validate(organizations);
