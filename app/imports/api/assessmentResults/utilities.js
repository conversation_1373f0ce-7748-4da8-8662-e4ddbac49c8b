export function getPercent(num, den) {
  if (typeof den !== "number" || typeof num !== "number" || !(den > 0) || !(num >= 0)) {
    return null;
  }
  let percent = num / den;
  percent *= 100;
  return Math.round(percent);
}

export function getIndividualRuleOutcome(score, targets) {
  if (score === "") return null;
  if (Number(score) < targets[0]) return "below";
  if (Number(score) < targets[1]) return "at";
  return "above";
}

export function getEnrolledStudentIds(assessmentResult) {
  // Returns a unique list of studentIds, method below is used for speed
  return Object.keys(
    assessmentResult.scores.reduce((a, c) => {
      const map = a;
      map[c.studentId] = true;
      return map;
    }, {})
  );
}

export function getIndividualResultsMessage(sUpdateSet) {
  // Note, the currentSkill here is AFTER ruleProcessing has been finalized...the next currentSkill
  // See message codes in utilities.js
  let messageCode = "100"; // default is an empty string
  const { currentSkill } = sUpdateSet;
  const mostRecentInterventionHistoryRelatedToCurrentSkill =
    currentSkill &&
    sUpdateSet.history &&
    sUpdateSet.history.find(
      historyItem =>
        historyItem.type === "individual" && historyItem.benchmarkPeriodId === currentSkill.benchmarkPeriodId
    );
  let mostRecentInterventionMeasure;
  let mostRecentInterventionScore;
  // Find most recent intervention score
  if (currentSkill && mostRecentInterventionHistoryRelatedToCurrentSkill) {
    mostRecentInterventionMeasure = mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentResultMeasures.find(
      assessmentResultMeas => assessmentResultMeas.assessmentId === currentSkill.assessmentId
    );
    mostRecentInterventionScore = mostRecentInterventionMeasure && mostRecentInterventionMeasure.medianScore;
  }
  // Evaluate what has landed the student at their currentSkill
  const currentSkillIsDrillDown =
    currentSkill &&
    currentSkill.assessmentId &&
    (!currentSkill.interventions || currentSkill.interventions.length === 0);
  const currentSkillHasInterventions =
    currentSkill && currentSkill.assessmentId && currentSkill.interventions && currentSkill.interventions.length > 0;
  const currentSkillHasNewGoalSkill =
    currentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill &&
    currentSkill.benchmarkAssessmentId !== mostRecentInterventionHistoryRelatedToCurrentSkill.benchmarkAssessmentId;
  const interventionAndBenchmarkAreSameAssessment =
    currentSkill && currentSkill.benchmarkAssessmentId === currentSkill.assessmentId;
  const previousScoreWasDrillDown =
    currentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill &&
    (!mostRecentInterventionHistoryRelatedToCurrentSkill.interventions ||
      mostRecentInterventionHistoryRelatedToCurrentSkill.interventions.length === 0);
  const studentIsWorkingOnSameAssessmentAndBenchmark =
    currentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentId === currentSkill.assessmentId &&
    mostRecentInterventionHistoryRelatedToCurrentSkill.benchmarkAssessmentId === currentSkill.benchmarkAssessmentId;

  // Determine messages based on previous determinations
  if (!mostRecentInterventionHistoryRelatedToCurrentSkill && currentSkillIsDrillDown) {
    messageCode = "51";
  } else if (!mostRecentInterventionHistoryRelatedToCurrentSkill && currentSkillHasInterventions) {
    messageCode = "58";
  } else if (!currentSkill) {
    messageCode = "56"; // This is actually handled elsewhere in the code...
  } else if (currentSkillHasNewGoalSkill && interventionAndBenchmarkAreSameAssessment && currentSkillIsDrillDown) {
    messageCode = "55";
  } else if (currentSkillIsDrillDown && !previousScoreWasDrillDown) {
    messageCode = "57";
  } else if (currentSkillIsDrillDown && previousScoreWasDrillDown) {
    messageCode = "61";
  } else if (previousScoreWasDrillDown && currentSkillHasInterventions) {
    messageCode = "59";
  } else if (
    studentIsWorkingOnSameAssessmentAndBenchmark &&
    mostRecentInterventionScore < mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentTargets[0]
  ) {
    messageCode = "52";
  } else if (
    studentIsWorkingOnSameAssessmentAndBenchmark &&
    mostRecentInterventionScore >= mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentTargets[0]
  ) {
    messageCode = "53";
  } else if (
    mostRecentInterventionHistoryRelatedToCurrentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentId !== currentSkill.assessmentId &&
    mostRecentInterventionHistoryRelatedToCurrentSkill.benchmarkAssessmentId === currentSkill.benchmarkAssessmentId &&
    !interventionAndBenchmarkAreSameAssessment
  ) {
    messageCode = "54";
  } else if (
    mostRecentInterventionHistoryRelatedToCurrentSkill &&
    mostRecentInterventionHistoryRelatedToCurrentSkill.assessmentId !== currentSkill.assessmentId &&
    interventionAndBenchmarkAreSameAssessment
  ) {
    messageCode = "60";
  }
  return {
    messageCode,
    dismissed: false
  };
}
