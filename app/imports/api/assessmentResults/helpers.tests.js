import { findIndex } from "lodash";
import {
  getIdsOfStudentsEligibleForIndividualIntervention,
  getIdsOfStudentsInFrustrationalRange,
  getRecommendationsForIndividualIntervention,
  have4WeeksPassedSinceDate,
  getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening
} from "./helpers";
import { getMedianNumber } from "../utilities/utilities";
import { getIndividualRuleOutcome, getPercent } from "./utilities";
import { Students } from "../students/students";
import { AssessmentResults } from "./assessmentResults";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";

function generateClasswideInterventionMeasure({
  studentScores = [],
  targetScores = [20, 39, 225],
  assessmentId = "assessmentId",
  assessmentName = "assessmentName"
}) {
  if (!studentScores.length) {
    return {};
  }
  const [, masteryTarget] = targetScores;
  let numberMeetingTarget = 0;
  const studentResults = studentScores.map((studentScore, index) => {
    const meetsTarget = studentScore >= masteryTarget;
    if (meetsTarget) {
      numberMeetingTarget += 1;
    }
    return {
      studentId: `student_${index + 1}`,
      status: "COMPLETE",
      score: studentScore.toString(),
      meetsTarget,
      individualRuleOutcome: getIndividualRuleOutcome(studentScore, targetScores)
    };
  });
  const totalStudentsAssessed = studentScores.length;
  const percentMeetingTarget = getPercent(numberMeetingTarget, totalStudentsAssessed);
  return {
    assessmentId,
    assessmentName,
    cutoffTarget: masteryTarget,
    targetScores,
    medianScore: getMedianNumber(studentScores, "roundUp"),
    percentMeetingTarget,
    numberMeetingTarget,
    totalStudentsAssessed,
    studentResults,
    studentScores,
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    grade: "01",
    assessmentResultType: "classwide"
  };
}

function generateStudentGroupHistory(assessmentMeasureParams = []) {
  if (!assessmentMeasureParams.length) {
    return assessmentMeasureParams;
  }
  return assessmentMeasureParams.map((measureParams, index) => {
    const { type = "classwide", date, targetScores, skill } = measureParams;
    const byDateOnObject = {
      by: "admin_user_id",
      on: date.valueOf(),
      date: date.toISOString()
    };

    if (type === "benchmark") {
      return {
        whenStarted: byDateOnObject,
        whenEnded: byDateOnObject,
        type
      };
    }

    const assessmentId = `assessmentId_${skill}`;
    const assessmentName = `Skill #${skill}`;
    const classwideInterventionMeasure = generateClasswideInterventionMeasure({
      ...measureParams,
      assessmentId,
      assessmentName
    });

    return {
      assessmentId,
      assessmentName,
      interventions: [],
      targets: targetScores,
      assessmentResultId: `assessmentResultId_${index + 1}`,
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      message: {
        additionalStudentsAddedToInterventionQueue: false,
        messageCode: "4",
        dismissed: false
      },
      whenStarted: byDateOnObject,
      whenEnded: byDateOnObject,
      assessmentResultMeasures: [classwideInterventionMeasure],
      type,
      enrolledStudentIds:
        measureParams.numberOfEnrolledStudents >= 0
          ? Array.from({ length: measureParams.numberOfEnrolledStudents }, (_, i) => `student_${i + 1}`)
          : classwideInterventionMeasure.studentResults.map(({ studentId }) => studentId)
    };
  });
}

function makeStudentAbsentInHistory(historyItem, studentId) {
  const assessmentResultMeasure = { ...historyItem.assessmentResultMeasures[0] };
  const indexOfStudentToRemove = findIndex(assessmentResultMeasure.studentResults, sr => sr.studentId === studentId);
  // const studentScore = assessmentResultMeasure.studentResults[indexOfStudentToRemove].score;
  assessmentResultMeasure.studentResults.splice(indexOfStudentToRemove, 1);
  // TODO(fmazur) - maybe need to findIndex by studentScore?
  assessmentResultMeasure.studentScores.splice(indexOfStudentToRemove, 1);
  assessmentResultMeasure.median = getMedianNumber(assessmentResultMeasure.studentScores, "roundUp");
  assessmentResultMeasure.totalStudentsAssessed -= 1;
  return { ...historyItem, assessmentResultMeasures: [assessmentResultMeasure] };
}

describe("4-week rule", () => {
  describe("have4WeeksPassed", () => {
    it("should return true if 4 weeks have passed since the specified date", () => {
      const sinceDate = new Date("2021-05-20");
      const toDate = new Date("2021-08-20");
      const have4WeeksPassed = have4WeeksPassedSinceDate(sinceDate, toDate);
      expect(have4WeeksPassed).toBe(true);
    });

    it("should return false if 4 weeks haven't passed since the specified date", () => {
      const sinceDate = new Date("2021-05-20");
      const toDate = new Date("2021-05-21");
      const have4WeeksPassed = have4WeeksPassedSinceDate(sinceDate, toDate);
      expect(have4WeeksPassed).toBe(false);
    });
  });

  describe("getIdsOfStudentsInFrustrationalRange", () => {
    it("should return an empty array when there aren't any students in the frustration range", () => {
      const { studentResults } = generateClasswideInterventionMeasure({
        studentScores: [43, 48, 30, 37],
        targetScores: [20, 39, 225]
      });
      expect(getIdsOfStudentsInFrustrationalRange(studentResults)).toEqual([]);
    });

    it("should return ids of students when there are any students in the frustration range", () => {
      const { studentResults } = generateClasswideInterventionMeasure({
        studentScores: [35, 48, 19, 25],
        targetScores: [20, 39, 225]
      });
      expect(getIdsOfStudentsInFrustrationalRange(studentResults)).toEqual(["student_3"]);
    });
  });

  describe("getIdsOfStudentsEligibleForIndividualIntervention", () => {
    it("should return an empty array if there aren't any students in the frustrational range", () => {
      const classwideInterventionMeasure = generateClasswideInterventionMeasure({
        studentScores: [43, 48, 30, 37],
        targetScores: [20, 39, 225]
      });
      const idsOfStudentsEligibleForIndividualIntervention = getIdsOfStudentsEligibleForIndividualIntervention({
        classwideInterventionMeasure
      });
      expect(idsOfStudentsEligibleForIndividualIntervention).toEqual([]);
    });

    it("should return ids of students eligible for individual interventions if there are any students in the frustrational range when the class moved up a skill", () => {
      const classwideInterventionMeasure = generateClasswideInterventionMeasure({
        studentScores: [43, 48, 19, 37],
        targetScores: [20, 39, 225]
      });
      const idsOfStudentsEligibleForIndividualIntervention = getIdsOfStudentsEligibleForIndividualIntervention({
        classwideInterventionMeasure
      });
      expect(idsOfStudentsEligibleForIndividualIntervention).toEqual(["student_3"]);
    });

    it("should return ids of students eligible for individual interventions when the class moved up a skill and there are any students in the frustrational range in the past for that skill", () => {
      const classwideInterventionMeasure = generateClasswideInterventionMeasure({
        studentScores: [43, 48, 37],
        targetScores: [20, 39, 225]
      });

      const idsOfStudentsInFrustrationalRangeInPast = ["student_4"];

      const idsOfStudentsEligibleForIndividualIntervention = getIdsOfStudentsEligibleForIndividualIntervention({
        classwideInterventionMeasure,
        idsOfStudentsInFrustrationalRangeInPast
      });
      expect(idsOfStudentsEligibleForIndividualIntervention).toEqual(["student_4"]);
    });
  });

  describe("getRecommendationsForIndividualIntervention", () => {
    it("should return undefined if there isn't any student group history", () => {
      const history = [];
      const recommendations = getRecommendationsForIndividualIntervention({
        history,
        currentDate: new Date("2021-05-29")
      });
      expect(recommendations).toEqual(undefined);
    });

    it("should return undefined if there aren't any classwide interventions in a student group history", () => {
      const history = [{ type: "benchmark", date: new Date("2021-05-01") }];
      const recommendations = getRecommendationsForIndividualIntervention({
        history,
        currentDate: new Date("2021-06-29")
      });
      expect(recommendations).toEqual(undefined);
    });

    describe("when 4 weeks haven't passed yet", () => {
      it("should return undefined", () => {
        const currentDate = new Date("2021-05-08");
        const history = generateStudentGroupHistory([
          { skill: 1, studentScores: [43, 48, 37, 19], targetScores: [20, 39, 225], date: currentDate },
          { skill: 1, studentScores: [41, 42, 25, 19], targetScores: [20, 39, 225], date: new Date("2021-05-01") }
        ]);
        const recommendations = getRecommendationsForIndividualIntervention({
          history,
          currentDate
        });
        expect(recommendations).toEqual(undefined);
      });
    });

    describe("when 4 weeks have passed", () => {
      describe("when a class didn't move up a skill", () => {
        const currentDate = new Date("2021-05-29");
        const classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              { skill: 1, studentScores: [42, 43, 26, 19], targetScores: [20, 39, 225], date: currentDate },
              { skill: 1, studentScores: [41, 42, 25, 18], targetScores: [20, 39, 225], date: new Date("2021-05-01") }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 1,
                studentScores: [36, 36, 36, 36, 36, 36, 36, 27, 27, 27, 19],
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 18],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return undefined ids of students in frustrational range in past when using the ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual(undefined);
          });
        });
      });

      describe("when a class moved up a skill", () => {
        let currentDate = new Date("2021-05-29");
        let classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              { skill: 1, studentScores: [43, 38, 37, 21], targetScores: [20, 39, 225], date: currentDate },
              { skill: 1, studentScores: [41, 42, 25, 19], targetScores: [20, 39, 225], date: new Date("2021-05-01") }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 1,
                studentScores: [41, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20],
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 18],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return an empty list if there aren't any students recommended for individual intervention when using the ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual([]);
          });
        });

        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              { skill: 1, studentScores: [43, 48, 37, 20, 37], targetScores: [20, 39, 225], date: currentDate },
              {
                skill: 1,
                studentScores: [41, 42, 25, 18, 37, 16],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              },
              {
                skill: 1,
                studentScores: [41, 42, 25, 18, 37, 16],
                targetScores: [20, 39, 225],
                date: new Date("2021-04-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 1,
                studentScores: [41, 41, 41, 20, 41, 41, 41, 27, 27, 27, 41],
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 18, 35, 35, 35, 26, 26, 26, 35, 17],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 18, 35, 35, 35, 26, 26, 26, 35, 17],
                targetScores: [20, 39, 225],
                date: new Date("2021-04-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return an empty list if there are inactive students recommended for individual intervention when using the ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual([]);
          });
        });

        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              { skill: 1, studentScores: [43, 48, 37, 19, 37], targetScores: [20, 39, 225], date: currentDate },
              {
                skill: 1,
                studentScores: [41, 42, 25, 18, 37],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 1,
                studentScores: [41, 41, 41, 19, 41, 41, 41, 27, 27, 27, 41],
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 18, 35, 35, 35, 26, 26, 26, 35],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return ids of students eligible for individual interventions if 4 weeks have passed and there are some student recommendations when using the ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual(["student_4"]);
          });
        });
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 1,
                studentScores: [43, 48, 37, 21, 20],
                numberOfEnrolledStudents: 6,
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [41, 42, 25, 19, 18, 17],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 1,
                studentScores: [41, 41, 41, 41, 41, 41, 41, 27, 27, 27, 35],
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: currentDate
              },
              {
                skill: 1,
                studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 35, 20],
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return an empty list when student was absent when the class moved up a skill and the most recent score was in the frustrational range but 4 weeks haven't passed yet for that score when using the ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual([]);
          });
        });
        describe("should return ids of students eligible for individual interventions when student was absent when the class moved up a skill and ", () => {
          currentDate = new Date("2021-06-05");
          classwideScoresCases = [
            {
              rule: "80% rule",
              history: [
                {
                  skill: 1,
                  studentScores: [43, 48, 37, 21, 20],
                  numberOfEnrolledStudents: 6,
                  targetScores: [20, 39, 225],
                  date: currentDate
                },
                {
                  skill: 1,
                  studentScores: [41, 42, 25, 19, 18, 17],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-29")
                },
                {
                  skill: 1,
                  studentScores: [41, 42, 25, 19, 18, 17],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-01")
                }
              ],
              expectedRecommendations: ["student_6"]
            },
            {
              rule: "median rule",
              history: [
                {
                  skill: 1,
                  studentScores: [41, 41, 41, 41, 41, 41, 41, 27, 27, 27, 27],
                  numberOfEnrolledStudents: 12,
                  targetScores: [20, 39, 225],
                  date: currentDate
                },
                {
                  skill: 1,
                  studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 26, 19],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-29")
                },
                {
                  skill: 1,
                  studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 26, 19],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-01")
                }
              ],
              expectedRecommendations: ["student_12"]
            }
          ];
          classwideScoresCases.forEach(classwideScoresCase => {
            it(`the most recent score was in the frustrational range when using the ${classwideScoresCase.rule}`, () => {
              const history = generateStudentGroupHistory(classwideScoresCase.history);
              const recommendations = getRecommendationsForIndividualIntervention({
                history,
                currentDate
              });
              expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual(
                classwideScoresCase.expectedRecommendations
              );
            });
          });

          classwideScoresCases = [
            {
              rule: "80% rule",
              history: [
                {
                  skill: 1,
                  studentScores: [43, 48, 37, 21, 20],
                  numberOfEnrolledStudents: 6,
                  targetScores: [20, 39, 225],
                  date: currentDate
                },
                {
                  skill: 1,
                  studentScores: [41, 42, 25, 19, 18, 21],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-29")
                },
                {
                  skill: 1,
                  studentScores: [41, 42, 25, 19, 18, 17],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-01")
                }
              ]
            },
            {
              rule: "median rule",
              history: [
                {
                  skill: 1,
                  studentScores: [41, 41, 41, 41, 41, 41, 41, 27, 27, 27, 27],
                  numberOfEnrolledStudents: 12,
                  targetScores: [20, 39, 225],
                  date: currentDate
                },
                {
                  skill: 1,
                  studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 26, 21],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-29")
                },
                {
                  skill: 1,
                  studentScores: [35, 35, 35, 35, 35, 35, 35, 26, 26, 26, 26, 19],
                  targetScores: [20, 39, 225],
                  date: new Date("2021-05-01")
                }
              ]
            }
          ];
          classwideScoresCases.forEach(classwideScoresCase => {
            it(`the most recent score wasn't in the frustrational range when using the ${classwideScoresCase.rule}`, () => {
              const history = generateStudentGroupHistory(classwideScoresCase.history);
              const recommendations = getRecommendationsForIndividualIntervention({
                history,
                currentDate
              });
              expect(recommendations.idsOfStudentsInFrustrationalRangeInPast).toEqual([]);
            });
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 3,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 3,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-01")
              },
              {
                skill: 1,
                studentScores: [1, 30, 25, 22, 22, 15], // 22 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-05-29")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 3,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 3,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-04")
              },
              {
                skill: 2,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20, 38], // 40
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [1, 35, 35, 35, 35, 35, 35, 26, 26, 26, 30, 0], // 33
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return missing student recommendations when all rules get fulfilled for ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[0] = makeStudentAbsentInHistory(history[0], "student_1");
            history[2] = makeStudentAbsentInHistory(history[2], "student_1");
            history[4] = makeStudentAbsentInHistory(history[4], "student_1");

            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations
            } = recommendations.missingScoresRecommendationsResult;
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_1"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual(["student_1"]);
            expect(missingScoresRecommendations).toEqual(["student_1"]);
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 3,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-01")
              },
              {
                skill: 1,
                studentScores: [1, 30, 25, 22, 22, 15], // 22 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-05-29")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 3,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20, 38], // 40
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [1, 35, 35, 35, 35, 35, 35, 26, 26, 26, 30, 0], // 33
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should return missing student recommendations when all rules get fulfilled for ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[0] = makeStudentAbsentInHistory(history[0], "student_1");
            history[1] = makeStudentAbsentInHistory(history[1], "student_1");
            history[3] = makeStudentAbsentInHistory(history[3], "student_1");

            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations
            } = recommendations.missingScoresRecommendationsResult;
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_1"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual(["student_1"]);
            expect(missingScoresRecommendations).toEqual(["student_1"]);
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 3,
                studentScores: [19, 20, 8, 5, 21, 5], // 14
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [10, 1, 38, 37, 21, 20], // 21,  2nd student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-04")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 1,
                studentScores: [10, 0, 38, 37, 21, 20], // 21 with second student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 3,
                studentScores: [1, 20, 1, 35, 18, 20, 5, 3, 26, 26, 30, 0, 10], // 18, student 2 at
                numberOfEnrolledStudents: 13,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-04")
              },
              {
                skill: 2,
                studentScores: [1, 2, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41, 41], // 41, student 2 absent
                numberOfEnrolledStudents: 13,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 2, 25, 41, 41, 25, 41, 27, 27, 27, 19, 41, 10], // 27, student 2 in frustrational range
                numberOfEnrolledStudents: 13,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 2, 41, 41, 41, 41, 41, 27, 27, 27, 20, 41, 41], // 41, second student absent
                numberOfEnrolledStudents: 13,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 0
                numberOfEnrolledStudents: 13,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should not return recommendation for student that was recommended but scored at or above target on subsequent skill for ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[1] = makeStudentAbsentInHistory(history[1], "student_2");
            history[3] = makeStudentAbsentInHistory(history[3], "student_2");
            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations
            } = recommendations.missingScoresRecommendationsResult;
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_2"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual([]);
            expect(missingScoresRecommendations).toEqual([]);
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 3,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent, second student below
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent, second student below
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-01")
              },
              {
                skill: 1,
                studentScores: [1, 30, 25, 22, 22, 15], // 22 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-05-29")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 3,
                studentScores: [1, 19, 41, 41, 41, 41, 41, 41, 27, 27, 20, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-04")
              },
              {
                skill: 2,
                studentScores: [1, 19, 41, 41, 41, 41, 41, 41, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20, 38], // 40
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [1, 35, 35, 35, 35, 35, 35, 26, 26, 26, 30, 0], // 33
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should retain four week rule recommendations when removing some of missing scores recommendations for ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[0] = makeStudentAbsentInHistory(history[0], "student_1");
            history[1] = makeStudentAbsentInHistory(history[1], "student_1");
            history[3] = makeStudentAbsentInHistory(history[3], "student_1");

            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations
            } = recommendations.missingScoresRecommendationsResult;
            const fourWeekRuleRecommendations = recommendations.idsOfStudentsInFrustrationalRangeInPast;
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_1"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual(["student_1"]);
            expect(missingScoresRecommendations).toEqual(["student_1"]);
            expect(fourWeekRuleRecommendations).toEqual(["student_2"]);
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 5,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-08")
              },
              {
                skill: 5,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-07")
              },
              {
                skill: 4,
                studentScores: [20, 10, 38, 37, 21, 20], // 21
                targetScores: [20, 39, 225],
                date: new Date("2021-06-06")
              },
              {
                skill: 4,
                studentScores: [20, 10, 10, 10, 21, 20], // 15 first student removed from queue
                targetScores: [20, 39, 225],
                date: new Date("2021-06-06")
              },
              {
                skill: 3,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-01")
              },
              {
                skill: 1,
                studentScores: [1, 30, 25, 22, 22, 15], // 22 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-05-29")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 5,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 5,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 4,
                studentScores: [20, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 4,
                studentScores: [20, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 3,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20, 38], // 40
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [1, 35, 35, 35, 35, 35, 35, 26, 26, 26, 30, 0], // 33
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should not return missing student recommendations on the next skill when meeting rule #3 after being removed by rule #4 ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[0] = makeStudentAbsentInHistory(history[0], "student_1");
            history[4] = makeStudentAbsentInHistory(history[4], "student_1");
            history[5] = makeStudentAbsentInHistory(history[5], "student_1");
            history[7] = makeStudentAbsentInHistory(history[7], "student_1");

            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations,
              absentWhenMovedUpStudentCountByStudentId
            } = recommendations.missingScoresRecommendationsResult;
            expect(absentWhenMovedUpStudentCountByStudentId.student_1.totalAbsentCount).toEqual(4);
            expect(absentWhenMovedUpStudentCountByStudentId.student_1.absentCountSinceScoredAtOrAbove).toEqual(1);
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_1"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual([]);
            expect(missingScoresRecommendations).toEqual([]);
          });
        });

        currentDate = new Date("2021-06-4");
        classwideScoresCases = [
          {
            rule: "80% rule",
            history: [
              {
                skill: 7,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-10")
              },
              {
                skill: 6,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-09")
              },
              {
                skill: 5,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-08")
              },
              {
                skill: 5,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-07")
              },
              {
                skill: 4,
                studentScores: [20, 10, 38, 37, 21, 20], // 21
                targetScores: [20, 39, 225],
                date: new Date("2021-06-06")
              },
              {
                skill: 4,
                studentScores: [20, 10, 10, 10, 21, 20], // 15 first student removed from queue
                targetScores: [20, 39, 225],
                date: new Date("2021-06-06")
              },
              {
                skill: 3,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [2, 10, 38, 37, 21, 20], // 21 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 2,
                studentScores: [19, 19, 21, 5, 21, 5], // 19
                targetScores: [20, 39, 225],
                date: new Date("2021-06-01")
              },
              {
                skill: 1,
                studentScores: [1, 30, 25, 22, 22, 15], // 22 with first student absent
                targetScores: [20, 39, 225],
                date: new Date("2021-05-29")
              },
              {
                skill: 1,
                studentScores: [0, 0, 0, 22, 22, 15], // 8
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          },
          {
            rule: "median rule",
            history: [
              {
                skill: 7,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-11")
              },
              {
                skill: 6,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-10")
              },
              {
                skill: 5,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-09")
              },
              {
                skill: 5,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-08")
              },
              {
                skill: 4,
                studentScores: [20, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-07")
              },
              {
                skill: 4,
                studentScores: [20, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-06")
              },
              {
                skill: 3,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-05")
              },
              {
                skill: 2,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 19, 41], // 41
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-03")
              },
              {
                skill: 2,
                studentScores: [1, 10, 10, 35, 20, 20, 5, 3, 26, 26, 30, 0], // 15
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-06-02")
              },
              {
                skill: 1,
                studentScores: [1, 41, 41, 41, 41, 41, 41, 27, 27, 27, 20, 38], // 40
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-30")
              },
              {
                skill: 1,
                studentScores: [1, 35, 35, 35, 35, 35, 35, 26, 26, 26, 30, 0], // 33
                numberOfEnrolledStudents: 12,
                targetScores: [20, 39, 225],
                date: new Date("2021-05-01")
              }
            ]
          }
        ];
        classwideScoresCases.forEach(classwideScoresCase => {
          it(`should not return missing student recommendations on the next skill when meeting rule #3 after being removed by rule #4 ${classwideScoresCase.rule}`, () => {
            const history = generateStudentGroupHistory(classwideScoresCase.history);
            history[0] = makeStudentAbsentInHistory(history[0], "student_1");
            history[1] = makeStudentAbsentInHistory(history[1], "student_1");
            history[2] = makeStudentAbsentInHistory(history[2], "student_1");
            history[6] = makeStudentAbsentInHistory(history[6], "student_1");
            history[7] = makeStudentAbsentInHistory(history[7], "student_1");
            history[9] = makeStudentAbsentInHistory(history[9], "student_1");

            const recommendations = getRecommendationsForIndividualIntervention({
              history,
              currentDate
            });
            const {
              twoAnySkillsAbsentWhenMovedUpStudentIds,
              absentStudentIdsNotMeetingTarget,
              missingScoresRecommendations,
              absentWhenMovedUpStudentCountByStudentId
            } = recommendations.missingScoresRecommendationsResult;
            expect(absentWhenMovedUpStudentCountByStudentId.student_1.totalAbsentCount).toEqual(6);
            expect(absentWhenMovedUpStudentCountByStudentId.student_1.absentCountSinceScoredAtOrAbove).toEqual(3);
            expect(twoAnySkillsAbsentWhenMovedUpStudentIds).toEqual(["student_1"]);
            expect(absentStudentIdsNotMeetingTarget).toEqual(["student_1"]);
            expect(missingScoresRecommendations).toEqual(["student_1"]);
          });
        });
      });
    });
  });

  describe("getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening", () => {
    const benchmarkPeriodsByLabel = {
      fall: "8S52Gz5o85hRkECgq",
      winter: "nEsbWokBWutTZFkTh"
    };
    const schoolYear = 2024;
    const type = "benchmark";
    const status = "COMPLETED";
    const studentIdNotCompletedIntervention = "studentIdNotCompletedIntervention";
    const studentIdCompletedInFallScreening = "studentIdCompletedInFallScreening";
    const studentIdCompletedInWinterScreening = "studentIdCompletedInWinterScreening";
    const studentGroupId = "studentGroupId";
    const assessmentResultScreeningsByPeriodLabel = {
      fall: {
        _id: "fallScreening",
        status,
        type,
        schoolYear,
        studentGroupId,
        benchmarkPeriodId: benchmarkPeriodsByLabel.fall
      },
      winter: {
        _id: "winterScreening",
        status,
        type,
        schoolYear,
        studentGroupId,
        benchmarkPeriodId: benchmarkPeriodsByLabel.winter
      }
    };

    beforeEach(async () => {
      await Students.insertAsync([
        {
          _id: studentIdNotCompletedIntervention,
          currentSkill: {
            assessmentId: "assessmentId",
            benchmarkPeriodId: benchmarkPeriodsByLabel.fall
          },
          history: []
        },
        {
          _id: studentIdCompletedInWinterScreening,
          currentSkill: {
            benchmarkPeriodId: benchmarkPeriodsByLabel.winter
          },
          history: []
        },
        {
          _id: studentIdCompletedInFallScreening,
          currentSkill: {
            benchmarkPeriodId: benchmarkPeriodsByLabel.fall
          },
          history: []
        }
      ]);
      await StudentGroupEnrollments.insertAsync([
        {
          _id: "sge1",
          isActive: true,
          schoolYear,
          studentId: studentIdNotCompletedIntervention,
          studentGroupId
        },
        {
          _id: "sge2",
          isActive: true,
          schoolYear,
          studentId: studentIdCompletedInWinterScreening,
          studentGroupId
        },
        {
          _id: "sge3",
          isActive: true,
          schoolYear,
          studentId: studentIdCompletedInFallScreening,
          studentGroupId
        }
      ]);
    });
    afterEach(async () => {
      await Students.removeAsync({});
      await AssessmentResults.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
    });
    it("should not return student that hasn't completed interventions in any benchmark period when class completed fall screening", async () => {
      await AssessmentResults.insertAsync([assessmentResultScreeningsByPeriodLabel.fall]);
      const studentIds = await getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening({
        studentIds: [studentIdNotCompletedIntervention, studentIdCompletedInFallScreening],
        schoolYear
      });
      expect(studentIds).toEqual([studentIdCompletedInFallScreening]);
    });
    it("should not return student that has completed only fall intervention when class completed fall and winter screenings", async () => {
      await AssessmentResults.insertAsync([
        assessmentResultScreeningsByPeriodLabel.fall,
        assessmentResultScreeningsByPeriodLabel.winter
      ]);
      const studentIds = await getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening({
        studentIds: [studentIdCompletedInFallScreening, studentIdCompletedInWinterScreening],
        schoolYear
      });
      expect(studentIds).toEqual([studentIdCompletedInWinterScreening]);
    });
  });
});
