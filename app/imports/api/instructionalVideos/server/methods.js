import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import queryString from "query-string";
import { InstructionalVideos } from "../instructionalVideos";
import * as auth from "../../authorization/server/methods";

export default async function getIdFromVideoUrl(videoType) {
  const videoData = await InstructionalVideos.findOneAsync({ type: videoType });
  if (videoData) {
    const { youTubeUrl } = videoData;
    const queryParameters = youTubeUrl.substring(youTubeUrl.indexOf("?"));
    const { v, t = "" } = queryString.parse(queryParameters);
    return {
      videoId: v,
      videoTimestamp: parseInt(t.replace(/[^0-9.]+/g, "")) // removes non-digit characters and parses into number
    };
  }
  return "";
}

Meteor.methods({
  async "InstructionalVideos:getIdFromVideoUrl"(videoType, siteId) {
    check(videoType, String);
    check(siteId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "User is not logged in");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return getIdFromVideoUrl(videoType);
    }
    throw new Meteor.Error("getIdFromVideoUrl", "You are not authorized to get VideoURL");
  }
});
