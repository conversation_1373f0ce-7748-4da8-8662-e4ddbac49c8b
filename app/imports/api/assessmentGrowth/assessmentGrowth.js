import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const AssessmentGrowth = new Mongo.Collection("AssessmentGrowth");

AssessmentGrowth.schema = new SimpleSchema({
  _id: { type: String },
  grade: { type: String },
  fallToWinter: [Object],
  "fallToWinter.$.fall": { type: Object },
  "fallToWinter.$.fall._id": { type: String },
  "fallToWinter.$.fall.name": { type: String },
  "fallToWinter.$.winter": { type: Object, optional: true },
  "fallToWinter.$.classwide": { type: Object, optional: true },
  winterToSpring: [Object],
  "winterToSpring.$.winter": { type: Object },
  "winterToSpring.$.winter._id": { type: String },
  "winterToSpring.$.winter.name": { type: String },
  "winterToSpring.$.spring": { type: Object, optional: true },
  "winterToSpring.$.classwide": { type: Object, optional: true }
});

// This may be removed once we introduce some kind of configuration utility for admins/superadmins
AssessmentGrowth.deny({
  insert() {
    return true;
  },
  update() {
    return true;
  },
  remove() {
    return true;
  }
});
