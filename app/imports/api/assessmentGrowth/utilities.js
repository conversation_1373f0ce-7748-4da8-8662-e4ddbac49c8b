import { get, isEmpty, isFinite, isNil } from "lodash";
import { Meteor } from "meteor/meteor";
import { hasGroupPassedClasswideIntervention } from "../utilities/utilities";

const benchmarkPeriodsToColor = {
  fall: "#f9bf68",
  winter: "#70a1d9",
  spring: "#aee57c"
};

const classwideInterventionColor = "#541f21";

const availableSeasonalComparisons = ["fallToWinter", "winterToSpring"];

export const getBenchmarkAndClasswideHistory = history =>
  history.reduce(
    (acc, currentItem) => {
      if (currentItem.type === "benchmark") {
        acc.benchmarkHistory.push(currentItem);
      } else {
        acc.classwideHistory.push(currentItem);
      }
      return acc;
    },
    { benchmarkHistory: [], classwideHistory: [] }
  );

const getHistoryItemTargetScore = (historyItem, path) =>
  // necessary default null for not skipping 0 scores
  get(historyItem, path, null);

const getClasswideScore = (classwideHistory, assessmentId) => {
  const lastMatchedPassingClasswideHistoryItems = classwideHistory.filter(item => {
    const { enrolledStudentIds } = item;
    const { medianScore, totalStudentsAssessed, targetScores, studentScores } = item.assessmentResultMeasures[0];
    return (
      item.assessmentId === assessmentId &&
      hasGroupPassedClasswideIntervention({
        medianScore,
        totalStudentsAssessed,
        targetScores,
        studentScores,
        numberOfEnrolledStudents: enrolledStudentIds.length
      })
    );
  });
  let totalStudentsAssessed = 0;
  let totalStudentScoresOverInstructionalTarget = 0;
  lastMatchedPassingClasswideHistoryItems.forEach(lastMatchedPassingClasswideHistoryItem => {
    const instructionalTarget = lastMatchedPassingClasswideHistoryItem.targets[0];
    const { studentScores } = lastMatchedPassingClasswideHistoryItem.assessmentResultMeasures[0];
    const studentScoresOverInstructionalTarget = studentScores.filter(score => score >= instructionalTarget);
    totalStudentsAssessed += studentScores.length;
    totalStudentScoresOverInstructionalTarget += studentScoresOverInstructionalTarget.length;
  });

  const percentageOverInstructionalTarget = Math.round(
    (totalStudentScoresOverInstructionalTarget / totalStudentsAssessed) * 100
  );

  const score = isFinite(percentageOverInstructionalTarget) ? percentageOverInstructionalTarget : null;
  return {
    score,
    totalStudentsAssessed: score === null ? null : totalStudentsAssessed
  };
};

export const getBenchmarkPeriodToIdMap = bmPeriods => {
  const benchmarkPeriodsToId = {};
  const periods = bmPeriods || [];
  periods.forEach(bmp => {
    benchmarkPeriodsToId[bmp.name.toLowerCase()] = bmp._id;
  });
  return benchmarkPeriodsToId;
};

const getBenchmarkScore = ({ benchmarkHistory, assessmentId, benchmarkPeriodId }) => {
  const benchmarkHistoryItems = benchmarkHistory.filter(item => item.benchmarkPeriodId === benchmarkPeriodId);
  let totalStudentsAssessed = 0;
  let totalStudentsMeetingTarget = 0;
  benchmarkHistoryItems.forEach(benchmarkHistoryItem => {
    if (benchmarkHistoryItem.assessmentResultMeasures) {
      const measureResult = benchmarkHistoryItem.assessmentResultMeasures.find(
        measure => measure.assessmentId === assessmentId
      );
      totalStudentsAssessed += getHistoryItemTargetScore(measureResult, "totalStudentsAssessed");
      totalStudentsMeetingTarget += getHistoryItemTargetScore(measureResult, "numberMeetingTarget");
    }
  });
  const percentMeetingTarget = Math.round((totalStudentsMeetingTarget / totalStudentsAssessed) * 100);
  const score = isFinite(percentMeetingTarget) ? percentMeetingTarget : null;
  return {
    score,
    totalStudentsAssessed: score === null ? null : totalStudentsAssessed
  };
};

const getScore = ({ measurePeriod, assessmentId, classwideHistory, benchmarkHistory, benchmarkPeriodId }) =>
  measurePeriod === "classwide"
    ? getClasswideScore(classwideHistory, assessmentId)
    : getBenchmarkScore({
        benchmarkHistory,
        assessmentId,
        benchmarkPeriodId
      });

export function validateGrowthAssessmentIds(assessmentComparisonMap, assessments) {
  const comparedAssessmentIds = [];
  availableSeasonalComparisons.forEach(seasonalComparison => {
    assessmentComparisonMap[seasonalComparison].forEach(comparison => {
      Object.entries(comparison).forEach(([, assessmentId]) => {
        comparedAssessmentIds.push(assessmentId);
      });
    });
  });
  const assessmentIdWithNoMatchingAssessment = comparedAssessmentIds.find(
    comparedAssId => !assessments.find(ass => ass._id === comparedAssId)
  );
  if (assessmentIdWithNoMatchingAssessment) {
    throw new Meteor.Error(
      "getGrowthResults",
      `Assessment with id: ${assessmentIdWithNoMatchingAssessment} could not be found`
    );
  }
  return true;
}

function getResult(assessmentId, assessmentName, measureResultData) {
  return {
    assessmentId,
    assessmentName,
    score: measureResultData.score,
    totalStudentsAssessed: measureResultData.totalStudentsAssessed
  };
}

export function getGrowthResults({ history = [], assessmentComparisonMap, assessments, bmPeriods }) {
  const { benchmarkHistory, classwideHistory } = getBenchmarkAndClasswideHistory(history);
  const benchmarkPeriodToId = getBenchmarkPeriodToIdMap(bmPeriods);
  if (isEmpty(benchmarkPeriodToId)) {
    return false;
  }
  validateGrowthAssessmentIds(assessmentComparisonMap, assessments);
  const growthData = {};
  availableSeasonalComparisons.forEach(seasonalComparison => {
    growthData[seasonalComparison] = [];
    assessmentComparisonMap[seasonalComparison].forEach(comparison => {
      const growthResult = {};
      Object.entries(comparison).forEach(([measurePeriod, assessmentId]) => {
        const measureResultData = getScore({
          measurePeriod,
          assessmentId,
          classwideHistory,
          benchmarkHistory,
          benchmarkPeriodId: benchmarkPeriodToId[measurePeriod]
        });
        const assessmentName = assessments.find(ass => ass._id === assessmentId).name;
        growthResult[measurePeriod] = getResult(assessmentId, assessmentName, measureResultData);
      });
      growthData[seasonalComparison].push(growthResult);
    });
  });
  return growthData;
}

function getMeasureColor(measurePeriod, customColorsByMeasurePeriod) {
  if (customColorsByMeasurePeriod) {
    return customColorsByMeasurePeriod[measurePeriod];
  }
  return measurePeriod === "classwide" ? classwideInterventionColor : benchmarkPeriodsToColor[measurePeriod];
}

function isNull(value) {
  return value === null;
}

function getChartYValue(measurePeriod, growthResult, customColorsByMeasurePeriod) {
  const isNA = isNil(growthResult.score);
  return {
    color: getMeasureColor(measurePeriod, customColorsByMeasurePeriod),
    labelText: isNA ? "N/A" : `${growthResult.score}%`,
    name: measurePeriod,
    y: isNA ? 0 : growthResult.score
  };
}

function getChartCategoryValue(growthResult) {
  return {
    name: growthResult.assessmentName,
    n: isNull(growthResult.score) ? null : growthResult.totalStudentsAssessed
  };
}

// adds an empty space between sets of data
function getFormattedData(totalLength, index, acc) {
  const isLastColumn = totalLength - 1 === index;
  const formattedData = { ...acc };
  if (!isLastColumn) {
    formattedData.scores.push(null);
    formattedData.categories.push(null);
  }
  return formattedData;
}

export function getParsedGrowthResultsForChart(growthResults, customColorsByMeasurePeriod) {
  const result = {};
  if (isEmpty(growthResults)) {
    return result;
  }
  Object.entries(growthResults).forEach(([seasonalComparison, comparisons]) => {
    const { scores, categories } = comparisons.reduce(
      (acc, comparison, index, list) => {
        Object.entries(comparison).forEach(([measurePeriod, growthResult]) => {
          acc.scores.push(getChartYValue(measurePeriod, growthResult, customColorsByMeasurePeriod));
          acc.categories.push({ ...getChartCategoryValue(growthResult), measurePeriod });
        });
        return getFormattedData(list.length, index, acc);
      },
      { scores: [], categories: [] }
    );
    result[seasonalComparison] = {
      scores,
      categories
    };
  });
  return result;
}

export function getParsedHSGrowthResultsForChart(growthResults) {
  const result = { categories: [], scores: [] };

  const dataLabels = [
    {
      color: "#000000",
      inside: false,
      enabled: true,
      align: "center",
      useHTML: true,
      formatter() {
        return this.point.n
          ? `<div class="text-center outline">${this.point.y.toFixed(1)}%<br />(n=${this.point.n})</div>`
          : "";
      },
      rotation: 0
    }
  ];
  const newResult = {
    categories: [],
    series: [
      {
        color: "#f9bf68",
        name: "Initial",
        data: [],
        dataLabels
      },
      {
        color: "#541f21",
        name: "Final",
        data: [],
        dataLabels
      }
    ]
  };
  if (isEmpty(growthResults)) {
    return result;
  }
  growthResults.forEach(stat => {
    newResult.categories.push({ name: stat.assessmentName });

    newResult.series[0].data.push({ y: stat.initial.atOrAbove, n: stat.initial.totalStudentsAssessed });
    newResult.series[1].data.push({
      y: stat.final.atOrAbove,
      n: stat.final.totalStudentsAssessed
    });
  });

  return newResult;
}
