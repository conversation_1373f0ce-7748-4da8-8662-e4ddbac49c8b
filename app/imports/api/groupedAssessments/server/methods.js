import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import * as auth from "../../authorization/server/methods";
import { GroupedAssessments } from "../groupedAssessments";

Meteor.methods({
  async updateAssessmentMeasuresForGroupedAssessments(groupName, assessmentMeasures) {
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to update assessment measures for GroupedAssessments");
    }
    check(groupName, String);
    check(assessmentMeasures, Array);

    await GroupedAssessments.updateAsync({ skillName: groupName }, { $set: { assessmentMeasures } });
  }
});
