import CronJobManager from "cron-job-manager";
import { Meteor } from "meteor/meteor";
import { get, random } from "lodash";
import moment from "moment";
import { Accounts } from "meteor/accounts-base";
import { Organizations } from "../organizations/organizations";
import { initRosterDocument, sendEmailForFailedRosterImport, validateAndImportRowData } from "../rostering/fetchData";
import { getLastDayOfMonth } from "../utilities/utilities";
import { failRosterImport } from "../rosterImports/methods";
import { Users } from "../users/users";
import { sendEmailForNewYearAccessReviewReminder } from "../utilities/methods";
import { getTimestampInfo } from "../helpers/getTimestampInfo";

const EMAIL_QUEUE_DELAY = 3000;

class CronManager {
  constructor() {
    this.manager = new CronJobManager();
  }

  addWatcher() {
    this.manager.add("NewYearEmailWatcher", "0 */12 * * *", this.newYearAccessEmailHandler(), {
      start: true,
      timeZone: moment.tz.guess()
    });
  }

  newYearAccessEmailHandler = () => async () => {
    const maxRandomDelayInMs = 10000;
    const orgs = await this.fetchOrganizationsForAccessReminder();
    let index = 0;
    for (const { _id: orgid } of orgs) {
      const users = await Users.find({
        "profile.orgid": orgid,
        "profile.siteAccess.role": { $in: ["arbitraryIdteacher", "arbitraryIdadmin"] }
      }).fetchAsync();
      if (users.length) {
        Meteor.setTimeout(() => {
          this.runAccessReminderCronJobFn(orgid)();
        }, EMAIL_QUEUE_DELAY * index + random(0, maxRandomDelayInMs));
      }
      index++;
    }
  };

  // eslint-disable-next-line class-methods-use-this
  fetchOrganizationsForAccessReminder(orgid = "") {
    const now = moment();
    const year = now.year();
    const month = now.month();
    const day = now.date();
    const pipeline = [{ $match: { isActive: true } }];

    if (orgid.length) {
      pipeline.push({
        $match: {
          _id: orgid
        }
      });
    }

    pipeline.push(
      {
        $project: {
          _id: 1,
          schoolYearBoundary: 1,
          accessReminderSentOn: 1,
          year: { $year: "$accessReminderSentOn" }
        }
      },
      {
        $match: {
          $and: [
            {
              $or: [
                { "schoolYearBoundary.month": { $lt: month + 1 } },
                {
                  "schoolYearBoundary.month": { $eq: month + 1 },
                  "schoolYearBoundary.day": { $lt: day }
                }
              ]
            },
            {
              $or: [
                { accessReminderSentOn: { $eq: null } },
                { accessReminderSentOn: { $exists: false } },
                { year: { $lt: year } }
              ]
            }
          ]
        }
      }
    );

    return Organizations.aggregate(pipeline);
  }

  runAccessReminderCronJobFn = orgid => async () => {
    const org = await this.fetchOrganizationsForAccessReminder(orgid);
    const lastModified = await getTimestampInfo("runAccessReminderCronJobFn", orgid);

    if (org.length) {
      await Organizations.updateAsync(
        { _id: orgid },
        {
          $set: {
            accessReminderSentOn: new Date(),
            lastModified
          }
        }
      );

      sendEmailForNewYearAccessReviewReminder({
        orgid,
        rosterPageLink: Accounts.buildEmailUrl(`data-admin/manage-users/${orgid}`)
      });
    }
  };

  // eslint-disable-next-line class-methods-use-this
  async runRosterImport(orgid) {
    console.log(`[CronManager] runRosterImport called with orgid: ${orgid}`);
    const syncSchedule = await this.fetchSyncSchedule(orgid);
    if (!syncSchedule) {
      console.log(`[CronManager] No syncSchedule found for orgid: ${orgid}`);
      return;
    }

    const org = await Organizations.findOneAsync(
      { _id: orgid, rostering: { $in: ["rosterEdFi", "rosterOR"] } },
      { fields: { schoolYearBoundary: 1 } }
    );

    if (!org) {
      console.log(`(CronManager): remove cron job for orgid: ${orgid} because it no longer uses auto rostering`);
      this.removeRosteringJob(orgid);
      return;
    }

    const { schoolYearBoundary } = org;

    const { startDate, endDate, frequency, schoolYear, timeZone } = syncSchedule;
    const jobs = this.getJobsByOrgId(orgid);
    const currentJob = jobs?.[orgid];

    const cronTime = this.getCronTime(syncSchedule);
    if (currentJob?.cronExpression !== cronTime && Meteor.settings.public.ENVIRONMENT !== "LOCAL") {
      console.log(`(CronManager): remove current instance's cron job for orgid: ${orgid} because it's out of sync`);
      this.removeRosteringJob(orgid);
      return;
    }

    const today = new Date();
    const endDateObject = new Date(endDate);
    const schoolYearEndDate = moment.tz({ ...schoolYearBoundary, year: schoolYear }, timeZone).endOf("day");

    if (endDate && !!endDateObject.valueOf() && today.valueOf() > endDateObject.valueOf()) {
      console.log(`(CronManager): remove cron job for orgid: ${orgid} because of sync schedule end date: ${endDate}`);
      this.removeRosteringJob(orgid);
      const lastModified = await getTimestampInfo("runRosterImport", orgid);
      await Organizations.updateAsync(
        { _id: orgid },
        {
          $unset: { "rosteringSettings.syncSchedule": "" },
          $set: { lastModified }
        }
      );
      return;
    }

    if (today.valueOf() > schoolYearEndDate.valueOf()) {
      console.log(
        `(CronManager): remove cron job for orgid: ${orgid} because of school year end: ${schoolYearEndDate}`
      );
      this.removeRosteringJob(orgid);
      const lastModified = await getTimestampInfo("runRosterImport", orgid);
      await Organizations.updateAsync(
        { _id: orgid },
        {
          $unset: { "rosteringSettings.syncSchedule": "" },
          $set: { lastModified }
        }
      );
      return;
    }

    const startDateObject = new Date(startDate);
    const startDateDayOfMonth = startDateObject.getDate();

    let shouldImportData = true;
    if (frequency === "monthly") {
      shouldImportData =
        startDateDayOfMonth < 28 || (startDateDayOfMonth >= 28 && today.getDate() === getLastDayOfMonth(today));
    }
    if (shouldImportData) {
      this.tryImportingData(orgid);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  tryImportingData(orgid) {
    console.log(`[CronManager] tryImportingData called with orgid: ${orgid}`);
    const maxRandomDelayInMs = 5000;
    Meteor.setTimeout(async () => {
      console.log(`[CronManager] After timeout, calling initRosterDocument with orgid: ${orgid}`);
      const rosterImportId = await initRosterDocument(orgid);
      if (!rosterImportId) {
        return;
      }
      validateAndImportRowData(orgid, rosterImportId).catch(e => {
        const endpointResponseErrorMessage = e.statusMessage ? `${e.statusCode}: ${e.statusMessage}` : null;
        failRosterImport({
          error: { message: e.message || endpointResponseErrorMessage || e.reason },
          rosterImportId,
          shouldThrow: false
        });
        sendEmailForFailedRosterImport(orgid, rosterImportId);
      });
    }, random(0, maxRandomDelayInMs));
  }

  cronJobFn = (orgid, isInFuture = false) => {
    console.log(`[CronManager] Creating cronJobFn for orgid: ${orgid}, isInFuture: ${isInFuture}`);
    const self = this;
    return async () => {
      console.log(`[CronManager] cronJobFn executing for orgid: ${orgid}, this: ${this}, self: ${self}`);
      await self.runRosterImport(orgid);
      if (isInFuture) {
        await self.updateFutureRosteringJob(orgid);
      }
    };
  };

  // eslint-disable-next-line class-methods-use-this
  async fetchSyncSchedule(orgid) {
    const org = await Organizations.findOneAsync({ _id: orgid }, { fields: { "rosteringSettings.syncSchedule": 1 } });
    return get(org, "rosteringSettings.syncSchedule");
  }

  async initJobs() {
    const environmentsWithAccessReminder = ["DEV", "QA", "PROD"];
    const environmentsWithoutAccessToSyncSchedule = ["LOCAL"];
    const currentEnvironment = Meteor.settings.public.ENVIRONMENT;

    if (environmentsWithAccessReminder.includes(currentEnvironment)) {
      console.log("Yearly access reminder watcher initialized.");
      this.addWatcher();
    }
    if (!environmentsWithoutAccessToSyncSchedule.includes(currentEnvironment)) {
      const orgsWithSyncSchedule = await Organizations.find(
        {
          "rosteringSettings.syncSchedule": { $exists: true },
          "rosteringSettings.filters.schools.0": { $exists: true },
          "rosteringSettings.filters.teachers.0": { $exists: true },
          "rosteringSettings.filters.classes.0": { $exists: true },
          rostering: { $in: ["rosterEdFi", "rosterOR"] }
        },
        { fields: { rosteringSettings: 1 } }
      ).fetchAsync();

      for (const {
        _id: orgid,
        rosteringSettings: { syncSchedule }
      } of orgsWithSyncSchedule) {
        // eslint-disable-next-line no-await-in-loop
        await this.setRosteringJob({ orgid, syncSchedule });
      }
    }
    this.listJobs();
  }

  async updateFutureRosteringJob(orgid) {
    const syncSchedule = await this.fetchSyncSchedule(orgid);
    if (!syncSchedule) return;

    const cronTime = this.getCronTime(syncSchedule);
    this.manager.update(orgid, cronTime, this.cronJobFn(orgid));
    this.manager.start(orgid);
    this.listJobs();
  }

  // eslint-disable-next-line class-methods-use-this
  getCronTime({ startDate, time, frequency, isInFuture = false }) {
    const [hours, minutes] = time.split(":");
    const date = new Date(startDate);
    let dayOfWeek = "*";
    let dayOfMonth = "*";
    const monthNumber = "*";
    if (frequency === "weekly") {
      // Week starts from Sunday as day 0 and end on Saturday as day 6
      dayOfWeek = date.getDay();
    }
    if (frequency === "monthly") {
      dayOfMonth = date.getDate() < 28 ? date.getDate() : "28-31";
    }
    if (isInFuture) {
      return new Date(`${startDate}T${time}`);
    }
    if (Meteor.settings.public.ENVIRONMENT === "LOCAL") {
      const seconds = new Date().getSeconds() + 5;
      const customMinutes = seconds > 59 ? new Date().getMinutes() + 1 : new Date().getMinutes();
      return `${seconds % 60} ${customMinutes} ${new Date().getHours()} ${dayOfMonth} ${monthNumber} ${dayOfWeek}`;
    }
    return `${minutes} ${hours} ${dayOfMonth} ${monthNumber} ${dayOfWeek}`;
  }

  listJobs() {
    // Server log
    console.log("===[ Cronjobs ]===");
    const cronJobs = Object.entries(this.manager.jobs);
    if (!cronJobs.length) {
      console.log("No cron jobs");
    } else {
      cronJobs.forEach(([orgid, cronObject]) => {
        const { source: cronExpression, zone } = cronObject.cronTime;
        const status = cronObject.running ? "Running" : "Stopped";
        console.log(
          `${orgid}\t${cronExpression}\t(GMT ${moment()
            .tz(zone)
            .format("Z")}) ${zone}\t${status}`
        );
      });
    }
    console.log("==================");
  }

  getJobsByOrgId() {
    return Object.entries(this.manager.jobs).reduce((a, [orgid, cronObject]) => {
      const cronExpression = cronObject.cronTime.source;
      // eslint-disable-next-line no-param-reassign
      a[orgid] = {
        cronExpression: moment.isMoment(cronExpression) ? "" : cronExpression,
        status: cronObject.running ? "Running" : "Stopped"
      };
      return a;
    }, {});
  }

  async setRosteringJob({ orgid, syncSchedule, shouldListJobs = false }) {
    let orgSyncSchedule = syncSchedule;
    if (!syncSchedule) {
      orgSyncSchedule = get(
        await Organizations.findOneAsync(
          { _id: orgid, rostering: { $in: ["rosterEdFi", "rosterOR"] } },
          { fields: { rosteringSettings: 1 } }
        ),
        "rosteringSettings.syncSchedule"
      );
    }
    if (this.manager.exists(orgid)) {
      this.manager.deleteJob(orgid);
    }
    if (!orgSyncSchedule) {
      throw new Meteor.Error("500", `CronManager: Organization with id: ${orgid} can't have job scheduled.`);
    }

    const { startDate, endDate, timeZone } = orgSyncSchedule;
    const shortDateToday = new Date().toISOString().split("T")[0];
    const isInFuture = startDate > shortDateToday;
    const isExpired = endDate < shortDateToday;
    if (!isExpired) {
      const cronTime = this.getCronTime({ ...orgSyncSchedule, isInFuture });
      this.manager.add(orgid, cronTime, this.cronJobFn(orgid, isInFuture), {
        start: true,
        timeZone
      });
    }
    if (shouldListJobs) {
      this.listJobs();
    }

    return this;
  }

  removeRosteringJob(orgid) {
    if (this.manager.exists(orgid)) {
      this.manager.deleteJob(orgid);
      this.listJobs();
    }
    return `CronJob has been successfully removed for this (${orgid}) organization.`;
  }
}

export default new CronManager();
