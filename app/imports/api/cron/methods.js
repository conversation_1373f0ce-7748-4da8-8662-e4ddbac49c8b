import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import cronManager from "./cronManager";
import * as auth from "../authorization/server/methods";
import { getMeteorUserId } from "../utilities/utilities";

Meteor.methods({
  async "Cron:removeCronJob"(orgid) {
    check(orgid, String);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use Cron:removeCronJob");
    }
    return cronManager.removeRosteringJob(orgid);
  },
  async "Cron:getJobsByOrgId"() {
    const userId = getMeteorUserId();
    if (!(await auth.hasAccess(["superAdmin", "universalDataAdmin"], { userId }))) {
      throw new Meteor.Error(403, "User is not authorized to use Cron:getJobsByOrgId");
    }
    return cronManager.getJobsByOrgId();
  }
});
