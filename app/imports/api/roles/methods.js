import get from "lodash/get";
import { getMeteorUser, getMeteorUserSync } from "../utilities/utilities";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";

// TODO(fmazur) - replace code calls with context
export const isSupportUser = () => get(getMeteorUserSync(), "profile.siteAccess[0].role") === ROLE_IDS.support;

export const isUniversalCoach = async () =>
  get(await getMeteorUser(), "profile.siteAccess[0].role") === ROLE_IDS.universalCoach;

export const isUniversalCoachSync = () =>
  get(getMeteorUserSync(), "profile.siteAccess[0].role") === ROLE_IDS.universalCoach;

export const roles = [
  {
    _id: ROLE_IDS.dataAdmin,
    label: "dataAdmin",
    name: "dataAdmin"
  },
  {
    _id: ROLE_IDS.superAdmin,
    label: "superAdmin",
    name: "superAdmin"
  },
  {
    _id: ROLE_IDS.teacher,
    label: "teacher",
    name: "teacher"
  },
  {
    _id: ROLE_IDS.admin,
    label: "admin",
    name: "admin"
  },
  {
    _id: ROLE_IDS.support,
    label: "support",
    name: "support"
  },
  {
    _id: ROLE_IDS.universalCoach,
    label: "universalCoach",
    name: "universalCoach"
  },
  {
    _id: ROLE_IDS.universalDataAdmin,
    label: "universalDataAdmin",
    name: "universalDataAdmin"
  },
  {
    _id: "arbitraryIddownloader",
    label: "downloader",
    name: "downloader"
  }
];

export const isAdminOrUniversalCoach = (siteId, siteAccess = []) => {
  if (isUniversalCoachSync()) {
    return true;
  }
  const currentSite = siteAccess.find(singleSite => singleSite.siteId === siteId);
  return currentSite && currentSite.role === ROLE_IDS.admin;
};

export default roles;
