import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { map } from "lodash";
import { Grades } from "./grades";
import { StudentGroups } from "../studentGroups/studentGroups";

export async function insert(gradeDoc) {
  check(gradeDoc, Object);
  Grades.validate(gradeDoc);
  await Grades.insertAsync(gradeDoc);
}

export async function getGradesWithGroupsForYears(siteId, schoolYears) {
  const yearsWithGrades = await Promise.all(
    schoolYears.map(async schoolYear => {
      const query = { siteId, isActive: true, schoolYear };
      const sgs = await StudentGroups.find(query, { fields: { grade: 1 } }).fetchAsync();
      const sgGrades = map(sgs, "grade");
      const grades = await Grades.find(
        { _id: { $in: sgGrades } },
        { fields: { sortorder: 1, display: 1 }, sort: { sortorder: 1 } }
      ).fetchAsync();
      return [schoolYear, grades];
    })
  );

  return Object.fromEntries(yearsWithGrades);
}

Meteor.methods({
  "Grades:getGradesWithGroupsForYears"(siteId, schoolYears = []) {
    check(siteId, String);
    check(schoolYears, [Number]);
    if (!this.userId || !siteId) {
      throw new Meteor.Error("Could not retrieve grades");
    }

    return getGradesWithGroupsForYears(siteId, schoolYears);
  }
});
