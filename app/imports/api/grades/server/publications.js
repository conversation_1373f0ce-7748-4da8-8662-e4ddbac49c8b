import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import map from "lodash/map";
import { Grades } from "../grades";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("Grades", function grades() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Grades.find({}, { fields: { sortorder: 1, display: 1 } });
});

Meteor.publish("GradesWithStudentGroupsInSite", async function gradesWithStudentGroupsInSite(siteId, schoolYear) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(siteId, Match.Maybe(String));
  check(schoolYear, Match.Maybe(Number));

  const query = { siteId, isActive: true };
  if (schoolYear) {
    query.schoolYear = schoolYear;
  }
  const sgs = await StudentGroups.find(query, { fields: { grade: 1 } }).fetchAsync();
  const sgGrades = map(sgs, "grade");
  return Grades.find({ _id: { $in: sgGrades } }, { fields: { sortorder: 1, display: 1 }, sort: { sortorder: 1 } });
});
