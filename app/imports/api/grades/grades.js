import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const Grades = new Mongo.Collection("Grades");

Grades.schema = new SimpleSchema({
  _id: { type: String },
  sortorder: { type: Number },
  display: { type: String }
});

Grades.validate = grade => {
  Grades.schema.validate(grade);
};
Grades.isValid = grade => Grades.schema.namedContext("gradeContext").validate(grade);
