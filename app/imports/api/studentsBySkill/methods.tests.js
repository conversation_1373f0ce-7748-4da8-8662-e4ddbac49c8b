import { shuffle } from "lodash";
import { Assessments } from "../assessments/assessments";
import { GroupedAssessments } from "../groupedAssessments/groupedAssessments";
import { Students } from "../students/students";
import { StudentsBySkill } from "./studentsBySkill";
import { assignStudentToSkillGroup, getSkillGroupAssignments } from "./methods";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";

const schoolYear = 2019;
const orgid = "testOrgId";
const siteId = "testSiteId";
const studentId = "studentId";
const firstAssessmentId = "firstAssessmentId";
const firstAssessmentName = "First Assessment Name";
const firstAssessmentMeasure = "1";

const secondAssessmentId = "secondAssessmentId";
const secondAssessmentName = "Second Assessment Name";
const secondAssessmentMeasure = "2";

const thirdAssessmentId = "thirdAssessmentId";
const thirdAssessmentName = "Third Assessment Name";
const thirdAssessmentMeasure = "3";

const fourthAssessmentId = "fourthAssessmentId";
const fourthAssessmentName = "Fourth Assessment Name";
const fourthAssessmentMeasure = "4";

const benchmarkAssessmentId = "benchmarkAssessmentId";
const benchmarkAssessmentName = "Assessment Name";

const firstAssessmentsGroupId = "firstAssessmentsGroupId";
const firstSkillName = "First Skill Name";

const secondAssessmentsGroupId = "secondAssessmentsGroupId";
const secondSkillName = "Second Skill Name";

const thirdAssessmentsGroupId = "thirdAssessmentsGroupId";
const thirdSkillName = "Third Skill Name";

const scoreTargets = [13, 26, 299];

const studentTemplate = {
  _id: studentId,
  orgid,
  schoolYear,
  grade: "01",
  identity: {
    name: {
      firstName: "Jane",
      lastName: "Mary"
    }
  },
  currentSkill: {
    benchmarkAssessmentId,
    benchmarkAssessmentName,
    assessmentId: firstAssessmentId,
    assessmentName: firstAssessmentName,
    assessmentTargets: scoreTargets
  }
};

const interventionsData = [
  {
    interventionId: "example",
    interventionLabel: "Intervention Adviser - Guided Practice",
    interventionAbbrv: "GP"
  }
];

const assessments = [
  {
    _id: firstAssessmentId,
    name: firstAssessmentName,
    schoolYear,
    orgid,
    monitorAssessmentMeasure: firstAssessmentMeasure
  },
  {
    _id: secondAssessmentId,
    name: secondAssessmentName,
    schoolYear,
    orgid,
    monitorAssessmentMeasure: secondAssessmentMeasure
  },
  {
    _id: thirdAssessmentId,
    name: thirdAssessmentName,
    schoolYear,
    orgid,
    monitorAssessmentMeasure: thirdAssessmentMeasure
  },
  {
    _id: fourthAssessmentId,
    name: fourthAssessmentName,
    schoolYear,
    orgid,
    monitorAssessmentMeasure: fourthAssessmentMeasure
  }
];

const groupedAssessments = [
  {
    _id: firstAssessmentsGroupId,
    skillName: firstSkillName,
    assessmentMeasures: [firstAssessmentMeasure, secondAssessmentMeasure]
  },
  { _id: secondAssessmentsGroupId, skillName: secondSkillName, assessmentMeasures: [thirdAssessmentMeasure] },
  { _id: thirdAssessmentsGroupId, skillName: thirdSkillName, assessmentMeasures: [fourthAssessmentMeasure] }
];

const initialStudentsBySkill = [
  {
    studentsBelowInstructionalTarget: [],
    studentsBelowMasteryTarget: [],
    studentsWithoutSkillHistory: [],
    skillName: firstSkillName,
    assessmentGroupId: firstAssessmentsGroupId,
    siteId
  },
  {
    studentsBelowInstructionalTarget: [],
    studentsBelowMasteryTarget: [],
    studentsWithoutSkillHistory: [],
    skillName: secondSkillName,
    assessmentGroupId: secondAssessmentsGroupId,
    siteId
  },
  {
    studentsBelowInstructionalTarget: [],
    studentsBelowMasteryTarget: [],
    studentsWithoutSkillHistory: [],
    skillName: thirdSkillName,
    assessmentGroupId: thirdAssessmentsGroupId,
    siteId
  }
];

const generateHistoryEntry = ({ assessmentId, assessmentName, score }) => ({
  benchmarkAssessmentId,
  benchmarkAssessmentName,
  assessmentId,
  assessmentName,
  assessmentTargets: scoreTargets,
  assessmentResultMeasures: [
    {
      assessmentId: benchmarkAssessmentId,
      assessmentName: benchmarkAssessmentName,
      targetScores: scoreTargets,
      studentResults: [
        {
          studentId,
          score: "0"
        }
      ]
    },
    {
      assessmentId: firstAssessmentId,
      assessmentName: firstAssessmentName,
      targetScores: scoreTargets,
      studentResults: [
        {
          studentId,
          score
        }
      ]
    }
  ],
  type: "individual"
});

describe("assignStudentToSkillGroup", () => {
  beforeAll(async () => {
    await Assessments.insertAsync(assessments);
    await GroupedAssessments.insertAsync(groupedAssessments);
  });
  afterAll(async () => {
    await Assessments.removeAsync({});
    await GroupedAssessments.removeAsync({});
  });

  describe("with no relevant data", () => {
    beforeEach(async () => {
      const student = { ...studentTemplate };
      delete student.currentSkill;
      await Students.insertAsync(student);
      await StudentsBySkill.insertAsync(initialStudentsBySkill);
    });
    afterEach(async () => {
      await Students.removeAsync({});
      await StudentsBySkill.removeAsync({});
    });

    it("should not do anything if a student has no currentSkill", async () => {
      await assignStudentToSkillGroup({ siteId, studentId });
      const studentsBySkill = await StudentsBySkill.find({ siteId }).fetchAsync();
      studentsBySkill.forEach(sbs => {
        /* eslint-disable-next-line no-param-reassign */
        delete sbs._id; // for some reason projection in query above didn't work
        expect(initialStudentsBySkill).toEqual(expect.arrayContaining([sbs]));
      });
    });
  });

  describe("for a student with no prior SkillGroup assignments", () => {
    beforeEach(async () => {
      await StudentsBySkill.insertAsync(initialStudentsBySkill);
    });
    afterEach(async () => {
      await StudentsBySkill.removeAsync({});
      await Students.removeAsync({});
    });

    it("should assign the student to studentsWithoutSkillHistory if he or she has no history", async () => {
      const student = { ...studentTemplate };
      await Students.insertAsync(student);
      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsWithoutSkillHistory).toContain(studentId);
    });
    it("should assign the student to studentsWithoutSkillHistory if he or she is currently working on a differentSkill than the last history entry", async () => {
      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = secondAssessmentId;
      student.currentSkill.assessmentName = secondAssessmentName;
      student.currentSkill.interventions = interventionsData;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "3"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsWithoutSkillHistory).toContain(studentId);
    });
    it("should assign the student to studentsBelowInstructionalTarget if he or she scored below the instructional target", async () => {
      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = firstAssessmentId;
      student.currentSkill.assessmentName = firstAssessmentName;
      student.currentSkill.interventions = interventionsData;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "3"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsBelowInstructionalTarget).toContain(studentId);
    });
    it("should assign the student to studentsBelowMasteryTarget if he or she scored below the mastery target", async () => {
      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = firstAssessmentId;
      student.currentSkill.assessmentName = firstAssessmentName;
      student.currentSkill.interventions = interventionsData;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "25"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsBelowMasteryTarget).toContain(studentId);
    });
  });
  describe("for a student with a prior SkillGroup assignment", () => {
    afterEach(async () => {
      await StudentsBySkill.removeAsync({});
      await Students.removeAsync({});
    });
    it("should pull the student first based on the last history entry before assigning the student to studentsWithoutSkillHistory if he or she starts a new skill", async () => {
      const studentsBySkill = JSON.parse(JSON.stringify(initialStudentsBySkill)); // deep copy of template groups
      studentsBySkill[0].studentsBelowInstructionalTarget = [studentId];
      await StudentsBySkill.insertAsync(studentsBySkill);

      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = secondAssessmentId;
      student.currentSkill.assessmentName = secondAssessmentName;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "3"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsWithoutSkillHistory).toContain(studentId);
      expect(relevantStudentsBySkill.studentsBelowInstructionalTarget).toEqual([]);
      expect(relevantStudentsBySkill.studentsBelowMasteryTarget).toEqual([]);
    });
    it("pull the student first based on the latest history entry before assigning the student to studentsBelowMasteryTarget if he or she scored below the instructional target", async () => {
      const studentsBySkill = JSON.parse(JSON.stringify(initialStudentsBySkill)); // deep copy of template groups
      studentsBySkill[0].studentsBelowInstructionalTarget = [studentId];
      await StudentsBySkill.insertAsync(studentsBySkill);

      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = firstAssessmentId;
      student.currentSkill.assessmentName = firstAssessmentName;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "3"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const relevantStudentsBySkill = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(relevantStudentsBySkill.studentsWithoutSkillHistory).toEqual([]);
      expect(relevantStudentsBySkill.studentsBelowInstructionalTarget).toContain(studentId);
      expect(relevantStudentsBySkill.studentsBelowMasteryTarget).toEqual([]);
    });
    it("should move the student to a different SkillGroup if he or she starts a new skill from a different group", async () => {
      const studentsBySkill = JSON.parse(JSON.stringify(initialStudentsBySkill)); // deep copy of template groups
      studentsBySkill[0].studentsBelowMasteryTarget = [studentId];
      await StudentsBySkill.insertAsync(studentsBySkill);

      const student = { ...studentTemplate };
      student.currentSkill.assessmentId = thirdAssessmentId;
      student.currentSkill.assessmentName = thirdAssessmentName;
      const firstHistoryEntry = generateHistoryEntry({
        assessmentId: firstAssessmentId,
        assessmentName: firstAssessmentName,
        score: "3"
      });
      student.history = [firstHistoryEntry];

      await Students.insertAsync(student);

      await assignStudentToSkillGroup({ siteId, studentId });

      const firstStudentsBySkillGroup = await StudentsBySkill.findOneAsync({
        assessmentGroupId: firstAssessmentsGroupId
      });
      expect(firstStudentsBySkillGroup.studentsWithoutSkillHistory).toEqual([]);
      expect(firstStudentsBySkillGroup.studentsBelowInstructionalTarget).toEqual([]);
      expect(firstStudentsBySkillGroup.studentsBelowMasteryTarget).toEqual([]);

      const secondStudentsBySkillGroup = await StudentsBySkill.findOneAsync({
        assessmentGroupId: secondAssessmentsGroupId
      });
      expect(secondStudentsBySkillGroup.studentsWithoutSkillHistory).toContain(studentId);
      expect(secondStudentsBySkillGroup.studentsBelowInstructionalTarget).toEqual([]);
      expect(secondStudentsBySkillGroup.studentsBelowMasteryTarget).toEqual([]);
    });
  });
});

describe("getSkillGroupAssignments", () => {
  const teacherId = "teacherId";
  const sgId1 = "studentGroupId1";
  const sgId2 = "studentGroupId2";
  const sgId3 = "studentGroupId3";
  const sgId4 = "studentGroupId4";
  beforeEach(async () => {
    await StudentGroups.insertAsync(
      shuffle([
        { _id: sgId1, schoolYear, siteId, orgid, ownerIds: [teacherId], grade: "01", name: "Group C" }, // 5
        { _id: sgId2, schoolYear, siteId, orgid, ownerIds: [teacherId], name: "Group B", grade: "05" }, // 1
        { _id: sgId3, schoolYear, siteId, orgid, ownerIds: [teacherId], name: "Group A", grade: "05" }, // 1
        { _id: sgId4, schoolYear, siteId, orgid, ownerIds: [teacherId], grade: "01", name: "Group D" } // 1
      ])
    );
    await StudentGroupEnrollments.insertAsync(
      shuffle([
        { studentGroupId: sgId1, studentId: "studentId1" },
        { studentGroupId: sgId4, studentId: "studentId2" },
        { studentGroupId: sgId4, studentId: "studentId3" },
        { studentGroupId: sgId1, studentId: "studentId4" },
        { studentGroupId: sgId1, studentId: "studentId5" },
        { studentGroupId: sgId1, studentId: "studentId6" },
        { studentGroupId: sgId2, studentId: "studentId7" },
        { studentGroupId: sgId3, studentId: "studentId8" }
      ])
    );
    await Students.insertAsync(
      shuffle([
        {
          _id: "studentId1",
          currentSkill: { assessmentName: "Skill1" },
          grade: "01",
          identity: { name: { firstName: "First1", lastName: "Last1" } }
        },
        {
          _id: "studentId2",
          currentSkill: { assessmentName: "Skill2 2" },
          grade: "01",
          identity: { name: { firstName: "First2", lastName: "Last2" } }
        },
        {
          _id: "studentId3",
          currentSkill: { assessmentName: "Skill1" },
          grade: "01",
          identity: { name: { firstName: "First3", lastName: "Last3" } }
        },
        {
          _id: "studentId4",
          currentSkill: { assessmentName: "Skill2 1" },
          grade: "01",
          identity: { name: { firstName: "First4", lastName: "Last4" } }
        },
        {
          _id: "studentId5",
          currentSkill: { assessmentName: "Skill2 1" },
          grade: "01",
          identity: { name: { firstName: "First5", lastName: "Last5" } }
        },
        { _id: "studentId6", grade: "01", identity: { name: { firstName: "First6", lastName: "Last6" } } },
        {
          _id: "studentId7",
          grade: "05",
          currentSkill: { assessmentName: "B" },
          identity: { name: { firstName: "First7", lastName: "Last7" } }
        },
        {
          _id: "studentId8",
          grade: "05",
          currentSkill: { assessmentName: "A" },
          identity: { name: { firstName: "First8", lastName: "Last8" } }
        }
      ])
    );
  });

  afterEach(async () => {
    await StudentGroups.removeAsync({});
    await StudentGroupEnrollments.removeAsync({});
    await Students.removeAsync({});
    await StudentsBySkill.removeAsync({});
  });

  it("should return empty list", async () => {
    await StudentsBySkill.removeAsync({});
    const result = await getSkillGroupAssignments({ siteId, schoolYear, orgid, userId: teacherId });
    const expected = [];
    expect(result).toEqual(expected);
  });
  it("should return skill groups", async () => {
    await StudentsBySkill.insertAsync(
      shuffle([
        {
          _id: "sbs1",
          skillName: "Skill1",
          siteId,
          studentsWithoutSkillHistory: [],
          studentsBelowInstructionalTarget: ["studentId3"],
          studentsBelowMasteryTarget: ["studentId1"]
        },
        {
          _id: "sbs2",
          skillName: "Skill2",
          siteId,
          studentsWithoutSkillHistory: ["studentId5"],
          studentsBelowInstructionalTarget: ["studentId7", "studentId8"],
          studentsBelowMasteryTarget: ["studentId4", "studentId2"]
        }
      ])
    );
    const result = await getSkillGroupAssignments({ siteId, schoolYear, orgid, userId: teacherId });

    const expected = [
      {
        skillName: "Skill1",
        students: [
          {
            _id: "studentId3",
            firstName: "First3",
            grade: "01",
            interventionName: "Skill1",
            lastName: "Last3",
            skillProgress: "Acquisition",
            studentGroupName: "Group D"
          },
          {
            _id: "studentId1",
            firstName: "First1",
            grade: "01",
            interventionName: "Skill1",
            lastName: "Last1",
            skillProgress: "Fluency",
            studentGroupName: "Group C"
          }
        ]
      },
      {
        skillName: "Skill2",
        students: [
          {
            _id: "studentId4",
            firstName: "First4",
            grade: "01",
            interventionName: "Skill2 1",
            lastName: "Last4",
            skillProgress: "Fluency",
            studentGroupName: "Group C"
          },
          {
            _id: "studentId5",
            firstName: "First5",
            grade: "01",
            interventionName: "Skill2 1",
            lastName: "Last5",
            skillProgress: "No Progress",
            studentGroupName: "Group C"
          },
          {
            _id: "studentId2",
            firstName: "First2",
            grade: "01",
            interventionName: "Skill2 2",
            lastName: "Last2",
            skillProgress: "Fluency",
            studentGroupName: "Group D"
          }
        ]
      },
      {
        skillName: "Skill2",
        students: [
          {
            _id: "studentId8",
            firstName: "First8",
            grade: "05",
            interventionName: "A",
            lastName: "Last8",
            skillProgress: "Acquisition",
            studentGroupName: "Group A"
          },
          {
            _id: "studentId7",
            firstName: "First7",
            grade: "05",
            interventionName: "B",
            lastName: "Last7",
            skillProgress: "Acquisition",
            studentGroupName: "Group B"
          }
        ]
      }
    ];
    expect(result).toEqual(expected);
  });
});
