import { LoadingCounter } from "./loadingCounter.js";

export async function incWaitingOn(count, message) {
  const lc = (await LoadingCounter.findOneAsync({})) || { _id: "lc_id" };
  await LoadingCounter.upsertAsync(lc._id, {
    $inc: {
      waitingOn: count ? parseInt(count) : 1
    },
    $push: {
      messages: message || "Loading"
    }
  });
}

export async function decWaitingOn(count) {
  const lc = await LoadingCounter.findOneAsync({});
  await LoadingCounter.updateAsync(lc._id, {
    $inc: {
      waitingOn: count ? -1 * parseInt(count) : -1
    },
    $pop: {
      messages: 1
    }
  });
}

export async function clearWaitingOn() {
  const lc = await LoadingCounter.findOneAsync({});
  await LoadingCounter.updateAsync(lc._id, {
    $set: {
      waitingOn: 0,
      messages: []
    }
  });
}
