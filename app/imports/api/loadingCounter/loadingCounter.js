import { Mongo } from "meteor/mongo";
import { Meteor } from "meteor/meteor";

export const LoadingCounter = new Mongo.Collection(null);

Meteor.startup(async () => {
  if ((await LoadingCounter.find({}).countAsync()) < 1) {
    await LoadingCounter.insertAsync({
      waitingOn: 0,
      messages: []
    });
  }
});

LoadingCounter.allow({
  insert() {
    return true;
  },
  update() {
    return true;
  },
  remove() {
    return true;
  }
});
