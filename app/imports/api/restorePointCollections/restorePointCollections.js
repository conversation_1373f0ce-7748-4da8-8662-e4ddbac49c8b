import { Mongo } from "meteor/mongo";

export const AssessmentResultsRestorePoint = new Mongo.Collection("AssessmentResultsRestorePoint");
export const BenchmarkWindowsRestorePoint = new Mongo.Collection("BenchmarkWindowsRestorePoint");
export const StudentGroupsRestorePoint = new Mongo.Collection("StudentGroupsRestorePoint");
export const StudentsRestorePoint = new Mongo.Collection("StudentsRestorePoint");
export const StudentsBySkillRestorePoint = new Mongo.Collection("StudentsBySkillRestorePoint");
