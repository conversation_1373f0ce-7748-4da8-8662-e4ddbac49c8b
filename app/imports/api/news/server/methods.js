import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import * as auth from "../../authorization/server/methods";
import { News } from "../news";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import { getMeteorUserId } from "../../utilities/utilities";

Meteor.methods({
  async "News:getActiveMessage"({ siteId, orgid, multi = false } = {}) {
    check(siteId, Match.Maybe(String));
    check(orgid, Match.Maybe(String));
    check(multi, Match.Maybe(Boolean));

    const { userId } = this;

    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    } else if (
      await auth.hasAccess(["teacher", "support", "universalCoach", "superAdmin", "universalDataAdmin"], {
        userId,
        orgid,
        siteId
      })
    ) {
      return getActiveMessage(multi);
    }
    if (siteId) {
      if (await auth.hasAccess(["admin"], { userId, siteId })) {
        return getActiveMessage(multi);
      }
    }
    if (orgid) {
      if (await auth.hasAccess(["dataAdmin"], { userId, orgid })) {
        return getActiveMessage(multi);
      }
    }
    throw new Meteor.Error("News:getActiveMessage", "Invalid permissions to call method News:getActiveMessage");
  },
  async "News:getAllMessages"() {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (await auth.hasAccess(["superAdmin"], { userId: this.userId })) {
      return getAllMessages();
    }
    throw new Meteor.Error("News:getAllMessages", "Invalid permissions to call method News:getAllMessages");
  },
  async "News:addNewMessage"(messageParams) {
    check(
      messageParams,
      Match.ObjectIncluding({
        messageColor: String,
        messageTextColor: String,
        buttonIconColor: String,
        buttonColor: String,
        messageContent: String,
        learnMoreUrl: String,
        isLearnMoreActive: Boolean,
        isSupportLinkActive: Boolean
      })
    );
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (await auth.hasAccess(["superAdmin"], { userId: this.userId })) {
      return addNewMessage(messageParams);
    }
    throw new Meteor.Error("News:addNewMessage", "Invalid permissions to call method News:addNewMessage");
  },
  async "News:changeActiveMessage"(id, type) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(id, String);
    check(type, Match.Maybe(String));

    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("News:changeActiveMessage", "Invalid permissions to call method News:changeActiveMessage");
    }

    const currentMessage = await News.findOneAsync({ _id: id }, { fields: { messageActive: 1, type: 1 } });

    if (currentMessage.messageActive) {
      await News.updateAsync({ _id: id }, { $set: { messageActive: false } });
      return { deactivated: true };
    }
    if (!type) {
      await News.updateAsync(
        {
          messageActive: true,
          type: { $ne: "rostering" }
        },
        { $set: { messageActive: false } }
      );
      await News.updateAsync({ _id: id }, { $set: { messageActive: true } });
      return { globalChanged: true };
    }
    if (type === "rostering") {
      await News.updateAsync({ _id: id }, { $set: { messageActive: true } });
      return { rosteringChecked: true };
    }
    return {};
  },
  async "News:updateMessage"(messageParams) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(
      messageParams,
      Match.ObjectIncluding({
        _id: String,
        messageColor: String,
        messageTextColor: String,
        buttonIconColor: String,
        buttonColor: String,
        messageContent: String,
        learnMoreUrl: String,
        isLearnMoreActive: Boolean,
        isSupportLinkActive: Boolean
      })
    );

    if (await auth.hasAccess(["superAdmin"], { userId: this.userId })) {
      return updateMessage(messageParams);
    }
    throw new Meteor.Error("News:updateMessage", "Invalid permissions to call method News:updateMessage");
  },
  async "News:removeMessage"(id) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(id, String);

    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("News:removeMessage", "Invalid permissions to call method News:removeMessage");
    }
    return News.removeAsync(id);
  }
});

const newsFields = {
  isLearnMoreActive: 1,
  learnMoreUrl: 1,
  messageActive: 1,
  messageContent: 1,
  messageColor: 1,
  messageTextColor: 1,
  buttonIconColor: 1,
  buttonColor: 1,
  isSupportLinkActive: 1,
  type: 1
};

export async function getActiveMessage(multi) {
  const query = { messageActive: true };
  const options = { fields: newsFields };
  if (multi) {
    return News.find(query, options).fetchAsync();
  }
  query.type = { $exists: false };
  return News.findOneAsync(query, options);
}

export async function getAllMessages() {
  const messages = await News.find({}, { fields: newsFields, sort: { $natural: -1 } }).fetchAsync();
  return messages.reduce(
    (a, c) => {
      if (c.type === "rostering") {
        a.rosteringMessage = c;
      } else {
        a.globalMessages.push(c);
      }
      return a;
    },
    { globalMessages: [], rosteringMessage: null }
  );
}

export async function addNewMessage(messageParams) {
  const timestampInfo = await getTimestampInfo(getMeteorUserId(), null, "addNewMessage");
  await News.updateAsync(
    { messageActive: true, type: { $ne: "rostering" } },
    { $set: { messageActive: false, lastModified: timestampInfo } },
    { multi: true }
  );
  const nextTimestampInfo = { ...timestampInfo, on: timestampInfo.on + 1 };
  return News.insertAsync({
    ...messageParams,
    messageActive: true,
    lastModified: nextTimestampInfo,
    created: nextTimestampInfo
  });
}

export async function updateMessage(messageParams) {
  const messageId = messageParams._id;
  const timestampInfo = await getTimestampInfo(getMeteorUserId(), null, "updateMessage");
  // NOTE(fmazur) - Deactivate message that does not have type and follow intended only one active at a time logic
  if (!messageParams.type && messageParams.messageActive) {
    await News.updateAsync(
      { _id: { $ne: messageId }, type: { $exists: false } },
      { $set: { messageActive: false, lastModified: timestampInfo } },
      { multi: true }
    );
  }
  const nextTimestampInfo = { ...timestampInfo, on: timestampInfo.on + 1 };
  return News.updateAsync({ _id: messageId }, { $set: { ...messageParams, lastModified: nextTimestampInfo } });
}
