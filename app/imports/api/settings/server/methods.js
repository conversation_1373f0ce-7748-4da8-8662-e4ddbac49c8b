import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { get } from "lodash";
import * as auth from "../../authorization/server/methods";
import { Settings } from "../settings";

Meteor.methods({
  async "Settings:updateZendeskWidget"(shouldEnableZendeskWidget) {
    check(shouldEnableZendeskWidget, Boolean);

    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (await auth.hasAccess(["superAdmin"], { userId })) {
      return updateZendeskWidget(shouldEnableZendeskWidget);
    }
    throw new Meteor.Error(403, "Not authorized to use Settings:zendeskWidget!");
  },
  async "Settings:getZendeskWidgetFlag"(siteId) {
    check(siteId, Match.Maybe(String));

    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "superAdmin"], {
        userId: this.userId,
        siteId
      })
    ) {
      return isZendeskWidgetEnabled();
    }
    throw new Meteor.Error(403, "Not authorized to use Settings:zendeskWidget!");
  }
});

async function updateZendeskWidget(shouldEnableZendeskWidget) {
  await Settings.updateAsync({}, { $set: { isZendeskWidgetEnabled: shouldEnableZendeskWidget } });
}
async function isZendeskWidgetEnabled() {
  return get(
    await Settings.findOneAsync({}, { fields: { isZendeskWidgetEnabled: 1 } }),
    "isZendeskWidgetEnabled",
    false
  );
}
