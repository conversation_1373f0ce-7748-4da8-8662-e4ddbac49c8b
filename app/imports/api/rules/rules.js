import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";

export const Rules = new Mongo.Collection("Rules");

Rules.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String },
  grade: { type: String },
  skills: [Object],
  "skills.$.assessmentId": { type: String },
  "skills.$.interventions": [Object],
  "skills.$.interventions.$.interventionId": { type: String },
  "skills.$.interventions.$.isActive": { type: Boolean },
  enabled: { type: Boolean },
  ruleDefinitionId: { type: String },
  conditionTypeId: { type: String },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn }
});

Rules.validate = rules => Rules.schema.validate(rules);

Rules.isValid = rules => Rules.schema.namedContext("testContext").validate(rules);
