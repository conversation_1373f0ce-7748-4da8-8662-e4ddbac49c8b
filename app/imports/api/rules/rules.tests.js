import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { Rules } from "./rules.js";
import { rule } from "../../test-helpers/data/rules.js";

if (Meteor.isServer) {
  describe("Rules", () => {
    describe("Should pass schema validation method", () => {
      it("validate", () => {
        assert.isUndefined(Rules.validate(rule({})));
      });
      it("isValid", () => {
        assert.isTrue(Rules.isValid(rule({})));
      });
    });
    describe("Should fail schema validation method", () => {
      it("isValid", () => {
        const assessmentScoreDoc = rule({});
        assessmentScoreDoc._id = 1234;
        assert.isFalse(Rules.isValid(assessmentScoreDoc));
      });
    });
  });
}
