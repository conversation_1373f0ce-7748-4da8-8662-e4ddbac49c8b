import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Rules } from "../rules";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("Rules", function rulesPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  return Rules.find();
});

Meteor.publish("GradeLevelRulesByStudentGroup", async function gradeLevelRulesByStudentGroupPublication(
  studentGroupId,
  additionalGradeForRules
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(additionalGradeForRules, Match.Maybe(String));

  const sg = await StudentGroups.findOneAsync(studentGroupId);
  const grades = [sg.grade];

  if (additionalGradeForRules) {
    grades.push(additionalGradeForRules);
  }
  return Rules.find({ grade: { $in: grades } });
});

Meteor.publish("Rules:IndividualRootRulesByGrade", function individualRootRulesByGradePublication(grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(grade, String);

  return Rules.find({
    // $expr isn't working correctly in Meteor 3.3.1
    // $expr: { $eq: ["$_id", "$rootRuleId"] },
    $where: "this._id === this.rootRuleId",
    "attributeValues.grade": grade
  });
});

Meteor.publish("ClasswideRules", function classwideRulesPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Rules.find({ grade: { $exists: true } });
});

Meteor.publish("Rules:ProgressMonitoringManagement", function pmRulesPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Rules.find(
    { grade: { $exists: false } },
    {
      fields: {
        attributeValues: 1,
        enabled: 1,
        name: 1,
        outcomes: 1,
        rootRuleId: 1
      }
    }
  );
});
