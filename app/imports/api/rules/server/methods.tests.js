/* eslint-disable no-use-before-define */
import { Rules } from "../rules.js";
import { Assessments } from "../../assessments/assessments";
import {
  addInterventionsToOutcome,
  addRootRuleFor,
  getValidatedRule,
  removeRuleOutcome,
  resolveClassWideInterventionRoute,
  updateOutcomeInterventions,
  updateRuleOutcomes
} from "./methods";

describe("Rules Management: ", () => {
  describe("resolveClassWideInterventionRoute", () => {
    describe("for benchmarks", () => {
      const grade = "08";
      const forceClasswideIntervention = false;

      beforeAll(async () => {
        await Rules.insertAsync({ _id: "ruleId", grade, skills: [{ assessmentId: "nextAssessmentId" }] });
      });

      afterAll(async () => {
        await Rules.removeAsync({});
      });

      it("should not assign any next skill when students are passing", async () => {
        const studentScores = [7, 11, 13];
        const totalStudentsEnrolled = studentScores.length;
        const medianScore = studentScores[Math.floor(totalStudentsEnrolled / 2)];
        const assessmentResultMeasure = {
          grade,
          assessmentId: "assessmentId",
          assessmentResultType: "benchmark",
          cutoffTarget: 10,
          medianScore,
          studentScores,
          targetScores: [10, 20, 300],
          totalStudentsAssessed: totalStudentsEnrolled
        };
        const result = await resolveClassWideInterventionRoute(
          assessmentResultMeasure,
          forceClasswideIntervention,
          totalStudentsEnrolled
        );

        expect(result).toEqual({ passed: true, nextSkill: undefined });
      });

      it("should assign a next skill when students are failing", async () => {
        const studentScores = [7, 9, 13];
        const totalStudentsEnrolled = studentScores.length;
        const medianScore = studentScores[Math.floor(totalStudentsEnrolled / 2)];
        const assessmentResultMeasure = {
          grade,
          assessmentId: "assessmentId",
          assessmentResultType: "benchmark",
          cutoffTarget: 10,
          medianScore,
          studentScores,
          targetScores: [10, 20, 300],
          totalStudentsAssessed: totalStudentsEnrolled
        };
        const result = await resolveClassWideInterventionRoute(
          assessmentResultMeasure,
          forceClasswideIntervention,
          totalStudentsEnrolled
        );

        expect(result).toEqual({ passed: false, nextSkill: { assessmentId: "nextAssessmentId" } });
      });
    });
  });

  describe("getValidatedRule", () => {
    it("should throw an error if provided rule does not exist", async () => {
      const nonExistingRule = "nonExistingRule";

      await expect(getValidatedRule(nonExistingRule, {})).rejects.toThrow(`${nonExistingRule} does not exist!`);
    });

    it("should throw an error if provided outcomes are incomplete", async () => {
      const ruleId = "ruleId";
      await Rules.insertAsync({ _id: ruleId });

      await expect(getValidatedRule(ruleId, { at: {} })).rejects.toThrow("Incomplete outcomes provided!");
    });
  });

  describe("updateRuleOutcomes", () => {
    afterEach(async () => {
      await Rules.removeAsync({});
    });
    it("should update assessmentIds in rule outcomes and set interventionIds to empty array if outcome assessmentId is changed", async () => {
      const [atAssessmentId, belowAssessmentId] = ["atAssessmentId", "belowAssessmentId"];
      const [updatedAtAssessmentId, updatedAboveAssessmentId] = ["updatedAtAssessmentId", "updatedAboveAssessmentId"];
      const modifiedRuleId = "modifiedRuleId";
      const interventionIdToKeep = "interventionIdToKeep";
      await Rules.insertAsync({
        _id: modifiedRuleId,
        attributeValues: {
          benchmarkPeriod: "fall-period",
          grade: "08",
          assessmentId: "someAssessmentId"
        },
        outcomes: {
          above: null,
          at: {
            assessmentId: atAssessmentId,
            interventionIds: ["interventionIdToRemove"]
          },
          below: {
            assessmentId: belowAssessmentId,
            interventionIds: [interventionIdToKeep]
          }
        }
      });

      await updateRuleOutcomes(modifiedRuleId, {
        above: {
          assessmentId: updatedAboveAssessmentId
        },
        at: {
          assessmentId: updatedAtAssessmentId
        },
        below: {
          assessmentId: belowAssessmentId
        }
      });

      expect((await Rules.findOneAsync(modifiedRuleId)).outcomes).toMatchObject({
        above: {
          assessmentId: updatedAboveAssessmentId,
          interventionIds: []
        },
        at: {
          assessmentId: updatedAtAssessmentId,
          interventionIds: []
        },
        below: {
          assessmentId: belowAssessmentId,
          interventionIds: [interventionIdToKeep]
        }
      });
    });

    it("should add new rules based on existing root rule if rules for new assessment ids do not exist", async () => {
      const rootRuleId = "rootRuleId";
      const grade = "08";
      const benchmarkPeriod = "fall-period";
      const assessmentIdsInRootRule = ["subAssessmentId1", "subAssessmentId2", "subAssessmentId3", "subAssessmentId4"];
      const subRuleIds = await createSubRulesFor(assessmentIdsInRootRule, rootRuleId, grade, benchmarkPeriod);
      const [newAssessmentId1, newAssessmentId2] = ["newAssessmentId1", "newAssessmentId2"];
      const currentNumberOfSubRules = await Rules.find({ rootRuleId }).countAsync();

      await updateRuleOutcomes(subRuleIds[0], {
        below: {
          assessmentId: assessmentIdsInRootRule[0]
        },
        at: {
          assessmentId: newAssessmentId1
        },
        above: {
          assessmentId: newAssessmentId2
        }
      });

      const numberOfExpectedNewRules = 2;
      const updatedNumberOfRules = await Rules.find({ rootRuleId }).countAsync();
      expect(updatedNumberOfRules - currentNumberOfSubRules).toBe(numberOfExpectedNewRules);
      expect(
        await Rules.find({
          "attributeValues.assessmentId": {
            $in: [newAssessmentId1, newAssessmentId2]
          }
        }).countAsync()
      ).toBe(numberOfExpectedNewRules);
    });

    it("should not add new rules for assessment ids that already have a rule in specified root rule id", async () => {
      const rootRuleId = "rootRuleId";
      const grade = "08";
      const benchmarkPeriod = "fall-period";
      const assessmentIdsInRootRule = ["subAssessmentId1", "subAssessmentId2", "subAssessmentId3"];
      const subRuleIds = await createSubRulesFor(assessmentIdsInRootRule, rootRuleId, grade, benchmarkPeriod);
      const currentNumberOfSubRules = await Rules.find({ rootRuleId }).countAsync();

      await updateRuleOutcomes(subRuleIds[0], {
        below: {
          assessmentId: assessmentIdsInRootRule[0]
        },
        at: {
          assessmentId: assessmentIdsInRootRule[1]
        },
        above: {
          assessmentId: assessmentIdsInRootRule[2]
        }
      });

      const updatedNumberOfRules = await Rules.find({ rootRuleId }).countAsync();
      expect(updatedNumberOfRules).toBe(currentNumberOfSubRules);
    });

    it("should update the outcome value to null if assessmentId is not provided", async () => {
      const modifiedRuleId = "modifiedRuleId";
      await Rules.insertAsync({
        _id: modifiedRuleId,
        outcomes: {
          above: null,
          at: {
            assessmentId: "someId",
            interventionIds: []
          },
          below: {
            assessmentId: "otherId",
            interventionIds: ["interventionId"]
          }
        }
      });

      await updateRuleOutcomes(modifiedRuleId, {
        above: {},
        at: {
          interventionIds: []
        },
        below: null
      });

      expect((await Rules.findOneAsync(modifiedRuleId)).outcomes).toMatchObject({
        above: null,
        at: null,
        below: null
      });
    });
  });

  describe("updateOutcomeInterventions", () => {
    it("should update interventionIds based on provided outcome interventions", async () => {
      const assessmentId = "assessmentId";
      const interventionIds = ["interventionId1", "interventionId2"];
      const ruleId = await Rules.insertAsync({
        outcomes: {
          above: { assessmentId, interventionIds: [interventionIds[0]] },
          at: null,
          below: { assessmentId, interventionIds: [] }
        }
      });
      const outcomeInterventions = {
        above: null,
        at: [],
        below: interventionIds.map(int => ({ interventionId: int }))
      };

      await updateOutcomeInterventions(ruleId, outcomeInterventions);

      expect((await Rules.findOneAsync(ruleId)).outcomes).toMatchObject({
        above: { assessmentId, interventionIds: [interventionIds[0]] },
        at: null,
        below: { assessmentId, interventionIds }
      });
    });

    it("should not attempt to update null outcomes", async () => {
      const interventionIds = ["interventionId"];
      const ruleId = await Rules.insertAsync({
        outcomes: { above: null, at: null, below: null }
      });
      const outcomeInterventions = {
        above: [],
        at: { interventionIds },
        below: { interventionIds }
      };

      await updateOutcomeInterventions(ruleId, outcomeInterventions);

      expect((await Rules.findOneAsync(ruleId)).outcomes).toMatchObject({
        above: null,
        at: null,
        below: null
      });
    });

    it("should make possible updating interventionIds to empty arrays", async () => {
      const assessmentId = "assessmentId";
      const interventionIds = ["someIntervention"];
      const ruleId = await Rules.insertAsync({
        outcomes: {
          above: null,
          at: { assessmentId, interventionIds },
          below: { assessmentId, interventionIds }
        }
      });
      const outcomeInterventions = { above: null, at: [], below: [] };

      await updateOutcomeInterventions(ruleId, outcomeInterventions);

      expect((await Rules.findOneAsync(ruleId)).outcomes).toMatchObject({
        above: null,
        at: { assessmentId, interventionIds: [] },
        below: { assessmentId, interventionIds: [] }
      });
    });
  });

  describe("addInterventionsToOutcome", () => {
    afterEach(async () => {
      await Rules.removeAsync({});
    });
    it("should throw an error if the provided rule does not exist", async () => {
      const ruleId = "non-existingRule";

      await expect(addInterventionsToOutcome({ outcomeName: "at", ruleId })).rejects.toThrow(
        `${ruleId} does not exist!`
      );
    });

    it("should throw an error if the provided outcome is not supported", async () => {
      const ruleId = await Rules.insertAsync({ _id: "someRule" });
      const unsupportedOutcome = "max";

      await expect(
        addInterventionsToOutcome({
          outcomeName: unsupportedOutcome,
          ruleId
        })
      ).rejects.toThrow(`The provided outcome: "${unsupportedOutcome}" is not supported!`);
    });

    it("should throw an error if the provided outcome already contains interventions", async () => {
      const ruleId = "ruleId";
      const modifiedOutcome = "at";
      const ruleWithInterventionSet = {
        _id: ruleId,
        outcomes: {
          [modifiedOutcome]: {
            interventionIds: ["interventionId"]
          }
        }
      };
      await Rules.insertAsync(ruleWithInterventionSet);

      await expect(addInterventionsToOutcome({ outcomeName: modifiedOutcome, ruleId })).rejects.toThrow(
        `The provided outcome: "${modifiedOutcome}" is not empty!`
      );
    });

    it("should throw an error if the provided assessment does not exist", async () => {
      const ruleId = "ruleId";
      const modifiedOutcome = "at";
      const rule = { _id: ruleId };
      const assessmentId = "non-existingAssessment";
      await Rules.insertAsync(rule);

      await expect(
        addInterventionsToOutcome({
          outcomeName: modifiedOutcome,
          ruleId,
          assessmentId
        })
      ).rejects.toThrow(`Assessment with _id: ${assessmentId} does not exist!`);
    });

    it("should set value for interventionIds and assessmentId properties in provided outcome", async () => {
      const ruleId = "ruleId";
      const modifiedOutcome = "above";
      const outcomeObject = {
        assessmentId: "someId",
        interventionIds: ["someId"]
      };
      const ruleToModify = {
        _id: ruleId,
        outcomes: {
          above: null,
          at: outcomeObject,
          below: outcomeObject
        }
      };
      await Rules.insertAsync(ruleToModify);
      const assessmentId = "assessmentId";
      await Assessments.insertAsync({ _id: assessmentId });

      await addInterventionsToOutcome({
        outcomeName: modifiedOutcome,
        ruleId,
        assessmentId
      });

      const expectedOutcomes = {
        above: { interventionIds: [], assessmentId },
        at: outcomeObject,
        below: outcomeObject
      };
      expect((await Rules.findOneAsync(ruleId)).outcomes).toMatchObject(expectedOutcomes);
    });
  });

  describe("removeRuleOutcome", () => {
    afterEach(async () => {
      await Rules.removeAsync({});
    });
    it("should set the provided rule outcome to null", async () => {
      const ruleId = await Rules.insertAsync({
        outcomes: {
          above: null,
          at: {
            assessmentId: "someId",
            interventionIds: ["someIntervention"]
          },
          below: {
            assessmentId: "someId",
            interventionIds: ["otherIntervention"]
          }
        }
      });

      await removeRuleOutcome(ruleId, "at");

      const expectedOutcomes = {
        above: null,
        at: null,
        below: {
          assessmentId: "someId",
          interventionIds: ["otherIntervention"]
        }
      };
      expect((await Rules.findOneAsync(ruleId)).outcomes).toMatchObject(expectedOutcomes);
    });
  });

  describe("addRootRuleFor", () => {
    // Mock the translateBenchmarkPeriod function
    const mockTranslateBenchmarkPeriod = jest.fn(() => ({
      label: "fall-period"
    }));

    beforeAll(() => {
      // Mock the utilities module
      jest.doMock("../../utilities/utilities", () => ({
        ...jest.requireActual("../../utilities/utilities"),
        translateBenchmarkPeriod: mockTranslateBenchmarkPeriod
      }));
    });

    afterEach(async () => {
      await Rules.removeAsync({});
      mockTranslateBenchmarkPeriod.mockClear();
    });
    afterAll(async () => {
      jest.restoreAllMocks();
    });
    it("should return error when provided insufficent data to create root rule", async () => {
      await expect(
        addRootRuleFor({
          assessmentId: "someId",
          grade: "someGrade",
          benchmarkPeriodId: ""
        })
      ).rejects.toThrow(/Insufficent data provided/);
    });
    it("should add a new root rule with provided data", async () => {
      const assessmentId = "screeningAssessment";

      await addRootRuleFor({
        assessmentId,
        grade: "someGrade",
        benchmarkPeriodId: "bpId"
      });

      const addedRule = await Rules.findOneAsync({
        "attributeValues.assessmentId": assessmentId
      });
      expect(addedRule).toBeTruthy();
      expect(addedRule._id).toEqual(addedRule.rootRuleId);
    });
  });
});

const createSubRulesFor = async (assessmentIds, rootRuleId, grade, benchmarkPeriod) => {
  const ruleIds = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const assessmentId of assessmentIds) {
    ruleIds.push(
      await Rules.insertAsync({
        rootRuleId,
        attributeValues: {
          grade,
          benchmarkPeriod,
          assessmentId
        },
        outcomes: {
          above: {
            assessmentId
          },
          at: {
            assessmentId
          },
          below: {
            assessmentId
          }
        }
      })
    );
  }
  return ruleIds;
};
