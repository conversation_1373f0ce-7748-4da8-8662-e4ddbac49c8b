import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import get from "lodash/get";

import { ROLE_IDS } from "/tests/cypress/support/common/constants";
import { Sites } from "../sites";
import * as auth from "../../authorization/server/methods";
import { getMeteorUser, isUserLoggedOut, ninjalog } from "../../utilities/utilities";
import { hasDashboard } from "../../users/server/methods";

Meteor.publish("Sites", async function sitesPublication(orgid, siteId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!orgid) {
    ninjalog.log({
      msg: "Publication Access Denied: Sites - no orgid"
    });
    return this.ready();
  }
  check(orgid, String);
  check(siteId, Match.Maybe(String));

  if (siteId && (await auth.hasAccess(["teacher"], { userId: this.userId, siteId }))) {
    const teacherSiteIds = get(await getMeteorUser(), "profile.siteAccess", []).map(sa => sa.siteId);
    const siteIds = teacherSiteIds.length ? teacherSiteIds : [siteId];
    return Sites.find({ _id: { $in: siteIds } }, { fields: { name: 1, orgid: 1 } });
  }

  if (
    await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    })
  ) {
    const currentSiteAccess = get(await getMeteorUser(), "profile.siteAccess.0", {});
    const isSingleSiteDataAdmin =
      currentSiteAccess.role === ROLE_IDS.dataAdmin && !["allSites", "none"].includes(currentSiteAccess.siteId);
    return Sites.find(
      { ...(isSingleSiteDataAdmin ? { _id: currentSiteAccess.siteId } : { orgid }) },
      {
        fields: {
          orgid: 1,
          schoolYear: 1,
          stateInformation: 1,
          "lastModified.on": 1,
          name: 1,
          grades: 1,
          isVisible: 1,
          rosterImportId: 1,
          isHighSchool: 1
        }
      }
    );
  }
  ninjalog.log({
    msg: "Publication Access Denied: Sites"
  });
  return this.ready();
});

Meteor.publish("Sites:List", async function getSitesList() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  if ((await auth.hasAccess(["superAdmin"], { userId: this.userId })) || (await hasDashboard())) {
    return Sites.find({}, { fields: { name: 1, orgid: 1 } });
  }
  ninjalog.log({
    msg: "Publication Access Denied: Sites:List"
  });
  return this.ready();
});
