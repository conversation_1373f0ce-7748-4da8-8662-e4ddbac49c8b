import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { difference, keyBy } from "lodash";
import { Sites } from "./sites";
import { Users } from "../users/users";
import * as auth from "../authorization/server/methods";
import * as utils from "../utilities/utilities";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getNormalizedId } from "../rosterImportItems/normalizeRosterImportItems";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { StudentGroups } from "../studentGroups/studentGroups";
import { getCurrentSchoolYear, getMeteorUser, getMeteorUserId } from "../utilities/utilities";
import { insertSite } from "../rosterImports/helpers";
import { Organizations } from "../organizations/organizations";

export async function insert(siteDoc) {
  Sites.validate(siteDoc);
  return Sites.insertAsync(siteDoc);
}

export async function getSchoolItemData(orgid) {
  const sites = await Sites.find(
    { orgid },
    { fields: { name: 1, stateInformation: 1 }, sort: { schoolYear: -1, name: 1 } }
  ).fetchAsync();
  const orgUsers = await Users.find(
    { "profile.orgid": orgid },
    {
      fields: {
        "emails.address": 1,
        "profile.onboarded": 1,
        "profile.siteAccess": 1,
        "profile.name": 1,
        "profile.orgid": 1
      }
    }
  ).fetchAsync();
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);

  const schoolItemData = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const site of sites) {
    const siteTeachers = orgUsers.filter(user =>
      user.profile.siteAccess.some(sa => sa.siteId === site._id && sa.role === "arbitraryIdteacher")
    );
    const siteAdmins = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const orgUser of orgUsers.filter(user =>
      user.profile.siteAccess.some(sa => sa.siteId === site._id && sa.role === "arbitraryIdadmin")
    )) {
      siteAdmins.push({
        ...orgUser,
        isUserAssignedToAnyGroup: await utils.isUserAssignedToAnyGroup({
          siteId: site._id,
          schoolYear: currentSchoolYear,
          userId: orgUser._id
        })
      });
    }
    schoolItemData.push({
      ...site,
      teachersOnBoardedCount: siteTeachers.filter(t => t.profile.onboarded).length,
      teachersYetToOnBoardCount: siteTeachers.filter(t => !t.profile.onboarded).length,
      siteAdmins
    });
  }

  return schoolItemData;
}

async function removeSitesWithoutData(orgid, schoolIdsToOmit, ncesNumberBySchoolNumber) {
  if (!Object.keys(ncesNumberBySchoolNumber).length) {
    return;
  }
  const sitesInOrg = await Sites.find(
    {
      orgid,
      _id: { $nin: schoolIdsToOmit.filter(f => f) },
      $or: [
        { "stateInformation.schoolNumber": { $in: Object.values(ncesNumberBySchoolNumber).map(n => n.ncesId) } },
        { "stateInformation.schoolNumber": { $in: Object.keys(ncesNumberBySchoolNumber) } }
      ]
    },
    { fields: { _id: 1 } }
  ).fetchAsync();
  // eslint-disable-next-line no-restricted-syntax
  for await (const siteId of sitesInOrg.map(s => s._id)) {
    const hasScores =
      siteId &&
      (!!(await AssessmentResults.findOneAsync(
        { status: "COMPLETED", "scores.siteId": siteId },
        { fields: { _id: 1 } }
      )) ||
        !!(await StudentGroups.findOneAsync({ siteId, isActive: true }, { fields: { _id: 1 } })));
    if (!hasScores) {
      if (!process.env.JEST_WORKER_ID?.length) {
        console.log(
          "Removing site document:",
          await Sites.findOneAsync({ _id: siteId }, { fields: { created: 0, lastModified: 0, isVisible: 0 } })
        );
      }

      await Sites.removeAsync({ _id: siteId });
      if (await Users.findOneAsync({ "profile.siteAccess": siteId })) {
        await Users.updateAsync(
          { "profile.orgid": orgid },
          { $pull: { "profile.siteAccess": siteId } },
          { multi: true }
        );
      } // TODO(fmazur) - Write up script to remove orphaned data from collections
      // TODO(fmazur) - assessment results, student groups, student group enrollments, benchmark windows, students by skill
    }
  }
}

async function updateSchoolNumber({ orgid, districtNumber, querySchoolNumber, newSchoolNumber, isBackup = false }) {
  const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "updateSchoolNumber");
  await Sites.updateAsync(
    { orgid, "stateInformation.schoolNumber": `${querySchoolNumber}` },
    {
      $set: {
        "stateInformation.districtNumber": `${districtNumber}`,
        "stateInformation.schoolNumber": `${newSchoolNumber}${isBackup ? "_old" : ""}`,
        "stateInformation.localSchoolNumber": `${newSchoolNumber}${isBackup ? "_old" : ""}`,
        lastModified
      }
    },
    { upsert: false }
  );
}

function sortNcesData(ncesIdBySchoolId) {
  const dataByNcesNumber = keyBy(Object.entries(ncesIdBySchoolId), el => {
    const [, { ncesId: c }] = el;
    return c;
  });
  const dataBySchoolNumber = keyBy(Object.entries(ncesIdBySchoolId), el => {
    const [a] = el;
    return a;
  });
  const existingSchoolNumbers = Object.keys(dataBySchoolNumber);
  const ncesSchoolNumbers = Object.keys(dataByNcesNumber);
  const schoolNumberByNcesNumber = Object.entries(ncesIdBySchoolId).reduce((a, c) => {
    const [schoolNumber, { ncesId: ncesNumber }] = c;
    // eslint-disable-next-line no-param-reassign
    a[ncesNumber] = schoolNumber;
    return a;
  }, {});
  const safeNcesNumbers = difference(ncesSchoolNumbers, existingSchoolNumbers);
  const result = [];
  let numbersToUse = [...safeNcesNumbers];
  while (result.length !== ncesSchoolNumbers.length && numbersToUse.length) {
    const nextInOrder = [];
    numbersToUse.forEach(number => {
      const schoolNumber = schoolNumberByNcesNumber[number];
      if (schoolNumber) {
        nextInOrder.push(schoolNumber);
        result.push(schoolNumber);
      }
    });
    numbersToUse = [...nextInOrder];
  }
  return result.map(r => dataBySchoolNumber[r]);
}

export async function convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid) {
  const districtNumber = getNormalizedId(orgid);
  const schoolIdsToOmit = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const [existingSchoolNumber, { ncesId: ncesSchoolNumber, name: schoolName }] of sortNcesData(
    ncesNumberBySchoolNumber
  )) {
    if (!ncesSchoolNumber || existingSchoolNumber === ncesSchoolNumber) {
      // eslint-disable-next-line no-continue
      continue;
    }
    const existingSiteId = (
      await Sites.findOneAsync(
        { orgid, "stateInformation.schoolNumber": existingSchoolNumber, name: schoolName },
        { fields: { _id: 1 } }
      )
    )?._id;
    const ncesSiteId = (
      await Sites.findOneAsync(
        { orgid, "stateInformation.schoolNumber": ncesSchoolNumber, name: schoolName },
        { fields: { _id: 1 } }
      )
    )?._id;
    if (!existingSiteId && !ncesSiteId) {
      // eslint-disable-next-line no-continue
      continue;
    }
    const matchedOtherSite = await Sites.findOneAsync(
      { orgid, "stateInformation.schoolNumber": ncesSchoolNumber },
      { fields: { name: 1 } }
    );
    if (matchedOtherSite && matchedOtherSite.name !== schoolName) {
      // NOTE(fmazur) - Avoid case where migrated school number would result in two sites within org with same number
      // eslint-disable-next-line no-continue
      continue;
    }

    const hasExistingSiteData =
      existingSiteId &&
      (!!(await AssessmentResults.findOneAsync(
        { status: "COMPLETED", "scores.siteId": existingSiteId },
        { fields: { _id: 1 } }
      )) ||
        !!(await StudentGroups.findOneAsync({ siteId: existingSiteId, isActive: true }, { fields: { _id: 1 } })));

    const hasNCESSiteData =
      ncesSiteId &&
      (!!(await AssessmentResults.findOneAsync(
        { status: "COMPLETED", "scores.siteId": ncesSiteId },
        { fields: { _id: 1 } }
      )) ||
        !!(await StudentGroups.findOneAsync({ siteId: ncesSiteId, isActive: true }, { fields: { _id: 1 } })));

    if (hasExistingSiteData && hasNCESSiteData) {
      schoolIdsToOmit.push(ncesSiteId);
      await updateSchoolNumber({
        orgid,
        districtNumber,
        querySchoolNumber: ncesSchoolNumber,
        newSchoolNumber: ncesSchoolNumber
      });
      await updateSchoolNumber({
        orgid,
        districtNumber,
        querySchoolNumber: existingSchoolNumber,
        newSchoolNumber: ncesSchoolNumber,
        isBackup: true
      });
    } else if (hasExistingSiteData) {
      schoolIdsToOmit.push(existingSiteId);
      await updateSchoolNumber({
        orgid,
        districtNumber,
        querySchoolNumber: existingSchoolNumber,
        newSchoolNumber: ncesSchoolNumber
      });
      if (existingSchoolNumber !== ncesNumberBySchoolNumber) {
        await updateSchoolNumber({
          orgid,
          districtNumber,
          querySchoolNumber: existingSchoolNumber,
          newSchoolNumber: ncesSchoolNumber,
          isBackup: true
        });
      }
    }
  }
  await removeSitesWithoutData(orgid, schoolIdsToOmit, ncesNumberBySchoolNumber);
}

Meteor.methods({
  async "Sites:getSchoolItemData"(orgid) {
    check(orgid, String);
    if (
      await auth.hasAccess(["support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      return getSchoolItemData(orgid);
    }
    throw new Meteor.Error(403, "You are not authorized to get Student Groups count!");
  },
  async "Sites:addNewSite"(newSiteData, orgid) {
    check(
      newSiteData,
      Match.ObjectIncluding({
        schoolName: newSiteData.schoolName,
        isHighSchool: newSiteData.isHighSchool,
        districtName: newSiteData.districtName,
        districtNumber: newSiteData.districtNumber,
        schoolNumber: newSiteData.schoolNumber,
        localSchoolNumber: newSiteData.localSchoolNumber,
        isFormValid: newSiteData.isFormValid
      })
    );
    check(orgid, String);

    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      const timestampInfo = await getTimestampInfo(getMeteorUserId(), orgid);
      const schoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
      const grades = newSiteData.isHighSchool ? ["HS"] : ["01", "02", "03", "04", "05", "06", "07", "08", "K"];

      const dataToProcess = {
        name: newSiteData.schoolName,
        stateInformation: {
          districtName: newSiteData.districtName,
          districtNumber: newSiteData.districtNumber,
          schoolNumber: newSiteData.schoolNumber,
          localSchoolNumber: newSiteData.localSchoolNumber
        },
        isHighSchool: newSiteData.isHighSchool,
        orgid,
        schoolYear,
        grades,
        isVisible: true,
        created: timestampInfo,
        lastModified: timestampInfo
      };

      return insert(dataToProcess);
    }
    throw new Meteor.Error("403", "You are not authorized to assign student to an skill group");
  },
  async "Sites:getSitesInOrg"(orgid) {
    check(orgid, String);

    const { userId } = this;
    if (!userId) {
      return [];
    }
    return (
      Sites.find(
        { orgid },
        { fields: { name: 1, orgid: 1, stateInformation: 1, "lastModified.on": 1, isHighSchool: 1 } }
      ).fetchAsync() || []
    );
  },
  async "sites:convertSchoolIdsToNCES"(ncesBySchoolId, orgid) {
    check(ncesBySchoolId, Object);
    check(orgid, String);

    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      await convertSchoolIdsToNCES(ncesBySchoolId, orgid);
    }
  },
  async "Sites:updateSchool"(orgid, siteId, { schoolName, schoolNumber }) {
    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(orgid, String);
    check(siteId, String);
    check(schoolName, String);
    check(schoolNumber, String);

    const bdo = await getTimestampInfo(getMeteorUserId(), orgid, "Sites:updateSchool");

    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId,
        orgid
      })
    ) {
      await Sites.updateAsync(
        { _id: siteId },
        {
          $set: {
            name: schoolName,
            "stateInformation.schoolNumber": schoolNumber,
            "stateInformation.localSchoolNumber": schoolNumber,
            lastModified: bdo
          }
        }
      );
    }
  },
  async "Sites:addSchool"({ orgid, schoolName, schoolNumber }) {
    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(orgid, String);
    check(schoolName, String);
    check(schoolNumber, String);

    const bdo = await getTimestampInfo(getMeteorUserId(), orgid, "Sites:addSchool");

    if (
      await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId,
        orgid
      })
    ) {
      const { name: districtName } = await Organizations.findOneAsync({ _id: orgid }, { fields: { name: 1 } });
      await insertSite({
        orgid,
        schoolYear: await getCurrentSchoolYear(this.user, orgid),
        districtId: orgid,
        districtName,
        schoolNumber,
        name: schoolName,
        grades: [],
        byDateOn: bdo,
        rosterImportId: "addSchool"
      });
    }
  }
});
