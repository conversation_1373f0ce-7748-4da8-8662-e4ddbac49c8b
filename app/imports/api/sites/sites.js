import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const Sites = new Mongo.Collection("Sites");

Sites.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  name: { type: String },
  schoolYear: { type: Number },
  isHighSchool: { type: Boolean, optional: true },
  gradeLevels: { type: Array, blackbox: true, optional: true },
  "gradeLevels.$": { type: String, optional: true },
  grades: { type: Array, blackbox: true, optional: true },
  "grades.$": { type: String, optional: true },
  stateInformation: { type: Object },
  "stateInformation.districtName": { type: String, optional: true },
  "stateInformation.districtNumber": { type: String, optional: true },
  "stateInformation.schoolNumber": { type: String, optional: true },
  "stateInformation.localSchoolNumber": { type: String, optional: true },
  isVisible: { type: Boolean }
});

Sites.validate = sites => {
  Sites.schema.validate(sites);
};
Sites.isValid = sites => Sites.schema.namedContext("testContext").validate(sites);
