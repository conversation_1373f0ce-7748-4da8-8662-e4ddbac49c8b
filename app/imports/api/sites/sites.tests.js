import { assert } from "chai";

import { Sites } from "./sites.js";
import { site } from "../../test-helpers/data/sites.js";

describe("Sites", () => {
  describe("Should pass schema validation method", () => {
    it("validate", () => {
      assert.isUndefined(Sites.validate(site()));
    });
    it("isValid", () => {
      assert.isTrue(Sites.isValid(site()));
    });
  });
  describe("Should fail schema validation method", () => {
    it("isValid", () => {
      const siteDoc = site();
      siteDoc._id = 1234;
      assert.isFalse(Sites.isValid(siteDoc));
    });
  });
});
