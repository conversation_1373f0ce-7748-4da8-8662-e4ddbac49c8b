/* eslint-disable global-require */
import { StudentGroups } from "../studentGroups/studentGroups";
import { Students } from "../students/students";
import { Sites } from "../sites/sites";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import {
  addArchivedStudent,
  clearCollections,
  setupDb,
  setupTestContext,
  TestGroup
} from "../rosterImports/rosterImportsProcessor.testHelpers";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { addStudentToGroup, archiveStudents, moveStudentsBetweenGroups } from "./helpers";
import { fetchFilteredUsersForDownloader } from "./methods";
import { Organizations } from "../organizations/organizations";
import { Users } from "../users/users";

// Mock the benchmark windows methods
jest.mock("../benchmarkWindows/methods.js", () => ({
  getCurrentBenchmarkWindowWithSiteId: jest.fn(),
  createCurrentBenchmarkWindowForSite: jest.fn()
}));

// Mock utilities
jest.mock("./utilities", () => ({
  ...jest.requireActual("./utilities"),
  getCurrentSchoolYear: jest.fn(),
  getMeteorUserId: jest.fn()
}));

describe("utilities", () => {
  const { schoolYear, orgid, siteId, benchmarkPeriodId } = setupTestContext();

  // Import the mocked modules
  const {
    getCurrentBenchmarkWindowWithSiteId,
    createCurrentBenchmarkWindowForSite
  } = require("../benchmarkWindows/methods.js");
  const { getCurrentSchoolYear, getMeteorUserId } = require("./utilities");

  beforeEach(() => {
    // Setup mock implementations
    getCurrentBenchmarkWindowWithSiteId.mockResolvedValue({
      schoolYear,
      orgid,
      benchmarkPeriodId
    });
    createCurrentBenchmarkWindowForSite.mockResolvedValue({});
    getCurrentSchoolYear.mockReturnValue(schoolYear);
    getMeteorUserId.mockReturnValue("customUserId");
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });
  describe("moveStudentsBetweenGroups", () => {
    beforeEach(async () => {
      await setupDb(await getTimestampInfo("moveStudentsUI", orgid), "moveStudentsUI");
    });
    afterEach(async () => {
      await clearCollections();
    });
    it("should modify the grade properties in student record", async () => {
      const previousGroup = await TestGroup.init("cookie");
      const nextGroup = await TestGroup.init("next");

      await moveStudentsBetweenGroups({
        students: previousGroup.students,
        previousGroup: previousGroup.studentGroup,
        nextGroup: nextGroup.studentGroup
      });

      const updatedStudents = await Students.find({
        _id: { $in: previousGroup.studentIds }
      }).fetchAsync();
      expect(updatedStudents.every(student => student.grade === nextGroup.grade)).toBeTruthy();
      const updatedEnrollments = await StudentGroupEnrollments.find({
        studentId: { $in: previousGroup.studentIds },
        isActive: true
      }).fetchAsync();
      expect(
        updatedEnrollments.every(enrollment => enrollment.studentGroupId === nextGroup.studentGroupId)
      ).toBeTruthy();
    });
    it("should add unarchived students to current assessments of a new group", async () => {
      const nextGroup = await TestGroup.init("next");
      await nextGroup.addActiveScreening();
      const studentsToUnarchive = [await addArchivedStudent(), await addArchivedStudent()];

      await moveStudentsBetweenGroups({
        students: studentsToUnarchive,
        previousGroup: null,
        nextGroup: nextGroup.studentGroup,
        isUnarchiveOperation: true
      });

      const activelyEnrolledStudentIds = await nextGroup.getActivelyEnrolledStudentIds();
      expect(studentsToUnarchive.every(student => activelyEnrolledStudentIds.includes(student._id))).toBeTruthy();
      const currentBenchmark = await nextGroup.getCurrentScreening();
      const studentIdsInBenchmark = currentBenchmark.scores.map(score => score.studentId);
      expect(studentsToUnarchive.every(student => studentIdsInBenchmark.includes(student._id))).toBeTruthy();
    });
    it("should keep forced individual interventions when all students are archived in one group and then moved to a new group in the same grade", async () => {
      const previousGroup = await TestGroup.init("cookie");
      const nextGroup = await TestGroup.init("golden");
      const previousGroupStudents = previousGroup.students;
      const idsOfStudentsToMove = await previousGroup.getActivelyEnrolledStudentIds();

      await previousGroup.addIndividualInterventionsToGroupAndStudents({
        idsOfStudentsInIntervention: idsOfStudentsToMove,
        previousAssessmentResultId: "startingAssessmentResult"
      });

      await archiveStudents({ studentIds: idsOfStudentsToMove, studentGroup: previousGroup.studentGroup });

      await moveStudentsBetweenGroups({
        students: previousGroupStudents,
        previousGroup: null,
        nextGroup: nextGroup.studentGroup,
        isUnarchiveOperation: true
      });

      expect((await nextGroup.getStudentsWithCurrentSkill(idsOfStudentsToMove)).length).toEqual(2);
      expect(await nextGroup.getActiveIndividualInterventions()).toHaveLength(2);
    });
    it("should end forced individual interventions when all students are archived in one group and then moved to a new group in a different grade", async () => {
      const previousGroup = await TestGroup.init("cookie");
      const nextGroup = await TestGroup.init("next");
      const previousGroupStudents = previousGroup.students;
      const idsOfStudentsToMove = await previousGroup.getActivelyEnrolledStudentIds();

      await previousGroup.addIndividualInterventionsToGroupAndStudents({
        idsOfStudentsInIntervention: idsOfStudentsToMove,
        previousAssessmentResultId: "startingAssessmentResult"
      });

      await archiveStudents({ studentIds: idsOfStudentsToMove, studentGroup: previousGroup.studentGroup });

      await moveStudentsBetweenGroups({
        students: previousGroupStudents,
        previousGroup: null,
        nextGroup: nextGroup.studentGroup,
        isUnarchiveOperation: true
      });

      expect((await nextGroup.getStudentsWithCurrentSkill(idsOfStudentsToMove)).length).toEqual(0);
      expect(await nextGroup.getActiveIndividualInterventions()).toHaveLength(0);
    });
    it("should deactivate the previous group if it no longer contains active enrollments", async () => {
      const previousGroup = await TestGroup.init("cookie");
      const nextGroup = await TestGroup.init("next");

      await moveStudentsBetweenGroups({
        students: previousGroup.students,
        previousGroup: previousGroup.studentGroup,
        nextGroup: nextGroup.studentGroup
      });

      expect(await previousGroup.getActivelyEnrolledStudentIds()).toEqual([]);
      expect((await previousGroup.getGroup()).isActive).toBe(false);
    });
  });

  describe("addStudentToGroup", () => {
    const studentGroupId = "studentGroupId";
    const studentLocalId = "123456";
    const studentStateId = "111123455";
    const studentGrade = "05";
    const groupGrade = "07";
    const student = {
      lastName: "Lastname",
      firstName: "Firstname",
      grade: studentGrade,
      birthDate: "2000-01-01",
      localId: studentLocalId,
      stateId: studentStateId
    };
    const studentGroup = {
      orgid,
      schoolYear,
      siteId,
      grade: groupGrade,
      _id: studentGroupId
    };
    const site = {
      _id: siteId,
      orgid,
      name: "Test Elementary Site",
      schoolYear,
      stateInformation: {
        districtNumber: "1"
      }
    };
    afterEach(async () => {
      await Sites.removeAsync({});
      await Students.removeAsync({});
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
    });
    it("should add new student to an existing group", async () => {
      await StudentGroups.insertAsync(studentGroup);
      await Sites.insertAsync(site);

      await addStudentToGroup({ student, studentGroup });

      const addedStudent = await Students.findOneAsync({
        "identity.identification.localId": studentLocalId,
        "identity.identification.stateId": studentStateId
      });
      expect(addedStudent).toBeDefined();
      expect(addedStudent.grade).toEqual(groupGrade);
      const studentEnrollment = await StudentGroupEnrollments.findOneAsync({
        studentId: addedStudent._id,
        studentGroupId,
        isActive: true,
        grade: groupGrade
      });
      expect(studentEnrollment).toBeDefined();
    });
    it("should throw an error if new student's state or local ID is already in use by another student in the same school year", async () => {
      await StudentGroups.insertAsync(studentGroup);
      await Sites.insertAsync(site);
      await addStudentToGroup({ student, studentGroup });
      const studentIdentification = `${student.lastName}, ${student.firstName}`;

      await expect(addStudentToGroup({ student, studentGroup })).rejects.toThrow(
        `Student with Local ID: ${studentLocalId} - ${studentIdentification}, already exists in this organization`
      );
    });
    it("should allow creating a new student when the new student's state or local Id is used by another student in previous school year", async () => {
      await StudentGroups.insertAsync(studentGroup);
      await Sites.insertAsync(site);
      await addStudentToGroup({ student, studentGroup });

      const updatedStudentGroup = { ...studentGroup, schoolYear: 2019 };
      await addStudentToGroup({ student, studentGroup: updatedStudentGroup });

      const addedStudents = await Students.find({
        "identity.identification.localId": studentLocalId,
        "identity.identification.stateId": studentStateId
      }).fetchAsync();
      expect(addedStudents.length).toBe(2);
      const addedStudentsSchoolYears = addedStudents.map(s => s.schoolYear);
      expect(addedStudentsSchoolYears).toContain(2018, 2019);
      const addedStudentIds = addedStudents.map(s => s._id);
      const studentEnrollmentsCount = await StudentGroupEnrollments.find({
        studentId: { $in: addedStudentIds },
        isActive: true
      }).countAsync();
      expect(studentEnrollmentsCount).toEqual(2);
    });
  });

  describe("fetchFilteredUsersForDownloader", () => {
    beforeEach(async () => {
      await Organizations.insertAsync([
        { _id: "orgId1", name: "orgName1" },
        { _id: "orgId2", name: "orgName2", isMFARequired: true },
        { _id: "orgId3", name: "orgName3", isMFARequired: true }
      ]);
      await StudentGroupEnrollments.insertAsync([
        { orgid: "orgId1" },
        { orgid: "orgId3" },
        { orgid: "orgId1" },
        { orgid: "orgId3" },
        { orgid: "orgId2" },
        { orgid: "orgId3" }
      ]);
      await Users.insertAsync([
        {
          profile: {
            orgid: "orgId1",
            siteAccess: [
              { role: "arbitraryIddataAdmin", isActive: false },
              { role: "arbitraryIdteacher", isActive: true }
            ],
            rosterImportId: "rosterImportId1",
            name: { first: "first1", last: "last1" }
          },
          emails: [{ address: "<EMAIL>" }],
          services: { twoFactorAuthentication: { secret: "secret", type: "otp" } }
        },
        {
          profile: {
            orgid: "orgId1",
            siteAccess: [{ role: "arbitraryIdadmin", isActive: true }],
            rosterImportId: "rosterImportId3",
            name: { first: "first3", last: "last3" }
          },
          emails: [{ address: "<EMAIL>" }]
        },
        {
          profile: {
            orgid: "orgId3",
            siteAccess: [{ role: "arbitraryIduniversalCoach", isActive: true }],
            rosterImportId: "rosterImportId4",
            name: { first: "first4", last: "last4" }
          },
          emails: [{ address: "<EMAIL>" }],
          services: { twoFactorAuthentication: { secret: "secret" } }
        }
      ]);
    });
    afterEach(async () => {
      await StudentGroupEnrollments.removeAsync({});
      await Organizations.removeAsync({});
      await Users.removeAsync({});
    });

    it("returns no data when used without parameters", async () => {
      const result = await fetchFilteredUsersForDownloader();
      expect(result).toEqual([]);
    });
    it("returns correct data when used with all parameters", async () => {
      const result = await fetchFilteredUsersForDownloader({
        orgIds: ["orgId1", "orgId3"],
        userRoles: ["Teachers", "Coaches"],
        userStatus: "Active",
        studentCount: 0,
        isStudentCountGreater: true
      });

      expect(result).toEqual([
        {
          "Archive Status": "Active",
          "District Name": "orgName1",
          Email: "<EMAIL>",
          "First Name": "first1",
          "Last Name": "last1",
          Roles: ["Data Admin", "Teacher"],
          "District MFA Enabled": "No",
          "User MFA Linked": "Yes"
        },
        {
          "Archive Status": "Active",
          "District Name": "orgName1",
          Email: "<EMAIL>",
          "First Name": "first3",
          "Last Name": "last3",
          Roles: ["Coach"],
          "District MFA Enabled": "No",
          "User MFA Linked": "No"
        }
      ]);
    });
  });
});
