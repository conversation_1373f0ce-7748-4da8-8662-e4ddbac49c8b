// @returns: The week number since January 1st of the year that the school year began
import moment from "moment";
import uniq from "lodash/uniq";
import range from "lodash/range";
import difference from "lodash/difference";
import compact from "lodash/compact";
import last from "lodash/last";
import { get } from "lodash";

import { getCurrentSchoolYear, getMeteorUser, isDateInPreviousSchoolYear } from "../utilities";

export async function getAbsoluteWeekNumber(date, schoolYear) {
  const dateToConvert = moment.utc(date);
  const schoolYearToUse = schoolYear || (await getCurrentSchoolYear(await getMeteorUser()));
  const lastDayOfYear = moment.utc((schoolYearToUse - 1).toString(), "YYYY").endOf("year");

  // normalizeOffset ensures that the absoluteWeekNumber for dates in the second half of schoolYear does not reset (e.g. absoluteWeekNumber for 1/8/2019 will be 54 instead of 2)
  let normalizeOffset = 0;
  if (dateToConvert.isoWeekYear() > schoolYearToUse - 1) {
    normalizeOffset = lastDayOfYear.isoWeeksInYear();
  }
  return parseInt(dateToConvert.format("W")) + normalizeOffset;
}

export function getInterventionConsistency(weekNumbersWithScores, numberOfWeeksActive) {
  let interventionConsistency = 0;
  // Values of 0 or undefined should fail this check
  if (weekNumbersWithScores.length > 0 && numberOfWeeksActive) {
    interventionConsistency = Math.round((weekNumbersWithScores.length / numberOfWeeksActive) * 100);
    interventionConsistency = interventionConsistency > 100 ? 100 : interventionConsistency;
  }
  return interventionConsistency;
}

export async function getWeekNumbersWithScores(history, type, studentId, schoolYear) {
  // go through each item in the history and get the week numbers
  // of the endDates (which indicate scores were entered)
  // Note: that we do not count drilldown history items as they shouldn't take
  // a week to complete and should be done immediately per Amanda
  let weekNumbersWithScores = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const historyItem of history) {
    if (historyItem && historyItem.whenEnded && historyItem.whenEnded.date) {
      if (
        (type === "group" && historyItem.type === "classwide") ||
        (type === "individual" && historyItem.interventions && historyItem.interventions.length > 0)
      ) {
        // Whole group classwide or individual
        weekNumbersWithScores.push(await getAbsoluteWeekNumber(historyItem.whenEnded.date, schoolYear));
      } else if (type === "individualClasswide" && historyItem.type === "classwide" && studentId) {
        // Individual calculation of a student in classwide - make sure that a score was recorded
        if (historyItem.assessmentResultMeasures[0].studentResults.some(sr => sr.studentId === studentId)) {
          weekNumbersWithScores.push(await getAbsoluteWeekNumber(historyItem.whenEnded.date, schoolYear));
        }
      }
    }
    weekNumbersWithScores = uniq(weekNumbersWithScores);
  }
  return weekNumbersWithScores;
}

export function getAverageWeeksPerSkill(numberOfSkillsPracticed, numberOfWeeksActive) {
  if (
    numberOfSkillsPracticed === null ||
    numberOfSkillsPracticed < 1 ||
    numberOfWeeksActive === 0 ||
    numberOfWeeksActive === null
  ) {
    return null;
  }
  return Math.round((numberOfWeeksActive / numberOfSkillsPracticed) * 10) / 10;
}

export async function getSummerBreakBoundaries(schoolYear) {
  const summerBreakStartDate = moment.utc(`15.05.${schoolYear} 12:00:00`, "DD.MM.YYYY hh:mm:ss").toDate();
  const summerBreakStartWeekNumber = await getAbsoluteWeekNumber(summerBreakStartDate, schoolYear);
  const summerBreakEndDate = moment.utc(`31.07.${schoolYear} 12:00:00`, "DD.MM.YYYY hh:mm:ss").toDate();
  const summerBreakEndWeekNumber = await getAbsoluteWeekNumber(summerBreakEndDate, schoolYear);
  return { summerBreakStartWeekNumber, summerBreakEndWeekNumber };
}

export function getFirstMondayOnOrAfter(momentDate) {
  return momentDate.isoWeekday() === 1 ? momentDate.toDate() : momentDate.isoWeekday(8).toDate();
}

export function getFirstSundayOnOrAfter(momentDate) {
  return momentDate.isoWeekday() === 7 ? momentDate.toDate() : momentDate.isoWeekday(7).toDate();
}

export async function getWeeksInDateRange(
  { startMonth, startDay, endMonth, endDay } = {},
  schoolYear,
  schoolYearBoundary = {
    month: 6,
    day: 31
  }
) {
  if (!startMonth || !startDay || !endMonth || !endDay) {
    return [];
  }

  const startYear = isDateInPreviousSchoolYear({ month: startMonth - 1, day: startDay, schoolYearBoundary })
    ? schoolYear - 1
    : schoolYear;
  const endYear = isDateInPreviousSchoolYear({ month: endMonth - 1, day: endDay, schoolYearBoundary })
    ? schoolYear - 1
    : schoolYear;

  const startMomentDate = moment.utc(`${startYear}-${startMonth}-${startDay}`, "YYYY-MM-DD");
  const startDate = getFirstMondayOnOrAfter(startMomentDate);
  const startWeekNumber = await getAbsoluteWeekNumber(startDate, schoolYear);
  const endMomentDate = moment.utc(`${endYear}-${endMonth}-${endDay}`, "YYYY-MM-DD");
  const endDate = getFirstSundayOnOrAfter(endMomentDate);
  const endWeekNumber = await getAbsoluteWeekNumber(endDate, schoolYear);

  return range(startWeekNumber, endWeekNumber + 1);
}

export async function getActiveWeeks({
  startWeek,
  endWeek,
  history,
  type,
  schoolYear,
  schoolBreaks = {},
  schoolYearBoundary
}) {
  const { summerBreakStartWeekNumber, summerBreakEndWeekNumber } = await getSummerBreakBoundaries(schoolYear);
  let weeksInSummerBreak = range(summerBreakStartWeekNumber, summerBreakEndWeekNumber + 1);
  if (endWeek > weeksInSummerBreak[0]) {
    weeksInSummerBreak = weeksInSummerBreak.filter(weekNo => weekNo > endWeek);
  }
  const weeksInWinterBreak = await getWeeksInDateRange(schoolBreaks.winter, schoolYear, schoolYearBoundary);
  const weeksInSpringBreak = await getWeeksInDateRange(schoolBreaks.spring, schoolYear, schoolYearBoundary);
  const weeksInOtherBreak = await getWeeksInDateRange(schoolBreaks.other, schoolYear, schoolYearBoundary);

  const weeksInAllBreaks = uniq([
    ...weeksInWinterBreak,
    ...weeksInSpringBreak,
    ...weeksInSummerBreak,
    ...weeksInOtherBreak
  ]);

  if (type === "individual" || type === "group") {
    const activeWeeks = range(startWeek, endWeek + 1);
    return difference(activeWeeks, weeksInAllBreaks);
  }

  // For individualClasswide calculations, count up the weeks that they were actually enrolled
  // during the period being examined

  // Assumption: this history is the studentGroup history, but it has
  // already been filtered to only represent the historyItems in which the student
  // being processed was enrolled in the class
  const activeWeeks = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const historyItem of history) {
    if (historyItem.whenEnded && historyItem.whenEnded.date) {
      const weekNumber = await getAbsoluteWeekNumber(historyItem.whenEnded.date, schoolYear);
      if (weekNumber >= startWeek && weekNumber <= endWeek) {
        activeWeeks.push(weekNumber);
      }
    }
  }
  // If the startWeek is before the oldest historyItem, change startWeek to reflect
  // when the first history item occurred.  This will account for students who were
  // enrolled after the startWeek
  const honoredStartWeek =
    activeWeeks[activeWeeks.length - 1] < startWeek ? activeWeeks[activeWeeks.length - 1] : startWeek;
  const weeksBetweenStartAndEnd = range(honoredStartWeek, endWeek + 1);
  // Remove any gap weeks in which the student was not active (they were removed from enrollment and then came back)
  const inactiveWeeksBetweenStartAndEnd = difference(weeksBetweenStartAndEnd, activeWeeks);
  // Only honor weeks in which the student was known to be present for results that are finished,
  // as well as all weeks since the last
  const honoredActiveWeeks = weeksBetweenStartAndEnd.filter(
    week => week >= activeWeeks[0] || (week <= activeWeeks[0] && !inactiveWeeksBetweenStartAndEnd.includes(week))
  );

  const honoredActiveWeeksWithoutAllBreaks = difference(honoredActiveWeeks, weeksInAllBreaks);
  return uniq(honoredActiveWeeksWithoutAllBreaks);
}

export function getNumberOfClasswideSkillsPracticed(cleanHistory, isInterventionComplete = false) {
  // Get all assessmentIds that were either scored during or after the startWeek,
  // as well as skills currently in progress
  const filteredHistory = cleanHistory.filter(
    historyItem => historyItem.type !== "benchmark" && (!historyItem.whenEnded || historyItem.whenEnded.date)
  );
  let assessmentIdsPracticed = filteredHistory.map(historyItem => historyItem.assessmentId);
  assessmentIdsPracticed = compact(uniq(assessmentIdsPracticed));
  let numberOfSkillsPracticed = assessmentIdsPracticed.length;
  // SPRIN-577 fix for getting skill CHANGES so subtract 1 from the number of ids practiced
  if (!isInterventionComplete) {
    numberOfSkillsPracticed -= 1;
  }
  return numberOfSkillsPracticed < 0 ? 0 : numberOfSkillsPracticed;
}

export async function getStartWeekOfInterventions(cleanHistory, schoolYear) {
  if (!cleanHistory || !cleanHistory.length) return 0;
  // Note: If their first score is from the same week that they were scheduled use that,
  // otherwise use the next week after scheduling took place
  const firstCompletedSkill = last(cleanHistory);
  let weekScored;
  const weekStarted = await getAbsoluteWeekNumber(firstCompletedSkill.whenStarted.date, schoolYear);
  if (firstCompletedSkill && firstCompletedSkill.whenEnded && firstCompletedSkill.whenEnded.date) {
    weekScored = await getAbsoluteWeekNumber(firstCompletedSkill.whenEnded.date, schoolYear);
  }
  // Check if firstCompletedSkill was scheduled and scored on the same week
  if (weekScored && weekStarted === weekScored) {
    return weekStarted;
  }
  // Otherwise, use the week after the interventions were scheduled
  return weekStarted + 1;
}

export function getHistoryForCurrentSkill(cleanHistory) {
  if (!cleanHistory || !cleanHistory.length) return [];
  // first element in cleanHistory is the current skill
  const currentSkill = cleanHistory[0];
  // find all skills that have the same assessmentId
  return cleanHistory.filter(history => history.assessmentId === currentSkill.assessmentId);
}

export async function getEndWeekOfInterventions({
  cleanHistory,
  schoolYear,
  isInterventionComplete = false,
  currentDate = new Date()
}) {
  const { summerBreakStartWeekNumber, summerBreakEndWeekNumber } = await getSummerBreakBoundaries(schoolYear);
  if (!cleanHistory || !cleanHistory.length) return 0;
  const lastCompleteItem = cleanHistory.find(
    historyItem => historyItem && historyItem.whenEnded && historyItem.whenEnded.date
  );
  const currentWeek = await getAbsoluteWeekNumber(currentDate, schoolYear);
  let latestScoredWeek;
  const lastCompletedItemDate = get(lastCompleteItem, "whenEnded.date");
  if (lastCompletedItemDate) {
    latestScoredWeek = await getAbsoluteWeekNumber(lastCompletedItemDate, schoolYear);
    if (isInterventionComplete || latestScoredWeek === currentWeek || latestScoredWeek > summerBreakStartWeekNumber) {
      return latestScoredWeek;
    }
  }
  // Otherwise, use the week previous (full) week from now and don't count the current week
  // since it is not over yet
  if (
    (currentWeek > summerBreakStartWeekNumber && currentWeek < summerBreakEndWeekNumber) ||
    schoolYear < (await getCurrentSchoolYear(await getMeteorUser()))
  ) {
    return summerBreakStartWeekNumber;
  }
  return currentWeek - 1;
}

export async function getNumberOfIndividualSkillsPracticed(cleanHistory, startWeek, schoolYear) {
  // Only get assessmentIds from skills that had assigned interventions,
  // and that were worked on (scored) during or after the startWeek,
  // as well as skills currently in progress.
  // Also, count skills of the same assessmentId that were performed in
  // different trees (different rootAssessmentId) as unique skills
  const filteredHistory = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const historyItem of cleanHistory) {
    if (
      historyItem?.interventions.length > 0 &&
      (!historyItem.whenEnded ||
        (historyItem.whenEnded && (await getAbsoluteWeekNumber(historyItem.whenEnded.date, schoolYear)) >= startWeek))
    ) {
      filteredHistory.push(historyItem);
    }
  }
  // Get rid of duplicate history items that have the same assessmentId and
  // rootAssessmentId so that they count as a single skill
  let uniqFilteredHistory = [];
  if (filteredHistory && filteredHistory.length > 0) {
    uniqFilteredHistory = filteredHistory.reduce((a, c) => {
      if (!a.some(fh => fh.assessmentId === c.assessmentId && fh.benchmarkAssessmentId === c.benchmarkAssessmentId)) {
        a.push(c);
      }
      return a;
    }, []);
  }
  // Since we are looking for completed skills, count one less than the number  of skills practiced...but ensure that the answer remains positive
  return uniqFilteredHistory.length >= 1 ? uniqFilteredHistory.length - 1 : 0;
}

function getLatestScoresFromHistory(cleanHistory) {
  const latestHistoryElement = cleanHistory.find(historyElement => {
    const scores = get(historyElement, "assessmentResultMeasures[0].studentResults", []);
    return scores.length > 0;
  });
  return get(latestHistoryElement, "assessmentResultMeasures[0].studentResults", []);
}

export function getWorseScoresRatio(cleanHistory, numberOfWeeksPracticingCurrentSkill) {
  if (numberOfWeeksPracticingCurrentSkill < 2) {
    return 0;
  }
  const skillHistory = getHistoryForCurrentSkill(cleanHistory);
  const latestResults = getLatestScoresFromHistory(skillHistory);
  const firstResults = get(skillHistory[skillHistory.length - 1], "assessmentResultMeasures[0].studentResults", []);
  const firstResultsDict = {};
  firstResults.forEach(result => {
    firstResultsDict[result.studentId] = parseInt(result.score);
  });
  const latestScoreIsLowerThanFirst = [];
  latestResults.forEach(result => {
    if (firstResultsDict[result.studentId] !== undefined) {
      const firstScore = firstResultsDict[result.studentId];
      const latestScore = parseInt(result.score);
      if (latestScore < firstScore) {
        latestScoreIsLowerThanFirst.push(true);
      } else {
        latestScoreIsLowerThanFirst.push(false);
      }
    }
  });
  if (latestScoreIsLowerThanFirst.length === 0) {
    return 0;
  }
  return (compact(latestScoreIsLowerThanFirst).length * 100) / latestScoreIsLowerThanFirst.length;
}
