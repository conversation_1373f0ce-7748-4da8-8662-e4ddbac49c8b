import * as crypto from "crypto";
import { ListObjectsV2Command, S3Client } from "@aws-sdk/client-s3";

// Consolidated S3 configuration
function getS3Config() {
  const key = Meteor.settings.S3_ACCESS_KEY_ID;
  const secret = Meteor.settings.S3_SECRET_ACCESS_KEY;
  const region = Meteor.settings.S3_REGION;
  const bucketName = Meteor.settings.public.S3_BUCKET_NAME || "springmath-web-data";
  const folderPath = Meteor.settings.public.S3_FOLDER_PATH || "protocol-images/Skills";
  const endpoint = Meteor.settings.public.S3_BASE_URL;

  return { key, secret, region, bucketName, folderPath, endpoint };
}

export default function fetchVideoSkillList() {
  const { key, secret, region, bucketName, folderPath, endpoint } = getS3Config();

  if (!key?.length) {
    return [];
  }

  const clientConfig = {
    credentials: {
      accessKeyId: key,
      secretAccessKey: secret
    },
    region
  };

  // Add endpoint for S3-compatible services like OVHCloud
  if (endpoint) {
    clientConfig.endpoint = endpoint;
    clientConfig.forcePathStyle = true; // Required for some S3-compatible services
  }

  const client = new S3Client(clientConfig);
  const input = {
    Bucket: bucketName,
    MaxKeys: 200,
    Prefix: folderPath
  };
  const command = new ListObjectsV2Command(input);
  return client.send(command);
}

// Custom AWS Signature Version 4 implementation
function createSignedUrl(
  region,
  accessKeyId,
  secretAccessKey,
  bucketName,
  objectKey,
  expiresIn = 3600,
  endpoint = null
) {
  const host = endpoint ? new URL(endpoint).host : `${bucketName}.s3.${region}.amazonaws.com`;
  const date = new Date();
  const dateStamp = date
    .toISOString()
    .substr(0, 10)
    .replace(/-/g, "");
  const timestamp = `${date
    .toISOString()
    .replace(/[-:]/g, "")
    .substr(0, 15)}Z`;
  const credentialScope = `${dateStamp}/${region}/s3/aws4_request`;

  // Query parameters
  const queryParams = new URLSearchParams({
    "X-Amz-Algorithm": "AWS4-HMAC-SHA256",
    "X-Amz-Credential": `${accessKeyId}/${credentialScope}`,
    "X-Amz-Date": timestamp,
    "X-Amz-Expires": expiresIn.toString(),
    "X-Amz-SignedHeaders": "host"
  });

  // URI-encode the object key to handle spaces and special characters
  // Each path segment must be encoded separately
  const encodedObjectKey = objectKey
    .split("/")
    .map(segment => encodeURIComponent(segment))
    .join("/");

  const canonicalUri =
    endpoint && endpoint.includes("amazonaws.com") === false
      ? `/${bucketName}/${encodedObjectKey}`
      : `/${encodedObjectKey}`;

  const canonicalQueryString = queryParams.toString();
  const canonicalHeaders = `host:${host}\n`;
  const signedHeaders = "host";
  const payloadHash = "UNSIGNED-PAYLOAD";

  const canonicalRequest = [
    "GET",
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join("\n");

  const stringToSign = [
    "AWS4-HMAC-SHA256",
    timestamp,
    credentialScope,
    crypto
      .createHash("sha256")
      .update(canonicalRequest)
      .digest("hex")
  ].join("\n");

  // Create signing key
  const kDate = crypto
    .createHmac("sha256", `AWS4${secretAccessKey}`)
    .update(dateStamp)
    .digest();
  const kRegion = crypto
    .createHmac("sha256", kDate)
    .update(region)
    .digest();
  const kService = crypto
    .createHmac("sha256", kRegion)
    .update("s3")
    .digest();
  const kSigning = crypto
    .createHmac("sha256", kService)
    .update("aws4_request")
    .digest();

  const signature = crypto
    .createHmac("sha256", kSigning)
    .update(stringToSign)
    .digest("hex");

  queryParams.append("X-Amz-Signature", signature);

  let protocol = "https";
  if (endpoint) {
    protocol = endpoint.startsWith("http") ? endpoint.split("://")[0] : "https";
  }

  return `${protocol}://${host}${canonicalUri}?${queryParams.toString()}`;
}

export async function generateVideoSignedUrl(measureNumber) {
  const { key, secret, region, bucketName, folderPath, endpoint } = getS3Config();

  if (!key?.length) {
    throw new Meteor.Error(500, "S3 credentials not configured");
  }

  const objectKey = `${folderPath}/${measureNumber}.mp4`;

  // Generate signed URL valid for 1 hour
  return createSignedUrl(region, key, secret, bucketName, objectKey, 3600, endpoint);
}

export async function generateS3SignedUrl(objectPath) {
  const { key, secret, region, bucketName, endpoint } = getS3Config();

  if (!key?.length) {
    throw new Meteor.Error(500, "S3 credentials not configured");
  }

  // Generate signed URL valid for 1 hour
  return createSignedUrl(region, key, secret, bucketName, objectPath, 3600, endpoint);
}
