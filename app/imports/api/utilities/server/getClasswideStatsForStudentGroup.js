import { findLast, get } from "lodash";
import { getAbsoluteWeekNumber } from "./utilities";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Students } from "../../students/students";
import { calculateStats } from "./calculateStats";

function getEntitySpecificContext(type, entity) {
  if (type === "individual") {
    return {
      currentSkill: entity.currentSkill,
      currentStatsAsOfDate: entity.individualStatsAsOfDate[entity.currentSkill.benchmarkPeriodId],
      propertyToUpdate: `individualStatsAsOfDate.${entity.currentSkill.benchmarkPeriodId}`,
      collection: Students
    };
  }
  return {
    currentSkill: entity.currentClasswideSkill,
    currentStatsAsOfDate: entity.classwideStatsAsOfDate,
    propertyToUpdate: "classwideStatsAsOfDate",
    collection: StudentGroups
  };
}

function getFirstIntervention(entity, currentInterventionBenchmarkPeriodId) {
  return findLast(
    entity.history,
    item => item.type !== "benchmark" && item.benchmarkPeriodId === currentInterventionBenchmarkPeriodId
  );
}

function getCurrentInterventionBenchmarkPeriod(currentSkill, entity) {
  const currentSkillBmp = get(currentSkill, "benchmarkPeriodId");
  if (currentSkillBmp) {
    return currentSkillBmp;
  }
  if (!entity.history) {
    return null;
  }
  const latestInterventionItem = entity.history.find(item => item.type !== "benchmark");
  return get(latestInterventionItem, "benchmarkPeriodId");
}

export async function getValidatedStatsAsOfDate(entity, type = "classwide") {
  const { currentSkill, currentStatsAsOfDate, collection, propertyToUpdate } = getEntitySpecificContext(type, entity);
  const currentInterventionBenchmarkPeriodId = getCurrentInterventionBenchmarkPeriod(currentSkill, entity);
  if (!currentInterventionBenchmarkPeriodId) return currentStatsAsOfDate;

  const firstIntervention = getFirstIntervention(entity, currentInterventionBenchmarkPeriodId);
  if (!get(firstIntervention, "whenStarted.date")) return currentStatsAsOfDate;

  const firstInterventionDate = firstIntervention.whenStarted.date;
  if (new Date(currentStatsAsOfDate) < new Date(firstInterventionDate)) {
    await collection.updateAsync({ _id: entity._id }, { $set: { [propertyToUpdate]: firstInterventionDate } });
    return firstInterventionDate;
  }
  return currentStatsAsOfDate;
}

/** *************************************************************************
 * Get classwide group stats (Weeks per skill and intervention consistency
 ************************************************************************* */
export async function getClasswideStatsForStudentGroup({
  studentGroup,
  studentIdsInGroup = [],
  schoolYear,
  numberOfSkillsInClasswideTree,
  startWeekNumberToUse = undefined,
  endWeekNumberToUse = null
}) {
  let startWeekNumber = startWeekNumberToUse;
  if (!startWeekNumber && studentGroup.classwideStatsAsOfDate) {
    // Note that this value may get overridden in the calculateStats function
    const startWeekDate = await getValidatedStatsAsOfDate(studentGroup);
    startWeekNumber = await getAbsoluteWeekNumber(startWeekDate, schoolYear);
  }
  // const numberOfStudentsInGroup =
  //   StudentGroupEnrollments.find({ studentGroupId: studentGroup._id, isActive: true }).count();
  const numberOfStudentsInGroup = studentIdsInGroup.length;
  // Note that the following function call can take an optional startWeek for future enhancements,
  // which could be determined from benchmarkPeriod or a custom value
  const overallGroupStats = await calculateStats(
    studentGroup.history,
    studentGroup.currentClasswideSkill,
    "group",
    studentIdsInGroup,
    startWeekNumber,
    endWeekNumberToUse,
    studentGroup.schoolYear,
    studentGroup.orgid
  );
  return {
    startWeek: startWeekNumber,
    studentGroupId: studentGroup._id,
    studentGroupName: studentGroup.name,
    numberOfStudentsInGroup,
    numberOfSkillsPracticed: overallGroupStats.numberOfSkillsPracticed,
    numberOfWeeksActive: overallGroupStats.numberOfWeeksActive,
    weekNumbersWithScoresEntered: overallGroupStats.weekNumbersWithScoresSinceStartWeek,
    numberOfWeeksWithScoresEntered: overallGroupStats.weekNumbersWithScoresSinceStartWeek.length,
    averageWeeksPerSkill: overallGroupStats.averageWeeksPerSkill,
    interventionConsistency: overallGroupStats.interventionConsistency,
    individualResults: overallGroupStats.individualResults,
    numberOfSkillsInClasswideTree,
    numberOfWeeksPracticingCurrentSkill: overallGroupStats.numberOfWeeksPracticingCurrentSkill,
    worseScoresRatio: overallGroupStats.worseScoresRatio
  };
}
