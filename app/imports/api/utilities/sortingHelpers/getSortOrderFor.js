import { isString, isUndefined, get } from "lodash";
import normalizeSortItem from "./normalizeSortItem";

export default function getSortOrderFor(list, path) {
  const directions = [];
  list.reduce((prev, next) => {
    const normalizedPrev = normalizeSortItem(prev);
    const normalizedNext = normalizeSortItem(next);
    if (next) {
      let prevValue = get(normalizedPrev, path);
      if (isString(prevValue)) {
        prevValue = prevValue.toLowerCase();
      }
      let nextValue = get(normalizedNext, path);
      if (isString(nextValue)) {
        nextValue = nextValue.toLowerCase();
      }
      if (prevValue === nextValue) {
        if (directions.length) {
          directions.push(directions[directions.length - 1]);
        }
      } else if (isUndefined(prevValue) || isUndefined(nextValue)) {
        // undefined values are grouped together by lodash
      } else {
        directions.push(prevValue < nextValue ? 1 : -1);
      }
    }
    return next;
  });

  if (isAscending(directions) && directions[0] === 1) {
    return 1;
  }
  if (isDescending(directions) && directions[0] === -1) {
    return -1;
  }

  return 0;
}

function isAscending(arr) {
  return arr.every((x, i) => i === 0 || x >= arr[i - 1]);
}

function isDescending(arr) {
  return arr.every((x, i) => i === 0 || x <= arr[i - 1]);
}
