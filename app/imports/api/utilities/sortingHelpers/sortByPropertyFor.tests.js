import sortByPropertyFor from "./sortByPropertyFor";
import { ascendingOrder, descendingOrder, sampleList } from "./sortingStubs";
import getSortOrderFor from "./getSortOrderFor";

describe("sortByPropertyFor", () => {
  describe("an empty list", () => {
    it("should throw an error", () => {
      expect(() => {
        sortByPropertyFor({
          list: [],
          paths: ["somePath"],
          order: ascendingOrder
        });
      }).toThrow("Cannot sort an empty list");
    });
  });
  describe("a missing list", () => {
    it("should throw an error", () => {
      expect(() => {
        sortByPropertyFor({ paths: ["somePath"], order: ascendingOrder });
      }).toThrow("List was not provided");
    });
  });
  describe("a missing paths", () => {
    it("should throw an error", () => {
      expect(() => {
        sortByPropertyFor({ list: sampleList, order: ascendingOrder });
      }).toThrow("No paths were provided");
    });
  });
  describe("a non-nested", () => {
    describe("string", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["name"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["name"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
      it("should work with normalized values", () => {
        const paths = ["grade"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
    describe("number", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["age"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["age"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
    describe("boolean", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["isOnlyPet"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["isOnlyPet"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
  });
  describe("a nested property", () => {
    describe("string", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["ownerDetails.name.firstName"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["ownerDetails.name.firstName"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
    describe("number", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["ownerDetails.age"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["ownerDetails.age"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
    describe("boolean", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["ownerDetails.isSingle"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should should return a sorted list in a descending order", () => {
        const paths = ["ownerDetails.age"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
    describe("missing in some objects in the list", () => {
      it("should return a sorted list in an ascending order", () => {
        const paths = ["ownerDetails.name.middleName"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: ascendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(ascendingOrder);
      });
      it("should return a sorted list in a descending order", () => {
        const paths = ["ownerDetails.name.middleName"];
        const sortedList = sortByPropertyFor({
          list: sampleList,
          paths,
          order: descendingOrder
        });

        expect(getSortOrderFor(sortedList, paths[0])).toEqual(descendingOrder);
      });
    });
  });
});
