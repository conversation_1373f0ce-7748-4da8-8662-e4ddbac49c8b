import { groupBy, isNumber, orderBy } from "lodash";
import { getPercent } from "../assessmentResults/utilities";
import { hasGroupPassedClasswideIntervention } from "./utilities";

export function getGradeDetailData({
  studentGroups,
  gradeSkills,
  assessmentResultsByGroupId,
  shouldCalculateData,
  assessmentNameById
}) {
  const gradeRowData = {};
  if (!shouldCalculateData) {
    return {
      rowData: {},
      summaryAll: {}
    };
  }
  studentGroups.forEach(studentGroup => {
    const { _id: studentGroupId, name, history } = studentGroup;

    if (!gradeRowData[studentGroupId]) {
      gradeRowData[studentGroupId] = {
        rowName: name,
        columns: []
      };
    }

    if (!gradeSkills) {
      gradeRowData[studentGroupId].columns = [];
      return;
    }
    const columnsWithSkillName = [
      ...gradeSkills.map(gs => ({ skillName: gs.assessmentName || assessmentNameById[gs.assessmentId] })),
      { skillName: "All Skills" }
    ];
    if (!history?.filter(h => h.type === "classwide")?.length) {
      gradeRowData[studentGroupId].columns = columnsWithSkillName;
      return;
    }
    const outcomesByAssessmentId = processDataForDetailData(assessmentResultsByGroupId[studentGroupId] || []);
    if (!outcomesByAssessmentId) {
      gradeRowData[studentGroupId].columns = columnsWithSkillName;
      return;
    }
    gradeRowData[studentGroupId].columns =
      getSummaryAllForIndividualStudentDetail({
        gradeSkills,
        outcomesByAssessmentId,
        assessmentNameById,
        shouldIncludeAllSkills: true,
        rowName: ""
      })?.columns || [];
  });

  const summaryAll = Object.values(gradeRowData).reduce(
    (total, groupData, groupRowIndex) => {
      groupData.columns.forEach((column, i) => {
        total.columns[i].skillName = column.skillName;
        total.columns[i].masteryCount += isNumber(column.masteryCount) ? column.masteryCount : 0;
        total.columns[i].instructionalCount += isNumber(column.instructionalCount) ? column.instructionalCount : 0;
        total.columns[i].frustrationalCount += isNumber(column.frustrationalCount) ? column.frustrationalCount : 0;
        total.columns[i].absentCount += isNumber(column.absentCount) ? column.absentCount : 0;
        total.columns[i].numberOfStudents += isNumber(column.numberOfStudents) ? column.numberOfStudents : 0;
        if (
          isNumber(column.masteryCount) ||
          isNumber(column.instructionalCount) ||
          isNumber(column.frustrationalCount) ||
          isNumber(column.absentCount)
        ) {
          total.columns[i].numberOfGroupsWithData += 1;
        }
      });
      if (Object.values(gradeRowData).length - 1 === groupRowIndex) {
        total.columns.forEach(column => {
          Object.entries(column).forEach(([key, value]) => {
            if (["skillName", "numberOfGroupsWithData", "numberOfStudents"].includes(key)) {
              if (column.numberOfGroupsWithData === 0 && key !== "skillName" && column.skillName !== "All Skills") {
                column[key] = null;
              }
              return;
            }

            if (key.includes("Count") && value !== null && column.numberOfGroupsWithData !== 0) {
              column[key.replace("Count", "")] = Math.round((value / column.numberOfStudents) * 100);
            }
            if (column.numberOfGroupsWithData === 0) {
              column[key] = null;
            }
          });
        });
      }
      return total;
    },
    {
      rowName: "Summary All",
      columns: [
        ...gradeSkills.map(skill => ({
          skillName: skill.assessmentName || assessmentNameById[skill.assessmentId] || "",
          mastery: 0,
          instructional: 0,
          frustrational: 0,
          absent: 0,
          masteryCount: 0,
          instructionalCount: 0,
          frustrationalCount: 0,
          absentCount: 0,
          numberOfGroupsWithData: 0,
          numberOfStudents: 0
        })),
        {
          skillName: "All Skills",
          mastery: 0,
          instructional: 0,
          frustrational: 0,
          absent: 0,
          masteryCount: 0,
          instructionalCount: 0,
          frustrationalCount: 0,
          absentCount: 0,
          numberOfGroupsWithData: 0,
          numberOfStudents: 0
        }
      ]
    }
  );

  return {
    rowData: gradeRowData,
    summaryAll
  };
}

export function getSummaryAllForIndividualStudentDetail({
  gradeSkills,
  outcomesByAssessmentId,
  assessmentNameById,
  shouldIncludeAllSkills,
  rowName = "Summary All"
}) {
  const summaryRowData = {
    rowName,
    columns: []
  };
  gradeSkills.forEach(skill => {
    const { assessmentId, assessmentName } = skill;
    const results = outcomesByAssessmentId[assessmentId] || {};

    summaryRowData.columns.push({
      skillName: results.assessmentName || assessmentName || assessmentNameById[assessmentId],
      mastery: getPercent(results.above?.length, results.numberOfStudents),
      instructional: getPercent(results.at?.length, results.numberOfStudents),
      frustrational: getPercent(results.below?.length, results.numberOfStudents),
      absent: getPercent(results.absent?.length, results.numberOfStudents),
      masteryCount: results.above?.length,
      instructionalCount: results.at?.length,
      frustrationalCount: results.below?.length,
      absentCount: results.absent?.length,
      numberOfStudents: results.numberOfStudents
    });
  });
  if (shouldIncludeAllSkills) {
    const allSkillData = summaryRowData.columns.reduce(
      (a, c) => {
        a.mastery += isNumber(c.masteryCount) ? c.masteryCount : 0;
        a.instructional += isNumber(c.instructionalCount) ? c.instructionalCount : 0;
        a.frustrational += isNumber(c.frustrationalCount) ? c.frustrationalCount : 0;
        a.absent += isNumber(c.absentCount) ? c.absentCount : 0;
        a.totalNumberOfStudents += isNumber(c.numberOfStudents) ? c.numberOfStudents : 0;
        return a;
      },
      {
        mastery: 0,
        instructional: 0,
        frustrational: 0,
        absent: 0,
        totalNumberOfStudents: 0
      }
    );
    summaryRowData.columns.push({
      skillName: "All Skills",
      mastery: Math.round((allSkillData.mastery / allSkillData.totalNumberOfStudents) * 100) || 0,
      instructional: Math.round((allSkillData.instructional / allSkillData.totalNumberOfStudents) * 100) || 0,
      frustrational: Math.round((allSkillData.frustrational / allSkillData.totalNumberOfStudents) * 100) || 0,
      absent: Math.round((allSkillData.absent / allSkillData.totalNumberOfStudents) * 100) || 0,
      masteryCount: allSkillData.mastery,
      instructionalCount: allSkillData.instructional,
      frustrationalCount: allSkillData.frustrational,
      absentCount: allSkillData.absent,
      numberOfStudents: allSkillData.totalNumberOfStudents
    });
  }
  return summaryRowData;
}

export function processDataForDetailData(assessmentResults) {
  const assessmentResultsByAssessmentId = groupBy(assessmentResults, "measures.0.assessmentId");
  const passedAssessmentResults = assessmentResults.filter(ar => {
    // Skip assessment results without measures
    if (!ar.measures || !ar.measures[0]) {
      return false;
    }
    return hasGroupPassedClasswideIntervention({
      medianScore: ar.measures[0].medianScore,
      totalStudentsAssessed: ar.scores.filter(s => s.status !== "CANCELLED").length,
      targetScores: ar.measures[0].targetScores,
      studentScores: ar.scores.map(s => s.value),
      numberOfEnrolledStudents: ar.scores.length
    });
  });
  if (!passedAssessmentResults.length) {
    return null;
  }
  return passedAssessmentResults.reduce((a, ar) => {
    const absentStudentIds = ar.scores.filter(s => s.status === "CANCELLED").map(s => s.studentId);

    const previousOutcomesOfAbsentStudents = {
      above: [],
      at: [],
      below: [],
      absent: [],
      numberOfStudentsWithScores: 0
    };

    if (absentStudentIds.length) {
      const allAssessmentResultsForSkill = orderBy(
        assessmentResultsByAssessmentId[ar.measures[0].assessmentId],
        "lastModified.on",
        "desc"
      );
      // NOTE(fmazur) - Get rid of passing assessment result;
      allAssessmentResultsForSkill.shift();
      absentStudentIds.forEach(studentId => {
        let foundPreviousOutcome = false;
        allAssessmentResultsForSkill.forEach(ars => {
          const outcome = ars.measures[0].studentResults.find(s => s.studentId === studentId);
          if (!foundPreviousOutcome && outcome) {
            previousOutcomesOfAbsentStudents[outcome.individualRuleOutcome].push({
              studentId,
              outcome: outcome.individualRuleOutcome
            });
            previousOutcomesOfAbsentStudents.numberOfStudentsWithScores += 1;
            foundPreviousOutcome = true;
          }
        });
        if (!foundPreviousOutcome) {
          previousOutcomesOfAbsentStudents.absent.push({ studentId, outcome: "absent" });
        }
      });
    }

    // NOTE(fmazur) - Classwide has only one measure
    const measure = ar.measures[0];
    const outcomes = groupBy(
      measure.studentResults.map(s => ({
        studentId: s.studentId,
        outcome: s.individualRuleOutcome
      })),
      "outcome"
    );

    // eslint-disable-next-line no-param-reassign
    a[measure.assessmentId] = {
      assessmentName: measure.assessmentName,
      above: [...(outcomes.above || []), ...previousOutcomesOfAbsentStudents.above],
      at: [...(outcomes.at || []), ...previousOutcomesOfAbsentStudents.at],
      below: [...(outcomes.below || []), ...previousOutcomesOfAbsentStudents.below],
      absent: previousOutcomesOfAbsentStudents.absent,
      numberOfStudents: ar.scores.length,
      numberOfStudentsWithScores:
        measure.studentResults.length + previousOutcomesOfAbsentStudents.numberOfStudentsWithScores
    };
    return a;
  }, {});
}
