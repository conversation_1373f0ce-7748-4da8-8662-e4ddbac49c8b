import { pick } from "lodash";
import { Organizations } from "../organizations/organizations";
import { decrypt, decryptRosteringSettings } from "../utilities/utilities";
import { EdFiConnectionException, getEdFiCompositeData, getEdFiData } from "../../scripts/edFi/edFi-connect";

export async function getRosterSettings(orgid) {
  const { rosteringSettings = {}, name } =
    (await Organizations.findOneAsync(orgid, { fields: { name: 1, rosteringSettings: 1 } })) || {};
  if (Object.keys(rosteringSettings).length) {
    const {
      apiUrl,
      authUrl,
      clientId,
      clientSecret,
      shouldUseScopes,
      shouldUseSequentialRequests = false,
      shouldIgnoreEnrollmentStartDate = false,
      shouldIgnoreEnrollmentEndDate = false,
      limit = 500,
      filters,
      translations,
      userIdentifiers
    } = rosteringSettings;
    return {
      apiUrl: decrypt(apiUrl),
      authUrl: decrypt(authUrl),
      clientId: decrypt(clientId),
      clientSecret: decrypt(clientSecret),
      shouldUseScopes,
      shouldUseSequentialRequests,
      shouldIgnoreEnrollmentStartDate,
      shouldIgnoreEnrollmentEndDate,
      limit,
      filters,
      translations,
      userIdentifiers,
      name
    };
  }
  return {};
}

export async function getDataFromResource({ orgid, itemsToFetch, query, fields, useComposites = true }) {
  const { rosteringSettings } = await Organizations.findOneAsync({ _id: orgid }, { fields: { rosteringSettings: 1 } });
  const { apiUrl, clientId, clientSecret } = decryptRosteringSettings(rosteringSettings);

  let result = null;
  try {
    const { schoolYear, limit = 500 } = rosteringSettings;
    if (useComposites) {
      result = await getEdFiCompositeData({
        apiUrl,
        clientId,
        clientSecret,
        schoolYear,
        itemsToFetch,
        limit,
        temporaryFilters: query,
        useComposites
      });
    } else {
      result = await getEdFiData({ apiUrl, clientId, clientSecret, schoolYear, itemsToFetch, limit, query });
    }
  } catch (e) {
    const { response = {}, code, message } = e;
    throw new EdFiConnectionException(response.status || code, response.statusText || message);
  }
  const { data = [] } = result || {};
  if (fields.length) {
    return data.map(elem => pick(elem, fields));
  }
  return data;
}

export function fetchSchoolsResource({ orgid, filters, useComposites }) {
  return getDataFromResource({
    orgid,
    itemsToFetch: "schools",
    query: useComposites ? {} : { schoolId: filters.schools },
    fields: [],
    useComposites
  });
}

export function fetchStaffsResource({ orgid, filters, useComposites }) {
  return getDataFromResource({
    orgid,
    itemsToFetch: "staffs",
    query: useComposites ? filters : { staffUniqueId: filters.teachers },
    fields: [],
    useComposites
  });
}

export function fetchSectionsResource({ orgid, filters, useComposites }) {
  if (useComposites) {
    return getDataFromResource({
      orgid,
      itemsToFetch: "sections",
      query: filters,
      fields: [],
      useComposites
    });
  }
  return getDataFromResource({
    orgid,
    itemsToFetch: "sections",
    query: { schoolId: filters.schoolIds },
    fields: [],
    useComposites
  });
}

export function fetchStaffSectionAssociationsResource({ orgid, sections, useComposites }) {
  const sectionIdentifiers = sections.map(s => s.sectionIdentifier);
  return getDataFromResource({
    orgid,
    itemsToFetch: "staffSectionAssociations",
    query: useComposites ? {} : { sectionIdentifier: sectionIdentifiers },
    fields: [],
    useComposites
  });
}

export function fetchStudentSectionAssociationsResource({ orgid, sections, useComposites }) {
  const sectionIdentifiers = sections.map(s => s.sectionIdentifier);
  return getDataFromResource({
    orgid,
    itemsToFetch: "studentSectionAssociations",
    query: useComposites ? {} : { sectionIdentifier: sectionIdentifiers },
    fields: [],
    useComposites
  });
}

export function fetchStudentsResource({ orgid, studentSectionAssociations, schoolIds, useComposites }) {
  if (useComposites) {
    return getDataFromResource({
      orgid,
      itemsToFetch: "students",
      query: { schoolIds },
      fields: [],
      useComposites
    });
  }
  const studentUniqueIds = new Set();
  studentSectionAssociations.forEach(studentAssociation => {
    const studentUniqueId = studentAssociation?.studentReference?.studentUniqueId || "";
    if (studentUniqueId) {
      studentUniqueIds.add(studentUniqueId);
    }
  });
  return getDataFromResource({
    orgid,
    itemsToFetch: "students",
    query: { studentUniqueId: Array.from(studentUniqueIds) },
    fields: [],
    useComposites
  });
}

export function fetchGradeLevelDescriptors(orgid) {
  return getDataFromResource({
    orgid,
    itemsToFetch: "GradeLevelDescriptors",
    query: {},
    fields: [],
    useComposites: false
  });
}
