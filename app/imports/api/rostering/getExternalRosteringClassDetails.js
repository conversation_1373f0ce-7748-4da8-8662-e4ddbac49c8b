import { get } from "lodash";

export function getExternalRosteringClassDetails({
  locationReference,
  classPeriods,
  courseOfferingReference,
  sectionName,
  sectionIdentifier
}) {
  const classroomId = get(locationReference, "classroomIdentificationCode", "");
  const classroomCode = `${sectionIdentifier}${classroomId ? ` ${classroomId}` : ""}`;
  const periodName = classPeriods[0].classPeriodReference.classPeriodName.replace(/ Traditional/i, "");
  const sessionName = get(
    courseOfferingReference.sessionName.match(/fall|winter|spring/i),
    0,
    courseOfferingReference.sessionName
  );
  const className = `${periodName} ${sectionName || courseOfferingReference.localCourseCode} ${sessionName}`;
  const classFullName = `${periodName} ${sectionName ||
    courseOfferingReference.localCourseCode} ${sessionName} (${classroomCode})`;
  return { classroomCode, className, classFullName };
}
