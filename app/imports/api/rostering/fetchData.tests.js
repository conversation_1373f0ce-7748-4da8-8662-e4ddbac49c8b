import td from "testdouble";
import _ from "lodash";
import moment from "moment";
import * as fetchData from "./fetchData";
import { ExternalRosteringAPIManager } from "../../scripts/externalRosteringAPIManager";
import { Organizations } from "../organizations/organizations";
import { SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP } from "../constants";

const { getOneRosterImportRowsData, getEdFiImportRowsDataFromComposite } = fetchData;

jest.mock("../../scripts/externalRosteringAPIManager");
jest.mock("./utils");
jest.mock("../sites/methods");

function getEntryGradeLevelDescriptorURI(gradeLabel) {
  return `uri://ed-fi.org/GradeLevelDescriptor#${gradeLabel}`;
}

function generateEdFiStudents({
  rangeTarget,
  isAssocation = true,
  studentGradeLabels = [],
  numberOfInactiveStudents = 0
}) {
  const tomorrow = `${moment()
    .add(1, "days")
    .format("YYYY-MM-DD")}T00:00:00`;
  const yesterday = `${moment()
    .add(-1, "days")
    .format("YYYY-MM-DD")}T00:00:00`;

  return _.range(rangeTarget).map(index => {
    if (isAssocation) {
      return {
        id: `studentId${index}`,
        studentUniqueId: index,
        enrollmentEndDate: rangeTarget - numberOfInactiveStudents > index ? tomorrow : yesterday
      };
    }
    return {
      id: `studentId${index}`,
      studentUniqueId: `${index}`,
      lastSurname: `lastNameStudent${index}`,
      firstName: `firstNameStudent${index}`,
      classId: "1",
      birthDate: "2006-02-20",
      endDate: rangeTarget - numberOfInactiveStudents > index ? tomorrow : yesterday,
      studentSchoolEnrollments: [
        {
          entryGradeLevelDescriptor: getEntryGradeLevelDescriptorURI(studentGradeLabels[index] || ""),
          schoolId: "1"
        }
      ]
    };
  });
}

let methods;
const orgid = "test_org_id";
describe("fetchData", () => {
  const existingSites = [
    {
      stateInformation: {
        schoolNumber: "1"
      },
      lastModified: {
        on: SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP + 10
      }
    }
  ];
  describe("getOneRosterImportRowsData", () => {
    beforeEach(() => {
      const utils = require("./utils");
      jest.spyOn(utils, "getRosterSettings").mockImplementation(() => ({
        apiUrl: "",
        clientId: "",
        clientSecret: "",
        filters: {
          schools: ["1"],
          teachers: ["1"],
          classes: ["1"]
        },
        name: "OneRoster"
      }));

      methods = require("../sites/methods");
      jest.spyOn(methods, "convertSchoolIdsToNCES").mockImplementation(() => ({}));
      jest.spyOn(ExternalRosteringAPIManager.prototype, "getEnrollmentsForSchools").mockImplementation(() => [
        {
          sourcedId: "1",
          classId: "1",
          role: "student",
          status: "active"
        },
        {
          sourcedId: "2",
          classId: "1",
          role: "teacher",
          status: "active",
          primary: "true",
          beginDate: moment().subtract(1, "day"),
          endDate: moment().add(1, "day"),
          user: { sourcedId: "1" }
        }
      ]);
      jest
        .spyOn(ExternalRosteringAPIManager.prototype, "getSchool")
        .mockImplementation(() => [{ sourcedId: "1", name: "School 1", parent: { sourcedId: "0" } }]);
      jest.spyOn(ExternalRosteringAPIManager.prototype, "getClassesForSchool").mockImplementation(() => [
        {
          sourcedId: "1",
          location: "location1",
          school: { sourcedId: "1" },
          classCode: "2",
          title: "title1",
          course: { sourcedId: "3" }
        }
      ]);
      jest.spyOn(ExternalRosteringAPIManager.prototype, "getTeachersForClass").mockImplementation(() => [
        {
          sourcedId: "1",
          classId: "1",
          email: "<EMAIL>",
          givenName: "firstNameTeacher1",
          familyName: "lastNameTeacher1"
        }
      ]);
      jest.spyOn(ExternalRosteringAPIManager.prototype, "getStudentsForSchool").mockImplementation(() => [
        {
          sourcedId: "1",
          classId: "1",
          familyName: "lastNameStudent1",
          givenName: "firstNameStudent1",
          grades: ["PR", "K", "05"]
        },
        {
          sourcedId: "2",
          classId: "1",
          familyName: "lastNameStudent2",
          givenName: "firstNameStudent2",
          grades: ["PR", "K"]
        },
        {
          sourcedId: "3",
          classId: "1",
          familyName: "lastNameStudent3",
          givenName: "firstNameStudent3",
          grades: ["K", "05"]
        },
        {
          sourcedId: "4",
          classId: "2",
          familyName: "lastNameStudent4",
          givenName: "firstNameStudent4",
          grades: ["06"]
        }
      ]);
      jest
        .spyOn(ExternalRosteringAPIManager.prototype, "getDemographics")
        .mockImplementation(() => [{ sourcedId: "1", birthDate: "2006-02-20" }]);
    });
    afterEach(() => {
      td.reset();
      jest.restoreAllMocks();
      Organizations.remove({});
    });
    it("should fetch import data with correct fields and values", async () => {
      Organizations.insert({
        _id: orgid,
        allowMultipleGradeLevels: true
      });
      const { importData } = await getOneRosterImportRowsData(orgid, "rosterOR", existingSites);
      expect(importData).toHaveLength(3);
      const [firstStudent, secondStudent, thirdStudent] = importData;
      expect(firstStudent.StudentGrade).toEqual("05");
      expect(firstStudent.SpringMathGrade).toEqual("05");
      expect(secondStudent.StudentGrade).toEqual("K");
      expect(secondStudent.SpringMathGrade).toEqual("05");
      expect(thirdStudent.StudentGrade).toEqual("05");
      expect(thirdStudent.SpringMathGrade).toEqual("05");
      // TODO(fmazur) - generate expected and actual to verify all fields
    });
  });
  describe("getEdFiImportRowsDataFromComposite", () => {
    beforeEach(() => {
      const utils = require("./utils");

      jest.spyOn(utils, "getRosterSettings").mockImplementation(() => ({
        apiUrl: "",
        clientId: "",
        clientSecret: "",
        filters: {
          schools: ["1"],
          teachers: ["1"],
          classes: ["1"]
        },
        name: "EdFi"
      }));

      jest.spyOn(utils, "fetchSchoolsResource").mockImplementation(() => [
        {
          schoolId: "1",
          nameOfInstitution: "School 1",
          localEducationAgencyReference: { localEducationAgencyId: "0" }
        }
      ]);

      jest.spyOn(utils, "fetchStaffsResource").mockImplementation(() => [
        {
          id: "id_1",
          staffUniqueId: "1",
          firstName: "firstNameTeacher1",
          lastSurname: "lastNameTeacher1",
          electronicMails: [{ electronicMailAddress: "<EMAIL>" }]
        }
      ]);

      jest.spyOn(utils, "fetchGradeLevelDescriptors").mockImplementation(() => [
        {
          codeValue: "Fifth grade",
          description: "Fifth grade"
        },
        {
          codeValue: "Grade 13",
          description: "Grade 13"
        },
        {
          codeValue: "Kindergarten",
          description: "Kindergarten"
        }
      ]);

      jest.spyOn(utils, "fetchSectionsResource").mockImplementation(params =>
        params.useComposites
          ? [
              {
                id: "1",
                sectionIdentifier: "1",
                sequenceOfCourse: "1",
                availableCredits: "1",
                localCourseCode: "ELA-01",
                classPeriods: [],
                staff: [
                  {
                    id: "id_1",
                    staffUniqueId: "1"
                  }
                ],
                students: generateEdFiStudents({ rangeTarget: 6, numberOfInactiveStudents: 1 }),
                session: {
                  schoolId: "1",
                  schoolYear: 2011,
                  termDescriptor: "uri://ed-fi.org/TermDescriptor#Fall Semester",
                  sessionName: "2010-2011 Fall Semester"
                },
                location: {
                  id: "86a1fe32-bd40-4379-8ac1-251157d4df41",
                  schoolId: "1",
                  classroomIdentificationCode: "104"
                }
              }
            ]
          : [
              {
                id: "1",
                courseOfferingReference: {
                  localCourseCode: "ART-01",
                  schoolId: "1",
                  schoolYear: 2011,
                  sessionName: "2010-2011 Fall Semester",
                  link: []
                },
                locationReference: {
                  classroomIdentificationCode: "504",
                  schoolId: "1",
                  link: []
                },
                locationSchoolReference: { schoolId: "1", link: [] },
                sectionIdentifier: "1",
                sequenceOfCourse: "1",
                classPeriods: [
                  {
                    classPeriodReference: {
                      schoolId: "1",
                      classPeriodName: "T07"
                    }
                  }
                ]
              }
            ]
      );

      jest.spyOn(utils, "fetchStudentsResource").mockImplementation(() => {
        const studentGradeLabels = ["Kindergarten", "Grade 13", "Fifth grade", "Fifth grade", "Fifth grade"];
        return generateEdFiStudents({ rangeTarget: 5, isAssocation: false, studentGradeLabels });
      });
    });
    afterEach(() => {
      jest.restoreAllMocks();
      Organizations.remove({});
    });
    it("should fetch import data with correct fields and values", async () => {
      Organizations.insert({
        _id: orgid,
        allowMultipleGradeLevels: true
      });
      const importData = await getEdFiImportRowsDataFromComposite(orgid, existingSites);
      expect(importData).toHaveLength(5);
      const [firstStudent, secondStudent, , , fifthStudent] = importData;
      expect(firstStudent.StudentGrade).toEqual("K");
      expect(firstStudent.SpringMathGrade).toEqual("05");
      expect(secondStudent.StudentGrade).toEqual("13");
      expect(secondStudent.SpringMathGrade).toEqual("05");
      expect(fifthStudent.StudentGrade).toEqual("05");
      expect(fifthStudent.SpringMathGrade).toEqual("05");
      // TODO(fmazur) - generate expected and actual to verify all fields
    });
  });
});
