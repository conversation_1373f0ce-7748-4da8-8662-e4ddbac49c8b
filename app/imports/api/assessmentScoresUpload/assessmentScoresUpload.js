import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";
import { idValidation, validateSchemaProperty } from "../utilities/utilities";

export const AssessmentScoresUpload = new Mongo.Collection("AssessmentScoresUpload");

const stateAssessmentFields = [
  "stateAssessmentName",
  "stateAssessmentScaleScore",
  "stateAssessmentProficient",
  "stateAssessmentPercentileScore"
];

const districtAssessmentOptionalParameters = ["districtAssessmentFallScaleScore", "districtAssessmentSpringScaleScore"];

const districtAssessmentFallFields = ["districtAssessmentFallScaleScore", "districtAssessmentFallProficient"];

const districtAssessmentSpringFields = ["districtAssessmentSpringScaleScore", "districtAssessmentSpringProficient"];

const assessmentFieldsValidator = (assessmentFields, errorName) =>
  function() {
    if (assessmentFields.some(field => this.field(field).value !== "") && this.value === "") {
      return errorName;
    }

    return undefined;
  };

const optionalAssessmentFieldsValidator = (assessmentName, assessmentOptionalParameters, errorName) =>
  function() {
    const isAssessmentNameEmpty = this.field(assessmentName).value === "";
    const areAssessmentOptionalFieldsPopulated = assessmentOptionalParameters.some(
      field => this.field(field).value !== ""
    );
    if (
      (!isAssessmentNameEmpty && areAssessmentOptionalFieldsPopulated) ||
      (isAssessmentNameEmpty && !areAssessmentOptionalFieldsPopulated)
    ) {
      return undefined;
    }
    return errorName;
  };

AssessmentScoresUpload.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  studentId: { type: String },
  orgid: { type: String, min: 1 },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  data: { type: Object, blackbox: true },
  grade: { type: String }
});

AssessmentScoresUpload.schemaAssessmentScore = new SimpleSchema({
  studentLocalID: {
    type: String,
    optional: true,
    custom: validateSchemaProperty(idValidation.regexWithEmptyString, "invalidStudentLocalID")
  },
  studentStateID: {
    type: String,
    min: 1,
    custom: validateSchemaProperty(idValidation.regex, "invalidStudentStateID")
  },
  studentLastName: { type: String, min: 1 },
  studentFirstName: { type: String, min: 1 },
  assessmentYear: SimpleSchema.oneOf(
    {
      type: String,
      min: 1
    },
    {
      type: SimpleSchema.Integer,
      min: 1
    }
  ),
  stateAssessmentName: {
    type: String,
    custom: assessmentFieldsValidator(stateAssessmentFields, "missingStateAssessmentFields")
  },
  stateAssessmentScaleScore: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(stateAssessmentFields, "missingStateAssessmentFields")
    },
    {
      type: SimpleSchema.Integer,
      min: 0,
      custom: assessmentFieldsValidator(stateAssessmentFields, "missingStateAssessmentFields")
    }
  ),
  stateAssessmentProficient: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(stateAssessmentFields, "missingStateAssessmentFields")
    },
    {
      type: Boolean,
      custom: assessmentFieldsValidator(stateAssessmentFields, "missingStateAssessmentFields")
    }
  ),
  stateAssessmentPercentileScore: SimpleSchema.oneOf(
    {
      type: String
    },
    {
      type: SimpleSchema.Integer,
      min: 0,
      max: 100
    }
  ),
  districtAssessmentName: {
    type: String,
    custom: optionalAssessmentFieldsValidator(
      "districtAssessmentName",
      districtAssessmentOptionalParameters,
      "missingDistrictAssessmentFields"
    )
  },
  districtAssessmentFallScaleScore: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(districtAssessmentFallFields, "missingDistrictAssessmentFallFields")
    },
    {
      type: SimpleSchema.Integer,
      min: 0,
      custom: assessmentFieldsValidator(districtAssessmentFallFields, "missingDistrictAssessmentFallFields")
    }
  ),
  districtAssessmentFallProficient: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(districtAssessmentFallFields, "missingDistrictAssessmentFallFields")
    },
    {
      type: Boolean,
      custom: assessmentFieldsValidator(districtAssessmentFallFields, "missingDistrictAssessmentFallFields")
    }
  ),
  districtAssessmentSpringScaleScore: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(districtAssessmentSpringFields, "missingDistrictAssessmentSpringFields")
    },
    {
      type: SimpleSchema.Integer,
      min: 0,
      custom: assessmentFieldsValidator(districtAssessmentSpringFields, "missingDistrictAssessmentSpringFields")
    }
  ),
  districtAssessmentSpringProficient: SimpleSchema.oneOf(
    {
      type: String,
      custom: assessmentFieldsValidator(districtAssessmentSpringFields, "missingDistrictAssessmentSpringFields")
    },
    {
      type: Boolean,
      custom: assessmentFieldsValidator(districtAssessmentSpringFields, "missingDistrictAssessmentSpringFields")
    }
  )
});

AssessmentScoresUpload.validate = assessmentScoreUpload => {
  AssessmentScoresUpload.schemaAssessmentScore.validate(assessmentScoreUpload.data);
};
AssessmentScoresUpload.isValid = assessmentScoreUpload =>
  AssessmentScoresUpload.schemaAssessmentScore.namedContext("testContext").validate(assessmentScoreUpload.data);

AssessmentScoresUpload.schemaAssessmentScore.messageBox.messages({
  en: {
    invalidStudentLocalID: `Invalid StudentLocalID format - ${idValidation.description}`,
    invalidStudentStateID: `Invalid StudentStateID format - ${idValidation.description}`,
    missingStateAssessmentFields:
      "At least one state assessment field contains a value. Make sure to provide a value for {{name}}",
    missingDistrictAssessmentFields:
      "There is a problem with district assessment fields. Make sure to provide a value for assessment name and spring or fall score fields",
    missingDistrictAssessmentSpringFields:
      "At least one spring district assessment field contains a value. Make sure to provide a value for {{name}}",
    missingDistrictAssessmentFallFields:
      "At least one fall district assessment field contains a value. Make sure to provide a value for {{name}}"
  }
});
