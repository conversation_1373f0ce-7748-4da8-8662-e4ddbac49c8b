import { Students } from "../students/students";
import { checkIfLocalAndStateIdsExist } from "./methods";

describe("checkIfLocalAndStateIdsExist", () => {
  const orgid = "testOrgId";
  const stateId = "1";
  const localId = "1";
  const firstName = "Bryan";
  const lastName = "Moran";

  const otherLocalId = "2";
  const otherStateId = "2";
  const otherFirstName = "Bryan1";
  const otherLastName = "Moran2";
  const schoolYear = 2020;

  beforeEach(async () => {
    await Students.insertAsync([
      {
        orgid,
        identity: {
          name: {
            firstName,
            lastName
          },
          identification: {
            localId,
            stateId
          }
        },
        schoolYear
      }
    ]);
  });
  afterEach(async () => {
    await Students.removeAsync({});
  });
  it("should return error when provided student data list is empty", async () => {
    const data = [];
    const expectedError = "Error fetching data from server, please refresh the page.";
    expect(await checkIfLocalAndStateIdsExist(data, orgid, schoolYear)).toEqual([expectedError]);
  });
  it("should return error when no students were found", async () => {
    const data = [
      {
        firstName,
        lastName,
        localId: otherLocalId,
        stateId: otherStateId
      }
    ];

    const expectedError = `Student: ${firstName} ${lastName} with localID 2 and stateID 2 doesn't exist in ${schoolYear} year. Please see row: 2.`;
    expect(await checkIfLocalAndStateIdsExist(data, orgid, schoolYear)).toEqual([expectedError]);
  });
  it("should return error when found student identity doesn't match provided student data", async () => {
    const data = [
      {
        firstName: otherFirstName,
        lastName: otherLastName,
        localId,
        stateId
      }
    ];
    const expectedError =
      "Student with localID 1 corresponds to Bryan Moran rather than Bryan1 Moran2. Please see row: 2.";
    expect(await checkIfLocalAndStateIdsExist(data, orgid, schoolYear)).toEqual([expectedError]);
  });
  it("should return empty error list when student identity matches", async () => {
    const data = [
      {
        firstName,
        lastName,
        localId,
        stateId
      }
    ];
    expect(await checkIfLocalAndStateIdsExist(data, orgid, schoolYear)).toEqual([]);
  });
});
