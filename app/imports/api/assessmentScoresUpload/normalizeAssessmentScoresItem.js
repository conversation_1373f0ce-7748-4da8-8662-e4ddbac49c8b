const proficiencyPositiveOutcomes = ["yes", "y", "pass", "p"];
const proficiencyNegativeOutcomes = ["no", "n", "fail", "f"];
const allowedProficiencyValues = [...proficiencyPositiveOutcomes, ...proficiencyNegativeOutcomes];

export const getNormalizedProficiency = (proficiency, errors = [], field) => {
  const isAllowedValue = allowedProficiencyValues.includes(proficiency.toLowerCase());
  if (proficiency?.length && !isAllowedValue) {
    errors.push(
      `${field} - Proficiency columns need to be any out of [${allowedProficiencyValues.join(", ")}] (case insensitive)`
    );
  }
  if (isAllowedValue) {
    const normalizedProficiency = proficiency?.toLowerCase();
    if (proficiencyPositiveOutcomes.includes(normalizedProficiency)) {
      return true;
    }
    if (proficiencyNegativeOutcomes.includes(normalizedProficiency)) {
      return false;
    }
  }
  return proficiency;
};

const getNumber = (stringifiedNumber = "") => stringifiedNumber && parseInt(stringifiedNumber);

export function normalizeAssessmentScoreItem(assessmentScoreItem, errors) {
  const normalizedItem = {
    ...assessmentScoreItem
  };
  normalizedItem.districtAssessmentSpringProficient = getNormalizedProficiency(
    assessmentScoreItem.districtAssessmentSpringProficient,
    errors,
    "DistrictAssessmentSpringProficient"
  );
  normalizedItem.districtAssessmentFallProficient = getNormalizedProficiency(
    assessmentScoreItem.districtAssessmentFallProficient,
    errors,
    "DistrictAssessmentFallProficient"
  );
  normalizedItem.assessmentYear = getNumber(assessmentScoreItem.assessmentYear);
  normalizedItem.stateAssessmentProficient = getNormalizedProficiency(
    assessmentScoreItem.stateAssessmentProficient,
    errors,
    "StateAssessmentProficient"
  );
  normalizedItem.stateAssessmentScaleScore = getNumber(assessmentScoreItem.stateAssessmentScaleScore);
  normalizedItem.stateAssessmentPercentileScore = getNumber(assessmentScoreItem.stateAssessmentPercentileScore);
  normalizedItem.districtAssessmentFallScaleScore = getNumber(assessmentScoreItem.districtAssessmentFallScaleScore);
  normalizedItem.districtAssessmentSpringScaleScore = getNumber(assessmentScoreItem.districtAssessmentSpringScaleScore);

  return normalizedItem;
}
