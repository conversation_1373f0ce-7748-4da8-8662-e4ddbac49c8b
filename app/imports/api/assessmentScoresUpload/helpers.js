import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getMeteorUserId } from "../utilities/utilities";

function generateDataObj() {
  return {
    studentLocalID: "",
    studentStateID: "",
    studentLastName: "",
    studentFirstName: "",
    assessmentYear: "",
    stateAssessmentName: "",
    stateAssessmentScaleScore: "",
    stateAssessmentProficient: "",
    stateAssessmentPercentileScore: "",
    districtAssessmentName: "",
    districtAssessmentFallScaleScore: "",
    districtAssessmentFallProficient: "",
    districtAssessmentSpringScaleScore: "",
    districtAssessmentSpringProficient: ""
  };
}

export default class AssessmentScoreUploadHelpers {
  static generateObj() {
    const orgid = "test_organization_id";
    const timestampInfo = getTimestampInfo(getMeteorUserId(), orgid);
    return {
      orgid,
      created: timestampInfo,
      lastModified: timestampInfo,
      data: generateDataObj()
    };
  }

  static createFromCSVDataRow(dataRow) {
    const item = this.generateObj();
    item.data.studentLocalID = trim(dataRow.StudentLocalID);
    item.data.studentStateID = trim(dataRow.StudentStateID);
    item.data.studentLastName = trim(dataRow.StudentLastName);
    item.data.studentFirstName = trim(dataRow.StudentFirstName);
    item.data.assessmentYear = trim(dataRow.AssessmentYear);
    item.data.stateAssessmentName = trim(dataRow.StateAssessmentName);
    item.data.stateAssessmentScaleScore = trim(dataRow.StateAssessmentScaleScore);
    item.data.stateAssessmentProficient = trim(dataRow.StateAssessmentProficient);
    item.data.stateAssessmentPercentileScore = trim(dataRow.StateAssessmentPercentileScore);
    item.data.districtAssessmentName = trim(dataRow.DistrictAssessmentName);
    item.data.districtAssessmentFallScaleScore = trim(dataRow.DistrictAssessmentFallScaleScore);
    item.data.districtAssessmentFallProficient = trim(dataRow.DistrictAssessmentFallProficient);
    item.data.districtAssessmentSpringScaleScore = trim(dataRow.DistrictAssessmentSpringScaleScore);
    item.data.districtAssessmentSpringProficient = trim(dataRow.DistrictAssessmentSpringProficient);

    return item;
  }
}

function trim(field = "") {
  return field.trim();
}
