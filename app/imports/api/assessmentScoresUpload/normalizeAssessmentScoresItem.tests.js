import { normalizeAssessmentScoreItem, getNormalizedProficiency } from "./normalizeAssessmentScoresItem";

describe("getNormalizedProficiency", () => {
  it("should be case insensitive", () => {
    const errors = [];
    expect(getNormalizedProficiency("YeS", errors)).toEqual(true);
    expect(getNormalizedProficiency("yeS", errors)).toEqual(true);
    expect(getNormalizedProficiency("yes", errors)).toEqual(true);
    expect(getNormalizedProficiency("y", errors)).toEqual(true);
    expect(getNormalizedProficiency("Y", errors)).toEqual(true);
    expect(getNormalizedProficiency("P", errors)).toEqual(true);
    expect(getNormalizedProficiency("p", errors)).toEqual(true);
    expect(getNormalizedProficiency("pass", errors)).toEqual(true);
    expect(getNormalizedProficiency("Pass", errors)).toEqual(true);
    expect(getNormalizedProficiency("PASS", errors)).toEqual(true);
    expect(getNormalizedProficiency("nO", errors)).toEqual(false);
    expect(getNormalizedProficiency("no", errors)).toEqual(false);
    expect(getNormalizedProficiency("n", errors)).toEqual(false);
    expect(getNormalizedProficiency("N", errors)).toEqual(false);
    expect(getNormalizedProficiency("F", errors)).toEqual(false);
    expect(getNormalizedProficiency("f", errors)).toEqual(false);
    expect(getNormalizedProficiency("fail", errors)).toEqual(false);
    expect(getNormalizedProficiency("Fail", errors)).toEqual(false);
    expect(getNormalizedProficiency("FAIL", errors)).toEqual(false);
    expect(errors).toEqual([]);
    expect(getNormalizedProficiency("illegalInput", errors, "Test Field")).toEqual("illegalInput");
    expect(errors).toContain(
      "Test Field - Proficiency columns need to be any out of [yes, y, pass, p, no, n, fail, f] (case insensitive)"
    );
  });
});

describe("normalizeAssessmentScoreItem", () => {
  it("should return an object with normalized values", () => {
    const fuli = {
      districtAssessmentFallProficient: "Yes",
      districtAssessmentFallScaleScore: "655",
      districtAssessmentName: "District Assessment",
      districtAssessmentSpringProficient: "No",
      districtAssessmentSpringScaleScore: "522",
      stateAssessmentName: "State Assessment",
      stateAssessmentPercentileScore: "50",
      stateAssessmentProficient: "Yes",
      stateAssessmentScaleScore: "624",
      assessmentYear: "2020",
      studentFirstName: "Mitch",
      studentLastName: "Dave",
      studentLocalID: "312",
      studentStateID: "5432"
    };

    const expectedResult = {
      districtAssessmentFallProficient: true,
      districtAssessmentFallScaleScore: 655,
      districtAssessmentName: "District Assessment",
      districtAssessmentSpringProficient: false,
      districtAssessmentSpringScaleScore: 522,
      stateAssessmentName: "State Assessment",
      stateAssessmentPercentileScore: 50,
      stateAssessmentProficient: true,
      stateAssessmentScaleScore: 624,
      assessmentYear: 2020,
      studentFirstName: "Mitch",
      studentLastName: "Dave",
      studentLocalID: "312",
      studentStateID: "5432"
    };

    const normalizedFuli = normalizeAssessmentScoreItem(fuli);

    expect(normalizedFuli).toMatchObject(expectedResult);
  });
});
