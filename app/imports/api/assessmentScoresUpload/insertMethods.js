import each from "lodash/each";
import { AssessmentScoresUpload } from "./assessmentScoresUpload";
import { Students } from "../students/students";
import { normalizeAssessmentScoreItem } from "./normalizeAssessmentScoresItem";
import AssessmentScoreUploadHelpers from "./helpers";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getMeteorUser } from "../utilities/utilities";

function convert(assessmentScoreItem, orgid, userId) {
  const item = AssessmentScoreUploadHelpers.createFromCSVDataRow(assessmentScoreItem);
  item.data = normalizeAssessmentScoreItem(item.data);
  const itemTimestampInfo = getTimestampInfo(userId, orgid);
  item.created = itemTimestampInfo;
  item.lastModified = itemTimestampInfo;
  item.orgid = orgid;
  AssessmentScoresUpload.validate(item);
  return item;
}

async function getStudents({ localIds = [], stateIds = [], schoolYear, orgid }) {
  return Students.find(
    {
      $or: [
        {
          "identity.identification.localId": { $in: localIds },
          orgid,
          schoolYear
        },
        {
          "identity.identification.stateId": { $in: stateIds },
          orgid,
          schoolYear
        }
      ]
    },
    { fields: { _id: 1, "identity.identification": 1, grade: 1 } }
  ).fetchAsync();
}

export async function bulkInsert(assessmentScoresData, orgid, shouldAddToExistingData) {
  const convertedItems = [];
  const bulkLineItems = AssessmentScoresUpload.rawCollection().initializeUnorderedBulkOp();
  const user = getMeteorUser();
  const schoolYear = parseInt(assessmentScoresData[0].AssessmentYear);
  const userId = user?._id || "unknown";
  const localIds = assessmentScoresData.map(item => item.StudentLocalID);
  const stateIds = assessmentScoresData.map(item => item.StudentStateID);
  const students = await getStudents({ localIds, stateIds, schoolYear, orgid });

  if (!shouldAddToExistingData) {
    await AssessmentScoresUpload.rawCollection().deleteMany({ schoolYear });
  }

  each(assessmentScoresData, d => {
    const item = convert(d, orgid, userId);
    const correspondingStudent = students.find(
      student =>
        (!item.data.studentLocalID || item.data.studentLocalID === student.identity.identification.localId) &&
        item.data.studentStateID === student.identity.identification.stateId
    );
    if (correspondingStudent) {
      item.studentId = correspondingStudent._id;
      item.schoolYear = schoolYear;
      item.grade = correspondingStudent.grade;
    }

    convertedItems.push(item);
    bulkLineItems
      .find({ studentId: item.studentId, schoolYear })
      .upsert()
      .replaceOne(item);
  });

  await bulkLineItems.execute();
  return convertedItems;
}

export default bulkInsert;
