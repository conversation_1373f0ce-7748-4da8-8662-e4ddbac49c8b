import { assert } from "chai";

import { Students } from "./students.js";
import { getStudent } from "../../test-helpers/data/students.js";

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  idValidation: {
    regex: /^([a-zA-Z0-9]+)$/,
    description: "should only contain letters and digits"
  }
}));

describe("Students", () => {
  describe("schema validation", () => {
    it("should not return anything if tested without context", () => {
      assert.isUndefined(Students.schema.validate(getStudent()));
    });
    it("should return true if provided student data is valid", () => {
      assert.isTrue(Students.schema.namedContext("testContext").validate(getStudent()));
    });
    it("should return false if _id is not a string", () => {
      const studentDoc = getStudent();
      studentDoc._id = 1234;
      assert.isFalse(Students.schema.namedContext("testContext").validate(studentDoc));
    });
    ["1020156163��_", "a-b-c", "7.9E+12", "$123"].forEach(studentStateId => {
      it(`should return false if student state ID: ${studentStateId} is of invalid format`, () => {
        const studentDoc = getStudent();
        studentDoc.identity.identification.stateId = studentStateId;
        assert.isFalse(Students.schema.namedContext("testContext").validate(studentDoc));
      });
    });
    ["0", "10", "a456Avs", "012345"].forEach(studentLocalId => {
      it(`should return false if student state ID: ${studentLocalId} is valid`, () => {
        const studentDoc = getStudent();
        studentDoc.identity.identification.localId = studentLocalId;
        assert.isTrue(Students.schema.namedContext("testContext").validate(studentDoc));
      });
    });
  });
});
