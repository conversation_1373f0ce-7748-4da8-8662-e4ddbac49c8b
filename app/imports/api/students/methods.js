import { check } from "meteor/check";
import { Meteor } from "meteor/meteor";
import { get } from "lodash";

import { Students } from "./students";
import * as auth from "../authorization/server/methods";
import { getMeteorUser, getMeteorUserId } from "../utilities/utilities";
import { getCurrentDate } from "../helpers/getCurrentDate";
import { getTimestampInfo } from "../helpers/getTimestampInfo";

Meteor.methods({
  async "Students:useIncrementalRehearsalForDay"({ studentId, siteId, measureNumber, day }) {
    check(studentId, String);
    check(siteId, String);
    check(measureNumber, String);
    check(day, Number);
    const student = await Students.findOneAsync({ _id: studentId }, { fields: { orgid: 1, ir: 1 } });
    if (!student) {
      throw new Meteor.Error(404, "Student not found");
    }
    const { orgid } = student;
    if (
      !(await auth.hasAccess(["teacher", "admin", "universalCoach", "support"], {
        userId: this.userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use Students:useIncrementalRehearsalForDay");
    }
    const meteorUser = await getMeteorUser();
    const customDate = get(meteorUser, "profile.customDate");
    const currentDate = await getCurrentDate(customDate, orgid);
    const timestamp = Math.floor(currentDate.getTime() / 1000);
    const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "Students:useIncrementalRehearsalForDay");
    await Students.updateAsync(
      { _id: studentId },
      { $push: { [`ir.${measureNumber}.dates`]: timestamp }, $set: { lastModified } }
    );
    return true;
  },
  async "Students:setStudentContinueSkill"({ studentId, orgid, siteId }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(studentId, String);
    check(orgid, String);
    check(siteId, String);

    if (
      !(await auth.hasAccess(["teacher", "admin", "universalCoach", "support"], {
        userId: this.userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use Students:setStudentContinueSkill");
    }

    const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "Students:setStudentContinueSkill");
    // NOTE(fmazur) - this will only remain until next score gets saved
    await Students.updateAsync(
      { _id: studentId },
      { $set: { "currentSkill.userSelectedContinue": true, lastModified } }
    );
  }
});
