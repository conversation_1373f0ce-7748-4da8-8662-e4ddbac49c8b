export function getCurrentEnrolledGrade(studentGrade) {
  if (!studentGrade) {
    return null;
  }

  let grade = studentGrade[0] === "0" ? studentGrade.slice(1) : studentGrade;

  switch (grade) {
    case "K":
      grade = "Kindergarten";
      break;
    case "k":
      grade = "Kindergarten";
      break;
    case "HS":
      grade = "HS Grade";
      break;
    case "1":
      grade += "st Grade"; // 1st Grade
      break;
    case "2":
      grade += "nd Grade"; // 2nd Grade
      break;
    case "3":
      grade += "rd Grade"; // 3rd Grade
      break;
    default:
      grade += "th Grade"; // 4th - 12th Grade
      break;
  }
  return grade;
}
