import { searchStudents } from "./methods";
import { Students } from "../students";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";

const schoolYear = 2018;
jest.mock("../../utilities/utilities", () => ({
  ...jest.requireActual("../../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => schoolYear)
}));

const testOrgId1 = "testOrgId1";
const testSiteId1 = "testSiteId1";
const testSiteId2 = "testSiteId2";
const testStudentGroupId1 = "testStudentGroupId1";
const studentId1 = "testStudent1";
const studentId2 = "testStudent2";
const studentId3 = "testStudent3";
const studentId4 = "testStudent4";
const studentId5 = "testStudent5";
const studentLocalId1 = "localId1";
const studentLocalId2 = "partial_localId2";
const studentLocalId3 = "partial_localId3";
const studentLocalId4 = "localId4";
const studentLocalId5 = "localId5";
const studentStateId1 = "stateId1";
const studentStateId2 = "partial_stateId2";
const studentStateId3 = "partial_stateId3";
const studentStateId4 = "stateId4";
const studentStateId5 = "stateId5";
const studentEnrollmentId1 = "studentEnrollmentId1";
const studentEnrollmentId2 = "studentEnrollmentId2";
const studentEnrollmentId3 = "studentEnrollmentId3";
const studentEnrollmentId4 = "studentEnrollmentId4";
const studentEnrollmentId5 = "studentEnrollmentId5";
const studentEnrollmentId6 = "studentEnrollmentId6";
const studentEnrollmentId7 = "studentEnrollmentId7";

const studentsForTest = [
  {
    _id: studentId1,
    grade: "K",
    identity: {
      name: { firstName: "Michale", lastName: "Gears" },
      identification: { localId: studentLocalId1, stateId: studentStateId1 }
    },
    demographic: { birthDate: new Date() },
    orgid: testOrgId1,
    siteId: testSiteId1,
    schoolYear
  },
  {
    _id: studentId2,
    grade: "02",
    identity: {
      name: { firstName: "Mia", lastName: "Mia" },
      identification: { localId: studentLocalId2, stateId: studentStateId2 }
    },
    demographic: { birthDate: new Date() },
    orgid: testOrgId1,
    siteId: testSiteId1,
    schoolYear
  },
  {
    _id: studentId3,
    grade: "03",
    identity: {
      name: { firstName: "Matt", lastName: "Mia" },
      identification: { localId: studentLocalId3, stateId: studentStateId3 }
    },
    demographic: { birthDate: new Date() },
    orgid: testOrgId1,
    siteId: testSiteId1,
    schoolYear
  },
  {
    _id: studentId4,
    grade: "02",
    identity: {
      name: { firstName: "Mia", lastName: "Mia" },
      identification: { localId: studentLocalId4, stateId: studentStateId4 }
    },
    demographic: { birthDate: new Date() },
    orgid: testOrgId1,
    siteId: testSiteId2,
    schoolYear
  },
  {
    _id: studentId5,
    grade: "02",
    identity: {
      name: { firstName: "Mia", lastName: "Mia" },
      identification: { localId: studentLocalId5, stateId: studentStateId5 }
    },
    demographic: { birthDate: new Date() },
    orgid: testOrgId1,
    siteId: testSiteId2,
    schoolYear: 2017
  }
];

const studentGroupEnrollmentsForTest = [
  {
    _id: studentEnrollmentId1,
    studentId: studentId1,
    siteId: testSiteId1,
    isActive: true,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 3
    }
  },
  {
    _id: studentEnrollmentId2,
    studentId: studentId2,
    siteId: testSiteId1,
    isActive: true,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 1
    }
  },
  {
    _id: studentEnrollmentId3,
    studentId: studentId3,
    siteId: testSiteId1,
    isActive: true,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 1
    }
  },
  {
    _id: studentEnrollmentId4,
    studentId: studentId4,
    siteId: testSiteId2,
    isActive: true,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 1
    }
  },
  {
    _id: studentEnrollmentId5,
    studentId: studentId1,
    siteId: testSiteId1,
    isActive: false,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 2
    }
  },
  {
    _id: studentEnrollmentId6,
    studentId: studentId1,
    siteId: testSiteId1,
    isActive: false,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 1
    }
  },
  {
    _id: studentEnrollmentId7,
    studentId: studentId2,
    siteId: testSiteId1,
    isActive: false,
    studentGroupId: testStudentGroupId1,
    schoolYear,
    created: {
      on: 1
    }
  }
];

const studentGroupsForTest = [{ _id: testStudentGroupId1, isActive: true }];

const expectedStudentGroups = [studentGroupsForTest[0]];

describe("Student Search Functionality", () => {
  describe("searchStudents", () => {
    beforeAll(async () => {
      await Students.insertAsync(studentsForTest);
      await StudentGroupEnrollments.insertAsync(studentGroupEnrollmentsForTest);
      await StudentGroups.insertAsync(studentGroupsForTest);
    });
    afterAll(async () => {
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
      await Students.removeAsync({});
      jest.restoreAllMocks();
    });

    it("Should find 2 students with first name with Mi, only from current schoolYear despite multiple enrollments for student1", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        firstName: "Mi"
      });
      const expectedStudents = [studentsForTest[0], studentsForTest[1], studentsForTest[3]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[0], studentGroupEnrollmentsForTest[1]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("Should find 2 students with first name with mi, check case insensitivity, only from current schoolYear despite multiple enrollments for student1", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        firstName: "mi"
      });
      const expectedStudents = [studentsForTest[0], studentsForTest[1], studentsForTest[3]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[0], studentGroupEnrollmentsForTest[1]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("Should find 2 students with last name with mi, only from current schoolYear despite multiple enrollments for student2", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        lastName: "mi"
      });
      const expectedStudents = [studentsForTest[1], studentsForTest[2], studentsForTest[3]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[1], studentGroupEnrollmentsForTest[2]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("Should find 1 student with last name with mi from different site, only from current schoolYear", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId2],
        lastName: "mi"
      });
      const expectedStudents = [studentsForTest[1], studentsForTest[2], studentsForTest[3]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[3]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("should find only the most recent studentGroupEnrollments for each student", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        firstName: "michale"
      });
      const expectedStudents = [studentsForTest[0]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[0]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("should find exactly 1 student when searching with a full stateId", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        stateId: studentStateId1
      });
      const expectedStudents = [studentsForTest[0]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[0]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("should find exactly 1 student when searching with a full localId", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        localId: studentLocalId1
      });
      const expectedStudents = [studentsForTest[0]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[0]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("should find multiple students when searching with a partial stateId", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        stateId: "partial_stateId"
      });
      const expectedStudents = [studentsForTest[1], studentsForTest[2]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[1], studentGroupEnrollmentsForTest[2]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
    it("should find multiple students when searching with a partial localId", async () => {
      const foundStudentsData = await searchStudents({
        orgid: testOrgId1,
        siteIds: [testSiteId1],
        localId: "partial_localId"
      });
      const expectedStudents = [studentsForTest[1], studentsForTest[2]];
      const expectedStudentEnrollments = [studentGroupEnrollmentsForTest[1], studentGroupEnrollmentsForTest[2]];

      expect(foundStudentsData.students).toMatchObject(expectedStudents);
      expect(foundStudentsData.studentEnrollments).toMatchObject(expectedStudentEnrollments);
      expect(foundStudentsData.studentGroups).toMatchObject(expectedStudentGroups);
    });
  });
});
