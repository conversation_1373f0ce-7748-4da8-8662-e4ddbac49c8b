import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import some from "lodash/some";
import { Students } from "../students";
import { StudentGroups } from "../../studentGroups/studentGroups";
import * as auth from "../../authorization/server/methods";
import * as utils from "../../utilities/utilities";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { getListOfStudentIdsFromStudentGroups } from "../../studentGroups/utilities";
import { getMeteorUser, isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("StudentsInStudentGroup", async function studentsInGroupPublication(studentGroupId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);

  const sg = await StudentGroups.findOneAsync(
    { _id: studentGroupId },
    { fields: { ownerIds: 1, secondaryTeachers: 1, siteId: 1, orgid: 1 } }
  );
  if (!sg) {
    return this.ready();
  }
  if (
    some(sg.ownerIds, oId => this.userId === oId) ||
    some(sg.secondaryTeachers, scId => this.userId === scId) ||
    (await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId: sg.siteId,
      orgid: sg.orgid
    }))
  ) {
    const studentIds = await getListOfStudentIdsFromStudentGroups([studentGroupId]);
    return Students.find({ _id: { $in: studentIds } });
  }
  return this.ready();
});

Meteor.publish("Students:StudentsInStudentGroup", async function getStudents(studentGroupId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  const studentIds = await getListOfStudentIdsFromStudentGroups([studentGroupId]);
  return Students.find({ _id: { $in: studentIds } });
});

Meteor.publish("Students:PerGrade", async function studentsPerOrgPublication({ orgid, grade, schoolYear, siteId }) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);
  check(grade, String);
  check(schoolYear, Number);
  check(siteId, String);

  if (!orgid) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerGrade - no orgid"
    });
    return this.ready();
  }
  if (
    // added universalDataAdmin
    !(await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    }))
  ) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerGrade"
    });
    return this.ready();
  }
  const sgIds = (
    await StudentGroups.find({ siteId, grade, schoolYear, isActive: true }, { fields: { _id: 1 } }).fetchAsync()
  ).map(sg => sg._id);
  const studentIds = await getListOfStudentIdsFromStudentGroups(sgIds);
  return Students.find({ _id: { $in: studentIds } });
});

Meteor.publish("Students:PerOrg", async function studentsPerOrgPublication(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!orgid) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerOrg - missing orgid"
    });
    return this.ready();
  }
  check(orgid, String);

  if (
    !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    }))
  ) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerOrg"
    });
    return this.ready();
  }
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  return Students.find(
    { orgid, schoolYear: currentSchoolYear },
    { fields: { orgid: 1, grade: 1, schoolYear: 1, identity: 1, demographic: 1, studentGrade: 1 } }
  );
});

Meteor.publish("Students:GetStudentById", async function studentsByIdPublication(siteId, studentGroupId, studentId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(siteId, String);
  check(studentId, String);
  check(studentGroupId, String);

  const sg = await StudentGroups.findOneAsync(
    { _id: studentGroupId },
    { fields: { ownerIds: 1, secondaryTeachers: 1, orgid: 1 } }
  );
  if (
    some(sg.ownerIds, oId => this.userId === oId) ||
    some(sg.secondaryTeachers, teacherId => this.userId === teacherId) ||
    (await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid: sg.orgid
    }))
  ) {
    return Students.find({ _id: studentId });
  }
  utils.ninjalog.log({
    msg: "Publication Access Denied: Students:GetStudentById"
  });
  return this.ready();
});

Meteor.publishComposite("Students:PerSiteInYear", async function getStudentsPerSiteInYear({
  orgid,
  siteId,
  schoolYear,
  includeFields = {}
}) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!siteId) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerSiteInYear - missing siteId"
    });
    return this.ready();
  }
  check(siteId, Match.Maybe(String));
  check(schoolYear, Match.Maybe(Number));
  if (
    !(await auth.hasAccess(["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      siteId,
      orgid
    }))
  ) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: Students:PerSiteInYear"
    });
    return this.ready();
  }
  const currentSchoolYear = schoolYear || (await utils.getCurrentSchoolYear(await getMeteorUser(), orgid));
  const query = { schoolYear: currentSchoolYear };
  if (siteId) {
    query.siteId = siteId;
  }

  return {
    find() {
      return StudentGroupEnrollments.find(query, { fields: { studentId: 1 } });
    },
    children: [
      {
        find(studentGroupEnrollment) {
          return Students.find(
            { _id: studentGroupEnrollment.studentId },
            {
              fields: {
                orgid: 1,
                grade: 1,
                schoolYear: 1,
                identity: 1,
                demographic: 1,
                studentGrade: 1,
                ...includeFields
              }
            }
          );
        }
      }
    ]
  };
});
