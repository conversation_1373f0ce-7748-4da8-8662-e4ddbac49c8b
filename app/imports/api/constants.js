export const SCORE_INCREASING_METRIC_THRESHOLD = 50;

export const PRINT_OPTIONS = {
  CURRENT_CLASSWIDE_INTERVENTION: "Print Current Classwide Intervention Graph",
  ALL_CLASSWIDE_INTERVENTION_GRAPHS: "Print All Classwide Intervention Graphs",
  ALL_INDIVIDUAL_INTERVENTION_GRAPHS: "Print All Individual Intervention Graphs",
  CURRENT_STUDENT_PROFILE: "Print Current Student Profile Page",
  ALL_STUDENT_DETAILS: "Print All Student Details Including All Graphs",
  STUDENT_CLASSWIDE_PROGRESS_AND_SCREENING_RESULTS: "Print Classwide Progress and Screening Results"
};

export const SCHOOL_OVERVIEW_TITLE = "School Overview/Coach Dashboard";

export const ROSTER_IMPORTS_LIMIT = 100;

export const colors = {
  orange: "#ed8b00",
  darkBlue: "#003349",
  steelBlue: "#506D85",
  steelBlue30: "#B6C1CB",
  steelBlue60: "#8196A7",
  violet: "#565294",
  white: "#ffffff",
  brightBlue: "#005bb5",
  oliveGreen: "#7A9A01",
  teal: "#009CA6"
};

// NOTE(fmazur) - 15th Jan 2025
export const SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP = 1736895600000;

export const FORCE_CHANGE_PASSWORD_INTERVAL_IN_DAYS = 70;
export const FORCE_CHANGE_PASSWORD_INTERVAL_WITH_MFA_IN_DAYS = 365;
