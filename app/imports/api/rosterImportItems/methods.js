import moment from "moment";

function generateDataObj() {
  return {
    districtID: "",
    districtName: "",
    schoolID: "",
    schoolName: "",
    teacherID: "",
    teacherLastName: "",
    teacherFirstName: "",
    teacherEmail: "",
    className: "",
    classSectionID: "",
    studentLocalID: "",
    studentStateID: "",
    studentLastName: "",
    studentFirstName: "",
    studentBirthDate: moment().toDate(),
    springMathGrade: ""
  };
}

export default class RosterImportItemsHelpers {
  static generateObj() {
    const orgid = "test_organization_id";
    return {
      orgid,
      created: null, // Will be set later in convert function
      rosterImportId: "",
      data: generateDataObj()
    };
  }

  static createFromCSVDataRow(dataRow) {
    const rosterItem = this.generateObj();
    rosterItem.data.districtID = safeTrim(dataRow.DistrictID);
    rosterItem.data.districtName = safeTrim(dataRow.DistrictName);
    rosterItem.data.schoolID = safeTrim(dataRow.SchoolID);
    rosterItem.data.schoolName = safeTrim(dataRow.SchoolName);
    rosterItem.data.teacherID = safeTrim(dataRow.TeacherID);
    rosterItem.data.teacherLastName = safeTrim(dataRow.TeacherLastName);
    rosterItem.data.teacherFirstName = safeTrim(dataRow.TeacherFirstName);
    rosterItem.data.teacherEmail = safeTrim(dataRow.TeacherEmail).toLowerCase();
    rosterItem.data.className = safeTrim(dataRow.ClassName);
    rosterItem.data.classSectionID = safeTrim(dataRow.ClassSectionID);
    rosterItem.data.studentLocalID = safeTrim(dataRow.StudentLocalID);
    rosterItem.data.studentStateID = safeTrim(dataRow.StudentStateID);
    rosterItem.data.studentLastName = safeTrim(dataRow.StudentLastName);
    rosterItem.data.studentFirstName = safeTrim(dataRow.StudentFirstName);
    rosterItem.data.studentBirthDate =
      dataRow.StudentBirthDate && moment.utc(new Date(dataRow.StudentBirthDate)).toDate();
    rosterItem.data.springMathGrade = safeTrim(dataRow.SpringMathGrade);
    if (dataRow.StudentGrade) {
      rosterItem.data.studentGrade = safeTrim(dataRow.StudentGrade);
    }

    return rosterItem;
  }
}

export function safeTrim(field = "") {
  if (typeof field === "string") {
    return field.trim();
  }
  if (typeof field === "number") {
    return field.toString().trim();
  }
  return field;
}
