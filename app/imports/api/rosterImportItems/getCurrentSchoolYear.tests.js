import MockDate from "mockdate";
import { Settings } from "../settings/settings.js";
import { Organizations } from "../organizations/organizations.js";
import { getCurrentSchoolYear } from "../utilities/utilities";

describe("getCurrentSchoolYear", () => {
  afterAll(async () => {
    MockDate.reset();
    await Settings.removeAsync({});
    await Organizations.removeAsync({});
  });

  it("should return 2017 if current date is 2017-07-31", async () => {
    MockDate.set("2017-07-31");

    expect(await getCurrentSchoolYear()).toEqual(2017);
  });

  it("should return 2015 if provided date is 2014-08-01", async () => {
    MockDate.set("2014-08-01", -360);

    expect(await getCurrentSchoolYear()).toEqual(2015);
  });

  it("should return 2018 if provided date is 2017-12-31", async () => {
    MockDate.set("2017-12-31");

    expect(await getCurrentSchoolYear()).toEqual(2018);
  });

  it("should return 2016 if provided date is 2016-01-01", async () => {
    MockDate.set("2016-01-01");

    expect(await getCurrentSchoolYear()).toEqual(2016);
  });

  it("should return 2016 if provided date is 2016-07-01", async () => {
    MockDate.set("2016-07-01");

    expect(await getCurrentSchoolYear()).toEqual(2016);
  });

  it("should take default app settings into consideration when returning current school year", async () => {
    await Settings.insertAsync({
      defaults: {
        schoolYearBoundary: {
          month: 7,
          day: 31
        }
      }
    });

    MockDate.set("2014-08-31", -360);
    expect(await getCurrentSchoolYear()).toEqual(2014);

    MockDate.set("2014-09-01", -360);
    expect(await getCurrentSchoolYear()).toEqual(2015);
  });

  it("should make possible overriding default app settings with custom organization settings", async () => {
    const user = {
      profile: {
        orgid: "orgid"
      }
    };
    await Organizations.insertAsync({
      _id: "orgid",
      schoolYearBoundary: {
        month: 8,
        day: 30
      }
    });

    MockDate.set("2014-09-30", -360);
    expect(await getCurrentSchoolYear(user)).toEqual(2014);

    MockDate.set("2014-10-01", -360);
    expect(await getCurrentSchoolYear(user)).toEqual(2015);
  });
});
