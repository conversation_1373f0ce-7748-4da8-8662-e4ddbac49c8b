export const getCourseSection = ({ courseID, classSectionID, classPeriod }) => {
  if (courseID !== undefined && classPeriod !== undefined) {
    const classSection = classSectionID.includes("-") ? `"${classSectionID}"` : classSectionID;
    return `${courseID}-${classSection}-${classPeriod}`;
  }

  return classSectionID;
};

export function normalizeRosterImportItems(rosterImportItem) {
  const normalizedItem = {};
  normalizedItem.springMathGrade = getNormalizedGrade(rosterImportItem.springMathGrade);

  const { courseID, classSectionID, classPeriod } = rosterImportItem;
  const courseSection = getCourseSection({
    courseID,
    classSectionID,
    classPeriod
  });

  normalizedItem.classSectionID = courseSection;
  normalizedItem.courseName = getNormalizedGroupName(rosterImportItem.className, courseSection);
  normalizedItem.districtID = getNormalizedId(rosterImportItem.districtID);
  normalizedItem.teacherID = rosterImportItem.teacherID;
  normalizedItem.teacherEmail = rosterImportItem.teacherEmail.trim().toLowerCase();

  return { ...rosterImportItem, ...normalizedItem };
}

export function getNormalizedGrade(grade) {
  if (["0", "k", "K"].includes(grade)) {
    return "K";
  }
  if (["9", "09", "10", "11", "12", "hS", "Hs", "hs"].includes(grade)) {
    return "HS";
  }
  if (grade.length === 2) {
    return grade;
  }
  if (parseInt(grade) < 10) {
    return `0${grade}`;
  }
  return grade.toString();
}

export function getNormalizedGroupName(className, courseSection) {
  if (className.includes(`(${courseSection})`)) {
    return className;
  }
  return `${className} (${courseSection})`;
}

export function getNormalizedId(id) {
  let normalizedId = id;
  // eslint-disable-next-line no-restricted-globals
  while (isNaN(normalizedId.charAt(normalizedId.length - 1)) && normalizedId.length > 1) {
    normalizedId = normalizedId.slice(0, -1);
  }
  normalizedId = stripLeadingZeros(normalizedId);

  return normalizedId;
}

export function stripLeadingZeros(id) {
  let normalizedId = id;
  while (normalizedId.charAt(0) === "0" && normalizedId.length > 1) {
    normalizedId = normalizedId.substr(1);
  }
  return normalizedId;
}
