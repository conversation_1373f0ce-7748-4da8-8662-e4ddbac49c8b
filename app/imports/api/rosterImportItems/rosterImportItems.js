import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const RosterImportItems = new Mongo.Collection("RosterImportItems");

RosterImportItems.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String, min: 1 },
  created: { type: ByDateOn },
  rosterImportId: { type: String },
  data: { type: Object, blackbox: true }
});

RosterImportItems.schemaStudentData = new SimpleSchema({
  districtID: {
    type: String,
    min: 1,
    max: 50
  },
  districtName: { type: String, min: 1 },
  schoolID: {
    type: String,
    min: 1,
    max: 50
  },
  schoolName: { type: String, min: 1 },
  teacherID: { type: String, min: 1 },
  teacherLastName: { type: String, min: 1 },
  teacherFirstName: { type: String, min: 1 },
  teacherEmail: { type: String, regEx: SimpleSchema.RegEx.EmailWithTLD },
  className: { type: String, min: 1 },
  classSectionID: { type: String, min: 1 },
  studentLocalID: {
    type: String,
    min: 1,
    max: 50
  },
  studentStateID: {
    type: String,
    min: 1,
    max: 50
  },
  studentLastName: { type: String, min: 1 },
  studentFirstName: { type: String, min: 1 },
  studentBirthDate: {
    type: SimpleSchema.oneOf(Date, String),
    min: new Date("1970-01-01"),
    max() {
      return new Date();
    },
    optional: true
  },
  studentGrade: { type: String, optional: true },
  springMathGrade: { type: String }
});

RosterImportItems.validate = rosterImportItems => {
  RosterImportItems.schema.validate(rosterImportItems);
  RosterImportItems.schemaStudentData.validate(rosterImportItems.data);
};
RosterImportItems.isValid = rosterImportItems =>
  RosterImportItems.schema.namedContext("testContext").validate(rosterImportItems) &&
  RosterImportItems.schemaStudentData.namedContext("testContext").validate(rosterImportItems.data);

RosterImportItems.schemaStudentData.messageBox.messages({
  en: {
    invalidSpringMathGrade: "Invalid SpringMathGrade - should be K, HS or 0 - 12"
  }
});
