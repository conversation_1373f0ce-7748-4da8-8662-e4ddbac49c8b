import { Users } from "../users/users";
import { Sites } from "../sites/sites";
import { StudentGroups } from "../studentGroups/studentGroups";
import RosterImportItemsValidator, {
  getStrippedClassName,
  validateDistrictName,
  validateStudentGroupsInUploadItems
} from "./rosterImportItemsValidator";
import {
  generateItem,
  generateNormalizedItem,
  validMockData
} from "../../test-helpers/data/rosterImportItemsValidator.testData";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Organizations } from "../organizations/organizations";

describe("RosterImportItemsValidator", () => {
  it("should return true if all values are valid", async () => {
    const result = await new RosterImportItemsValidator(validMockData).validate();

    expect(result.success).toBe(true);
  });
  describe("for schoolId", () => {
    it("should return true if schoolId is a number with leading zeros", async () => {
      const itemWithValidSchoolID = [generateItem({ schoolID: "00012345" })];

      const result = await new RosterImportItemsValidator(itemWithValidSchoolID).validate();

      expect(result.success).toBe(true);
    });

    it("should return error if there is more than one school name per schoolID", async () => {
      const invalidMockData = [
        generateItem({ schoolID: "12345", schoolName: "BCD" }),
        generateItem({ schoolID: "44444", schoolName: "XYZ" })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.schoolID).toContain(
        "More than one schoolName is present for schoolID: 12345, found:\nSchool Name: ABC 12345\nSchool Name: BCD"
      );
      expect(result.errors.schoolID).toContain(
        "More than one schoolName is present for schoolID: 44444, found:\nSchool Name: ABC 44444\nSchool Name: XYZ"
      );
    });

    it("should return error if the same school name is used across more than one school ID", async () => {
      const duplicatedSchoolName1 = "Duplicated School Name";
      const schoolID1 = "998";
      const schoolID2 = "999";
      const duplicatedSchoolName2 = "Duplicated School Name2";
      const schoolID3 = "887";
      const schoolID4 = "888";
      const invalidMockData = [
        generateItem({ schoolID: schoolID1, schoolName: duplicatedSchoolName1 }),
        generateItem({ schoolID: schoolID2, schoolName: duplicatedSchoolName1 }),
        generateItem({ schoolID: schoolID3, schoolName: duplicatedSchoolName2 }),
        generateItem({ schoolID: schoolID4, schoolName: duplicatedSchoolName2 })
      ];

      const mockData = [...validMockData, ...invalidMockData];
      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.schoolName).toContain(
        `The same schoolName: ${duplicatedSchoolName1} is used for schoolIDs: ${schoolID1}, ${schoolID2}`
      );
      expect(result.errors.schoolName).toContain(
        `The same schoolName: ${duplicatedSchoolName2} is used for schoolIDs: ${schoolID3}, ${schoolID4}`
      );
      expect(result.errors.schoolName.length).toBe(2);
    });
  });
  describe("for districtId", () => {
    it("should return true if districtID is a number with leading zeros", async () => {
      const overriddenMockData = [generateItem({ districtID: "00012345" })];

      const result = await new RosterImportItemsValidator(overriddenMockData).validate();

      expect(result.success).toBe(true);
    });

    it("should return error if there is more than one districtID in the collection", async () => {
      const invalidMockData = [generateItem({ districtID: "00002" }), generateItem({ districtID: "00003" })];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.districtID).toContain(
        "Expected a single districtID in all records, instead found: 12345, 00002, 00003"
      );
    });
  });
  describe("for districtName", () => {
    it("should return error if there is more than one districtName in the collection", async () => {
      const invalidMockData = [
        generateItem({ districtName: "OtherDistrictName" }),
        generateItem({ districtName: "AnotherDistrictName" })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.districtName).toContain(
        "Expected a single districtName in all records, instead found: SunnySlope, OtherDistrictName, AnotherDistrictName"
      );
    });

    it("should display empty districtName in error messages as [empty]", async () => {
      const invalidMockData = [
        generateItem({ districtName: "" }),
        generateItem({ districtName: "AnotherDistrictName" })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.districtName).toContain(
        "Expected a single districtName in all records, instead found: SunnySlope, [empty], AnotherDistrictName"
      );
    });
  });
  describe("for studentLocalID and studentStateID", () => {
    it("should be validated successfully if there is more than one occurrence with different teachers and same classSectionID", async () => {
      const validTestMockData = [
        generateItem({
          studentLocalID: "1234",
          studentStateID: "1234",
          teacherID: "12",
          teacherEmail: "<EMAIL>",
          classSectionID: "someSectionID-K"
        }),
        generateItem({
          studentLocalID: "1234",
          studentStateID: "1234",
          teacherID: "13",
          teacherEmail: "<EMAIL>",
          classSectionID: "someSectionID-K"
        })
      ];

      const result = await new RosterImportItemsValidator(validTestMockData).validate();

      expect(result.success).toBe(true);
      expect(result.errors.studentLocalID).toBeFalsy();
    });
    it("should return error if there is more than one occurrence with different classSectionID", async () => {
      const invalidMockData = [
        generateItem({
          studentLocalID: "1234",
          studentStateID: "1234",
          teacherID: "12",
          teacherEmail: "<EMAIL>",
          classSectionID: "test"
        }),
        generateItem({
          studentLocalID: "1234",
          studentStateID: "1234",
          teacherID: "13",
          teacherEmail: "<EMAIL>",
          classSectionID: "test2"
        })
      ];

      const result = await new RosterImportItemsValidator(invalidMockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.studentLocalID).toContain(
        "More than one classSectionID is present for studentLocalID: 1234, found:\n" +
          "Class: First Last - someClassName (test)\n" +
          "Class: First Last - someClassName (test2)"
      );
    });
    it("should return error if the same classSectionID, TeacherID and studentLocalID was used in more than one row", async () => {
      const invalidMockData = [
        generateItem({
          studentLocalID: "10",
          studentStateID: "1234",
          teacherID: "12",
          teacherEmail: "<EMAIL>",
          classSectionID: "test"
        }),
        generateItem({
          studentLocalID: "10",
          studentStateID: "1234",
          teacherID: "12",
          teacherEmail: "<EMAIL>",
          classSectionID: "test"
        }),
        generateItem({
          studentLocalID: "10",
          studentStateID: "1234",
          teacherID: "12",
          teacherEmail: "<EMAIL>",
          classSectionID: "test"
        })
      ];

      const result = await new RosterImportItemsValidator(invalidMockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.studentLocalID).toContain(
        "The same Class Section ID, Teacher ID, Student Local ID was used in more than one row:\n" +
          "Class Section ID: test,\tTeacher ID: 12,\tStudent Local ID: 10\n" +
          "Please see student with:\n" +
          "\tFirst Name: someStudentFirstName\n" +
          "\tLast Name: someStudentLastName\n" +
          "\tLocalID: 10\n" +
          "\tStateID: 1234\n" +
          "\tClass: someClassName (test)\n" +
          "\tTeacher: Last, First,\n" +
          "Please see student with:\n" +
          "\tFirst Name: someStudentFirstName\n" +
          "\tLast Name: someStudentLastName\n" +
          "\tLocalID: 10\n" +
          "\tStateID: 1234\n" +
          "\tClass: someClassName (test)\n" +
          "\tTeacher: Last, First,\n" +
          "Please see student with:\n" +
          "\tFirst Name: someStudentFirstName\n" +
          "\tLast Name: someStudentLastName\n" +
          "\tLocalID: 10\n" +
          "\tStateID: 1234\n" +
          "\tClass: someClassName (test)\n" +
          "\tTeacher: Last, First"
      );
    });
  });
  describe("for teacherId", () => {
    afterEach(async () => {
      await Users.removeAsync({});
    });

    it("should return error if there is more than one teacher first name per teacherId", async () => {
      const invalidMockData = [generateItem({ teacherFirstName: "OtherFirst" })];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.teacherID).toContain(
        "More than one teacherFirstName, teacherLastName, teacherEmail is present for teacherID: 1000, found:\n" +
          "Teacher First Name: First,\tTeacher Last Name: Last,\tTeacher Email: <EMAIL>\n" +
          "Teacher First Name: OtherFirst,\tTeacher Last Name: Last,\tTeacher Email: <EMAIL>"
      );
    });

    it("should return error if there is more than one teacher last name per teacherId", async () => {
      const invalidMockData = [generateItem({ teacherLastName: "OtherLast" })];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.teacherID).toContain(
        "More than one teacherFirstName, teacherLastName, teacherEmail is present for teacherID: 1000, found:\n" +
          "Teacher First Name: First,	Teacher Last Name: Last,	Teacher Email: <EMAIL>\n" +
          "Teacher First Name: First,	Teacher Last Name: OtherLast,	Teacher Email: <EMAIL>"
      );
    });

    it("should return error if there is more than one teacher email name per teacherId", async () => {
      const invalidMockData = [generateItem({ teacherEmail: "<EMAIL>" })];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.teacherID).toContain(
        "More than one teacherFirstName, teacherLastName, teacherEmail is present for teacherID: 1000, found:\n" +
          "Teacher First Name: First,\tTeacher Last Name: Last,\tTeacher Email: <EMAIL>\n" +
          "Teacher First Name: First,\tTeacher Last Name: Last,\tTeacher Email: <EMAIL>"
      );
    });

    it("should not return error when matched email has different localId", async () => {
      const teacherEmail = "<EMAIL>";
      const lastName = "ExistingTeacherLastName";
      const firstName = "ExistingTeacherFirstName";
      const localId = 1000;
      await Users.insertAsync({
        _id: "testExistingTeacher",
        emails: [{ address: teacherEmail }],
        profile: {
          localId,
          name: {
            last: lastName,
            first: firstName
          },
          siteAccess: [{}]
        }
      });

      const mockData = [...validMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
    });

    it("should return error when matched teacher has localId from differentUser", async () => {
      const teacherEmail = "<EMAIL>";
      const otherTeacherEmail = "<EMAIL>";
      const lastName = "ExistingTeacherLastName";
      const otherLastName = "otherTeacherLast";
      const firstName = "otherFirstName";
      const otherFirstName = "otherTeacherFirst";
      const localId = "1000";
      const otherLocalId = "1001";
      const orgid = "12345";
      await Users.insertAsync([
        {
          _id: "testExistingTeacher",
          emails: [{ address: teacherEmail }],
          profile: {
            localId,
            orgid,
            name: {
              last: lastName,
              first: firstName
            },
            siteAccess: [{}]
          }
        },
        {
          _id: "otherTeacher",
          emails: [{ address: otherTeacherEmail }],
          profile: {
            localId: otherLocalId,
            orgid,
            name: {
              last: otherLastName,
              first: otherFirstName
            },
            siteAccess: [{}]
          }
        }
      ]);

      const mockData = [
        {
          data: {
            schoolID: "12345",
            districtID: "12345",
            districtName: "SunnySlope",
            springMathGrade: "K",
            schoolName: "ABC 12345",
            studentLocalID: "1234",
            studentStateID: "00000000",
            studentLastName: "studentLastName1",
            studentFirstName: "studentFirstName1",
            teacherLastName: "Last",
            teacherFirstName: "First",
            teacherID: "1001",
            teacherEmail: "<EMAIL>",
            classSectionID: "someSectionID-K",
            className: "someClassName-K"
          }
        }
      ];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.teacherID).toContain(
        `Teacher e-mail: ${teacherEmail} or teacher id: 1001 is already used by a different teacher: otherTeacherFirst otherTeacherLast (TeacherID: 1001, Email: <EMAIL>).\nYou can manage teachers' data under Manage Accounts tab.`
      );
    });
    it("should return error when teacher id varies in roster file", async () => {
      const invalidMockData = [generateItem({ teacherID: "1001" })];

      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.TeacherEmail).toContain(
        "More than one teacherID is present for TeacherEmail: <EMAIL>, found:\n" +
          "Teacher ID: 1000\n" +
          "Teacher ID: 1001"
      );
    });
  });
  describe("for classSectionID", () => {
    it("should be validated successfully if the same classSectionID is used in more than one school", async () => {
      const invalidMockData = [
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          schoolID: "firstSchoolID",
          schoolName: "firstSchool"
        }),
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-B",
          schoolID: "secondSchoolID",
          schoolName: "secondSchool"
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
      expect(result.errors.classSectionID).toBeFalsy();
    });

    it("should return error if the same classSectionID is used for more than one className across a school", async () => {
      const invalidMockData = [
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A"
        }),
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-B"
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.classSectionID).toContain(
        "The same classSectionID: someSectionID-3 is present for more than one className across a school: ABC 12345"
      );
    });

    it("should be validated successfully if only one teacherID is used for same className and classID", async () => {
      const validTestMockData = [
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherName-1",
          teacherEmail: "<EMAIL>"
        }),
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherName-1",
          teacherEmail: "<EMAIL>"
        })
      ];

      const mockData = [...validMockData, ...validTestMockData];
      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
      expect(result.errors.classSectionID).toBeFalsy();
    });

    it("should be validated successfully if there is more than one teacherID used for same className and classID in the same school", async () => {
      const invalidMockData = [
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherId-1",
          teacherEmail: "<EMAIL>"
        }),
        generateItem({
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherId-2",
          teacherEmail: "<EMAIL>"
        })
      ];

      const mockData = [...validMockData, ...invalidMockData];
      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
      expect(result.errors.classSectionID).toBeFalsy();
    });

    it("should be validated successfully if the same class section ID is used in two different schools", async () => {
      const validTestMockData = [
        generateItem({
          schoolID: "12345",
          schoolName: "ABC 12345",
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherId-1",
          teacherEmail: "<EMAIL>"
        }),
        generateItem({
          schoolID: "6789",
          schoolName: "XYZ 6789",
          classSectionID: "someSectionID-3",
          className: "someClassName-A",
          teacherID: "someTeacherId-2",
          teacherEmail: "<EMAIL>"
        })
      ];

      const mockData = [...validMockData, ...validTestMockData];
      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
      expect(result.errors.classSectionID).toBeFalsy();
    });
  });
  describe("for springMathGrade", () => {
    it("should not return any errors if elementary and highSchool grades are mixed within the same school", async () => {
      const invalidMockData = [
        generateItem({
          springMathGrade: "3",
          schoolID: "111",
          schoolName: "ABC 111"
        }),
        generateItem({
          springMathGrade: "4",
          schoolID: "111",
          schoolName: "ABC 111"
        }),
        generateItem({
          springMathGrade: "9",
          schoolID: "111",
          schoolName: "ABC 111"
        }),
        generateItem({
          springMathGrade: "11",
          schoolID: "111",
          schoolName: "ABC 111"
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(true);
    });
    it("should not return any errors if elementary and highSchool grades are mixed but in different schools", async () => {
      const invalidMockData = [
        generateItem({
          springMathGrade: "3",
          schoolID: "111",
          schoolName: "ABC 111",
          classSectionID: "someSection grade: 3"
        }),
        generateItem({
          springMathGrade: "4",
          schoolID: "111",
          schoolName: "ABC 111",
          classSectionID: "someSection grade: 4"
        }),
        generateItem({
          springMathGrade: "9",
          schoolID: "222",
          schoolName: "ABC 222",
          classSectionID: "someSection grade: 9"
        }),
        generateItem({
          springMathGrade: "11",
          schoolID: "222",
          schoolName: "ABC 222",
          classSectionID: "someSection grade: 11"
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();
      expect(result.success).toBe(true);
      expect(result.errors.gradeSeparation).toBeUndefined();
    });
    it("should return an error if all students in group are missing grade", async () => {
      const invalidMockData = [
        generateItem({
          springMathGrade: ""
        }),
        generateItem({
          springMathGrade: ""
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];

      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.springMathGrade[0]).toEqual(
        "All students for group:\n\tClass: someClassName (someSectionID)\n\tSchool: ABC 12345\nare missing SpringMathGrade."
      );
    });
    it("should return an error if students have invalid spring math grades", async () => {
      const invalidMockData = [
        generateItem({
          springMathGrade: "13",
          studentLastName: "studentLast4",
          studentFirstName: "studentFirst4",
          studentLocalID: "4",
          studentStateID: "4"
        }),
        generateItem({
          springMathGrade: "-",
          studentLastName: "studentLast5",
          studentFirstName: "studentFirst5",
          studentLocalID: "5",
          studentStateID: "5"
        }),
        generateItem({
          springMathGrade: "-1",
          studentLastName: "studentLast6",
          studentFirstName: "studentFirst6",
          studentLocalID: "6",
          studentStateID: "6"
        }),
        generateItem({
          springMathGrade: "00",
          studentLastName: "studentLast7",
          studentFirstName: "studentFirst7",
          studentLocalID: "7",
          studentStateID: "7"
        }),
        generateItem({
          springMathGrade: "003",
          studentLastName: "studentLast8",
          studentFirstName: "studentFirst8",
          studentLocalID: "8",
          studentStateID: "8"
        })
      ];
      const mockData = [...validMockData, ...invalidMockData];
      const result = await new RosterImportItemsValidator(mockData).validate();

      expect(result.success).toBe(false);
      expect(result.errors.springMathGrade).toEqual([
        "Student with:\n\tFirst Name: studentFirst4\n\tLast Name: studentLast4\n\tLocalID: 4\n\tStateID: 4\n\tClass: someClassName (someSectionID)\n\tTeacher: Last, First\nhas invalid SpringMathGrade - should be K, HS or 0 - 12.",
        "Student with:\n\tFirst Name: studentFirst5\n\tLast Name: studentLast5\n\tLocalID: 5\n\tStateID: 5\n\tClass: someClassName (someSectionID)\n\tTeacher: Last, First\nhas invalid SpringMathGrade - should be K, HS or 0 - 12.",
        "Student with:\n\tFirst Name: studentFirst6\n\tLast Name: studentLast6\n\tLocalID: 6\n\tStateID: 6\n\tClass: someClassName (someSectionID)\n\tTeacher: Last, First\nhas invalid SpringMathGrade - should be K, HS or 0 - 12.",
        "Student with:\n\tFirst Name: studentFirst7\n\tLast Name: studentLast7\n\tLocalID: 7\n\tStateID: 7\n\tClass: someClassName (someSectionID)\n\tTeacher: Last, First\nhas invalid SpringMathGrade - should be K, HS or 0 - 12.",
        "Student with:\n\tFirst Name: studentFirst8\n\tLast Name: studentLast8\n\tLocalID: 8\n\tStateID: 8\n\tClass: someClassName (someSectionID)\n\tTeacher: Last, First\nhas invalid SpringMathGrade - should be K, HS or 0 - 12."
      ]);
    });
  });
});

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2018),
  idValidation: { regex: /.*/, description: "" }
}));

describe("validateStudentGroupsInUploadItems", () => {
  // eslint-disable-next-line global-require,import/newline-after-import
  const schoolYear = 2018;
  const orgid = "someOrgId";
  afterAll(() => {
    jest.restoreAllMocks();
  });
  describe("for missing student groups and for student groups attempting to change name or grade", () => {
    const districtNumber = "10";
    const siteLocalId = "1";
    const siteName = "Sunny Slope Elementary";
    const sectionId = "3020-03";
    const grade = "02";
    const name = "Some Name (3020-03)";
    const testSite = {
      orgid,
      stateInformation: {
        districtNumber,
        schoolNumber: siteLocalId
      },
      name: siteName
    };

    beforeAll(async () => {
      const siteId = await Sites.insertAsync(testSite);
      const studentGroup = {
        schoolYear,
        orgid,
        isActive: true,
        siteId,
        name,
        grade,
        sectionId
      };
      const sgId = await StudentGroups.insertAsync(studentGroup);
      // inserting fake enrollment to a created student group
      await StudentGroupEnrollments.insertAsync({ studentGroupId: sgId, isActive: true, schoolYear });
    });
    afterAll(async () => {
      await Sites.removeAsync({});
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
    });

    it("should return an error if new items don't contain all currently active groups", async () => {
      const unrelatedItems = [generateNormalizedItem({ schoolID: siteLocalId, districtID: districtNumber })];

      const { success, errors } = await validateStudentGroupsInUploadItems(unrelatedItems, orgid);

      expect(success).toBe(false);
      expect(errors.missingStudentGroups[0]).toEqual({
        classSectionID: sectionId,
        schoolID: siteLocalId,
        districtID: districtNumber,
        springMathGrade: grade,
        className: name
      });
      expect(errors.missingStudentGroups).toHaveLength(1);
    });
  });
  describe("for new organizations", () => {
    it("should return success", async () => {
      const { success, errors } = await validateStudentGroupsInUploadItems([generateItem()], orgid);

      expect(success).toBe(true);
      expect(errors).toEqual({});
    });
  });
});

describe("getStrippedClassName", () => {
  describe("should strip group name from classSectionID", () => {
    const groupNameToExpectedStrippedNameList = [
      ["2nd Grade AM Attendance Section 111 Period 8(A) (11161)", "2nd Grade AM Attendance Section 111 Period 8(A)"],
      ["Math06 (1)", "Math06"],
      ["Intervention HS (8)", "Intervention HS"],
      ["RtI - Young (14)", "RtI - Young"],
      ["Mathematics 7 Section 7 Period 4(A) (10705)", "Mathematics 7 Section 7 Period 4(A)"]
    ];
    groupNameToExpectedStrippedNameList.forEach(testData => {
      it(`for ${testData[0]} should return ${testData[1]}`, () => {
        expect(getStrippedClassName(testData[0])).toEqual(testData[1]);
      });
    });
  });
});

describe("validateDistrictName", () => {
  const orgid = "test";
  beforeAll(async () => {
    await Organizations.insertAsync({
      _id: orgid,
      name: "DistrictName"
    });
  });
  afterAll(async () => {
    await Organizations.removeAsync({});
  });
  describe("should pass validation with leading and trailing whitespaces", () => {
    const districtNames = ["  DistrictName  ", "  DistrictName", "DistrictName  "];
    districtNames.forEach(districtName => {
      it(`for "${districtName}" should return true`, async () => {
        expect(await validateDistrictName([{ data: { districtName } }], orgid)).toEqual({
          errors: [],
          failedRows: [],
          success: true
        });
      });
    });
  });
});
