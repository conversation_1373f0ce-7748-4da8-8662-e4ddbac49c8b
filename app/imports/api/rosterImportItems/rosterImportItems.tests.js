import { assert } from "chai";
import { repeat } from "lodash";

import { RosterImportItems } from "./rosterImportItems";
import { getRosterImportItem } from "../../test-helpers/data/rosterImportItems";

describe("RosterImportItems", () => {
  it("should pass schema validation methods if data is in expected format", () => {
    const rosterItem = getRosterImportItem();
    assert.isUndefined(RosterImportItems.validate(rosterItem));
    assert.isTrue(RosterImportItems.isValid(rosterItem));
  });

  it("should fail if one of the optional values is in incorrect format", () => {
    const rosterItem = getRosterImportItem({ _id: 1234 });

    const result = RosterImportItems.isValid(rosterItem);
    expect(() => RosterImportItems.validate(rosterItem)).toThrow(/ID must be of type String/);
    assert.isFalse(result);
  });

  it("should fail if one of the required values is undefined", () => {
    const rosterItem = getRosterImportItem();
    delete rosterItem.data.teacherID;

    const result = RosterImportItems.isValid(rosterItem);

    expect(() => RosterImportItems.validate(rosterItem)).toThrow(/Teacher ID is required/);
    assert.isFalse(result);
  });

  it("should fail if one of the required values is an empty string", () => {
    const rosterItem = getRosterImportItem({ data: { districtName: "" } });

    const result = RosterImportItems.isValid(rosterItem);

    expect(() => RosterImportItems.validate(rosterItem)).toThrow(/District name must be at least 1 characters/);
    assert.isFalse(result);
  });

  describe("custom format validation", () => {
    [
      getRosterImportItem({ data: { schoolID: repeat("ab", 26) } }),
      getRosterImportItem({ data: { schoolID: repeat("a9", 26) } }),
      getRosterImportItem({ data: { schoolID: repeat("1-2", 17) } })
    ].forEach(rosterItem => {
      it(`should throw error message when school ID is of invalid format ${rosterItem.data.schoolID}`, () => {
        const isFuliValid = RosterImportItems.isValid(rosterItem);

        expect(isFuliValid).toBeFalsy();
        expect(() => RosterImportItems.validate(rosterItem)).toThrow(/School ID cannot exceed 50 characters/);
      });
    });

    [
      getRosterImportItem({ data: { districtID: repeat("ww", 26) } }),
      getRosterImportItem({ data: { districtID: repeat("b1", 26) } }),
      getRosterImportItem({ data: { districtID: repeat("+", 51) } })
    ].forEach(rosterItem => {
      it("should throw error message when district ID is of invalid format", () => {
        const isFuliValid = RosterImportItems.isValid(rosterItem);

        expect(isFuliValid).toBeFalsy();
        expect(() => RosterImportItems.validate(rosterItem)).toThrow(/District ID cannot exceed 50 characters/);
      });
    });

    [
      getRosterImportItem({ data: { teacherEmail: "teacher.site.com" } }),
      getRosterImportItem({ data: { teacherEmail: "<EMAIL>" } }),
      getRosterImportItem({ data: { teacherEmail: "not@valid.a" } }),
      getRosterImportItem({ data: { teacherEmail: "@wrong.ab" } }),
      getRosterImportItem({ data: { teacherEmail: "()@bad.com" } }),
      getRosterImportItem({ data: { teacherEmail: "l@.us" } }),
      getRosterImportItem({ data: { teacherEmail: "a" } })
    ].forEach(rosterItem => {
      it(`should throw error message when teacher email is of invalid format ${rosterItem.data.teacherEmail}`, () => {
        const isFuliValid = RosterImportItems.isValid(rosterItem);

        expect(isFuliValid).toBeFalsy();
        expect(() => RosterImportItems.validate(rosterItem)).toThrow(/Teacher email must be a valid email address/);
      });
    });

    [
      getRosterImportItem({ data: { studentLocalID: repeat("1020156163��_", 4) } }),
      getRosterImportItem({ data: { studentLocalID: repeat("a-b-c", 11) } }),
      getRosterImportItem({ data: { studentLocalID: repeat("7.9E+12", 8) } })
    ].forEach(rosterItem => {
      it(`should throw error message when student local ID is of invalid format ${rosterItem.data.studentLocalID}`, () => {
        const isFuliValid = RosterImportItems.isValid(rosterItem);

        expect(isFuliValid).toBeFalsy();
        expect(() => RosterImportItems.validate(rosterItem)).toThrow(/Student local ID cannot exceed 50 characters/);
      });
    });

    [
      getRosterImportItem({ data: { studentStateID: repeat("1020156163��_", 4) } }),
      getRosterImportItem({ data: { studentStateID: repeat("a-b-c", 11) } }),
      getRosterImportItem({ data: { studentStateID: repeat("7.9E+12", 8) } })
    ].forEach(rosterItem => {
      it(`should throw error message when student state ID is of invalid format ${rosterItem.data.studentStateID}`, () => {
        const isFuliValid = RosterImportItems.isValid(rosterItem);

        expect(isFuliValid).toBeFalsy();
        expect(() => RosterImportItems.validate(rosterItem)).toThrow(/Student state ID cannot exceed 50 characters/);
      });
    });
  });
});
