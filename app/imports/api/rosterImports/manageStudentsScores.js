import { Random } from "meteor/random";
import groupBy from "lodash/groupBy";

import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { Students } from "../students/students";
import { createCurrentBenchmarkWindowForSite, getCurrentBenchmarkWindowWithSiteId } from "../benchmarkWindows/methods";
import { getCurrentSchoolYear, getMeteorUser, getMeteorUserId } from "../utilities/utilities";
import { endCurrentIndividualIntervention } from "../utilities/server/individualRuleProcessing";
import { getTimestampInfo } from "../helpers/getTimestampInfo";

export async function manageStudentsScores(studentIdsByStudentGroupIds, orgid) {
  const schoolYear = await getCurrentSchoolYear(getMeteorUser(), orgid);
  // eslint-disable-next-line no-restricted-syntax
  for await (const [previousGroupId, studentIds] of Object.entries(studentIdsByStudentGroupIds.classwide)) {
    const { previousGroup, groupedNewEnrollments } = await getStudentMobilityContext({
      previousGroupId,
      studentIds,
      schoolYear
    });
    await removeStudentsFromActiveScreening(previousGroup, studentIds);
    const lastModified = getTimestampInfo(getMeteorUserId(), orgid, "manageStudentScores");
    const previousGroupCurrentClasswideSkillId = previousGroup.currentClasswideSkill.assessmentResultId;
    await AssessmentResults.updateAsync(
      { _id: previousGroupCurrentClasswideSkillId },
      { $pull: { scores: { studentId: { $in: studentIds } } }, $set: { lastModified } }
    );
    await addStudentsToCurrentActivitiesInGroups({
      groupedNewEnrollments,
      previousGroup,
      schoolYear
    });
  }

  // eslint-disable-next-line no-restricted-syntax
  for await (const [previousGroupId, studentIds] of Object.entries(studentIdsByStudentGroupIds.individual)) {
    const { previousGroup, groupedNewEnrollments } = await getStudentMobilityContext({
      previousGroupId,
      studentIds,
      schoolYear
    });
    await removeStudentsFromActiveScreening(previousGroup, studentIds);
    const individualAssessmentsOfMovedStudents = await deactivateIndividualAssessmentsOfMovedStudents({
      orgid,
      previousGroup,
      studentIds
    });
    await addStudentsToCurrentActivitiesInGroups({
      groupedNewEnrollments,
      previousGroup,
      individualAssessments: individualAssessmentsOfMovedStudents,
      schoolYear
    });
  }

  // eslint-disable-next-line no-restricted-syntax
  for await (const [previousGroupId, studentIds] of Object.entries(studentIdsByStudentGroupIds.other)) {
    const { previousGroup, groupedNewEnrollments } = await getStudentMobilityContext({
      previousGroupId,
      studentIds,
      schoolYear
    });
    await removeStudentsFromActiveScreening(previousGroup, studentIds);
    const individualAssessments = await AssessmentResults.find({
      type: "individual",
      status: "OPEN",
      studentId: { $in: studentIds },
      schoolYear
    }).fetchAsync();
    await addStudentsToCurrentActivitiesInGroups({
      groupedNewEnrollments,
      previousGroup,
      individualAssessments,
      schoolYear
    });
  }
}

async function removeStudentsFromActiveScreening(previousGroup, studentIds) {
  if (Object.keys(previousGroup).length === 0) return;
  const previousGroupActiveAssessments = previousGroup.currentAssessmentResultIds || [];
  const currentAssessmentsOfPreviousGroup = await AssessmentResults.find({
    _id: { $in: previousGroupActiveAssessments }
  }).fetchAsync();
  const activeScreening = currentAssessmentsOfPreviousGroup.filter(
    assessment => assessment.type === "benchmark" && assessment.status === "OPEN"
  );
  if (activeScreening.length === 0) return;
  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    currentAssessmentsOfPreviousGroup.orgid,
    "removeStudentsFromActiveScreening"
  );
  await AssessmentResults.updateAsync(
    { _id: { $in: activeScreening.map(a => a._id) } },
    { $pull: { scores: { studentId: { $in: studentIds } } }, $set: { lastModified } },
    { multi: true }
  );
}

async function deactivateIndividualAssessmentsOfMovedStudents({ orgid, previousGroup, studentIds }) {
  const hasPreviousGroup = previousGroup && previousGroup._id;
  const individualAssessmentsOfMovedStudents = await AssessmentResults.find({
    ...(hasPreviousGroup && { _id: { $in: previousGroup.currentAssessmentResultIds } }),
    type: "individual",
    status: "OPEN",
    studentId: { $in: studentIds },
    schoolYear: await getCurrentSchoolYear(getMeteorUser(), orgid)
  }).fetchAsync();
  const idsOfPreviousIndividualAssessments = individualAssessmentsOfMovedStudents.map(ass => ass._id);
  if (hasPreviousGroup) {
    const lastModified = await getTimestampInfo(
      getMeteorUserId(),
      orgid,
      "deactivateIndividualAssessmentsOfMovedStudents"
    );
    await StudentGroups.updateAsync(
      { _id: previousGroup._id },
      {
        $pull: {
          currentAssessmentResultIds: { $in: idsOfPreviousIndividualAssessments }
        },
        $set: { lastModified }
      }
    );
  }
  return individualAssessmentsOfMovedStudents;
}

async function addInterventionClosedPropToEnrollments({ newStudentsIds, newGroupId, schoolYear }) {
  await StudentGroupEnrollments.updateAsync(
    {
      studentId: { $in: newStudentsIds },
      isActive: true,
      studentGroupId: newGroupId,
      schoolYear
    },
    { $set: { wasIndividualInterventionClosed: true } },
    { multi: true }
  );
}

async function addCurrentSkillForStudent(assessmentResult) {
  const currentSkill = {
    benchmarkAssessmentId: assessmentResult.individualSkills.benchmarkAssessmentId,
    benchmarkAssessmentName: assessmentResult.individualSkills.benchmarkAssessmentName,
    benchmarkAssessmentTargets: assessmentResult.individualSkills.benchmarkAssessmentTargets,
    assessmentId: assessmentResult.individualSkills.assessmentId,
    assessmentName: assessmentResult.individualSkills.assessmentName,
    assessmentTargets: assessmentResult.individualSkills.assessmentTargets,
    interventions: assessmentResult.individualSkills.interventions,
    assessmentResultId: assessmentResult.individualSkills.assessmentResultId,
    whenStarted: assessmentResult.created,
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
    message: {
      messageCode: "55",
      dismissed: true
    }
  };
  const lastModified = await getTimestampInfo(getMeteorUserId(), assessmentResult.orgid, "addCurrentSkillForStudent");
  await Students.updateAsync(
    { _id: assessmentResult.studentId, currentSkill: { $exists: false } },
    { $set: { currentSkill, lastModified } }
  );
}

async function addStudentsToCurrentActivitiesInGroups({
  groupedNewEnrollments,
  previousGroup,
  individualAssessments = null,
  schoolYear
}) {
  // eslint-disable-next-line no-restricted-syntax
  for await (const [newGroupId, studentEnrollments] of Object.entries(groupedNewEnrollments)) {
    const newStudentsIds = studentEnrollments.map(enrollment => enrollment.studentId);
    const currentGroup = await StudentGroups.findOneAsync({ _id: newGroupId });
    const individualAssessmentsInSameGrade = [];
    const individualAssessmentsInDifferentGrade = [];
    if (individualAssessments && individualAssessments.length) {
      individualAssessments.forEach(individualAssessment => {
        if (individualAssessment.grade === currentGroup.grade) {
          individualAssessmentsInSameGrade.push(individualAssessment);
        } else {
          individualAssessmentsInDifferentGrade.push(individualAssessment);
        }
      });
    }
    if (individualAssessmentsInSameGrade.length) {
      await addStudentAssessmentsToCurrentGroup(individualAssessmentsInSameGrade, newStudentsIds, currentGroup);
      // eslint-disable-next-line no-restricted-syntax
      for await (const individualAssessment of individualAssessmentsInSameGrade) {
        await addCurrentSkillForStudent(individualAssessment);
      }
    }
    if (individualAssessmentsInDifferentGrade.length) {
      const newStudentsIdsInDifferentGrade = [];
      // eslint-disable-next-line no-restricted-syntax
      for await (const { studentId } of individualAssessmentsInDifferentGrade) {
        newStudentsIdsInDifferentGrade.push(studentId);
        await endCurrentIndividualIntervention({ studentId });
      }
      const lastModified = getTimestampInfo(
        getMeteorUserId(),
        currentGroup?.orgid,
        "addInterventionClosedPropToEnrollments"
      );
      await addInterventionClosedPropToEnrollments({
        newStudentsIds: newStudentsIdsInDifferentGrade,
        newGroupId,
        schoolYear,
        lastModified
      });
    }
    await addStudentsToCurrentClasswideActivities({
      currentGroup,
      previousGroup,
      newStudentsIds
    });
  }
}

async function getStudentMobilityContext({ previousGroupId, studentIds, schoolYear }) {
  const previousGroup =
    previousGroupId === "unenrolled" ? {} : await StudentGroups.findOneAsync({ _id: previousGroupId });
  const currentStudentEnrollments = await getActiveStudentEnrollmentsByStudentIds(studentIds, schoolYear);
  const groupedNewEnrollments = groupBy(currentStudentEnrollments, "studentGroupId");
  return { previousGroup, groupedNewEnrollments };
}

function getAssessmentScores({ assessmentIds, studentIds, status = "STARTED", orgid, siteId }) {
  const scores = [];
  assessmentIds.forEach(assessmentId => {
    studentIds.forEach(studentId => {
      scores.push({
        _id: Random.id(),
        assessmentId,
        orgid,
        siteId,
        status,
        studentId
      });
    });
  });
  return scores;
}

async function getActiveScreeningSession({ studentGroupId, studentIds, benchmarkPeriodId, schoolYear }) {
  return AssessmentResults.findOneAsync({
    studentGroupId,
    type: "benchmark",
    status: "OPEN",
    schoolYear,
    benchmarkPeriodId,
    "scores.studentId": { $nin: studentIds }
  });
}

function isScreeningSessionActive(activeScreeningSession, studentGroup) {
  return activeScreeningSession && studentGroup.currentAssessmentResultIds.includes(activeScreeningSession._id);
}

async function addStudentsToActiveScreeningSession({ screeningId, studentIds, orgid, siteId }) {
  const screeningAssessment = await AssessmentResults.findOneAsync({ _id: screeningId });
  const draftScoresForNewStudents = getAssessmentScores({
    assessmentIds: screeningAssessment.assessmentIds,
    studentIds,
    orgid,
    siteId
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "addStudentsToActiveScreeningSession");
  await AssessmentResults.updateAsync(
    { _id: screeningId },
    { $push: { scores: { $each: draftScoresForNewStudents } }, $set: { lastModified } }
  );
}

function isInClasswideIntervention(studentGroup) {
  return studentGroup.currentClasswideSkill && studentGroup.currentClasswideSkill.assessmentResultId;
}

async function addStudentsToActiveClasswideIntervention({ orgid, siteId, studentIds, studentGroup }) {
  const currentClasswideSkill = await AssessmentResults.findOneAsync({
    _id: studentGroup.currentClasswideSkill.assessmentResultId,
    "scores.studentId": { $nin: studentIds }
  });
  if (!currentClasswideSkill) return;
  const currentClasswideSkillAssessmentIds = currentClasswideSkill.assessmentIds;
  const draftScoresForNewStudents = getAssessmentScores({
    assessmentIds: currentClasswideSkillAssessmentIds,
    studentIds,
    orgid,
    siteId
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "addStudentsToActiveClasswideIntervention");
  await AssessmentResults.updateAsync(
    { _id: currentClasswideSkill._id },
    { $push: { scores: { $each: draftScoresForNewStudents } }, $set: { lastModified } }
  );
}

async function getLastCompletedScreening({ studentGroupId, schoolYear }) {
  return AssessmentResults.findOneAsync(
    {
      studentGroupId,
      type: "benchmark",
      schoolYear,
      status: "COMPLETED"
    },
    { sort: { $natural: -1 } }
  );
}

export async function addStudentsToCurrentClasswideActivities({ currentGroup, previousGroup = null, newStudentsIds }) {
  const { siteId, orgid } = currentGroup;
  const currentBenchmarkWindow = await getCurrentBenchmarkWindowWithSiteId({
    orgid,
    siteId
  });
  let benchmarkPeriodId;
  let schoolYear;
  if (!currentBenchmarkWindow) {
    ({ benchmarkPeriodId, schoolYear } = await createCurrentBenchmarkWindowForSite({
      orgid,
      siteId,
      userId: "group_enrollments_processor"
    }));
  } else {
    ({ benchmarkPeriodId, schoolYear } = currentBenchmarkWindow);
  }
  const newGroupActiveScreeningSession = await getActiveScreeningSession({
    studentGroupId: currentGroup._id,
    studentIds: newStudentsIds,
    benchmarkPeriodId,
    schoolYear
  });

  if (isScreeningSessionActive(newGroupActiveScreeningSession, currentGroup)) {
    await addStudentsToActiveScreeningSession({
      screeningId: newGroupActiveScreeningSession._id,
      studentIds: newStudentsIds,
      orgid,
      siteId
    });
  }

  if (isInClasswideIntervention(currentGroup)) {
    await addStudentsToActiveClasswideIntervention({
      orgid,
      siteId,
      studentIds: newStudentsIds,
      studentGroup: currentGroup
    });
  }
  await moveIndividualInterventionEligibility({
    currentGroup,
    previousGroup,
    newStudentsIds,
    schoolYear
  });
}

export async function removeIndividualInterventionsFromActiveAssessments(removedStudents) {
  const activeIndividualAssessments = await AssessmentResults.find(
    {
      studentId: {
        $in: removedStudents.map(s => s.studentId)
      },
      type: "individual",
      status: "OPEN"
    },
    { fields: { studentGroupId: 1 } }
  ).fetchAsync();

  const assessmentIdsByGroupId = activeIndividualAssessments.reduce((items, item) => {
    const result = { ...items };
    (result[item.studentGroupId] || (result[item.studentGroupId] = [])).push(item._id);
    return result;
  }, {});
  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    activeIndividualAssessments[0]?.orgid,
    "removeIndividualInterventionsFromActiveAssessments"
  );
  // eslint-disable-next-line no-restricted-syntax
  for await (const groupId of Object.keys(assessmentIdsByGroupId)) {
    const assessmentIds = assessmentIdsByGroupId[groupId];
    await StudentGroups.updateAsync(
      { _id: groupId },
      { $pull: { currentAssessmentResultIds: { $in: assessmentIds } }, $set: { lastModified } }
    );
  }
}

async function moveIndividualInterventionEligibility({
  currentGroup,
  previousGroup = null,
  newStudentsIds,
  schoolYear
}) {
  if (!previousGroup || !previousGroup.individualInterventionQueue) return;

  const idsOfNewStudentsWithEligibility = previousGroup.individualInterventionQueue.filter(queueItem =>
    newStudentsIds.includes(queueItem)
  );
  if (idsOfNewStudentsWithEligibility.length) {
    const lastModified = await getTimestampInfo(
      getMeteorUserId(),
      currentGroup?.orgid,
      "moveIndividualInterventionEligibility"
    );
    await StudentGroups.updateAsync(
      { _id: previousGroup._id },
      {
        $pull: {
          individualInterventionQueue: { $in: idsOfNewStudentsWithEligibility }
        },
        $set: { lastModified }
      }
    );

    const currentGroupLatestScreeningResult = await getLastCompletedScreening({
      studentGroupId: currentGroup._id,
      schoolYear
    });

    const previousGroupLatestScreeningResult = await getLastCompletedScreening({
      studentGroupId: currentGroup._id,
      schoolYear
    });

    if (
      !currentGroupLatestScreeningResult ||
      !previousGroupLatestScreeningResult ||
      currentGroupLatestScreeningResult.benchmarkPeriodId !== previousGroupLatestScreeningResult.benchmarkPeriodId ||
      currentGroup.grade !== previousGroup.grade
    )
      return;

    await StudentGroups.updateAsync(
      { _id: currentGroup._id },
      {
        $addToSet: {
          individualInterventionQueue: {
            $each: idsOfNewStudentsWithEligibility
          }
        },
        $set: { lastModified }
      }
    );
  }
}

async function getActiveStudentEnrollmentsByStudentIds(studentIds, schoolYear) {
  return StudentGroupEnrollments.find({
    studentId: { $in: studentIds },
    isActive: true,
    schoolYear
  }).fetchAsync();
}

export async function removeStudentsFromActiveAssessments(removedStudents) {
  const studentGroupIds = removedStudents.map(s => s.studentGroupId);
  const studentIds = removedStudents.map(s => s.studentId);
  const studentGroups = await StudentGroups.find(
    { _id: { $in: studentGroupIds } },
    { fields: { currentAssessmentResultIds: 1 } }
  ).fetchAsync();
  const assessmentResultIds = [];
  studentGroups.forEach(group => {
    if (group.currentAssessmentResultIds) {
      assessmentResultIds.push(...group.currentAssessmentResultIds);
    }
  });
  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    studentGroups[0]?.orgid,
    "removeStudentsFromActiveAssessments"
  );
  await AssessmentResults.updateAsync(
    {
      _id: { $in: assessmentResultIds },
      type: { $in: ["classwide", "benchmark"] },
      status: "OPEN"
    },
    { $pull: { scores: { studentId: { $in: studentIds } } }, $set: { lastModified } },
    { multi: true }
  );
}

async function addStudentAssessmentsToCurrentGroup(individualAssessments, newStudentsIds, currentGroup) {
  const assessmentsIdsForNewGroup = individualAssessments
    .filter(ass => newStudentsIds.includes(ass.studentId))
    .map(ass => ass._id);
  const lastModified = getTimestampInfo(getMeteorUserId(), currentGroup?.orgid, "addCurrentSkillForStudent");
  await AssessmentResults.updateAsync(
    { _id: { $in: assessmentsIdsForNewGroup } },
    { $set: { studentGroupId: currentGroup._id, lastModified } },
    { multi: true }
  );
  await StudentGroups.updateAsync(
    { _id: currentGroup._id },
    {
      $push: {
        currentAssessmentResultIds: { $each: assessmentsIdsForNewGroup }
      },
      $set: { lastModified }
    }
  );
}
