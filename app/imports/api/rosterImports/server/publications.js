import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import * as auth from "../../authorization/server/methods";
import { isUserLoggedOut, ninjalog } from "../../utilities/utilities";
import { RosterImports } from "../rosterImports";

Meteor.publish("RosterImports:lastImport", async function rosterImportsPub(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);

  if (
    !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
      userId: this.userId,
      orgid
    }))
  ) {
    ninjalog.log({
      msg: "Publication Access Denied: RosterImport:lastImport"
    });
    return this.ready();
  }
  return RosterImports.find(
    { orgid },
    {
      sort: { "started.date": -1 },
      limit: 1
    }
  );
});
