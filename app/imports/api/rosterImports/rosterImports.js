import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const RosterImports = new Mongo.Collection("RosterImports");

RosterImports.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  status: { type: String },
  orgid: { type: String },
  itemCount: { type: Number },
  started: { type: ByDateOn },
  finished: { type: ByDateOn, optional: true },
  students: { type: Object },
  "students.added": { type: Number },
  "students.updated": { type: Number },
  "students.removed": { type: Number },
  teachers: { type: Object },
  "teachers.added": { type: Number },
  "teachers.updated": { type: Number },
  "teachers.removed": { type: Number },
  sites: { type: Object },
  "sites.added": { type: Number },
  "sites.updated": { type: Number },
  "sites.removed": { type: Number },
  studentGroups: { type: Object },
  "studentGroups.added": { type: Number },
  "studentGroups.updated": { type: Number },
  "studentGroups.removed": { type: Number },
  source: { type: String },
  error: { type: Object, optional: true },
  studentGroupErrors: { type: Object, optional: true },
  uniqueEmailErrors: { type: Array, optional: true },
  "uniqueEmailErrors.$": { type: Object, optional: true },
  districtErrors: { type: Array, optional: true },
  "districtErrors.$": { type: Object, optional: true }
});

RosterImports.validate = rosterImports => {
  RosterImports.schema.validate(rosterImports);
};
RosterImports.isValid = rosterImports => RosterImports.schema.namedContext("testContext").validate(rosterImports);
