import { Sites } from "../sites/sites";
import { getNormalizedId } from "../rosterImportItems/normalizeRosterImportItems";
import { SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP } from "../constants";

const updateSchoolNumber = async ({
  siteId,
  existingSchoolNumber,
  incomingSchoolNumber,
  shouldUpdateDocuments,
  matchingIncomingSchoolNumberByExistingSchoolNumber
}) => {
  if (shouldUpdateDocuments) {
    await Sites.updateAsync(
      { _id: siteId },
      {
        $set: {
          "stateInformation.schoolNumber": incomingSchoolNumber,
          "stateInformation.localSchoolNumber": incomingSchoolNumber
        }
      }
    );
  }
  matchingIncomingSchoolNumberByExistingSchoolNumber[existingSchoolNumber] = incomingSchoolNumber;
};

/*
1. Schools in production that have matching school numbers are not in current school year therefore importing data shouldn't cause issues
2. Cases when we are sure update schools to new non normalized school numbers
3. Cases when we are not sure are not updated and in case of csv new schools will get created for not matched ones and for auto rostering merge data modal will show up
 */

export function prepareSitesWithPotentialNormalization(
  sitesInOrg,
  incomingSchoolNumbers,
  shouldUpdateDocuments = true
) {
  if (!sitesInOrg.length) {
    return {};
  }

  const matchingIncomingSchoolNumberByExistingSchoolNumber = {};

  const schoolNumbersToCheckWithoutNormalization = sitesInOrg
    .filter(s => s.lastModified.on > SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP)
    .map(s => s.stateInformation.schoolNumber);

  sitesInOrg.forEach(site => {
    const { schoolNumber } = site.stateInformation;
    const siteId = site._id;

    if (schoolNumbersToCheckWithoutNormalization.includes(schoolNumber)) {
      const matchingIncomingSchoolNumber = incomingSchoolNumbers.find(s => s === schoolNumber);
      if (matchingIncomingSchoolNumber) {
        updateSchoolNumber({
          siteId,
          existingSchoolNumber: schoolNumber,
          incomingSchoolNumber: matchingIncomingSchoolNumber,
          shouldUpdateDocuments,
          matchingIncomingSchoolNumberByExistingSchoolNumber
        });
      }
    } else {
      const matchingNormalizedIncomingSchoolNumbers = incomingSchoolNumbers.filter(
        s => getNormalizedId(s) === schoolNumber && !schoolNumbersToCheckWithoutNormalization.includes(s)
      );
      if (matchingNormalizedIncomingSchoolNumbers.length === 1) {
        updateSchoolNumber({
          siteId,
          existingSchoolNumber: schoolNumber,
          incomingSchoolNumber: matchingNormalizedIncomingSchoolNumbers[0],
          shouldUpdateDocuments,
          matchingIncomingSchoolNumberByExistingSchoolNumber
        });
      }
    }
  });

  return matchingIncomingSchoolNumberByExistingSchoolNumber;
}

export async function insertSite({
  orgid,
  schoolYear,
  districtId,
  districtName,
  schoolNumber,
  name,
  grades = [],
  isHighSchool = false,
  byDateOn,
  rosterImportId
}) {
  return Sites.insertAsync({
    orgid,
    schoolYear,
    stateInformation: {
      districtNumber: districtId,
      districtName,
      schoolNumber,
      localSchoolNumber: schoolNumber
    },
    name,
    grades: Array.from(grades),
    created: byDateOn,
    lastModified: byDateOn,
    isVisible: true,
    rosterImportId,
    isHighSchool
  });
}
