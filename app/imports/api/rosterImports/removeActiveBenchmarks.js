import flatten from "lodash/flatten";
import { StudentGroups } from "../studentGroups/studentGroups.js";
import { AssessmentResults } from "../assessmentResults/assessmentResults.js";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getMeteorUserId } from "../utilities/utilities";

export async function removeActiveBenchmarks(studentGroupIds) {
  const studentGroups = await StudentGroups.find(
    { _id: { $in: studentGroupIds } },
    { fields: { currentAssessmentResultIds: 1 } }
  ).fetchAsync();
  const activeAssessmentResultIds = flatten(studentGroups.map(sg => sg.currentAssessmentResultIds));
  if (activeAssessmentResultIds.length) {
    const activeBenchmarkAssessmentResultIds = (
      await AssessmentResults.find(
        {
          _id: { $in: activeAssessmentResultIds },
          type: "benchmark",
          status: "OPEN"
        },
        { fields: { _id: 1 } }
      ).fetchAsync()
    ).map(ar => ar._id);
    if (activeBenchmarkAssessmentResultIds.length) {
      await AssessmentResults.removeAsync({
        _id: { $in: activeBenchmarkAssessmentResultIds }
      });
      const lastModified = await getTimestampInfo(this?.userId || getMeteorUserId(), null, "removeActiveBenchmarks");
      await StudentGroups.updateAsync(
        { _id: { $in: studentGroupIds } },
        {
          $pull: {
            currentAssessmentResultIds: {
              $in: activeBenchmarkAssessmentResultIds
            }
          },
          $set: { lastModified }
        },
        { multi: true }
      );
    }
  }
}
