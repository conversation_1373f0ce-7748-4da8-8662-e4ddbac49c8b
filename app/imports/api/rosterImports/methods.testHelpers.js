import { Organizations } from "../organizations/organizations";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Students } from "../students/students";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Sites } from "../sites/sites";
import { Users } from "../users/users";
import { getCurrentSchoolYear } from "../utilities/utilities";

const districtName = "Anytown ISD";
const districtNumber = "138";

export async function insertTestStudentGroup({
  _id = undefined,
  orgid,
  siteId,
  teacherId,
  name,
  grade = "02",
  sectionId = "2",
  isActive = true
}) {
  return StudentGroups.insertAsync({
    _id,
    orgid,
    type: "CLASS",
    schoolYear: await getCurrentSchoolYear(),
    isActive,
    siteId,
    ownerIds: teacherId ? [teacherId] : [],
    name,
    grade,
    sectionId
  });
}

export async function insertTestStudent({
  _id = undefined,
  orgid,
  birthDate = new Date("2005-01-02"),
  firstName,
  lastName,
  localId,
  stateId,
  grade = "02",
  ethnicity = "White",
  middleName = "",
  gender = "M"
}) {
  return Students.insertAsync({
    _id,
    orgid,
    schoolYear: await getCurrentSchoolYear(),
    districtNumber: "138",
    created: {
      by: "file_upload",
      on: 1512399938206.0,
      date: new Date("2017-12-04T15:05:38.206+0000")
    },
    lastModified: {
      by: "file_upload",
      on: 1512399938206.0,
      date: new Date("2017-12-04T15:05:38.206+0000")
    },
    rosterImportId: "He2JRnd7km3bJ9Tfe",
    grade,
    demographic: {
      birthDate,
      ethnicity,
      gender,
      gt: "",
      sped: "",
      ell: "",
      title: "",
      birthDateTimeStamp: 123456778900.0
    },
    identity: {
      name: {
        firstName,
        lastName,
        middleName
      },
      identification: {
        localId,
        stateId
      }
    }
  });
}

export async function insertTestEnrollment({
  orgid,
  siteId,
  studentId,
  studentGroupId,
  grade,
  isActive = true,
  schoolYear
}) {
  // eslint-disable-next-line no-param-reassign
  schoolYear ??= await getCurrentSchoolYear();
  return StudentGroupEnrollments.insertAsync({
    orgid,
    siteId,
    studentId,
    studentGroupId,
    grade,
    isActive,
    schoolYear,
    freeReducedLunch: ""
  });
}

export async function addGroupWithoutOwner(orgid) {
  await insertTestOrganization(orgid);
  const siteId = await insertTestSite({
    orgid,
    name: "Test Elementary Site",
    schoolNumber: "01"
  });
  const studentGroupId = await insertTestStudentGroup({
    orgid,
    siteId,
    name: "Group 1 (5GRP-2-3)"
  });
  const activeStudentId = await insertTestStudent({
    orgid,
    birthDate: "2004-01-03",
    grade: "04",
    firstName: "Active",
    lastName: "Student",
    localId: "111",
    stateId: "1111"
  });
  await insertTestEnrollment({
    orgid,
    siteId,
    studentId: activeStudentId,
    studentGroupId,
    grade: "04"
  });
}

export async function addCurrentAndPastEnrollmentToGroup(orgid) {
  await insertTestOrganization(orgid);
  const siteId = await insertTestSite({
    orgid,
    name: "Test Elementary Site",
    schoolNumber: "01"
  });
  const teacherId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "1459",
    firstName: "William",
    lastName: "Cook"
  });
  const studentGroupId = await insertTestStudentGroup({
    orgid,
    siteId,
    teacherId,
    name: "Group 1 (5GRP-2-3)"
  });
  const studentId1 = await insertTestStudent({
    orgid,
    birthDate: "2004-01-03",
    grade: "04",
    firstName: "Current",
    lastName: "Student",
    localId: "111",
    stateId: "1111"
  });
  const studentId2 = await insertTestStudent({
    orgid,
    birthDate: "2003-04-13",
    grade: "04",
    firstName: "Past",
    lastName: "Pupil",
    localId: "222",
    stateId: "2222"
  });
  await insertTestEnrollment({
    orgid,
    siteId,
    studentId: studentId1,
    studentGroupId,
    grade: "04"
  });
  await insertTestEnrollment({
    orgid,
    siteId,
    studentId: studentId2,
    studentGroupId,
    grade: "04",
    schoolYear: "2016"
  });
}

export async function addActiveAndInactiveEnrollmentToGroup(orgid) {
  await insertTestOrganization(orgid);
  const siteId = await insertTestSite({
    orgid,
    name: "Test Elementary Site",
    schoolNumber: "01"
  });
  const teacherId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "1459",
    firstName: "William",
    lastName: "Cook"
  });
  const studentGroupId = await insertTestStudentGroup({
    orgid,
    siteId,
    teacherId,
    name: "Group 1 (5GRP-2-3)"
  });
  const activeStudentId = await insertTestStudent({
    orgid,
    birthDate: "2004-01-03",
    grade: "04",
    firstName: "Active",
    lastName: "Student",
    localId: "111",
    stateId: "1111"
  });
  const inactiveStudentId = await insertTestStudent({
    orgid,
    birthDate: "2004-04-13",
    grade: "04",
    firstName: "Inactive",
    lastName: "Pupil",
    localId: "222",
    stateId: "2222"
  });
  await insertTestEnrollment({
    orgid,
    siteId,
    studentId: activeStudentId,
    studentGroupId,
    grade: "04",
    isActive: true
  });
  await insertTestEnrollment({
    orgid,
    siteId,
    studentId: inactiveStudentId,
    studentGroupId,
    grade: "04",
    isActive: false
  });
}

export async function addEnrollmentToInactiveGroup(orgid) {
  await insertTestOrganization(orgid);
  const inactiveSiteId = await insertTestSite({
    orgid,
    name: "Unused Elementary",
    schoolNumber: "1"
  });
  const teacherId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "2500",
    firstName: "Not",
    lastName: "Important"
  });
  const inactiveGroupId = await insertTestStudentGroup({
    orgid,
    siteId: inactiveSiteId,
    teacherId,
    name: "Unused Group 4 (4)",
    grade: "04",
    sectionId: "4",
    isActive: false
  });
  const leftStudentId = await insertTestStudent({
    orgid,
    birthDate: "2003-02-03",
    grade: "04",
    firstName: "Andy",
    lastName: "Hobane",
    middleName: "B",
    localId: "885124",
    stateId: "8999000685556"
  });
  await insertTestEnrollment({
    orgid,
    siteId: inactiveSiteId,
    studentId: leftStudentId,
    studentGroupId: inactiveGroupId,
    grade: "04"
  });
}

export async function addOrganizationTestData(orgid) {
  await insertTestOrganization(orgid);
  const sunnyId = await insertTestSite({
    orgid,
    name: "Sunny Slope Elementary",
    schoolNumber: "60"
  });
  const rainyId = await insertTestSite({
    orgid,
    name: "Rainy Slope Elementary",
    schoolNumber: "61"
  });

  const wCookId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "1459",
    firstName: "William",
    lastName: "Cook"
  });
  const jBondId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "1458",
    firstName: "James",
    lastName: "Bond"
  });
  const jForesterId = await insertTestTeacher({
    orgid,
    address: "<EMAIL>",
    localId: "1457",
    firstName: "Jeanette",
    lastName: "Forester"
  });

  const cookieGroupId = await insertTestStudentGroup({
    orgid,
    siteId: rainyId,
    teacherId: wCookId,
    name: "Cookie 3 (3)",
    grade: "03",
    sectionId: "3"
  });
  const goldenGroupId = await insertTestStudentGroup({
    orgid,
    siteId: rainyId,
    teacherId: jBondId,
    name: "Golden 2 (2G)"
  });
  const forestGroupId = await insertTestStudentGroup({
    orgid,
    siteId: sunnyId,
    teacherId: jForesterId,
    name: "Forest 2 (2F)"
  });

  const forestStudent1Id = await insertTestStudent({
    orgid,
    birthDate: "2005-01-03",
    firstName: "Adam",
    lastName: "Anderson",
    localId: "685123",
    stateId: "3999000685555"
  });
  const forestStudent2Id = await insertTestStudent({
    orgid,
    birthDate: "2005-02-03",
    firstName: "Bill",
    lastName: "Bane",
    middleName: "B",
    localId: "685124",
    stateId: "3999000685556"
  });
  const forestStudent3Id = await insertTestStudent({
    orgid,
    birthDate: "2005-03-03",
    firstName: "Chris",
    lastName: "Conary",
    middleName: "C",
    localId: "685125",
    stateId: "3999000685557"
  });
  const goldenStudent1Id = await insertTestStudent({
    orgid,
    grade: "",
    birthDate: "2005-04-03",
    firstName: "Daniel",
    lastName: "Dawson",
    gender: "F",
    localId: "685126",
    stateId: "3999000685558"
  });
  const goldenStudent2Id = await insertTestStudent({
    orgid,
    birthDate: "2005-05-03",
    firstName: "Gail",
    lastName: "Ender",
    gender: "F",
    localId: "685127",
    stateId: "3999000685559"
  });
  const cookieStudent1Id = await insertTestStudent({
    orgid,
    grade: "03",
    birthDate: "2004-06-02",
    firstName: "Frank",
    lastName: "Fowler",
    middleName: "F",
    localId: "685128",
    stateId: "3999000685551"
  });
  const cookieStudent2Id = await insertTestStudent({
    orgid,
    grade: "03",
    birthDate: "2004-07-03",
    firstName: "Harry",
    lastName: "Haston",
    middleName: "H",
    localId: "685129",
    stateId: "3999000685552"
  });

  await insertTestEnrollment({
    orgid,
    siteId: sunnyId,
    studentId: forestStudent1Id,
    studentGroupId: forestGroupId,
    grade: "02"
  });
  await insertTestEnrollment({
    orgid,
    siteId: sunnyId,
    studentId: forestStudent2Id,
    studentGroupId: forestGroupId,
    grade: "02"
  });
  await insertTestEnrollment({
    orgid,
    siteId: sunnyId,
    studentId: forestStudent3Id,
    studentGroupId: forestGroupId,
    grade: "02"
  });
  await insertTestEnrollment({
    orgid,
    siteId: rainyId,
    studentId: goldenStudent1Id,
    studentGroupId: goldenGroupId,
    grade: "02"
  });
  await insertTestEnrollment({
    orgid,
    siteId: rainyId,
    studentId: goldenStudent2Id,
    studentGroupId: goldenGroupId,
    grade: "02"
  });
  await insertTestEnrollment({
    orgid,
    siteId: rainyId,
    studentId: cookieStudent1Id,
    studentGroupId: cookieGroupId,
    grade: "03"
  });
  await insertTestEnrollment({
    orgid,
    siteId: rainyId,
    studentId: cookieStudent2Id,
    studentGroupId: cookieGroupId,
    grade: "03"
  });
}

export function getExpectedCurrentRoster() {
  return [
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "60",
      SchoolName: "Sunny Slope Elementary",
      TeacherID: "1457",
      TeacherLastName: "Forester",
      TeacherFirstName: "Jeanette",
      TeacherEmail: "<EMAIL>",
      ClassName: "Forest 2",
      ClassSectionID: "2",
      StudentLocalID: "685123",
      StudentStateID: "3999000685555",
      StudentLastName: "Anderson",
      StudentFirstName: "Adam",
      StudentBirthDate: "2005-01-03",
      SpringMathGrade: "2"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "60",
      SchoolName: "Sunny Slope Elementary",
      TeacherID: "1457",
      TeacherLastName: "Forester",
      TeacherFirstName: "Jeanette",
      TeacherEmail: "<EMAIL>",
      ClassName: "Forest 2",
      ClassSectionID: "2",
      StudentLocalID: "685124",
      StudentStateID: "3999000685556",
      StudentLastName: "Bane",
      StudentFirstName: "Bill",
      StudentBirthDate: "2005-02-03",
      SpringMathGrade: "2"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "60",
      SchoolName: "Sunny Slope Elementary",
      TeacherID: "1457",
      TeacherLastName: "Forester",
      TeacherFirstName: "Jeanette",
      TeacherEmail: "<EMAIL>",
      ClassName: "Forest 2",
      ClassSectionID: "2",
      StudentLocalID: "685125",
      StudentStateID: "3999000685557",
      StudentLastName: "Conary",
      StudentFirstName: "Chris",
      StudentBirthDate: "2005-03-03",
      SpringMathGrade: "2"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "61",
      SchoolName: "Rainy Slope Elementary",
      TeacherID: "1458",
      TeacherLastName: "Bond",
      TeacherFirstName: "James",
      TeacherEmail: "<EMAIL>",
      ClassName: "Golden 2",
      ClassSectionID: "2",
      StudentLocalID: "685126",
      StudentStateID: "3999000685558",
      StudentLastName: "Dawson",
      StudentFirstName: "Daniel",
      StudentBirthDate: "2005-04-03",
      SpringMathGrade: "2"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "61",
      SchoolName: "Rainy Slope Elementary",
      TeacherID: "1458",
      TeacherLastName: "Bond",
      TeacherFirstName: "James",
      TeacherEmail: "<EMAIL>",
      ClassName: "Golden 2",
      ClassSectionID: "2",
      StudentLocalID: "685127",
      StudentStateID: "3999000685559",
      StudentLastName: "Ender",
      StudentFirstName: "Gail",
      StudentBirthDate: "2005-05-03",
      SpringMathGrade: "2"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "61",
      SchoolName: "Rainy Slope Elementary",
      TeacherID: "1459",
      TeacherLastName: "Cook",
      TeacherFirstName: "William",
      TeacherEmail: "<EMAIL>",
      ClassName: "Cookie 3",
      ClassSectionID: "3",
      StudentLocalID: "685128",
      StudentStateID: "3999000685551",
      StudentLastName: "Fowler",
      StudentFirstName: "Frank",
      StudentBirthDate: "2004-06-02",
      SpringMathGrade: "3"
    },
    {
      DistrictID: districtNumber,
      DistrictName: districtName,
      SchoolID: "61",
      SchoolName: "Rainy Slope Elementary",
      TeacherID: "1459",
      TeacherLastName: "Cook",
      TeacherFirstName: "William",
      TeacherEmail: "<EMAIL>",
      ClassName: "Cookie 3",
      ClassSectionID: "3",
      StudentLocalID: "685129",
      StudentStateID: "3999000685552",
      StudentLastName: "Haston",
      StudentFirstName: "Harry",
      StudentBirthDate: "2004-07-03",
      SpringMathGrade: "3"
    }
  ];
}

export async function insertTestSite({ orgid, name, schoolNumber }) {
  return Sites.insertAsync({
    orgid,
    schoolYear: await getCurrentSchoolYear(),
    stateInformation: {
      districtNumber,
      districtName,
      schoolNumber,
      localSchoolNumber: schoolNumber
    },
    name,
    isVisible: true,
    grades: []
  });
}

export async function insertTestOrganization(orgid) {
  return Organizations.insertAsync({ _id: orgid, name: districtName });
}

export async function insertTestTeacher({ orgid, address, localId, firstName, lastName }) {
  return Users.insertAsync({
    emails: [{ address }],
    profile: {
      orgid,
      localId,
      siteAccess: [],
      name: {
        first: firstName,
        last: lastName
      }
    }
  });
}

export async function insertTestStudents(identificationDetails, schoolYear) {
  // eslint-disable-next-line no-restricted-syntax
  for await (const details of identificationDetails) {
    await Students.insertAsync({
      orgid: details.orgid,
      identity: {
        identification: {
          localId: details.localId,
          stateId: details.stateId
        }
      },
      schoolYear
    });
  }
}
