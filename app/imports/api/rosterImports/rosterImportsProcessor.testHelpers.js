// eslint-disable-next-line import/no-cycle
import {
  getAllCookieGroupItems,
  getAllRosterImportItems,
  getAllGoldenGroupUploadItems,
  getAllNextGroupItems
} from "../../test-helpers/data/rosterImportItems";
import { Organizations } from "../organizations/organizations";
import { Users } from "../users/users";
import { Sites } from "../sites/sites";
import RosterImportsProcessor from "./rosterImportsProcessor";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { Students } from "../students/students";
import { RosterImports } from "./rosterImports";

export function setupTestContext() {
  return {
    schoolYear: 2018,
    orgid: "test_orgid",
    siteId: "test_elementary_site_id",
    benchmarkPeriodId: "winter",
    districtID: "111",
    districtName: "District Name"
  };
}

const { orgid, schoolYear, benchmarkPeriodId, siteId, districtName } = setupTestContext();

export class TestGroup {
  constructor(groupName, groupData) {
    this.assessmentIds = ["firstAssessmentId", "secondAssessmentId"];
    this.students = groupData.groupStudents;
    this.studentIds = groupData.studentIds;
    this.studentGroupId = groupData.studentGroupId;
    this.studentGroup = groupData.studentGroup;
    this.grade = groupData.studentGroup.grade;
    this.schoolYear = 2018;
    this.orgid = orgid;
    this.siteId = siteId;
    this.benchmarkPeriodId = benchmarkPeriodId;
  }

  static async init(groupName) {
    const groupData = await getGroupDataFromRosterImportItems(groupName);
    return new TestGroup(groupName, groupData);
  }

  async getGroup() {
    return StudentGroups.findOneAsync(this.studentGroupId);
  }

  async getCurrentAssessmentResultIds() {
    const group = await this.getGroup();
    return group.currentAssessmentResultIds;
  }

  async getStudentsWithCurrentSkill(studentIds = this.studentIds) {
    return Students.find({ _id: { $in: studentIds }, currentSkill: { $exists: true } }).fetchAsync();
  }

  async getCurrentScreening() {
    const currentAssessmentResultIds = await this.getCurrentAssessmentResultIds();
    return (await AssessmentResults.find({ _id: { $in: currentAssessmentResultIds } }).fetchAsync()).find(
      ass => ass.type === "benchmark"
    );
  }

  async getCurrentClasswideIntervention() {
    const currentAssessmentResultIds = await this.getCurrentAssessmentResultIds();
    return (await AssessmentResults.find({ _id: { $in: currentAssessmentResultIds } }).fetchAsync()).find(
      ass => ass.type === "classwide"
    );
  }

  async getActiveIndividualInterventions() {
    const currentAssessmentResultIds = await this.getCurrentAssessmentResultIds();
    return (await AssessmentResults.find({ _id: { $in: currentAssessmentResultIds } }).fetchAsync()).filter(
      ass => ass.type === "individual"
    );
  }

  async getIndividualInterventionEligibility() {
    return (await this.getGroup()).individualInterventionQueue;
  }

  async getActivelyEnrolledStudentIds() {
    return (
      await StudentGroupEnrollments.find({
        studentGroupId: this.studentGroupId,
        isActive: true
      }).fetchAsync()
    ).map(e => e.studentId);
  }

  async getNumberOfAssessments() {
    return AssessmentResults.find({
      studentGroupId: this.studentGroupId
    }).countAsync();
  }

  async addActiveClasswideIntervention() {
    const benchmarkAssessmentResult = await this.addFailedScreeningAssessmentResult();
    const history = getBenchmarkHistoryItem(benchmarkAssessmentResult);
    const currentClasswideSkill = getCurrentClasswideSkill(benchmarkAssessmentResult);
    await StudentGroups.updateAsync(
      { _id: this.studentGroupId },
      {
        $push: { history },
        $set: { currentClasswideSkill },
        $addToSet: {
          currentAssessmentResultIds: benchmarkAssessmentResult.nextAssessmentResultId
        }
      }
    );
    return this;
  }

  async addFailedScreeningAssessmentResult() {
    const { studentIds } = this;
    const meetsTarget = false;
    const scores = this.getAssessmentScores();
    const measures = this.getMeasuresFor();
    const classwideInterventionId = await this.addClasswideInterventionAssessmentResult();
    const assessmentObject = this.getAssessmentObject({
      measures,
      scores,
      passed: meetsTarget,
      studentIdsNotMeetingTarget: studentIds
    });
    assessmentObject.nextAssessmentResultId = classwideInterventionId;
    return {
      _id: await AssessmentResults.insertAsync(assessmentObject),
      ...assessmentObject
    };
  }

  getAssessmentScores({
    assessmentIds = this.assessmentIds,
    value = "0",
    status = "COMPLETE",
    studentIdsNotMeetingTarget = []
  } = {}) {
    const scores = [];
    assessmentIds.forEach(assessmentId => {
      this.studentIds.forEach(studentId => {
        const scoreObject = {
          assessmentId,
          orgid: this.orgid,
          siteId: this.siteId,
          status,
          studentId
        };
        if (status !== "STARTED") {
          scoreObject.value = studentIdsNotMeetingTarget.includes(studentId) ? "0" : value;
        }
        scores.push(scoreObject);
      });
    });
    return scores;
  }

  getMeasuresFor({ assessmentIds = this.assessmentIds, status = "COMPLETE", studentIdsNotMeetingTarget = [] } = {}) {
    const measures = [];

    assessmentIds.forEach((assessmentId, assessmentIndex) => {
      const studentResults = [];
      this.students.forEach(student => {
        const meetsTarget = !studentIdsNotMeetingTarget.includes(student._id);
        studentResults.push({
          studentId: student._id,
          status,
          firstName: student.firstName,
          lastName: student.lastName,
          score: meetsTarget ? "10" : "0",
          meetsTarget,
          individualRuleOutcome: meetsTarget ? "at" : "below"
        });
      });

      const cutoffTarget = 10;
      const studentScores = studentResults.map(result => Number(result.score));
      const numberMeetingTarget = studentScores.filter(score => score >= cutoffTarget).length;
      const percentMeetingTarget = (numberMeetingTarget / studentScores.length) * 100;
      measures.push({
        assessmentId,
        assessmentName: `AssessmentName${assessmentIndex}`,
        cutoffTarget,
        targetScores: [10, 22, 300],
        medianScore: 0, // not important
        studentScores,
        percentMeetingTarget,
        numberMeetingTarget,
        totalStudentsAssessed: studentScores.length,
        benchmarkPeriodId,
        grade: this.grade,
        assessmentResultType: "benchmark",
        studentResults
      });
    });
    return measures;
  }

  async addClasswideInterventionAssessmentResult() {
    const assessmentIds = ["assessmentId"];
    const scores = this.getAssessmentScores({
      studentIds: this.studentIds,
      assessmentIds,
      status: "STARTED"
    });
    return AssessmentResults.insertAsync({
      orgid: this.orgid,
      benchmarkPeriodId: this.benchmarkPeriodId,
      schoolYear: this.schoolYear,
      status: "OPEN",
      studentGroupId: this.studentGroupId,
      type: "classwide",
      previousAssessmentResultId: "not_important",
      grade: this.grade,
      assessmentIds,
      scores
    });
  }

  getAssessmentObject({ status = "COMPLETED", measures, scores, passed = true, studentIdsNotMeetingTarget = [] }) {
    const ruleResults = {
      passed: false,
      nextSkill: passed
        ? null
        : {
            assessmentId: this.assessmentIds[0],
            assessmentName: "ClasswideInterventionAssessment",
            interventions: [],
            targets: [10, 22, 300]
          }
    };
    const totalStudentsMeetingAllTargets = measures[0].numberMeetingTarget;
    const totalStudentsAssessedOnAllMeasures = measures[0].totalStudentsAssessed;
    const percentMeetingTarget = Math.round(
      (totalStudentsMeetingAllTargets / totalStudentsAssessedOnAllMeasures) * 100
    );
    const percentAtRisk = 100 - percentMeetingTarget;
    return {
      orgid: this.orgid,
      benchmarkPeriodId: this.benchmarkPeriodId,
      schoolYear: this.schoolYear,
      status,
      studentGroupId: this.studentGroupId,
      type: "benchmark",
      grade: this.grade,
      scores,
      assessmentIds: this.assessmentIds,
      classwideResults: {
        percentMeetingTarget,
        percentAtRisk,
        totalStudentsMeetingAllTargets,
        totalStudentsAssessedOnAllMeasures,
        studentIdsNotMeetingTarget
      },
      measures,
      ruleResults
    };
  }

  getAssessmentObjectForScreening(status) {
    return {
      orgid: this.orgid,
      benchmarkPeriodId: this.benchmarkPeriodId,
      schoolYear: this.schoolYear,
      status,
      studentGroupId: this.studentGroupId,
      type: "benchmark",
      grade: this.grade
    };
  }

  async addActiveScreening() {
    const assessmentObject = this.getAssessmentObjectForScreening("OPEN");
    assessmentObject.scores = this.getAssessmentScores({ status: "STARTED" });
    assessmentObject.assessmentIds = this.assessmentIds;
    const screeningId = await AssessmentResults.insertAsync(assessmentObject);
    await StudentGroups.updateAsync(
      { _id: this.studentGroupId },
      { $addToSet: { currentAssessmentResultIds: screeningId } }
    );
    return this;
  }

  async addIndividualIntervention({
    studentIdsNotMeetingTarget = this.studentIds,
    assessmentIds = this.assessmentIds
  } = {}) {
    const screeningId = await this.addCompletedScreening({
      studentIdsNotMeetingTarget,
      assessmentIds
    });
    await this.addIndividualInterventionsToGroupAndStudents({
      idsOfStudentsInIntervention: studentIdsNotMeetingTarget,
      previousAssessmentResultId: screeningId
    });
    return this;
  }

  async addCompletedScreening({ studentIdsNotMeetingTarget = [], assessmentIds = this.assessmentIds } = {}) {
    const benchmarkAssessmentResult = await this.addPassedScreeningAssessmentResult({
      assessmentIds,
      studentIdsNotMeetingTarget
    });
    const history = getBenchmarkHistoryItem(benchmarkAssessmentResult);
    await StudentGroups.updateAsync(
      { _id: this.studentGroupId },
      {
        $push: { history },
        $addToSet: {
          individualInterventionQueue: { $each: studentIdsNotMeetingTarget }
        }
      }
    );
    return benchmarkAssessmentResult._id;
  }

  async addPassedScreeningAssessmentResult({ studentIdsNotMeetingTarget = [] }) {
    const meetsTarget = true;
    const scores = this.getAssessmentScores({
      value: "10",
      studentIdsNotMeetingTarget
    });
    const measures = this.getMeasuresFor({ studentIdsNotMeetingTarget });
    const assessmentObject = this.getAssessmentObject({
      measures,
      scores,
      passed: meetsTarget,
      studentIdsNotMeetingTarget
    });
    return {
      _id: await AssessmentResults.insertAsync(assessmentObject),
      ...assessmentObject
    };
  }

  async addIndividualInterventionsToGroupAndStudents({ idsOfStudentsInIntervention, previousAssessmentResultId }) {
    // eslint-disable-next-line no-restricted-syntax
    for await (const studentId of idsOfStudentsInIntervention) {
      const individualInterventionId = await this.addIndividualInterventionAssessmentResult({
        studentId,
        previousAssessmentResultId
      });
      await StudentGroups.updateAsync(
        { _id: this.studentGroupId },
        { $addToSet: { currentAssessmentResultIds: individualInterventionId } }
      );
      await Students.updateAsync(
        { _id: studentId },
        {
          $set: {
            "currentSkill.assessmentResultId": individualInterventionId,
            "currentSkill.benchmarkAssessmentId": previousAssessmentResultId
          }
        }
      );
    }
  }

  async addIndividualInterventionAssessmentResult({ studentId, previousAssessmentResultId = "" }) {
    const assessmentIds = ["individualInterventionAssessment"];
    const scores = this.getAssessmentScores({
      studentIds: [studentId],
      assessmentIds,
      status: "STARTED"
    });

    const assessmentResultId = await AssessmentResults.insertAsync({
      orgid: this.orgid,
      benchmarkPeriodId: this.benchmarkPeriodId,
      schoolYear: this.schoolYear,
      status: "OPEN",
      studentGroupId: this.studentGroupId,
      studentId,
      type: "individual",
      previousAssessmentResultId,
      grade: this.grade,
      assessmentIds,
      scores,
      rootRuleId: "some_rule_id",
      individualSkills: {}
    });

    const individualSkills = {
      benchmarkAssessmentId: "benchmarkAssessmentId",
      benchmarkAssessmentName: "benchmarkAssessmentName",
      benchmarkAssessmentTargets: [13, 26, 300],
      assessmentId: assessmentIds[0],
      assessmentName: "assessmentName",
      assessmentTargets: [16, 31, 300],
      interventions: [],
      assessmentResultId
    };

    await AssessmentResults.updateAsync({ _id: assessmentResultId }, { $set: { individualSkills } });

    return assessmentResultId;
  }

  async archive() {
    await StudentGroups.updateAsync(
      { _id: this.studentGroupId },
      {
        $set: {
          isActive: false,
          individualInterventionQueue: [],
          currentAssessmentResultIds: []
        }
      }
    );
    await StudentGroupEnrollments.updateAsync(
      { studentGroupId: this.studentGroupId },
      { $set: { isActive: false } },
      { multi: true }
    );
    return this;
  }
}

export function getBenchmarkHistoryItem(benchmarkAssessmentResult) {
  return {
    type: "benchmark",
    benchmarkPeriodId,
    assessmentResultId: benchmarkAssessmentResult._id,
    assessmentResultMeasures: benchmarkAssessmentResult.measures
  };
}

export function getCurrentClasswideSkill(benchmarkAssessmentResult) {
  const assessment = benchmarkAssessmentResult.ruleResults.nextSkill;
  return {
    assessmentId: assessment.assessmentId,
    assessmentName: assessment.assessmentName,
    interventions: [],
    targets: assessment.targets,
    assessmentResultId: benchmarkAssessmentResult.nextAssessmentResultId,
    benchmarkPeriodId,
    message: {
      additionalStudentsAddedToInterventionQueue: false,
      messageCode: "1",
      dismissed: false
    }
  };
}

export async function getGroupBySectionId(sectionId) {
  return StudentGroups.findOneAsync({ sectionId });
}

export async function getStudentsByLocalIds(localIds) {
  return Students.find({
    "identity.identification.localId": { $in: localIds }
  }).fetchAsync();
}

export function getNumberOfStudentScores(assessment, studentIds) {
  return assessment.scores.filter(score => studentIds.includes(score.studentId)).length;
}

export async function getStudentsFromRosterImportItems(rosterImportItems) {
  const localIdsOfStudentsInClasswideIntervention = rosterImportItems.map(item => item.data.studentLocalID);
  return getStudentsByLocalIds(localIdsOfStudentsInClasswideIntervention);
}

export async function clearCollections() {
  await Students.removeAsync({});
  await StudentGroups.removeAsync({});
  await StudentGroupEnrollments.removeAsync({});
  await AssessmentResults.removeAsync({});
  await Sites.removeAsync({});
  await Users.removeAsync({});
  await Organizations.removeAsync({});
  await RosterImports.removeAsync({});
}

export async function setupDb(byDateOn, rosterImportId) {
  const fileUploadItems = await getAllRosterImportItems();
  await Organizations.insertAsync({
    _id: orgid,
    name: districtName
  });
  await (
    await RosterImportsProcessor.init({
      items: fileUploadItems,
      orgid,
      byDateOn,
      rosterImportId
    })
  ).process();

  return fileUploadItems;
}

export async function getGroupDataFromRosterImportItems(groupName) {
  let isUsingRosterImportItems = true;
  let groupItems;
  switch (groupName) {
    case "cookie":
      groupItems = await getAllCookieGroupItems();
      break;
    case "golden":
      groupItems = await getAllGoldenGroupUploadItems();
      break;
    case "next":
      groupItems = await getAllNextGroupItems();
      break;
    default:
      groupItems = await getStudentGroupDataByGroupName(groupName);
      isUsingRosterImportItems = false;
      break;
  }

  let groupStudents;
  let studentIds;
  let studentGroup;
  let studentGroupId;

  if (isUsingRosterImportItems) {
    groupStudents = await getStudentsFromRosterImportItems(groupItems);
    studentIds = groupStudents.map(s => s._id);
    studentGroup = await getGroupBySectionId(groupItems[0].data.classSectionID);
    studentGroupId = studentGroup._id;
  } else {
    ({ groupStudents, studentIds, studentGroupId, studentGroup } = groupItems);
  }

  return {
    groupStudents,
    studentIds,
    studentGroupId,
    studentGroup
  };
}

export function areAllScoresStarted(assessment) {
  return assessment.scores.every(score => score.status === "STARTED");
}

export async function getStudentGroupDataByGroupName(groupName) {
  const studentGroup = await StudentGroups.findOneAsync({ name: groupName });
  const studentGroupId = studentGroup._id;
  const studentIds = (
    await StudentGroupEnrollments.find({
      studentGroupId,
      isActive: true
    }).fetchAsync()
  ).map(e => e.studentId);
  const groupStudents = await Students.find({ _id: { $in: studentIds } }).fetchAsync();
  return {
    studentGroup,
    studentGroupId,
    studentIds,
    groupStudents
  };
}

export async function addArchivedStudent() {
  const studentId = await Students.insertAsync({
    orgid,
    grade: "04",
    schoolYear,
    identity: {
      identification: {
        stateId: "1",
        localId: "1"
      }
    },
    demographic: {
      birthDate: "2006-05-17"
    }
  });
  await StudentGroupEnrollments.insertAsync({
    orgid,
    studentId,
    studentGroupId: "notExistingGroup",
    siteId,
    schoolYear,
    isActive: false
  });
  return Students.findOneAsync(studentId);
}
