import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { parseBoolean } from "./utilities/utilities";

import { BenchmarkPeriods } from "./benchmarkPeriods/benchmarkPeriods";
import { Interventions } from "./interventions/interventions";
import { Roles } from "./roles/roles";
import { Grades } from "./grades/grades";
import { Assessments } from "./assessments/assessments";
import { Rules } from "./rules/rules";
import { GroupedAssessments } from "./groupedAssessments/groupedAssessments";
import { Settings } from "./settings/settings";
import { AssessmentGrowth } from "./assessmentGrowth/assessmentGrowth";
import { ScreeningAssignments } from "./screeningAssignments/screeningAssignments";

const extractSystemEnv = keys => {
  return keys.reduce((envs, envProperty) => {
    const envValue = process.env[envProperty] || "";
    // eslint-disable-next-line no-param-reassign
    envs[envProperty] = ["true", "false"].includes(envValue.toLowerCase()) ? parseBoolean(envValue) : envValue;
    return envs;
  }, {});
};
// TODO(fmazur) - load environment variables to local storage on client startup
Meteor.methods({
  getEnvironmentVariables(keys) {
    // Environment variables are not sensitive and needed during app initialization
    // Removing auth check to prevent race condition errors during login
    check(keys, Array);

    return extractSystemEnv(keys);
  },
  getMeteorPrivateSettings(keys) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(keys, Array);

    return keys.reduce((settings, settingKey) => {
      const settingValue = Meteor.settings[settingKey] || "";
      settings[settingKey] = ["true", "false"].includes(settingValue.toLowerCase())
        ? parseBoolean(settingValue)
        : settingValue;
      return settings;
    }, {});
  },
  async "StaticData:getStaticData"() {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const env = extractSystemEnv(["CI", "METEOR_ENVIRONMENT"]);
    return {
      assessments: await Assessments.find().fetchAsync(), // 157 documents
      assessmentGrowth: await AssessmentGrowth.find().fetchAsync(), // 9 documents
      // rules: await Rules.find({}, { fields: { name: 1, rootRuleId: 1, attributeValues: 1, outcomes: 1 } }).fetchAsync(),
      classwideRules: await Rules.find(
        { grade: { $exists: true } },
        { fields: { conditionTypeId: 0, created: 0, lastModified: 0 } }
      ).fetchAsync(), // 10 documents
      roleDefinitions: await Roles.find({}, { fields: { _id: 1, name: 1 } }).fetchAsync(), // 8 documents
      news: [], // NOTE(fmazur) - not needed at the moment - await News.find({}).fetchAsync(),
      grades: await Grades.find({}, { fields: { lastModified: 0 }, sort: { sortorder: 1 } }).fetchAsync(), // 10 documents
      instructionalVideos: [],
      settings: await Settings.find({}, { fields: { defaults: 1 } }).fetchAsync(),
      screeningAssignments: await ScreeningAssignments.find(
        {},
        { fields: { grade: 1, benchmarkPeriodId: 1, assessmentIds: 1 } }
      ).fetchAsync(), // 28 documents
      interventions: await Interventions.find({}).fetchAsync(), // 24 documents
      groupedAssessments: await GroupedAssessments.find({}).fetchAsync(), // 45 documents
      // 4 documents
      benchmarkPeriods: await BenchmarkPeriods.find(
        { name: { $exists: true } },
        {
          sort: { sortOrder: 1 },
          fields: {
            name: 1,
            label: 1,
            sortOrder: 1,
            startDate: 1,
            endDate: 1
          }
        }
      ).fetchAsync(),
      env
      // TODO(fmazur) - better handle this, maybe split into isLocalEnvDebugMode, isDevEnvDebugMode etc
      // TODO(fmazur) - not ready to use yet
      // isDevMode: shouldUseDevMode(env.CI, [env.METEOR_ENVIRONMENT])
    };
  }
});
