import get from "lodash/get";

export function getScoreTargets({ assessment, grade, benchmarkPeriodId, assessmentType }) {
  const assessmentScores = get(assessment, "strands[0].scores");

  let numberCorrectTargets = [];
  if (assessmentScores) {
    numberCorrectTargets = assessmentScores.find(s => s.externalId === "number_correct");
  } else {
    throw new Error("Assessment not supplied for getScoreTargets");
  }

  let numberCorrectTargetsForGrade = null;
  if (numberCorrectTargets && numberCorrectTargets.targets) {
    // find targets for defined assessmentType
    numberCorrectTargetsForGrade = numberCorrectTargets.targets.find(
      t => t.grade === grade && t.assessmentType === assessmentType
    );
    if (!numberCorrectTargetsForGrade) {
      // fall back to default targets
      numberCorrectTargetsForGrade = numberCorrectTargets.targets.find(t => t.grade === grade);
    }
  } else {
    throw new Error(
      `Cannot find correct score targets for grade ${grade} for assessmentId: ${assessment._id} assessment name: ${assessment.name}`
    );
  }

  let targets = null;
  if (numberCorrectTargetsForGrade && numberCorrectTargetsForGrade.periods) {
    targets = numberCorrectTargetsForGrade.periods.find(p => p.benchmarkPeriodId === benchmarkPeriodId);
  } else {
    throw new Error(
      `Cannot find correct score targets for grade ${grade} for assessmentId: ${assessment._id} assessment name: ${assessment.name}`
    );
  }

  if (targets && targets.values && targets.values.length) {
    return targets.values;
  }
  throw new Error(
    `Cannot find correct score targets for benchmarkPeriodId ${benchmarkPeriodId} for grade ${grade} for assessmentId: ${assessment._id} assessment name: ${assessment.name}`
  );
}
