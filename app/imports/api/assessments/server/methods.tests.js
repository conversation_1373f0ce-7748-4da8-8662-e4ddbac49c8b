/* eslint-disable no-use-before-define */
import { Assessments } from "../assessments";
import { BenchmarkPeriods } from "../../benchmarkPeriods/benchmarkPeriods";
import {
  addTargetToAssessment,
  calculateYearlyGrowthStats,
  getAssessmentsFunctioningStats,
  getFinalClasswideAssessmentId,
  getNciiValuesStats,
  removeTargetFromAssessment,
  updateOrCreateClasswideTargets,
  updateTargets
} from "./methods";
import { getBenchmarkPeriods } from "../../../test-helpers/data/benchmarkPeriods";
import {
  assessmentWithBenchmarkTarget,
  assessmentWithDefaultAndIndividualTarget,
  expectedUpdatedHighSchoolAssessment,
  expectedUpdatedPrimaryGradeAssessment,
  fallPeriodId,
  grade,
  highSchoolAssessment,
  idOfHighSchoolAssessment,
  highSchoolAssessmentTargetsWithRemovedIndividualTarget,
  highSchoolGrade,
  idOfAssessmentWithBenchmarkTarget,
  springPeriodId,
  winterPeriodId
} from "../../../test-helpers/data/assessments";

describe("Assessment methods", () => {
  beforeAll(async () => {
    await BenchmarkPeriods.insertAsync(getBenchmarkPeriods());
  });
  afterAll(async () => {
    await BenchmarkPeriods.removeAsync({});
  });
  describe("addTargetToAssessment", () => {
    beforeEach(async () => {
      await Assessments.insertAsync(assessmentWithBenchmarkTarget);
    });
    afterEach(async () => {
      await Assessments.removeAsync({});
    });
    it("should not add target if the target already exist", async () => {
      const newTargetName = "benchmark";

      const returnValue = await addTargetToAssessment({
        assessmentId: idOfAssessmentWithBenchmarkTarget,
        targetName: newTargetName,
        grade
      });

      expect(returnValue).toBeNull();
    });
    it("should copy existing assessment's grade target and add it as a new target type with newly generated id", async () => {
      const newTargetName = "classwide";

      await addTargetToAssessment({
        assessmentId: idOfAssessmentWithBenchmarkTarget,
        targetName: newTargetName,
        grade
      });

      const updatedAssessment = await Assessments.findOneAsync(idOfAssessmentWithBenchmarkTarget);
      const updatedTargets = updatedAssessment.strands[0].scores[0].targets;
      const newlyAddedTarget = updatedTargets.find(t => t.assessmentType === newTargetName);
      expect(updatedTargets.length).toBe(2);
      expect(getExpectedNewTarget(newTargetName)).toMatchObject(newlyAddedTarget);
    });
    it("should add a target without an assessmentType property if target name is set to default", async () => {
      const newTargetName = "default";

      await addTargetToAssessment({
        assessmentId: idOfAssessmentWithBenchmarkTarget,
        targetName: newTargetName,
        grade
      });

      const updatedAssessment = await Assessments.findOneAsync(idOfAssessmentWithBenchmarkTarget);
      const updatedTargets = updatedAssessment.strands[0].scores[0].targets;
      const newlyAddedTarget = updatedTargets.find(t => !t.assessmentType);
      expect(getExpectedNewTarget(newTargetName)).toMatchObject(newlyAddedTarget);
    });
  });

  describe("updateTargets", () => {
    afterEach(async () => {
      await Assessments.removeAsync({});
    });
    it("should throw an error if received targets do not match existing targets", async () => {
      await Assessments.insertAsync(assessmentWithBenchmarkTarget);
      const targetsToUpdate = { classwide: {}, individual: {} };

      await expect(
        updateTargets({
          targets: targetsToUpdate,
          grade,
          assessmentId: idOfAssessmentWithBenchmarkTarget
        })
      ).rejects.toThrow(/The following targets has not yet been created: classwide, individual/);
    });
    it("should throw an error if targets for invalid periods are found", async () => {
      await Assessments.insertAsync(assessmentWithBenchmarkTarget);
      const targetsToUpdate = { benchmark: { Other: {} } };

      await expect(
        updateTargets({
          targets: targetsToUpdate,
          grade,
          assessmentId: idOfAssessmentWithBenchmarkTarget
        })
      ).rejects.toThrow(/Unknown benchmark periods found: Other/);
    });
    it("should throw an error if the targets contain negative, falsy or non-numeric values", async () => {
      await Assessments.insertAsync(assessmentWithBenchmarkTarget);
      const targetsToUpdate = {
        benchmark: {
          Fall: {
            instructionalTarget: 3,
            masteryTarget: 10
          },
          Spring: {
            instructionalTarget: 3,
            masteryTarget: 10
          },
          Winter: {
            instructionalTarget: 3,
            masteryTarget: -1
          }
        }
      };

      await expect(
        updateTargets({
          targets: targetsToUpdate,
          grade,
          assessmentId: idOfAssessmentWithBenchmarkTarget
        })
      ).rejects.toThrow(/Please make sure all the provided targets are positive numbers/);
    });
    it("should update the values corresponding to instructional and mastery targets on provided assessment for provided grade", async () => {
      const assessmentId = await Assessments.insertAsync(assessmentWithDefaultAndIndividualTarget);
      const targetsToUpdate = {
        default: {
          Fall: {
            instructionalTarget: 1,
            masteryTarget: 2
          },
          Spring: {
            instructionalTarget: 3,
            masteryTarget: 4
          },
          Winter: {
            instructionalTarget: 5,
            masteryTarget: 6
          }
        },
        individual: {
          Fall: {
            instructionalTarget: 7,
            masteryTarget: 8
          },
          Spring: {
            instructionalTarget: 9,
            masteryTarget: 10
          },
          Winter: {
            instructionalTarget: 11,
            masteryTarget: 12
          }
        }
      };

      await updateTargets({ assessmentId, targets: targetsToUpdate, grade });

      expect(await Assessments.findOneAsync(assessmentId)).toMatchObject(expectedUpdatedPrimaryGradeAssessment);
    });
    it('should work with updates for "All" benchmark period available in High School', async () => {
      const assessmentId = await Assessments.insertAsync(highSchoolAssessment);
      const targetsToUpdate = {
        individual: {
          All: {
            instructionalTarget: 7,
            masteryTarget: 15
          }
        },
        classwide: {
          All: {
            instructionalTarget: 15,
            masteryTarget: 30
          }
        }
      };

      await updateTargets({
        assessmentId,
        targets: targetsToUpdate,
        grade: highSchoolGrade
      });

      expect(await Assessments.findOneAsync(assessmentId)).toMatchObject(expectedUpdatedHighSchoolAssessment);
    });
  });

  describe("removeTargetFromAssessment", () => {
    it("should remove the specified assessment type target from a given grade in the assessment", async () => {
      const assessmentId = await Assessments.insertAsync(highSchoolAssessment);

      await removeTargetFromAssessment({
        grade: highSchoolGrade,
        targetName: "individual",
        assessmentId
      });

      expect((await Assessments.findOneAsync(assessmentId)).strands[0].scores[0].targets).toMatchObject(
        highSchoolAssessmentTargetsWithRemovedIndividualTarget
      );
    });
  });

  describe("updateOrCreateClasswideTargets", () => {
    const filterClasswideTargetsOut = gradeToMatch => target =>
      target.grade === gradeToMatch && (!target.assessmentType || target.assessmentType !== "classwide");

    afterEach(async () => {
      await Assessments.removeAsync({});
    });

    it("should create a new classwide target if it does not exist", async () => {
      await Assessments.insertAsync(assessmentWithBenchmarkTarget);
      const instructionalTarget = 10;
      const masteryTarget = 20;

      await updateOrCreateClasswideTargets({
        gradeId: grade,
        assessmentId: idOfAssessmentWithBenchmarkTarget,
        instructionalTarget,
        masteryTarget
      });

      // VERIFY CLASSWIDE TARGETS WERE ADDED
      const modifiedAssessment = await Assessments.findOneAsync(idOfAssessmentWithBenchmarkTarget);
      const allTargets = modifiedAssessment.strands[0].scores[0].targets;
      const addedAssessmentClasswideTarget = allTargets.find(
        t => t.assessmentType === "classwide" && t.grade === grade
      );
      const [instructionalTargetSet, masteryTargetSet] = addedAssessmentClasswideTarget.periods[0].values;
      expect(instructionalTargetSet).toEqual(instructionalTarget);
      expect(masteryTargetSet).toEqual(masteryTarget);

      // VERIFY OTHER TARGETS WERE NOT CHANGED
      const otherTargets = allTargets.filter(filterClasswideTargetsOut(grade));
      const otherTargetsBeforeModification = assessmentWithBenchmarkTarget.strands[0].scores[0].targets.filter(
        filterClasswideTargetsOut(grade)
      );
      expect(otherTargets).toMatchObject(otherTargetsBeforeModification);
    });
    it("should modify (only) existing classwide target", async () => {
      await Assessments.insertAsync(highSchoolAssessment);
      const instructionalTarget = 20;
      const masteryTarget = 40;

      await updateOrCreateClasswideTargets({
        gradeId: highSchoolGrade,
        assessmentId: idOfHighSchoolAssessment,
        instructionalTarget,
        masteryTarget
      });

      // VERIFY CLASSWIDE TARGETS CHANGED
      const modifiedHighSchoolAssessment = await Assessments.findOneAsync(idOfHighSchoolAssessment);
      const allTargets = modifiedHighSchoolAssessment.strands[0].scores[0].targets;
      const modifiedClasswideTarget = allTargets.find(t => t.assessmentType === "classwide");
      const [instructionalTargetSet, masteryTargetSet] = modifiedClasswideTarget.periods[0].values;
      expect(instructionalTargetSet).toEqual(instructionalTarget);
      expect(masteryTargetSet).toEqual(masteryTarget);

      // VERIFY OTHER TARGETS WERE NOT CHANGED
      const otherTargets = allTargets.filter(filterClasswideTargetsOut(highSchoolGrade));
      const otherTargetsBeforeModification = highSchoolAssessment.strands[0].scores[0].targets.filter(
        filterClasswideTargetsOut(highSchoolGrade)
      );
      expect(otherTargets).toMatchObject(otherTargetsBeforeModification);
    });
  });

  describe("getAssessmentsFunctioningStats", () => {
    it("should return default stats when there are no data", () => {
      const result = getAssessmentsFunctioningStats();
      expect(result).toEqual({
        sensitivity: "N/A",
        specificity: "N/A",
        ppv: "N/A",
        npv: "N/A",
        falsePositiveRate: "N/A",
        falseNegativeRate: "N/A",
        positiveLikelihoodRatio: "N/A",
        negativeLikelihoodRatio: "N/A",
        baseRate: "N/A",
        pptp: "N/A",
        nptp: "N/A"
      });
    });

    it("should return appropriate stats when there is low proficiency", () => {
      const baseRate = 0.5;
      const result = getAssessmentsFunctioningStats(
        {
          notProficient: { belowTarget: 100, total: 120 },
          proficient: { belowTarget: 20, total: 40 }
        },
        baseRate
      );
      expect(result).toEqual({
        sensitivity: 0.833,
        specificity: 0.333,
        ppv: 0.833,
        npv: 0.5,
        falsePositiveRate: 0.5,
        falseNegativeRate: 0.167,
        positiveLikelihoodRatio: 1.25,
        negativeLikelihoodRatio: 0.5,
        baseRate: 0.5,
        pptp: 0.556,
        nptp: 0.333
      });
    });

    it("should return appropriate stats when there is high proficiency", () => {
      const baseRate = 0.5;
      const result = getAssessmentsFunctioningStats(
        {
          notProficient: { belowTarget: 20, total: 40 },
          proficient: { belowTarget: 20, total: 120 }
        },
        baseRate
      );
      expect(result).toEqual({
        sensitivity: 0.5,
        specificity: 0.714,
        ppv: 0.5,
        npv: 0.833,
        falsePositiveRate: 0.167,
        falseNegativeRate: 0.5,
        positiveLikelihoodRatio: 1.75,
        negativeLikelihoodRatio: 0.7,
        baseRate: 0.5,
        pptp: 0.636,
        nptp: 0.412
      });
    });

    it("should return appropriate stats when there are no proficient students", () => {
      const baseRate = 0.5;
      const result = getAssessmentsFunctioningStats(
        {
          notProficient: { belowTarget: 20, total: 40 },
          proficient: { belowTarget: 0, total: 0 }
        },
        baseRate
      );
      expect(result).toEqual({
        sensitivity: 0.5,
        specificity: "N/A",
        ppv: 1,
        npv: 0,
        falsePositiveRate: "N/A",
        falseNegativeRate: 0.5,
        positiveLikelihoodRatio: "N/A",
        negativeLikelihoodRatio: "N/A",
        baseRate: 0.5,
        pptp: "N/A",
        nptp: "N/A"
      });
    });

    it("should return appropriate stats when all students are proficient", () => {
      const baseRate = 0.5;
      const result = getAssessmentsFunctioningStats(
        {
          notProficient: { belowTarget: 0, total: 0 },
          proficient: { belowTarget: 20, total: 40 }
        },
        baseRate
      );
      expect(result).toEqual({
        sensitivity: "N/A",
        specificity: 0.333,
        ppv: 0,
        npv: 1,
        falsePositiveRate: 0.5,
        falseNegativeRate: "N/A",
        positiveLikelihoodRatio: "N/A",
        negativeLikelihoodRatio: "N/A",
        baseRate: 0.5,
        pptp: "N/A",
        nptp: "N/A"
      });
    });
  });
});

describe("getNciiValuesStats", () => {
  it("should return default stats when there are no data", () => {
    const result = getNciiValuesStats();
    expect(result).toEqual({
      falsePositiveRate: "N/A",
      falseNegativeRate: "N/A",
      sensitivity: "N/A",
      specificity: "N/A",
      positivePredictivePower: "N/A",
      negativePredictivePower: "N/A"
    });
  });

  it("should return appropriate stats for dataset one", () => {
    const result = getNciiValuesStats({ a: 200, b: 20, c: 25, d: 240 });
    expect(result).toEqual({
      falseNegativeRate: 0.111,
      falsePositiveRate: 0.077,
      negativePredictivePower: 0.906,
      positivePredictivePower: 0.909,
      sensitivity: 0.889,
      specificity: 0.923
    });
  });

  it("should return appropriate stats for dataset two", () => {
    const result = getNciiValuesStats({ a: 0, b: 20, c: 0, d: 240 });
    expect(result).toEqual({
      falseNegativeRate: "N/A",
      falsePositiveRate: 0.077,
      negativePredictivePower: 1,
      positivePredictivePower: 0,
      sensitivity: "N/A",
      specificity: 0.923
    });
  });

  it("should return appropriate stats for dataset three", () => {
    const result = getNciiValuesStats({ a: 0, b: 0, c: 0, d: 0 });
    expect(result).toEqual({
      falsePositiveRate: "N/A",
      falseNegativeRate: "N/A",
      sensitivity: "N/A",
      specificity: "N/A",
      positivePredictivePower: "N/A",
      negativePredictivePower: "N/A"
    });
  });
});

describe("getFinalClasswideAssessmentId", () => {
  const assessmentId = "assessmentId";
  const finalClasswideAssessmentId = "finalClasswideAssessmentId";
  const finalClasswideAssessmentId2 = "finalClasswideAssessmentId2";
  const fallbackFinalClasswideAssessmentId = "fallbackFinalClasswideAssessmentId";
  const studentGroupId = "studentGroupId";
  const studentGroupId2 = "studentGroupId2";
  const assessmentGrowthMap = { [assessmentId]: fallbackFinalClasswideAssessmentId };
  const benchmarkPeriodId = "benchmarkPeriodId";
  const validFinalClasswideAssessmentsIdsFromScreenings = {
    [benchmarkPeriodId]: {
      default: [assessmentId],
      [studentGroupId2]: [finalClasswideAssessmentId2],
      [studentGroupId]: [finalClasswideAssessmentId]
    }
  };

  it("should return null if no valid final classwide and fallback assessmentId is find", () => {
    const result = getFinalClasswideAssessmentId({
      assessmentId,
      studentGroupId,
      assessmentGrowthMap: { [assessmentId]: "notAvailableAssessmentId" },
      validFinalClasswideAssessmentsIdsFromScreenings
    });
    expect(result).toBe(null);
  });

  describe("should return correct final classwide assessmentId", () => {
    it("if no valid final classwide assessmentId is find", () => {
      const result = getFinalClasswideAssessmentId({
        assessmentId,
        studentGroupId,
        assessmentGrowthMap: { [assessmentId]: fallbackFinalClasswideAssessmentId },
        validFinalClasswideAssessmentsIdsFromScreenings: {
          [benchmarkPeriodId]: {
            default: [fallbackFinalClasswideAssessmentId]
          }
        }
      });
      expect(result).toBe(fallbackFinalClasswideAssessmentId);
    });

    it("if no valid final classwide assessmentId is find but a default one is available", () => {
      const result = getFinalClasswideAssessmentId({
        assessmentId,
        studentGroupId,
        assessmentGrowthMap,
        validFinalClasswideAssessmentsIdsFromScreenings: {
          [benchmarkPeriodId]: {
            default: [assessmentId]
          }
        }
      });
      expect(result).toBe(assessmentId);
    });

    it("if there are no final classwide assessmentIds for a specific student group but for some group in a grade", () => {
      const result = getFinalClasswideAssessmentId({
        assessmentId,
        studentGroupId,
        assessmentGrowthMap,
        validFinalClasswideAssessmentsIdsFromScreenings: {
          [benchmarkPeriodId]: {
            default: ["defaultAssessmentId"],
            [studentGroupId2]: [assessmentId]
          }
        }
      });
      expect(result).toBe(assessmentId);
    });
  });
});

describe("calculateYearlyGrowthStats", () => {
  const studentGroupId = "studentGroupId";
  const assessmentId = "assessmentId";
  const relatedAssessmentId = "relatedAssessmentId";
  const assessmentName = "assessmentName";
  const benchmarkPeriodId = "benchmarkPeriodId";
  const assessmentGrowthMap = { [assessmentId]: relatedAssessmentId };
  const validFinalClasswideAssessmentsIdsFromScreenings = {
    [benchmarkPeriodId]: {
      default: [relatedAssessmentId],
      [studentGroupId]: [relatedAssessmentId]
    }
  };

  it("should not calculate stats when there are no screening and final classwide intervention data", () => {
    const finalAssessments = {};
    const studentIdsPassingMeasure = [];
    const studentIdsAssessed = [];
    const statsAccumulator = {
      [benchmarkPeriodId]: {}
    };
    calculateYearlyGrowthStats({
      finalAssessments,
      studentGroupId,
      assessmentId,
      assessmentName,
      benchmarkPeriodId,
      assessmentGrowthMap,
      statsAccumulator,
      studentIdsPassingMeasure,
      studentIdsAssessed,
      validFinalClasswideAssessmentsIdsFromScreenings
    });
    expect(statsAccumulator).toEqual({
      [benchmarkPeriodId]: {}
    });
  });

  it("should calculate stats correctly when students don't have screening results but only final classwide intervention data", () => {
    const studentIdsPassingMeasure = [];
    const studentIdsAssessed = [];
    const finalAssessments = {
      [studentGroupId]: {
        [relatedAssessmentId]: {
          measures: [
            {
              studentResults: [
                { status: "COMPLETE", studentId: "studentId1", meetsTarget: false },
                { status: "COMPLETE", studentId: "studentId2", meetsTarget: true },
                { status: "COMPLETE", studentId: "studentId3", meetsTarget: false }
              ]
            }
          ]
        }
      }
    };
    const statsAccumulator = {
      [benchmarkPeriodId]: {}
    };
    calculateYearlyGrowthStats({
      finalAssessments,
      studentGroupId,
      assessmentId,
      assessmentName,
      benchmarkPeriodId,
      assessmentGrowthMap,
      statsAccumulator,
      studentIdsPassingMeasure,
      studentIdsAssessed,
      validFinalClasswideAssessmentsIdsFromScreenings
    });
    expect(statsAccumulator).toEqual({
      [benchmarkPeriodId]: {
        [relatedAssessmentId]: {
          assessmentName,
          numberOfStudentsProficient: 0,
          percentOfStudentsProficient: 0,
          totalStudentsAssessedOnAllMeasures: 0
        }
      }
    });
  });

  it("should calculate stats correctly when students have screening results and final classwide intervention data", () => {
    const studentIdsPassingMeasure = ["studentId1"];
    const studentIdsAssessed = ["studentId1", "studentId2", "studentId3"];
    const finalAssessments = {
      [studentGroupId]: {
        [relatedAssessmentId]: {
          measures: [
            {
              studentResults: [
                { status: "COMPLETE", studentId: "studentId1", meetsTarget: false },
                { status: "COMPLETE", studentId: "studentId2", meetsTarget: true },
                { status: "COMPLETE", studentId: "studentId3", meetsTarget: false }
              ]
            }
          ]
        }
      }
    };
    const statsAccumulator = {
      [benchmarkPeriodId]: {}
    };
    calculateYearlyGrowthStats({
      finalAssessments,
      studentGroupId,
      assessmentId,
      assessmentName,
      benchmarkPeriodId,
      assessmentGrowthMap,
      statsAccumulator,
      studentIdsPassingMeasure,
      studentIdsAssessed,
      validFinalClasswideAssessmentsIdsFromScreenings
    });
    expect(statsAccumulator).toEqual({
      [benchmarkPeriodId]: {
        [relatedAssessmentId]: {
          assessmentName,
          numberOfStudentsProficient: 1,
          percentOfStudentsProficient: 33.333,
          totalStudentsAssessedOnAllMeasures: 3
        }
      }
    });
  });
});

const getExpectedNewTarget = (newTargetName = null) => ({
  grade,
  periods: [
    {
      name: "Fall",
      benchmarkPeriodId: fallPeriodId,
      values: [20, 40, 300]
    },
    {
      name: "Spring",
      benchmarkPeriodId: springPeriodId,
      values: [20, 40, 300]
    },
    {
      name: "Winter",
      benchmarkPeriodId: winterPeriodId,
      values: [20, 40, 300]
    }
  ],
  ...(newTargetName ? { assessmentType: newTargetName } : {})
});
