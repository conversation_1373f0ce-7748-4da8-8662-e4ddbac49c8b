import React, { Component } from "react";
import { withRouter } from "react-router-dom";

class InformationStatement extends Component {
  goToStaffEnrollment() {
    this.props.history.push("/self-enrollment");
  }

  render() {
    return (
      <div>
        <div className="information-box mt-1">
          <p>
            <b>SpringMath</b> is an excellent tool for assessing and providing interventions to individual or small
            groups of students.
          </p>
          <p>
            For only <b>$5</b> per season or <b>$15</b> per year you will receive all the assessments and interventions
            you need. It is easy to set up your account and pay by credit card.
          </p>
          <p>
            On <b>August 1st</b>, when your student moves up to a new grade level you will need to re-enroll for the
            next school year.
          </p>
          <div className="text-center">
            <button type="button" className="btn btn-success btn-lg" onClick={this.goToStaffEnrollment}>
              Click here to get started
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default withRouter(InformationStatement);
