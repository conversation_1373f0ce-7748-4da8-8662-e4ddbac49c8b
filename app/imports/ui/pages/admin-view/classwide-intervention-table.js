import React, { Component } from "react";
import PropTypes from "prop-types";
import { get, uniq } from "lodash";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { <PERSON> } from "react-router-dom";
import moment from "moment";
import { <PERSON><PERSON> } from "react-bootstrap";

import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";
import { Loading } from "../../components/loading";
import ClasswideOverviewItem from "./classwide-overview-item.jsx";
import { isOnPrintPage } from "../../utilities";
import ClasswideDetailModal from "../student-detail/classwide-detail-modal";

const renderNoGroupsMessage = groupType => (
  <h3 className="stamped">{`No students in this grade are currently practicing ${groupType} interventions.`}</h3>
);

export default class ClasswideInterventionTable extends Component {
  constructor(props) {
    super(props);

    this.state = {
      sortProperty: "grade",
      sortOrder: 1,
      isDataFetching: false,
      groupOwners: [],
      groupOwnerIds: uniq(props.studentGroups.map(sg => sg.ownerIds[0] || "")),
      shouldShowDetailModal: false
    };
  }

  getGroupOwners = () => {
    this.setState({ isDataFetching: true });
    Meteor.call("users:getGroupOwners", this.state.groupOwnerIds, (err, groupOwners) => {
      if (err) {
        Alert.error("Error getting group owners");
        return;
      }

      this.setState({ groupOwners, isDataFetching: false });
    });
  };

  componentDidMount() {
    this.getGroupOwners();
  }

  classwideGroupsExist() {
    return this.props.studentGroups.find(group => group.currentClasswideSkill);
  }

  getClasswideOverviewData = ({ classwideAssessmentResults, group }) => {
    const classwideAssessmentResult =
      classwideAssessmentResults.find(
        ar => group.currentAssessmentResultIds && group.currentAssessmentResultIds.includes(ar._id)
      ) || {};
    let { lastScoreUpdatedAt } = classwideAssessmentResult;
    if (!lastScoreUpdatedAt) {
      const previousAssessmentResult = classwideAssessmentResults.find(
        ar => ar._id === classwideAssessmentResult.previousAssessmentResultId
      );
      lastScoreUpdatedAt = previousAssessmentResult && previousAssessmentResult.lastScoreUpdatedAt;

      if (!lastScoreUpdatedAt) {
        const lastCompletedClasswideIntervention = group.history && group.history.find(ar => ar.type === "classwide");
        lastScoreUpdatedAt = get(lastCompletedClasswideIntervention, "whenEnded.on");
      }
    }

    return {
      classwideAssessmentResult,
      lastScoreUpdatedAt
    };
  };

  getAugmentedStudentGroups = () =>
    this.props.studentGroups.map(sg => {
      const user = this.state.groupOwners.find(owner => owner._id === sg.ownerIds[0]);
      return {
        ...sg,
        ownerName: user ? `${user.profile.name.last} ${user.profile.name.first.slice(0, 1)}.` : null
      };
    });

  renderProgramEvaluationTable = studentGroups => {
    const classwideAssessmentResults = this.props.assessmentResults.filter(ar => ar.type === "classwide");
    let previousGrade = "";
    const isPrinting = isOnPrintPage();
    return (
      <div className="tblIntvSummaryTable" data-testid={"classwideInterventionTable"}>
        <div className={`row row-margin-fixed rowIndvSummaryHeading${isPrinting ? " font-13" : ""}`}>
          <div className="col-1" style={{ textAlign: "center" }}>
            Grade
          </div>
          <div className="col-3" style={{ textAlign: "center" }}>
            Teacher (Group)
          </div>
          <div className="col-1 text-center">
            <span className="text-nowrap">Number of </span>
            {isPrinting ? <br /> : null}
            <span className="text-nowrap">students in class</span>
            <br />
            <small>{moment().format("L")}</small>
          </div>
          {!this.props.isHighSchool && (
            <div className="col-2">
              Number of students <br /> screened
            </div>
          )}
          <div className={`col-${this.props.isHighSchool ? "4" : "2"}`}>
            Intervention <br /> Progress
          </div>
          <div className="col-1">
            Intervention <br /> Consistency
          </div>
          <div className="col-1">
            Average Weeks <br /> Per Skill
          </div>
          <div className="col-1">% of Scores Increasing</div>
        </div>

        <div className="individual-interventions-group">
          {studentGroups.map(group => {
            // Only include groups with in-progress or completed classwide interventions
            if (!group.currentClasswideSkill) {
              return null;
            }
            const { classwideAssessmentResult, lastScoreUpdatedAt } = this.getClasswideOverviewData({
              classwideAssessmentResults,
              group
            });
            const individualInterventionAssessmentResults = this.props.assessmentResults.filter(
              ar => ar.type === "individual" && ar.status === "OPEN" && ar.studentGroupId === group._id
            );
            const isNewGrade = previousGrade && previousGrade !== group.grade;
            previousGrade = group.grade;
            return (
              <div
                key={group._id}
                className={`${isNewGrade ? "grade-separator" : ""}${isPrinting ? " avoid-page-break-inside" : ""}`}
              >
                <ClasswideOverviewItem
                  classwideStats={this.props.allClasswideStats?.find(cws => cws.studentGroupId === group._id)}
                  ownerIds={group.ownerIds}
                  group={group}
                  currentBMPeriod={this.props.currentBMPeriod}
                  assessmentResult={classwideAssessmentResult}
                  lastScoreUpdatedAt={lastScoreUpdatedAt}
                  grade={group.grade}
                  studentGroupEnrollments={this.props.studentGroupEnrollments}
                  isSchoolOverview={this.props.isSchoolOverview}
                  isHighSchool={this.props.isHighSchool}
                  individualInterventionAssessmentResults={individualInterventionAssessmentResults}
                  type={this.props.type}
                />
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  renderGrowthTabTable = studentGroups => {
    const classwideAssessmentResults = this.props.assessmentResults.filter(ar => ar.type === "classwide");
    const group = studentGroups[0];
    const classwideStats = this.props.allClasswideStats?.find(cws => cws.studentGroupId === group._id);
    // Only include groups with in-progress or completed classwide interventions
    if (!group.currentClasswideSkill) {
      return null;
    }

    const { classwideAssessmentResult, lastScoreUpdatedAt } = this.getClasswideOverviewData({
      classwideAssessmentResults,
      group
    });

    const individualInterventionAssessmentResults = this.props.assessmentResults.filter(
      ar => ar.type === "individual" && ar.status === "OPEN" && ar.studentGroupId === group._id
    );
    const enterClasswideScoreLink = `/${group.orgid}/site/${group.siteId}/student-groups/${group._id}/classwide`;
    const isPrinting = isOnPrintPage();
    return (
      <section className={`studentListIndividualInterventionList${isPrinting ? " m-r-25" : ""}`}>
        {!isPrinting ? (
          <React.Fragment>
            <span className="pull-right">
              <Link className="btn btn-primary m-r-5" data-testid="classwideDetails" to={enterClasswideScoreLink}>
                Classwide Intervention Detail
              </Link>
              <span>
                {this.state.shouldShowDetailModal ? (
                  <ClasswideDetailModal
                    onCloseModal={() => {
                      this.setState({ shouldShowDetailModal: false });
                    }}
                    showModal={this.state.shouldShowDetailModal}
                    studentGroupId={group._id}
                    componentContext="groupDetail"
                  />
                ) : (
                  <Button
                    className="btn btn-primary"
                    data-testid="groupDetail"
                    onClick={() => {
                      this.setState({ shouldShowDetailModal: true });
                    }}
                  >
                    Individual Student Skill Progress
                  </Button>
                )}
              </span>
            </span>
          </React.Fragment>
        ) : null}{" "}
        <h2 className="no-bottom-border text-start">
          <i className="fa fa-users" />
          <span> Classwide Intervention </span>
        </h2>
        <div className="row header-row">
          <div className="col-3 header"># of students needing / getting / completed individual intervention</div>
          <div className="col-2 header">
            Most Recent <br /> Score Entry
          </div>
          <div className="col-3 header">
            Intervention <br /> Progress
          </div>
          <div className="col-2 header">
            Intervention <br /> Consistency
          </div>
          <div className="col-1 header">
            Average Weeks <br /> per Skill
          </div>
          <div className="col-1 header">% of Scores Increasing</div>
        </div>
        <div className="individual-interventions-group">
          <ClasswideOverviewItem
            classwideStats={classwideStats}
            ownerIds={group.ownerIds}
            group={group}
            currentBMPeriod={this.props.currentBMPeriod}
            assessmentResult={classwideAssessmentResult}
            lastScoreUpdatedAt={lastScoreUpdatedAt}
            grade={group.grade}
            studentGroupEnrollments={this.props.studentGroupEnrollments}
            isSchoolOverview={this.props.isSchoolOverview}
            isHighSchool={this.props.isHighSchool}
            individualInterventionAssessmentResults={individualInterventionAssessmentResults}
            type="growth"
          />
        </div>
      </section>
    );
  };

  renderClasswideSummaryTable = studentGroups => {
    const {
      assessmentResults,
      isSchoolOverview,
      isHighSchool,
      allClasswideStats,
      currentBMPeriod,
      studentGroupEnrollments
    } = this.props;
    const classwideAssessmentResults = assessmentResults.filter(ar => ar.type === "classwide");
    const isPrinting = isOnPrintPage();
    let previousGrade = "";
    const interventionProgressColumnWidth = this.props.isHighSchool ? 3 : 2;
    return (
      <div className={`tblIntvSummaryTable ${isPrinting ? "isPrinting page-break-after" : ""}`}>
        <div className="row rowIndvSummaryHeading sticky-header">
          {isSchoolOverview && !isHighSchool && <div className="col-1 text-center">Grade</div>}
          <div className="col-2 text-center">Teacher (Group)</div>
          {isSchoolOverview ? (
            <div className="col-1"># of students needing&nbsp;/ getting&nbsp;/ completed individual intervention</div>
          ) : (
            <div className="col-1">Total Students in Interventions</div>
          )}
          <div className="col-2">
            Most Recent <br /> Score Entry
          </div>
          <div className={`col-${interventionProgressColumnWidth}`}>
            Intervention <br /> Progress
          </div>
          <div className={`${isSchoolOverview ? "col-1" : "col-2"}`}>
            Intervention <br /> Consistency
          </div>
          <div className="col-1">
            Average Weeks <br /> Per Skill
          </div>
          {isSchoolOverview ? (
            <>
              <div className="col-1">
                Coaching Visit <br /> Recommended
              </div>
              <div className="col-1">% of Scores Increasing</div>
            </>
          ) : (
            <div className="col-2 input-date">
              Calculations <br /> As Of Date
            </div>
          )}
        </div>

        <div className="individual-interventions-group">
          {studentGroups.map(group => {
            // Only include groups with in-progress or completed classwide interventions
            if (!group.currentClasswideSkill) {
              return null;
            }
            const { classwideAssessmentResult, lastScoreUpdatedAt } = this.getClasswideOverviewData({
              classwideAssessmentResults,
              group
            });
            const individualInterventionAssessmentResults = assessmentResults.filter(
              ar => ar.type === "individual" && ar.status === "OPEN" && ar.studentGroupId === group._id
            );
            const isNewGrade = previousGrade && previousGrade !== group.grade;
            previousGrade = group.grade;
            return (
              <div
                key={group._id}
                className={`${isNewGrade ? "grade-separator" : ""}${isPrinting ? " avoid-page-break-inside" : ""}`}
              >
                <ClasswideOverviewItem
                  classwideStats={allClasswideStats?.find(cws => cws.studentGroupId === group._id)}
                  ownerIds={group.ownerIds}
                  group={group}
                  currentBMPeriod={currentBMPeriod}
                  assessmentResult={classwideAssessmentResult}
                  lastScoreUpdatedAt={lastScoreUpdatedAt}
                  grade={group.grade}
                  studentGroupEnrollments={studentGroupEnrollments}
                  isSchoolOverview={isSchoolOverview}
                  isHighSchool={isHighSchool}
                  individualInterventionAssessmentResults={individualInterventionAssessmentResults}
                />
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  render() {
    if (this.props.loading || this.state.isDataFetching) {
      return <Loading />;
    }
    if (!this.classwideGroupsExist()) {
      return renderNoGroupsMessage("classwide");
    }
    const { sortProperty, sortOrder } = this.state;
    const studentGroups = sortProperty
      ? sortByPropertyFor({
          list: this.getAugmentedStudentGroups(),
          paths: ["grade", "ownerName"],
          order: sortOrder
        })
      : this.getAugmentedStudentGroups();

    switch (this.props.type) {
      case "evaluation":
        return this.renderProgramEvaluationTable(studentGroups);
      case "growth":
        return this.renderGrowthTabTable(studentGroups);
      default:
        return this.renderClasswideSummaryTable(studentGroups);
    }
  }
}

ClasswideInterventionTable.propTypes = {
  allClasswideStats: PropTypes.array,
  assessmentResults: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  grade: PropTypes.string,
  gradeLevelScreeningProgress: PropTypes.array,
  growthChartItems: PropTypes.object,
  headerTitle: PropTypes.string,
  inActiveSchoolYear: PropTypes.bool,
  isHighSchool: PropTypes.bool,
  isHighSchoolGroup: PropTypes.bool,
  isSchoolOverview: PropTypes.bool,
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  percentMeetingTargetByClassAndSeason: PropTypes.array,
  ruleSkillsCount: PropTypes.number,
  siteName: PropTypes.string,
  studentGroupEnrollments: PropTypes.array,
  studentGroups: PropTypes.array,
  students: PropTypes.array,
  type: PropTypes.string
};

ClasswideInterventionTable.defaultProps = {
  isSchoolOverview: false,
  assessmentResults: []
};
