import React from "react";
import PropTypes from "prop-types";
import { getLegendText } from "/imports/ui/utilities";

const getPeriodIcon = period => {
  const legendText = getLegendText(period);
  return (
    <div className="d-flex gap-1 align-items-center">
      <span className={`dot growth-legend-item ${period}-icon`} />
      {legendText}
    </div>
  );
};

export const GrowthChartLegend = ({ comparisonPeriod = "fall" }) => {
  let icons = [];
  if (comparisonPeriod !== "all") {
    icons.push(getPeriodIcon("winter"));
    if (comparisonPeriod === "fall") {
      icons.unshift(getPeriodIcon("fall"));
    } else {
      icons.push(getPeriodIcon("spring"));
    }
  } else {
    icons = [getPeriodIcon("fall"), getPeriodIcon("winter"), getPeriodIcon("spring")];
  }
  icons.push(getPeriodIcon("classwide"));
  return (
    <div className="d-flex justify-content-center gap-3 font-13">
      {icons.map((icon, index) => (
        <span key={`legend_icon_winter_${comparisonPeriod}_${index}`}>{icon}</span>
      ))}
    </div>
  );
};

GrowthChartLegend.propTypes = {
  comparisonPeriod: PropTypes.string
};
