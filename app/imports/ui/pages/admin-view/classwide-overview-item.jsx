import { Meteor } from "meteor/meteor";
import React from "react";
import PropTypes from "prop-types";
import Al<PERSON> from "react-s-alert";
import moment from "moment";
import { Link } from "react-router-dom";
import CalculateAsOfDate from "../../components/admin-view/calculateAsOfDate";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../routing/AppDataContext";
import { getMeteorUserSync, translateBenchmarkPeriod } from "/imports/api/utilities/utilities";
import { SCORE_INCREASING_METRIC_THRESHOLD } from "/imports/api/constants";

function skillsCompletePercentage(numberOfSkillsWorkedOn, numberOfSkillsInTree) {
  let completePercent = 0;
  if (numberOfSkillsWorkedOn && numberOfSkillsWorkedOn > 0 && numberOfSkillsInTree > 0) {
    completePercent = `${((numberOfSkillsWorkedOn / numberOfSkillsInTree) * 100).toFixed(0)}%`;
  }
  return completePercent;
}

export function getOwnerName(ownerIds) {
  if (ownerIds) {
    const user = Meteor.users.findOne({ _id: ownerIds[0] });
    if (user) {
      return `${user.profile.name.first.slice(0, 1)} ${user.profile.name.last}`;
    }
  }
  return undefined;
}

export default class ClasswideOverviewItem extends React.Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);

    this.state = {
      numberOfCompletedInterventions: 0,
      classwideStats: props.classwideStats
    };
  }

  componentDidMount() {
    Meteor.call(
      "Interventions:getNumberOfCompletedInterventionsInGroup",
      { siteId: this.props.group.siteId, studentGroupId: this.props.group._id },
      (err, resp) => {
        if (!err) {
          this.setState({ numberOfCompletedInterventions: resp });
        } else {
          Alert.error("Error getting number of completed interventions");
        }
      }
    );
  }

  updateStats = classwideStatsAsOfDate => {
    const studentGroup = { ...this.props.group, classwideStatsAsOfDate };
    Meteor.call("CalculateClasswideStats", studentGroup, false, (err, res) => {
      if (err) {
        console.log(`Failed to calculate classwide stats`);
      } else {
        this.setState({ classwideStats: res });
      }
    });
  };

  isCoachingVisitRecommended = scoringTrendValue => {
    const { group, classwideStats } = this.props;
    const { interventionConsistency, numberOfWeeksPracticingCurrentSkill } = classwideStats || {};

    const curStudentGroupClasswideHistory = group?.history?.some(h => h.type === "classwide");
    const isLowConsistencyReason =
      typeof interventionConsistency === "number" && interventionConsistency < 80 && curStudentGroupClasswideHistory;

    const isLingeringReason = numberOfWeeksPracticingCurrentSkill && numberOfWeeksPracticingCurrentSkill > 4;

    const isLowScoringTrend = scoringTrendValue ? scoringTrendValue < SCORE_INCREASING_METRIC_THRESHOLD : false;

    return isLingeringReason || isLowConsistencyReason || isLowScoringTrend;
  };

  determineScoringTrend = scoringTrendValue => (scoringTrendValue !== undefined ? `${scoringTrendValue}%` : "N/A");

  renderProgramEvaluationRow = ({
    groupStats = {},
    isInterventionComplete,
    interventionProgressStyle,
    groupHasInterventionConsistency,
    scoringTrendValue
  }) => {
    const studentGroupBenchmarkHistory = this.props.group.history.find(item => item.type === "benchmark");
    let numberOfStudentsAssessedClassName = "";
    let studentsScreenedWithLabel = "N/A";
    if (studentGroupBenchmarkHistory) {
      const currentBenchmarkHistoryItem = studentGroupBenchmarkHistory.assessmentResultMeasures.find(
        arm => arm.assessmentResultType === "benchmark"
      );
      const latestBenchmarkPeriodName = translateBenchmarkPeriod(currentBenchmarkHistoryItem?.benchmarkPeriodId);
      const numberOfStudentsAssessed = currentBenchmarkHistoryItem?.totalStudentsAssessed || 0;
      if (groupStats?.numberOfStudentsInGroup && (numberOfStudentsAssessed / groupStats.numberOfStudentsInGroup) * 100 < 90) {
        numberOfStudentsAssessedClassName = " text-danger";
      }
      studentsScreenedWithLabel = (
        <React.Fragment>
          {numberOfStudentsAssessed}
          {Object.keys(latestBenchmarkPeriodName).length ? (
            <small>(Most recent - {latestBenchmarkPeriodName.title})</small>
          ) : null}
        </React.Fragment>
      );
    }

    const renderWeeksWithScores = stats => {
      if (stats?.numberOfWeeksActive > 0) {
        return (
          <React.Fragment>
            <span className="text-nowrap">
              {stats?.numberOfWeeksWithScoresEntered} of {stats?.numberOfWeeksActive}
            </span>{" "}
            weeks with scores
          </React.Fragment>
        );
      }
      return null;
    };

    return (
      <div className="row rowIndvSummary classwideSummary" data-testid="rowIndvSummary">
        <div className="col-1" style={{ textAlign: "center" }}>
          {Number.isNaN(Number(this.props.grade)) ? this.props.grade : Number(this.props.grade)}
        </div>
        <div className={"col-3 text-start"}>
          {this.props.ownerIds ? `${this.props.group.ownerName} (${this.props.group.name})` : this.props.group.name}
        </div>
        <div className={`col-1 ${groupStats?.numberOfStudentsInGroup < 11 ? "text-danger" : ""}`}>
          {groupStats?.numberOfStudentsInGroup || "N/A"}
        </div>
        {!this.props.isHighSchool && (
          <div className={`col-2${numberOfStudentsAssessedClassName}`}>{studentsScreenedWithLabel}</div>
        )}
        <div className={`col-${this.props.isHighSchool ? "4" : "2"}`}>
          {!isInterventionComplete ? (
            <div className="intervention-progress">
              <div className="progress">
                <div
                  className="animated progress-animated progress-bar progress-bar-success"
                  role="progressbar"
                  aria-valuenow="81%"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style={interventionProgressStyle}
                />
              </div>
              <small
                className={`text-nowrap${
                  groupStats?.numberOfSkillsPracticed === 0 ? " text-danger text-underline" : ""
                }`}
              >
                {groupStats?.numberOfSkillsPracticed || 0} of {groupStats?.numberOfSkillsInClasswideTree || 0} Skills
              </small>
            </div>
          ) : (
            <span className="text-success">All interventions complete. Excellent!</span>
          )}
        </div>
        <div
          className={`${this.props.isSchoolOverview ? "col-1" : "col-2"} rowIndvStudents-consistency${
            groupHasInterventionConsistency && groupStats?.interventionConsistency < 80 ? " text-danger" : ""
          }`}
        >
          {groupHasInterventionConsistency ? `${groupStats?.interventionConsistency}%` : "N/A"}
          <small>{renderWeeksWithScores(groupStats)}</small>
        </div>
        <div className={"col-1"}>
          {groupStats?.numberOfWeeksActive && groupStats?.averageWeeksPerSkill ? groupStats?.averageWeeksPerSkill : "N/A"}
        </div>
        <div
          className={`col-1 text-center ${scoringTrendValue < SCORE_INCREASING_METRIC_THRESHOLD ? "text-danger" : ""}`}
          data-testid={`scoringTrend_${this.props.group.name}`}
        >
          {this.determineScoringTrend(scoringTrendValue)}
        </div>
      </div>
    );
  };

  renderGrowthTabRow = ({
    groupStats,
    isInterventionComplete,
    interventionProgressStyle,
    groupHasInterventionConsistency,
    needingGettingCompletedColumnData,
    group,
    scoreIncreasingMetric,
    lastScoreUpdatedAt
  }) => {
    return (
      <div className={`row rowIndvSummary classwideSummary`} data-testid="rowIndvSummary">
        <div className="col-3">{needingGettingCompletedColumnData}</div>
        <div className="col-2">{lastScoreUpdatedAt}</div>
        <div className="col-3">
          {!isInterventionComplete ? (
            <div className="intervention-progress">
              <div className="progress">
                <div
                  className="animated progress-animated progress-bar progress-bar-success"
                  role="progressbar"
                  aria-valuenow="81%"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style={interventionProgressStyle}
                />
              </div>
              <small>
                Intervention Skill {groupStats?.numberOfSkillsPracticed || 0} of {groupStats?.numberOfSkillsInClasswideTree || 0}
              </small>
            </div>
          ) : (
            <span className="text-success">Classwide interventions complete. Excellent!</span>
          )}
        </div>
        {!isInterventionComplete && (
          <React.Fragment>
            <div
              className={`col-2 rowIndvStudents-consistency${
                groupHasInterventionConsistency && groupStats?.interventionConsistency < 80 ? " text-danger" : ""
              }`}
            >
              {groupHasInterventionConsistency ? `${groupStats?.interventionConsistency}%` : "N/A"}
              <small>
                {groupStats?.numberOfWeeksActive > 0 &&
                  `${groupStats?.numberOfWeeksWithScoresEntered} of ${groupStats?.numberOfWeeksActive} weeks with scores`}
              </small>
            </div>

            <div className={"col-1"}>
              {groupStats?.numberOfWeeksActive && groupStats?.averageWeeksPerSkill
                ? groupStats?.averageWeeksPerSkill
                : "N/A"}
            </div>

            <div
              className={`col-1 text-center ${
                scoreIncreasingMetric && parseFloat(scoreIncreasingMetric) < SCORE_INCREASING_METRIC_THRESHOLD
                  ? " text-danger"
                  : ""
              }`}
              data-testid={`scoringTrend_${group.name}`}
            >
              {scoreIncreasingMetric}
            </div>
          </React.Fragment>
        )}
      </div>
    );
  };

  renderClasswideSummaryRow = ({
    groupStats,
    isInterventionComplete,
    interventionProgressStyle,
    groupHasInterventionConsistency,
    needingGettingCompletedColumnData,
    group,
    scoreIncreasingMetric,
    lastScoreUpdatedAt,
    scoringTrendValue,
    isSchoolOverview,
    enterClasswideScoreLink,
    grade,
    isHighSchool
  }) => {
    const isSchoolOverviewAndOngoingIntervention = !isInterventionComplete && this.props.isSchoolOverview;
    const interventionProgressColumnWidth = this.props.isHighSchool ? 3 : 2;
    const isAcquisitionIntervention = group.currentClasswideSkill?.message?.messageCode === "2";

    return (
      <div className="row rowIndvSummary classwideSummary" data-testid="rowIndvSummary">
        {isSchoolOverview && !isHighSchool ? (
          <div className="col-1" style={{ textAlign: "center" }}>
            {Number.isNaN(Number(grade)) ? grade : Number(grade)}
          </div>
        ) : null}
        <div className={"col-2 text-start"}>
          <Link to={enterClasswideScoreLink}>
            {group.ownerName ? `${group.ownerName} (${group.name})` : group.name}
          </Link>
        </div>
        {!isSchoolOverview && <div className="col-1">{groupStats?.numberOfStudentsInGroup || 0}</div>}
        {isSchoolOverview && <div className="col-1 text-nowrap">{needingGettingCompletedColumnData}</div>}
        <div className="col-2">{lastScoreUpdatedAt}</div>
        <div className={`col-${interventionProgressColumnWidth}`}>
          {!isInterventionComplete ? (
            <div className="intervention-progress">
              <div className="progress">
                <div
                  className="animated progress-animated progress-bar progress-bar-success"
                  role="progressbar"
                  aria-valuenow="81%"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style={interventionProgressStyle}
                />
              </div>
              <small className={isAcquisitionIntervention ? "text-special" : ""}>
                Intervention Skill {groupStats?.numberOfSkillsPracticed || 0} of {groupStats?.numberOfSkillsInClasswideTree || 0}
              </small>
            </div>
          ) : (
            <span className="text-success">Classwide interventions complete. Excellent!</span>
          )}
        </div>
        {!isInterventionComplete && (
          <div
            className={`${isSchoolOverview ? "col-1" : "col-2"} rowIndvStudents-consistency${
              groupHasInterventionConsistency && groupStats?.interventionConsistency < 80 ? " text-danger" : ""
            }`}
          >
            {groupHasInterventionConsistency ? `${groupStats?.interventionConsistency}%` : "N/A"}
            <small>
              {groupStats?.numberOfWeeksActive > 0 &&
                `${groupStats?.numberOfWeeksWithScoresEntered} of ${groupStats?.numberOfWeeksActive} weeks with scores`}
            </small>
          </div>
        )}
        {!isInterventionComplete && (
          <div className={"col-1"}>
            {groupStats?.numberOfWeeksActive && groupStats?.averageWeeksPerSkill
              ? groupStats?.averageWeeksPerSkill
              : "N/A"}
          </div>
        )}
        {isSchoolOverviewAndOngoingIntervention &&
          (this.isCoachingVisitRecommended(scoringTrendValue) ? (
            <div className="col-1 text-center text-danger">Yes</div>
          ) : (
            <div className="col-1 text-center">No</div>
          ))}
        {isSchoolOverviewAndOngoingIntervention && (
          <div
            className={`col-1 text-center ${
              scoreIncreasingMetric && parseFloat(scoreIncreasingMetric) < SCORE_INCREASING_METRIC_THRESHOLD
                ? " text-danger"
                : ""
            }`}
            data-testid={`scoringTrend_${group.name}`}
          >
            {scoreIncreasingMetric}
          </div>
        )}
        {!isInterventionComplete && !this.props.isSchoolOverview && (
          <div className={"col-2 input-group-date"}>
            <CalculateAsOfDate
              {...this.props}
              type="classwide"
              benchmarkPeriodId={group.currentClasswideSkill.benchmarkPeriodId}
              updateStats={this.updateStats}
            />
          </div>
        )}
      </div>
    );
  };

  render() {
    const {
      group,
      lastScoreUpdatedAt,
      studentGroupEnrollments,
      type,
      individualInterventionAssessmentResults,
      isSchoolOverview,
      isHighSchool,
      grade
    } = this.props;
    const { numberOfCompletedInterventions, classwideStats } = this.state;
    const groupStats = classwideStats;
    const enterClasswideScoreLink = `/${group.orgid}/site/${group.siteId}/student-groups/${group._id}/classwide`;
    const interventionProgressStyle = {
      width:
        groupStats &&
        skillsCompletePercentage(groupStats?.numberOfSkillsPracticed || 0, groupStats?.numberOfSkillsInClasswideTree || 0)
    };
    const groupHasInterventionConsistency =
      groupStats &&
      groupStats?.numberOfWeeksActive &&
      typeof groupStats?.interventionConsistency === "number" &&
      groupStats?.interventionConsistency >= 0;
    const lastScoreUpdatedAtProp = (lastScoreUpdatedAt && moment(lastScoreUpdatedAt).format("L")) || "N/A";
    const isProgramEvaluation =
      window.location.pathname.includes("program-evaluation") || window.location.pathname.includes("ProgramEvaluation");
    const userWithoutSelectedSchoolYear = getMeteorUserSync();
    delete userWithoutSelectedSchoolYear?.profile?.selectedSchoolYear;
    const isPriorYear =
      (this.context.schoolYear &&
        this.context.schoolYear !== utils.getCurrentSchoolYear(userWithoutSelectedSchoolYear, group.orgid)) ||
      isProgramEvaluation;
    const isInterventionComplete =
      (groupStats && groupStats?.numberOfSkillsPracticed === groupStats?.numberOfSkillsInClasswideTree) ||
      (isPriorYear && !group.currentClasswideSkill?.assessmentId);
    const individualInterventionQueue = group.individualInterventionQueue || [];
    const scoringTrendValue = utils.getScoringTrend({
      type: "classwide",
      group,
      numStudents: classwideStats?.numberOfStudentsInGroup,
      studentGroupEnrollments
    });

    const idsOfStudentsInIndividualInterventions = individualInterventionAssessmentResults.map(
      ({ studentId }) => studentId
    );

    const idsOfStudentsRecommendedForIndividualInterventions = individualInterventionQueue.filter(
      studentId => !idsOfStudentsInIndividualInterventions.includes(studentId)
    );

    const numberOfStudentsNeedingIndividualIntervention =
      idsOfStudentsInIndividualInterventions.length +
      idsOfStudentsRecommendedForIndividualInterventions.length +
      numberOfCompletedInterventions;

    const needingGettingCompletedColumnData = `${numberOfStudentsNeedingIndividualIntervention} / ${individualInterventionAssessmentResults.length +
      numberOfCompletedInterventions} / ${numberOfCompletedInterventions}`;
    const scoreIncreasingMetric = this.determineScoringTrend(scoringTrendValue);
    switch (type) {
      case "evaluation":
        return this.renderProgramEvaluationRow({
          groupStats,
          isInterventionComplete,
          interventionProgressStyle,
          groupHasInterventionConsistency,
          scoringTrendValue
        });
      case "growth":
        return this.renderGrowthTabRow({
          groupStats,
          isInterventionComplete,
          interventionProgressStyle,
          groupHasInterventionConsistency,
          needingGettingCompletedColumnData,
          group,
          scoreIncreasingMetric,
          lastScoreUpdatedAt: lastScoreUpdatedAtProp,
          enterClasswideScoreLink
        });

      default:
        return this.renderClasswideSummaryRow({
          groupStats,
          isInterventionComplete,
          interventionProgressStyle,
          groupHasInterventionConsistency,
          needingGettingCompletedColumnData,
          group,
          scoreIncreasingMetric,
          lastScoreUpdatedAt: lastScoreUpdatedAtProp,
          scoringTrendValue,
          isSchoolOverview,
          enterClasswideScoreLink,
          grade,
          isHighSchool
        });
    }
  }
}

ClasswideOverviewItem.propTypes = {
  classwideStats: PropTypes.object, // TODO Shape
  group: PropTypes.shape({
    _id: PropTypes.string,
    name: PropTypes.string,
    siteId: PropTypes.string,
    ownerName: PropTypes.string,
    orgid: PropTypes.string,
    history: PropTypes.array,
    individualInterventionQueue: PropTypes.array,
    currentClasswideSkill: PropTypes.object
  }),
  loading: PropTypes.bool,
  ownerIds: PropTypes.array,
  assessmentResult: PropTypes.object,
  lastScoreUpdatedAt: PropTypes.number,
  isSchoolOverview: PropTypes.bool,
  isHighSchool: PropTypes.bool,
  individualInterventionAssessmentResults: PropTypes.array,
  type: PropTypes.string,
  grade: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  studentGroupEnrollments: PropTypes.array
};

ClasswideOverviewItem.defaultProps = {
  isSchoolOverview: false,
  individualInterventionAssessmentResults: []
};
