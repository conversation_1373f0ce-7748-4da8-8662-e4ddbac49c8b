import React, { Component } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";
import _isNumber from "lodash/isNumber";

function isCategoryValid(categoryValue) {
  // With Highcharts API a null value is assigned a number under the hood that indicates a position in the array of categories.
  // That means we have to check if the value is something else than a number here.
  return !_isNumber(categoryValue);
}

function getCategoryValue(value, isFirst = false) {
  const maxLineLength = 35;
  const shouldDisplayInline = isFirst && value && value.name && value.name.length > maxLineLength;
  return isCategoryValid(value)
    ? `<div><p>${value.name}</p>${
        value.n ? `${shouldDisplayInline ? " " : "<br />"}<b>(n = ${value.n})</b></div>` : ""
      }`
    : "";
}

export default class HSGrowthChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    this.chart.destroy();
  }

  updateChart() {
    const { categories } = this.props.chartItems;

    const style = this.props.shouldDisplayModifiedTitle
      ? {
          style: {
            textOverflow: "none",
            fontSize: "8px"
          },
          rotation: "0"
        }
      : {
          style: { textOverflow: "none" },
          rotation: "0"
        };

    this.chart = new Highcharts.Chart(this.props.chartId, {
      chart: {
        type: "column",
        height: 450,
        zoomType: "xy"
      },
      accessibility: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      title: {
        text: ""
      },
      xAxis: {
        categories,
        labels: {
          formatter() {
            return getCategoryValue(this.value, this.isFirst);
          },
          ...style
        }
      },
      yAxis: {
        title: {
          text: "% At/Above Instructional Target"
        },
        max: 100
      },
      legend: {
        enabled: !this.props.noChartTitleAndLegend,
        floating: false,
        layout: "horizontal",
        align: "center",
        verticalAlign: "top",
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      plotOptions: {
        column: {
          minPointLength: 3,
          maxPointWidth: 100
        }
      },
      series: this.props.chartItems.series,
      tooltip: {
        enabled: false
      }
    });
    this.chart.reflow();
  }

  // Create the div which the chart will be rendered to.
  render() {
    return <div id={this.props.chartId} className="growth-chart" />;
  }
}

HSGrowthChart.propTypes = {
  chartId: PropTypes.string,
  grade: PropTypes.string,
  shouldDisplayModifiedTitle: PropTypes.bool,
  chartItems: PropTypes.object,
  noChartTitleAndLegend: PropTypes.bool
};

HSGrowthChart.defaultProps = {
  grade: "",
  shouldDisplayModifiedTitle: false,
  generateAllPeriods: false
};
