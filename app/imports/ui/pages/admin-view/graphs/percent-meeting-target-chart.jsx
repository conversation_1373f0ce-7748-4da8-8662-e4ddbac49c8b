import React, { Component } from "react";
import PropTypes from "prop-types";
import { isEqual } from "lodash";
import Highcharts from "highcharts/highstock";

import { isOnPrintPage } from "../../../utilities";

export class PercentMeetingTarget<PERSON>hart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Only update the pmGraph after the latestResultDoc for the whole group has changed
  shouldComponentUpdate(nextProps) {
    return this.props.chartName !== nextProps.chartName || !isEqual(nextProps.data, this.props.data);
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  graphData() {
    const graphData = [];
    this.props.data.forEach(gd => {
      const seasonalGraphData = gd;
      // Fall - orange
      if (gd.name.toLowerCase() === "fall") {
        seasonalGraphData.color = "#f9bf68";
        // Winter - blue
      } else if (gd.name.toLowerCase() === "winter") {
        seasonalGraphData.color = "#70a1d9";
        // Spring - green
      } else if (gd.name.toLowerCase() === "spring") {
        seasonalGraphData.color = "#aee57c";
      }
      graphData.push(seasonalGraphData);
    });
    return graphData;
  }

  updateChart() {
    const isPrinting = isOnPrintPage();
    const customPrintingTextColor = isPrinting ? { color: "#000" } : {};

    this.chart = new Highcharts.Chart(this.props.chartId, {
      chart: {
        type: "column"
      },
      accessibility: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      title: {
        text: "Percent of Students At Or Above the Instructional Target by Season"
      },
      xAxis: {
        type: "category",
        labels: {
          style: customPrintingTextColor
        }
      },
      yAxis: {
        title: {
          text: "% At/Above Instructional Target",
          style: customPrintingTextColor
        },
        min: 0,
        max: 100
      },
      legend: {
        enabled: true,
        floating: false,
        layout: "horizontal",
        align: "center",
        verticalAlign: "top",
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      plotOptions: {
        series: {
          borderWidth: 0,
          dataLabels: {
            enabled: true,
            format: "{point.y:.1f}%",
            style: {
              ...(isPrinting ? { textOutline: "0px", ...customPrintingTextColor } : {})
            }
          }
        }
      },
      tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y:.2f}%</b> of total<br/>'
      },
      series: this.graphData()
    });
  }
  // Create the div which the chart will be rendered to.

  render() {
    return <div id={this.props.chartId} className="chart" />;
  }
}

PercentMeetingTargetChart.propTypes = {
  chartName: PropTypes.string.isRequired,
  type: PropTypes.string,
  chartId: PropTypes.string,
  data: PropTypes.array
};
