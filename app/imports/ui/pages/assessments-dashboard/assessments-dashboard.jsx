import React from "react";
import { Link } from "react-router-dom";
import PropTypes from "prop-types";
import PageHeader from "../../components/page-header";
import InterventionIntegrity from "./intervention-integrity";
import ScreeningAssessments from "./screening-assessments";
import AssessmentsFunctioning from "./assessments-functioning";
import NciiValues from "./ncii-values";
import InterventionProgress from "./intervention-progress";
import InterventionGrowth from "./intervention-growth";

const availablePages = {
  "intervention-integrity": {
    Component: <InterventionIntegrity />,
    title: "Intervention Integrity"
  },
  "screening-assessments": {
    Component: <ScreeningAssessments />,
    title: "Screening Assessments"
  },
  "assessment-functioning": {
    Component: <AssessmentsFunctioning />,
    title: "Assessments Functioning"
  },
  ncii: {
    Component: <NciiValues />,
    title: "NCII Values"
  },
  "intervention-progress": {
    Component: <InterventionProgress />,
    title: "Intervention Progress"
  },
  "intervention-growth": {
    Component: <InterventionGrowth />,
    title: "Intervention Growth"
  }
};

export default function AssessmentsDashboard({ pageName, pathPrefix = "" }) {
  const selectedPage = availablePages[pageName] || availablePages["assessment-functioning"];
  const urlPrefix = `${pathPrefix}/assessments-dashboard`;
  return (
    <div className="conFullScreen">
      <PageHeader title={selectedPage.title} />
      <div className="container">
        <div className="d-flex gap-2">
          <Link to="#" className="btn btn-success btn-high">
            Usage
          </Link>
          <Link to={`${urlPrefix}/intervention-integrity`} className="btn btn-success">
            Intervention
            <br />
            Integrity
          </Link>
          <Link to={`${urlPrefix}/screening-assessments`} className="btn btn-success">
            Screening
            <br />
            Assessments
          </Link>
          <Link to={`${urlPrefix}/assessment-functioning`} className="btn btn-success">
            Assessment
            <br />
            Functioning
          </Link>
          <Link to={`${urlPrefix}/ncii`} className="btn btn-success btn-high">
            NCII Values
          </Link>
          <Link to={`${urlPrefix}/intervention-progress`} className="btn btn-success">
            Intervention
            <br />
            Progress
          </Link>
          <Link to={`${urlPrefix}/intervention-growth`} className="btn btn-success">
            Intervention
            <br />
            Growth
          </Link>
          <Link to="#" className="btn btn-success btn-high">
            Dosage Effects
          </Link>
        </div>
        {selectedPage.Component}
      </div>
    </div>
  );
}

AssessmentsDashboard.propTypes = {
  pageName: PropTypes.string,
  pathPrefix: PropTypes.string
};
