import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";

import { Grades } from "/imports/api/grades/grades";
import { Organizations } from "/imports/api/organizations/organizations";
import { areSubscriptionsLoading } from "/imports/ui/utilities";
import Loading from "/imports/ui/components/loading";
import PageHeader from "/imports/ui/components/page-header";

import ExportScores from "./export-scores";
import CopyData from "./copy-data";
import { getUserRoles } from "../data-admin/utilities";

class DataScripts extends Component {
  isDataAdmin = getUserRoles().includes("dataAdmin");

  state = {
    showInterventionNotesCheckbox: false
  };

  updateColumn = ({ key, value }) => {
    this.setState({ [key]: value });
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }
    const { showInterventionNotesCheckbox } = this.state;
    return (
      <div className="conFullScreen">
        <PageHeader title="Data Scripts" />
        <div className="container">
          <div className="row">
            <table className="table table-condensed">
              <thead>
                <tr>
                  <th className="col-md-1">Script Name</th>
                  <th className={`${this.isDataAdmin ? "col-md-6" : "col-md-5"} text-center`}>Organization</th>
                  <th className={`${showInterventionNotesCheckbox ? "col-md-2" : "col-md-3"} text-center`}>Type</th>
                  {/* <th className="col-md-1">Grade</th> */}
                  {showInterventionNotesCheckbox ? <th className="col-md-1 text-center">Intervention Notes</th> : null}
                  <th className="col-md-1 text-center">School Year</th>
                  {!this.isDataAdmin ? <th className="col-md-1 text-center">Export Archive</th> : null}
                  <th className="col-md-1 text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                {/* <ExportClasswideSkillStats orgs={this.props.orgs} /> */}
                <ExportScores orgs={this.props.orgs} updateColumn={this.updateColumn} />
              </tbody>
            </table>
          </div>

          <hr />

          {!this.isDataAdmin && (
            <div className="row">
              <table className="table table-condensed">
                <thead>
                  <tr>
                    <th className="col-md-2">Script Name</th>
                    <th className="col-md-6 text-center">Target Organization</th>
                    <th className="col-md-4 text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <CopyData orgs={this.props.orgs} />
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    );
  }
}
DataScripts.propTypes = {
  grades: PropTypes.array,
  orgs: PropTypes.array,
  loading: PropTypes.bool
};

export default withTracker(() => {
  const gradesHandle = Meteor.subscribe("Grades");
  const orgHandle = Meteor.subscribe("Organizations:NamesAndStates");

  const loading = areSubscriptionsLoading(gradesHandle, orgHandle);
  let grades = [];
  let orgs = [];
  if (!loading) {
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    orgs = Organizations.find().fetch();
  }
  return {
    grades,
    loading,
    orgs
  };
})(DataScripts);
