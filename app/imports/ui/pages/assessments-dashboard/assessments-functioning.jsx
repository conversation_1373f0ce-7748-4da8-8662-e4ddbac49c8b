import React, { useState, useEffect } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker, useSubscribe } from "meteor/react-meteor-data";
import { map, mapKeys, range } from "lodash";
import Alert from "react-s-alert";

import { Grades } from "/imports/api/grades/grades";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";
import {
  getCurrentSchoolYear,
  getLatestAvailableSchoolYear,
  getMeteorUserSync
} from "/imports/api/utilities/utilities";
import { download } from "../../utilities";
import Loading from "../../components/loading";
import { getCSV } from "../data-admin/upload/file-upload-utils";

export function AssessmentsFunctioning() {
  // Use useSubscribe for subscription management
  const isGradesLoading = useSubscribe("Grades");
  const isBenchmarkPeriodsLoading = useSubscribe("BenchmarkPeriods");

  // State for async data
  const [currentSchoolYear, setCurrentSchoolYear] = useState(null);
  const [schoolYearSelection, setSchoolYearSelection] = useState([]);
  const [asyncLoading, setAsyncLoading] = useState(true);

  // Component state - must be declared at top level
  const [selectedGrade, setGrade] = useState("K");
  const [selectedBenchmarkPeriodId, setBenchmarkPeriodId] = useState("8S52Gz5o85hRkECgq");
  const [selectedState, setState] = useState("AZ");
  const [selectedSchoolYear, setSchoolYear] = useState(null); // Will be set when currentSchoolYear is available
  const [result, setResult] = useState();
  const [isLoading, setLoading] = useState(false);

  // Use useTracker for synchronous reactive data
  const { grades, benchmarkPeriods, states } = useTracker(() => {
    let fetchedGrades = [];
    let fetchedBenchmarkPeriods = [];
    const unitedStatesNames = getUnitedStatesNames();

    // Always try to fetch data - useSubscribe handles the loading
    fetchedGrades = Grades.find({ _id: { $ne: "HS" } }, { sort: { sortorder: 1 } }).fetch();
    fetchedBenchmarkPeriods = BenchmarkPeriods.find(
      {},
      { fields: { name: 1, label: 1 }, sort: { sortorder: 1 } }
    ).fetch();

    return {
      grades: fetchedGrades,
      benchmarkPeriods: fetchedBenchmarkPeriods,
      states: unitedStatesNames
    };
  }, []);

  // Use useTracker for user (synchronous)
  const user = useTracker(() => getMeteorUserSync(), []);

  // Use useEffect for async operations
  useEffect(() => {
    if (!user) {
      setAsyncLoading(false);
      return;
    }

    const loadSchoolYearData = async () => {
      try {
        setAsyncLoading(true);

        // Get school year data using await
        const [currentYear, latestYear] = await Promise.all([
          getCurrentSchoolYear(user),
          getLatestAvailableSchoolYear(user)
        ]);

        setCurrentSchoolYear(currentYear);

        const yearSelection = range(2016, latestYear + 1).map(schoolYear => {
          const schoolYearLabel = `${schoolYear - 1}-${schoolYear % 100}`;
          return {
            label: schoolYearLabel,
            value: schoolYear
          };
        });
        setSchoolYearSelection(yearSelection);
      } catch (error) {
        console.error("Error loading school year data:", error);
      } finally {
        setAsyncLoading(false);
      }
    };

    loadSchoolYearData();
  }, [user]); // Run when user changes

  // Set selectedSchoolYear when currentSchoolYear becomes available
  useEffect(() => {
    if (currentSchoolYear && selectedSchoolYear === null) {
      setSchoolYear(currentSchoolYear - 1);
    }
  }, [currentSchoolYear, selectedSchoolYear]);

  const subscriptionLoading = isGradesLoading() || isBenchmarkPeriodsLoading();

  if (subscriptionLoading || asyncLoading || !currentSchoolYear) {
    return <Loading />;
  }

  const onOptionChange = setFunction => e => {
    setFunction(e.target.value);
  };

  const getBenchmarkPeriods = () => {
    return benchmarkPeriods.filter(bp => bp.name !== "All");
  };

  const onSubmit = () => {
    setLoading(true);
    Meteor.call(
      "getAssessmentsFunctioning",
      {
        grade: selectedGrade,
        benchmarkPeriodId: selectedBenchmarkPeriodId,
        state: selectedState,
        schoolYear: parseInt(selectedSchoolYear)
      },
      (error, response) => {
        setLoading(false);
        if (!error) {
          setResult(response);
        } else if (error.reason) {
          Alert.error(error.reason);
        } else {
          Alert.error("Error while getting assessments functioning");
        }
      }
    );
  };

  const downloadCSV = () => {
    const data = result.map(({ assessment, classwideProblem, noClasswideProblem }) => {
      return {
        assessment,
        ...mapKeys(classwideProblem, (_, key) => `classwideProblem_${key}`),
        ...mapKeys(noClasswideProblem, (_, key) => `noClasswideProblem_${key}`)
      };
    });
    const benchmarkPeriodName = benchmarkPeriods.find(bp => bp._id === selectedBenchmarkPeriodId).name;
    const hrefData = `data:application/octet-stream,${getCSV(data)}`;
    download({
      filename: `assessments-functioning_${selectedGrade}_${benchmarkPeriodName}_${selectedState}.csv`,
      hrefData
    });
  };

  const renderAssessmentsFunctioning = data => {
    if (!data.length) {
      return <div className="alert alert-info text-center">No assessments found</div>;
    }
    return (
      <div className="row table-responsive">
        <table className="table table-condensed table-striped table-bordered table-hover">
          <thead>
            <tr>
              <th rowSpan="2">Assessment</th>
              <th colSpan="11">Classwide problem</th>
              <th colSpan="11">No classwide problem</th>
            </tr>
            <tr>
              <th>Sensitivity</th>
              <th>Specificity</th>
              <th>PPV</th>
              <th>NPV</th>
              <th>False Positive Rate</th>
              <th>False Negative Rate</th>
              <th>Positive Likelihood Ratio</th>
              <th>Negtive Likelihood Ratio</th>
              <th>Base Rate</th>
              <th>PPTP</th>
              <th>NPTP</th>

              <th>Sensitivity</th>
              <th>Specificity</th>
              <th>PPV</th>
              <th>NPV</th>
              <th>False Positive Rate</th>
              <th>False Negative Rate</th>
              <th>Positive Likelihood Ratio</th>
              <th>Negtive Likelihood Ratio</th>
              <th>Base Rate</th>
              <th>PPTP</th>
              <th>NPTP</th>
            </tr>
          </thead>
          <tbody>
            {data.map(datum => (
              <tr key={datum.assessment}>
                <th>{datum.assessment}</th>
                <th>{datum.classwideProblem.sensitivity}</th>
                <th>{datum.classwideProblem.specificity}</th>
                <th>{datum.classwideProblem.ppv}</th>
                <th>{datum.classwideProblem.npv}</th>
                <th>{datum.classwideProblem.falsePositiveRate}</th>
                <th>{datum.classwideProblem.falseNegativeRate}</th>
                <th>{datum.classwideProblem.positiveLikelihoodRatio}</th>
                <th>{datum.classwideProblem.negativeLikelihoodRatio}</th>
                <th>{datum.classwideProblem.baseRate}</th>
                <th>{datum.classwideProblem.pptp}</th>
                <th>{datum.classwideProblem.nptp}</th>

                <th>{datum.noClasswideProblem.sensitivity}</th>
                <th>{datum.noClasswideProblem.specificity}</th>
                <th>{datum.noClasswideProblem.ppv}</th>
                <th>{datum.noClasswideProblem.npv}</th>
                <th>{datum.noClasswideProblem.falsePositiveRate}</th>
                <th>{datum.noClasswideProblem.falseNegativeRate}</th>
                <th>{datum.noClasswideProblem.positiveLikelihoodRatio}</th>
                <th>{datum.noClasswideProblem.negativeLikelihoodRatio}</th>
                <th>{datum.noClasswideProblem.baseRate}</th>
                <th>{datum.noClasswideProblem.pptp}</th>
                <th>{datum.noClasswideProblem.nptp}</th>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <React.Fragment>
      <div className="row m-t-15 m-b-15">
        <div className="col-md-1">
          <select
            className="form-select"
            name="grade"
            onChange={onOptionChange(setGrade)}
            value={selectedGrade || "Grade"}
          >
            <option disabled>Grade</option>
            {grades.map(grade => (
              <option key={grade._id}>{grade._id}</option>
            ))}
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            name="benchmarkPeriodId"
            onChange={onOptionChange(setBenchmarkPeriodId)}
            value={selectedBenchmarkPeriodId || "Benchmark Period"}
          >
            <option disabled>Benchmark Period</option>
            {getBenchmarkPeriods().map(bp => (
              <option key={bp._id} value={bp._id}>
                {bp.name}
              </option>
            ))}
          </select>
        </div>
        {selectedGrade && selectedBenchmarkPeriodId && (
          <React.Fragment>
            <div className="col-md-3">
              <select
                className="form-select"
                name="state"
                onChange={onOptionChange(setState)}
                value={selectedState || "State"}
              >
                <option disabled>State</option>
                {map(states, (state, key) => (
                  <option key={key} value={key}>
                    {state}
                  </option>
                ))}
                <option value="international">International</option>
              </select>
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                name="schoolYear"
                onChange={onOptionChange(setSchoolYear)}
                value={selectedSchoolYear || "School Year"}
              >
                <option disabled>School Year</option>
                {schoolYearSelection.map(schoolYear => (
                  <option key={schoolYear.value} value={schoolYear.value}>
                    {schoolYear.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="col-md-2 d-grid">
              <button className="btn btn-success" onClick={onSubmit} disabled={!selectedState}>
                Submit
              </button>
            </div>
            <div className="col-md-2 d-grid">
              <button className="btn btn-primary" onClick={downloadCSV} disabled={!result}>
                Download
              </button>
            </div>
          </React.Fragment>
        )}
      </div>
      {isLoading ? <Loading /> : result && renderAssessmentsFunctioning(result)}
    </React.Fragment>
  );
}

export default AssessmentsFunctioning;
