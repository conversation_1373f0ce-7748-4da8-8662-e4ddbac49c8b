import React, { Component } from "react";
import PropTypes from "prop-types";
import Select from "react-select";
import Alert from "react-s-alert";

import Loading from "../../components/loading";

export default class CopyData extends Component {
  state = {
    selectedTargetOrgid: "",
    isCopying: false
  };

  getOrganizationOptions = () => {
    return this.props.orgs
      .filter(({ _id }) => _id !== Meteor.settings.public.DATA_COPY_SOURCE_ORG_ID)
      .sort((a, b) => {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      })
      .map(org => ({ value: org._id, label: `${org.name}` }));
  };

  onChangeValue = type => e => {
    const targetValue = e.value || e.target.value;
    const stateToSet = { [type]: targetValue };
    return this.setState(stateToSet);
  };

  copyData = () => {
    const { selectedTargetOrgid } = this.state;
    this.setState({ isCopying: true });
    Meteor.call("Script:runSchoolScrubber", { targetOrgId: selectedTargetOrgid }, err => {
      if (!err) {
        Alert.success("Successfully copied historical data");
      } else {
        console.log("Script:runSchoolScrubber error: ", err);
        Alert.error(err.error);
      }
      this.setState({ isCopying: false });
    });
  };

  render() {
    const organizationOptions = this.getOrganizationOptions();
    const { selectedTargetOrgid, isCopying } = this.state;
    const sourceOrgId = Meteor.settings.public.DATA_COPY_SOURCE_ORG_ID;
    const hasSourceOrg = !!(sourceOrgId && this.props.orgs.find(({ _id }) => _id === sourceOrgId));
    const shouldEnableSubmitBtn = !!selectedTargetOrgid;

    return (
      <tr>
        <td className="col-md-2 middle-align">Copy Data</td>
        {hasSourceOrg ? (
          <React.Fragment>
            <td className="col-md-6 middle-align">
              {this.isDataAdmin ? (
                this.props.orgs[0].name
              ) : (
                <Select
                  id="copyData_target_orgid"
                  name="copyData_target_orgid"
                  onChange={this.onChangeValue("selectedTargetOrgid")}
                  isSearchable={true}
                  value={organizationOptions.filter(org => org.value === this.state.selectedTargetOrgid)}
                  options={organizationOptions}
                  placeholder={"Organization Name or Id"}
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              )}
            </td>
            <td className="col-md-4 middle-align">
              {isCopying ? (
                <div className="text-center">
                  <Loading inline={true} />
                </div>
              ) : (
                <div className="d-grid">
                  <button className="btn btn-primary btn-lg" onClick={this.copyData} disabled={!shouldEnableSubmitBtn}>
                    Copy
                  </button>
                </div>
              )}
            </td>
          </React.Fragment>
        ) : (
          <td className="col-md-10 middle-align" colSpan="2">
            <div className="alert alert-warning text-center p-2 m-b-0">No Source Organization available</div>
          </td>
        )}
      </tr>
    );
  }
}
CopyData.propTypes = {
  orgs: PropTypes.array
};
