import React, { useState, useEffect } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker, useSubscribe } from "meteor/react-meteor-data";
import { map, range, countBy, uniqBy } from "lodash";
import Alert from "react-s-alert";
import Select from "react-select";

import { Grades } from "/imports/api/grades/grades";
import {
  getCurrentSchoolYear,
  getLatestAvailableSchoolYear,
  getMeteorUserSync,
  getOrganizationIdsForState
} from "/imports/api/utilities/utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { formatPercentage, getSelectCustomStyles } from "../../utilities";
import Loading from "../../components/loading";
import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";

export function InterventionGrowth() {
  // Use useSubscribe for subscription management
  const isGradesLoading = useSubscribe("Grades");
  const isOrganizationsLoading = useSubscribe("Organizations:NameById");

  // State for async data
  const [currentSchoolYear, setCurrentSchoolYear] = useState(null);
  const [schoolYearSelection, setSchoolYearSelection] = useState([]);
  const [asyncLoading, setAsyncLoading] = useState(true);
  const [availableOrganizations, setAvailableOrganizations] = useState([]);

  // Component state - must be declared at top level
  const [selectedGrade, setGrade] = useState("K");
  const [selectedSchoolYear, setSchoolYear] = useState(null); // Will be set when currentSchoolYear is available
  const [selectedState, setState] = useState("");
  const [selectedOrganizations, setSelectedOrganizations] = useState([]);
  const [result, setResult] = useState();
  const [isLoading, setLoading] = useState(false);

  // Use useTracker for synchronous reactive data
  const { grades, organizations, states } = useTracker(() => {
    let fetchedGrades = [];
    let fetchedOrganizationsList = [];
    const unitedStatesNames = getUnitedStatesNames();

    // Always try to fetch data - useSubscribe handles the loading
    fetchedGrades = Grades.find({ _id: { $ne: "HS" } }, { sort: { sortorder: 1 } }).fetch();
    fetchedOrganizationsList = Organizations.find({}, { sort: { name: 1 } }).fetch();

    return {
      grades: fetchedGrades,
      organizations: fetchedOrganizationsList,
      states: unitedStatesNames
    };
  }, []);

  // Use useTracker for user (synchronous)
  const user = useTracker(() => getMeteorUserSync(), []);

  // Use useEffect for async operations
  useEffect(() => {
    if (!user) {
      setAsyncLoading(false);
      return;
    }

    const loadSchoolYearData = async () => {
      try {
        setAsyncLoading(true);

        // Get school year data using await
        const [currentYear, latestYear] = await Promise.all([
          getCurrentSchoolYear(user),
          getLatestAvailableSchoolYear(user)
        ]);

        setCurrentSchoolYear(currentYear);

        const yearSelection = range(2016, latestYear + 1).map(schoolYear => {
          const schoolYearLabel = `${schoolYear - 1}-${schoolYear % 100}`;
          return {
            label: schoolYearLabel,
            value: schoolYear
          };
        });
        setSchoolYearSelection(yearSelection);
      } catch (error) {
        console.error("Error loading school year data:", error);
      } finally {
        setAsyncLoading(false);
      }
    };

    loadSchoolYearData();
  }, [user]); // Run when user changes

  // Set selectedSchoolYear when currentSchoolYear becomes available
  useEffect(() => {
    if (currentSchoolYear && selectedSchoolYear === null) {
      setSchoolYear(currentSchoolYear - 1);
    }
  }, [currentSchoolYear, selectedSchoolYear]);

  // Handle organization filtering based on selected state
  useEffect(() => {
    const filterOrganizations = async () => {
      if (selectedState) {
        try {
          const organizationIds = await getOrganizationIdsForState(selectedState);
          const filtered = organizations.filter(organization => organizationIds.includes(organization._id));
          setAvailableOrganizations(filtered);
        } catch (error) {
          console.error("Error filtering organizations by state:", error);
          setAvailableOrganizations(organizations);
        }
      } else {
        setAvailableOrganizations(organizations);
      }
    };

    filterOrganizations();
  }, [selectedState, organizations]);

  const subscriptionLoading = isGradesLoading() || isOrganizationsLoading();

  if (subscriptionLoading || asyncLoading || !currentSchoolYear) {
    return <Loading />;
  }

  const onOptionChange = (setFunction, stateSetFunctionsToReset = []) => e => {
    setFunction(e.target.value);
    stateSetFunctionsToReset.forEach(stateSetFunction => stateSetFunction([]));
  };

  const handleSelectedOrganizationsChange = selectedOrgs => {
    if (!selectedOrgs) {
      setSelectedOrganizations([]);
    } else {
      setSelectedOrganizations(selectedOrgs);
    }
  };

  const onSubmit = () => {
    setLoading(true);
    const orgIds = selectedOrganizations?.map(selectedOrganization => selectedOrganization.value);

    Meteor.call(
      "getInterventionGrowth",
      {
        grade: selectedGrade,
        schoolYear: parseInt(selectedSchoolYear),
        orgIds,
        state: selectedState
      },
      (error, response) => {
        setLoading(false);
        if (!error) {
          setResult(response);
        } else if (error.reason) {
          Alert.error(error.reason);
        } else {
          Alert.error("Error while getting Intervention Progress");
        }
      }
    );
  };

  const sectionTitleMap = {
    allScreeningMeasures: "N & % of students proficient on all screening measures",
    seasonalScreeningMeasures: "N & % of students proficient on seasonal screening measures",
    fallScreeningAndFinalClasswideMeasures:
      "N & % of students who were proficient on the final classwide intervention skill that is linked to this fall screening measure",
    winterScreeningAndFinalClasswideMeasures:
      "N & % of students who were proficient on the final classwide intervention skill that is linked to this winter screening measure",
    finalClasswideInterventionByYear: "N & % of students proficient on final classwide intervention by year"
  };

  const renderInterventionGrowth = data => {
    const { benchmarkPeriodLabelsById, ...stats } = data;

    const generateHeaders = (sectionValues, sectionKey) => {
      let resultHeaders = {};
      if (sectionKey === "allScreeningMeasures") {
        resultHeaders = (
          <thead>
            <tr>
              <th></th>
              {Object.values(sectionValues).map((value, index) => (
                <th key={`headers${index}`}>{benchmarkPeriodLabelsById[value.benchmarkPeriodId]}</th>
              ))}
            </tr>
          </thead>
        );
      } else if (sectionKey === "seasonalScreeningMeasures") {
        const colSpansOfBenchmarkPeriods = countBy(sectionValues, "benchmarkPeriodId");
        const benchmarkPeriodHeaders = uniqBy(sectionValues, "benchmarkPeriodId").map((value, index) => (
          <th key={`headersBenchmark${index}`} colSpan={colSpansOfBenchmarkPeriods[value.benchmarkPeriodId]}>
            {benchmarkPeriodLabelsById[value.benchmarkPeriodId]}
          </th>
        ));
        resultHeaders = (
          <thead>
            <tr>
              <th></th>
              {benchmarkPeriodHeaders}
            </tr>
            <tr>
              <th></th>
              {Object.values(sectionValues).map((value, index) => (
                <th key={`headersAssessments${index}`}>{value.assessmentName}</th>
              ))}
            </tr>
          </thead>
        );
      } else if (
        sectionKey === "fallScreeningAndFinalClasswideMeasures" ||
        sectionKey === "winterScreeningAndFinalClasswideMeasures"
      ) {
        resultHeaders = (
          <thead>
            <tr>
              <th></th>
              {Object.values(sectionValues).map((value, index) => (
                <th key={`headersAssessments${index}`}>{value.assessmentName}</th>
              ))}
            </tr>
          </thead>
        );
      } else if (sectionKey === "finalClasswideInterventionByYear") {
        resultHeaders = (
          <thead>
            <tr>
              <th></th>
              {Object.values(sectionValues).map((value, index) => (
                <th key={`headersYearly${index}`}>{value.schoolYear}</th>
              ))}
            </tr>
          </thead>
        );
      }

      return resultHeaders;
    };

    return (
      <React.Fragment>
        {map(sectionTitleMap, (sectionValue, sectionKey) => {
          return (
            <div key={sectionKey} className="row">
              <h3>{sectionValue}</h3>

              {stats[sectionKey].length ? (
                <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
                  {generateHeaders(stats[sectionKey], sectionKey)}
                  <tbody>
                    <tr>
                      <th>% of students proficient</th>
                      {Object.values(stats[sectionKey]).map((value, index) => (
                        <th key={`POSrow${index}`}>{formatPercentage(value.percentOfStudentsProficient)}</th>
                      ))}
                    </tr>
                    <tr>
                      <th>Number of students proficient</th>
                      {Object.values(stats[sectionKey]).map((value, index) => (
                        <th key={`NOSProw${index}`}>{value.numberOfStudentsProficient}</th>
                      ))}
                    </tr>
                    <tr>
                      <th>Number of students assessed</th>
                      {Object.values(stats[sectionKey]).map((value, index) => (
                        <th key={`NOSArow${index}`}>{value.totalStudentsAssessedOnAllMeasures}</th>
                      ))}
                    </tr>
                  </tbody>
                </table>
              ) : (
                <div className="alert alert-info text-center">No assessment results found</div>
              )}
            </div>
          );
        })}
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <div className="row m-t-15 m-b-15">
        <div className="col-md-2">
          <select
            className="form-select"
            name="schoolYear"
            onChange={onOptionChange(setSchoolYear)}
            value={selectedSchoolYear || "School Year"}
          >
            <option disabled>School Year</option>
            {schoolYearSelection.map(schoolYear => (
              <option key={schoolYear.value} value={schoolYear.value}>
                {schoolYear.label}
              </option>
            ))}
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            name="state"
            onChange={onOptionChange(setState, [setSelectedOrganizations])}
            value={selectedState || ""}
          >
            <option value="">All States</option>
            {map(states, (state, key) => (
              <option key={key} value={key}>
                {state}
              </option>
            ))}
            <option value="international">International</option>
          </select>
        </div>

        <div className="col-md-5">
          <Select
            id="selectedOrganizations"
            value={selectedOrganizations}
            isMulti
            isSearchable={true}
            styles={getSelectCustomStyles()}
            name="selectedOrganizations"
            options={availableOrganizations.map(organization => ({
              label: organization.name,
              value: organization._id
            }))}
            className="basic-multi-select"
            classNamePrefix="select"
            placeholder="Search in all organizations or select specific ones"
            onChange={handleSelectedOrganizationsChange}
          />
        </div>
        <div className="col-md-1">
          <select className="form-select" name="grade" onChange={onOptionChange(setGrade)} value={selectedGrade}>
            {grades.map(grade => (
              <option key={grade._id}>{grade._id}</option>
            ))}
          </select>
        </div>
        <div className="col-md-2 d-grid">
          <button className="btn btn-success" onClick={onSubmit}>
            Submit
          </button>
        </div>
      </div>
      {isLoading ? <Loading /> : result && renderInterventionGrowth(result)}
    </React.Fragment>
  );
}

export default InterventionGrowth;
