import React, { Component } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";
import HighchartsMore from "highcharts/highcharts-more";
import { isEqual } from "lodash";

// eslint-disable-next-line new-cap
HighchartsMore(Highcharts);

export class RangeBar<PERSON>hart extends Component {
  componentDidMount() {
    this.updateChart();
  }

  shouldComponentUpdate(nextProps) {
    return !isEqual(this.props, nextProps);
  }

  componentDidUpdate() {
    this.updateChart();
  }

  componentWillUnmount() {
    this.chart?.destroy();
  }

  updateChart() {
    const { minValue, maxValue, meanValue, scaleMax, scaleMin = 0, chartId } = this.props;

    this.chart = Highcharts.chart(chartId, {
      chart: {
        type: "columnrange",
        height: "43px",
        inverted: true,
        margin: [0, 15, 20, 15],
        spacing: [0, 0, 0, 0]
      },
      accessibility: {
        enabled: false
      },
      title: {
        text: null
      },
      xAxis: {
        labels: {
          enabled: false
        }
      },
      credits: {
        enabled: false
      },
      yAxis: {
        title: {
          text: null
        },
        height: "30px",
        top: -18,
        min: scaleMin,
        max: scaleMax
      },
      tooltip: {
        enabled: false
      },
      plotOptions: {
        columnrange: {
          grouping: false,
          shadow: false,
          dataLabels: {
            enabled: true
          }
        }
      },
      colors: ["#005bb5", "#005bb5"],
      legend: {
        enabled: false
      },
      series: [
        {
          enableMouseTracking: false,
          pointPlacement: 0,
          pointWidth: 25,
          data: [[minValue, maxValue]]
        },
        {
          enableMouseTracking: false,
          pointPlacement: -1,
          pointWidth: 25,
          data: [[minValue, meanValue]]
        }
      ]
    });
  }

  render() {
    return <div id={this.props.chartId} />;
  }
}

RangeBarChart.propTypes = {
  chartId: PropTypes.string.isRequired,
  minValue: PropTypes.number,
  maxValue: PropTypes.number,
  meanValue: PropTypes.number,
  scaleMin: PropTypes.number,
  scaleMax: PropTypes.number
};
