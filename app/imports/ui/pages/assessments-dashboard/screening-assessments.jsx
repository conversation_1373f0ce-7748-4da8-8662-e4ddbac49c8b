import React, { useState, useEffect } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker, useSubscribe } from "meteor/react-meteor-data";
import { map, range } from "lodash";
import Alert from "react-s-alert";

import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";
import {
  getCurrentSchoolYear,
  getLatestAvailableSchoolYear,
  getMeteorUserSync
} from "/imports/api/utilities/utilities";

import Loading from "../../components/loading";

export function ScreeningAssessments() {
  // Use useSubscribe for subscription management
  const isSubscriptionLoading = useSubscribe("BenchmarkPeriods");

  // State for async data
  const [currentSchoolYear, setCurrentSchoolYear] = useState(null);
  const [schoolYearSelection, setSchoolYearSelection] = useState([]);
  const [asyncLoading, setAsyncLoading] = useState(true);

  // Component state - must be declared at top level
  const [selectedState, setState] = useState("AZ");
  const [selectedBenchmarkPeriodId, setBenchmarkPeriodId] = useState("8S52Gz5o85hRkECgq");
  const [selectedSchoolYear, setSchoolYear] = useState(null); // Will be set when currentSchoolYear is available
  const [result, setResult] = useState(null);
  const [isLoading, setLoading] = useState(false);

  // Use useTracker for synchronous reactive data
  const { benchmarkPeriods, states } = useTracker(() => {
    let fetchedBenchmarkPeriods = [];
    const unitedStatesNames = getUnitedStatesNames();

    // Always try to fetch benchmarkPeriods - useSubscribe handles the loading
    fetchedBenchmarkPeriods = BenchmarkPeriods.find(
      {},
      { fields: { name: 1, label: 1 }, sort: { sortorder: 1 } }
    ).fetch();

    return {
      benchmarkPeriods: fetchedBenchmarkPeriods,
      states: unitedStatesNames
    };
  }, []);

  // Use useTracker for user (synchronous)
  const user = useTracker(() => getMeteorUserSync(), []);

  // Use useEffect for async operations
  useEffect(() => {
    if (!user) {
      setAsyncLoading(false);
      return;
    }

    const loadSchoolYearData = async () => {
      try {
        setAsyncLoading(true);
        // Get school year data using await
        const [currentYear, latestYear] = await Promise.all([
          getCurrentSchoolYear(user),
          getLatestAvailableSchoolYear(user)
        ]);
        setCurrentSchoolYear(currentYear);

        const yearSelection = range(2016, latestYear + 1).map(schoolYear => {
          const schoolYearLabel = `${schoolYear - 1}-${schoolYear % 100}`;
          return {
            label: schoolYearLabel,
            value: schoolYear
          };
        });
        setSchoolYearSelection(yearSelection);
      } catch (error) {
        console.error("Error loading school year data:", error);
      } finally {
        setAsyncLoading(false);
      }
    };

    loadSchoolYearData();
  }, [user]); // Run when user changes

  // Set selectedSchoolYear when currentSchoolYear becomes available
  useEffect(() => {
    if (currentSchoolYear && selectedSchoolYear === null) {
      setSchoolYear(currentSchoolYear - 1);
    }
  }, [currentSchoolYear, selectedSchoolYear]);

  const subscriptionLoading = isSubscriptionLoading();

  if (subscriptionLoading || asyncLoading || !currentSchoolYear) {
    return <Loading />;
  }

  const onOptionChange = setFunction => e => {
    setFunction(e.target.value);
  };

  const getBenchmarkPeriods = () => {
    return benchmarkPeriods.filter(bp => bp.name !== "All");
  };

  const onSubmit = () => {
    setLoading(true);
    Meteor.call(
      "getScreeningAssessments",
      {
        state: selectedState,
        benchmarkPeriodId: selectedBenchmarkPeriodId,
        schoolYear: parseInt(selectedSchoolYear)
      },
      (error, response) => {
        setLoading(false);
        if (!error) {
          setResult(response);
        } else if (error.reason) {
          Alert.error(error.reason);
        } else {
          Alert.error("Error while getting Screening Assessments");
        }
      }
    );
  };

  const renderScreeningAssessments = data => {
    if (!data.length) {
      return <div className="alert alert-info text-center">No assessment results found</div>;
    }

    return (
      <div className="row table-responsive">
        <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
          <thead>
            <tr>
              <th>Grade</th>
              <th>Assessment Name</th>
              <th>N of Students Tested (all states)</th>
              <th>Mean (all states)</th>
              <th>Standard Deviation (all states)</th>
              <th>% at risk (all states)</th>
              <th>N of students (selected state)</th>
              <th>Mean (selected state)</th>
              <th>Standard deviation (selected state)</th>
              <th>% at risk (selected state)</th>
            </tr>
          </thead>
          <tbody>
            {data.map(datum => (
              <tr key={datum.assessmentId}>
                <th>{datum.grade}</th>
                <th>{datum.assessmentName}</th>
                <th>{datum.numberOfStudentsTestedFromAllStates}</th>
                <th>{datum.meanFromAllStates}</th>
                <th>{datum.standardDeviationFromAllStates}</th>
                <th>{datum.percentAtRiskFromAllStates}</th>
                <th>{datum.numberOfStudentsTestedFromSelectedState}</th>
                <th>{datum.meanFromSelectedState}</th>
                <th>{datum.standardDeviationFromSelectedState}</th>
                <th>{datum.percentAtRiskFromSelectedState}</th>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <React.Fragment>
      <div className="row m-t-15 m-b-15">
        <div className="col-md-4">
          <select
            className="form-select"
            name="state"
            onChange={onOptionChange(setState)}
            value={selectedState || "State"}
          >
            <option disabled>State</option>
            {map(states, (state, key) => (
              <option key={key} value={key}>
                {state}
              </option>
            ))}
            <option value="international">International</option>
          </select>
        </div>
        <div className="col-md-3">
          <select
            className="form-select"
            name="benchmarkPeriodId"
            onChange={onOptionChange(setBenchmarkPeriodId)}
            value={selectedBenchmarkPeriodId || "Benchmark Period"}
          >
            <option disabled>Benchmark Period</option>
            {getBenchmarkPeriods().map(bp => (
              <option key={bp._id} value={bp._id}>
                {bp.name}
              </option>
            ))}
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            name="schoolYear"
            onChange={onOptionChange(setSchoolYear)}
            value={selectedSchoolYear || "School Year"}
          >
            <option disabled>School Year</option>
            {schoolYearSelection.map(schoolYear => (
              <option key={schoolYear.value} value={schoolYear.value}>
                {schoolYear.label}
              </option>
            ))}
          </select>
        </div>
        {selectedBenchmarkPeriodId && (
          <React.Fragment>
            <div className="col-md-2 d-grid">
              <button className="btn btn-success" onClick={onSubmit} disabled={!selectedState}>
                Submit
              </button>
            </div>
          </React.Fragment>
        )}
      </div>
      {isLoading ? <Loading /> : result && renderScreeningAssessments(result)}
    </React.Fragment>
  );
}

export default ScreeningAssessments;
