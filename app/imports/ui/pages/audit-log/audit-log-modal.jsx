import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import PropTypes from "prop-types";
import React from "react";
import Diff from "diff";
import { getTypeLabel } from "./utils/get-type-label";
import { formatProfileName } from "./utils/format-profile-name";

const AuditLogModal = ({ showModal, onHide, log }) => {
  const getDiffColor = diffObj => {
    if (diffObj.removed) return "#ff7079";
    if (diffObj.added) return "#638C5C";
    return "#4c5667";
  };

  const generateDiff = ({ outdated, updated }) => {
    const diff = Diff.diffJson(outdated, updated);
    return (
      <pre>
        {diff.map(item => {
          const separatedValues = item.value.split("\n");
          return separatedValues.map((value, index) => (
            <div key={`${value}_${index}`} style={{ color: getDiffColor(item) }}>
              {value}
            </div>
          ));
        })}
      </pre>
    );
  };

  if (!log) return null;
  const dateString = log.created.date.toLocaleString();
  return (
    <Modal show={showModal} onHide={onHide} size="lg" backdrop="static">
      <Modal.Header>
        <div>
          <strong>{getTypeLabel(log.type)}</strong> updated at <strong>{dateString}</strong> by{" "}
          <strong>{formatProfileName(log.userProfileName)}</strong> ({log.created.by})
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="card font-13 lh-sm p-2">{generateDiff(log)}</div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="default" onClick={onHide}>
          OK
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

AuditLogModal.propTypes = {
  showModal: PropTypes.bool,
  onHide: PropTypes.func,
  log: PropTypes.shape({
    _id: PropTypes.string,
    type: PropTypes.string,
    created: PropTypes.shape({
      by: PropTypes.string,
      date: PropTypes.object,
      on: PropTypes.number
    }),
    outdated: PropTypes.array,
    updated: PropTypes.array,
    userProfileName: PropTypes.shape({
      first: PropTypes.string,
      last: PropTypes.string,
      middle: PropTypes.string
    })
  })
};

export default AuditLogModal;
