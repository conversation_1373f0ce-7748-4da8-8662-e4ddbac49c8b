import React, { Component } from "react";
import Select from "react-select";
import PropTypes from "prop-types";
import { Button } from "react-bootstrap";
import get from "lodash/get";
import isNumber from "lodash/isNumber";
import { capitalizeFirstLetter } from "/imports/api/utilities/utilities";

export class AssessmentOutcomesRow extends Component {
  render() {
    const outcomeColors = {
      above: "#b2df8a",
      at: "#a6cee3",
      below: "#e31a1c"
    };

    const renderDot = outcome => {
      return <span className="dot-small" style={{ background: outcomeColors[outcome] }}></span>;
    };
    return (
      <tr>
        <td>
          {renderDot(this.props.outcomeName)} {capitalizeFirstLetter(this.props.outcomeName)}
        </td>
        <td>
          {this.props.outcome && this.props.outcome.assessmentId ? (
            <div className="row">
              {this.props.isPreviewOnly ? (
                <span>
                  {this.props.availableAssessments?.find(el => el.value === this.props.outcome?.assessmentId)?.label ||
                    "N/A"}
                </span>
              ) : (
                <React.Fragment>
                  <Select
                    key={`${this.props.outcomeName}-${this.props.outcome.assessmentId}`}
                    value={this.props.availableAssessments.find(el => el.value === this.props.outcome.assessmentId)}
                    isSearchable={true}
                    name={this.props.outcomeName}
                    options={this.props.availableAssessments}
                    className="basic-single col-md-11"
                    classNamePrefix="select"
                    onChange={this.props.handleRuleChange(this.props.outcomeName)}
                  />
                  <Button
                    className="btn btn-danger col-md-1"
                    onClick={this.props.removeRuleOutcome(this.props.outcomeName)}
                  >
                    <span className="fa fa fa-close removeIcon" />
                  </Button>
                </React.Fragment>
              )}
            </div>
          ) : (
            <React.Fragment>
              <span className="text-center">Progress Monitoring Completed</span>
              {this.props.isPreviewOnly ? null : (
                <Button
                  className="btn btn-primary pull-right"
                  onClick={this.props.addOutcomeToRule(this.props.outcomeName)}
                >
                  Add Rule?
                </Button>
              )}
            </React.Fragment>
          )}
        </td>
        <td>{this.props.outcome && this.props.isEnding ? this.getInterventionsCell() : null}</td>
      </tr>
    );
  }

  getInterventionsCell() {
    const interventionsLength = get(this.props, `interventionOutcomes[${this.props.outcomeName}].length`);
    let currentValue = [];
    if (interventionsLength) {
      currentValue = this.props.interventionOutcomes[this.props.outcomeName].map(int => ({
        label: int.label,
        value: int.interventionId
      }));
    }

    if (isNumber(interventionsLength)) {
      return (
        <Select
          value={currentValue}
          isMulti
          isSearchable={true}
          isDisabled={this.props.isPreviewOnly}
          name={this.props.outcomeName}
          options={this.props.availableInterventions}
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={this.props.handleInterventionsChange(this.props.outcomeName)}
        />
      );
    }
    if (!this.props.isPreviewOnly) {
      return (
        <Button
          className="btn btn-primary"
          disabled={!this.props.outcome}
          onClick={this.props.addInterventionsToOutcome(
            this.props.outcomeName,
            get(this.props, "outcome.assessmentId")
          )}
        >
          Add Interventions!
        </Button>
      );
    }
    return null;
  }
}

AssessmentOutcomesRow.propTypes = {
  outcome: PropTypes.object,
  outcomeName: PropTypes.string,
  availableAssessments: PropTypes.array,
  interventionOutcomes: PropTypes.any,
  availableInterventions: PropTypes.any,
  monitorAssessmentMeasure: PropTypes.any,
  handleRuleChange: PropTypes.func,
  handleInterventionsChange: PropTypes.func,
  addInterventionsToOutcome: PropTypes.func,
  addOutcomeToRule: PropTypes.func,
  removeRuleOutcome: PropTypes.func,
  isEnding: PropTypes.bool,
  isPreviewOnly: PropTypes.bool
};
