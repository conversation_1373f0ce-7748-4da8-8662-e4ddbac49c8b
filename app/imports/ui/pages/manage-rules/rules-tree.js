import React, { useEffect, useRef, useCallback, useContext, memo } from "react";
import PropTypes from "prop-types";
import * as d3 from "d3-3";

import { StaticDataContext } from "/imports/contexts/StaticDataContext";
import { arePropsEqual } from "/imports/ui/utilities";

import { TreeDisplayData, getAssessmentNameForTree, getRelatedAssessmentIds } from "./TreeDisplayData";

const RulesTree = ({ tree, errors, depth, setSelectedAssessment, isPreviewOnly }) => {
  const rootRef = useRef(null);
  const marginRef = useRef({ top: 20, right: 120, bottom: 20, left: 160 });
  const heightRef = useRef(700 - marginRef.current.top - marginRef.current.bottom);
  const widthRef = useRef(null);
  const treeRef = useRef(null);
  const diagonalRef = useRef(null);
  const svgRef = useRef(null);

  const getCircleColor = useCallback(d => {
    if (!d.outcome) {
      return "#fff";
    }
    switch (d.outcome) {
      case "below":
        return "#e31a1c";
      case "at":
        return "#a6cee3";
      case "above":
        return "#b2df8a";
      default:
        return "#fff";
    }
  }, []);

  const handleClick = useCallback(
    item => {
      d3.selectAll("g.node").each(function(d) {
        const isActive = d.assessmentId === item.assessmentId && d.id === item.id;
        d3.select(this)
          .select("circle")
          .classed("active", isActive);
      });
      setSelectedAssessment(item);
    },
    [setSelectedAssessment]
  );

  const getNodeEnter = useCallback(
    (node, source) => {
      return node
        .enter()
        .append("g")
        .attr("class", "node")
        .attr("transform", () => `translate(${source.y0},${source.x0})`)
        .on("click", handleClick);
    },
    [handleClick]
  );

  const getNodeUpdateTransition = useCallback((node, duration) => {
    return node
      .transition()
      .duration(duration)
      .attr("transform", d => `translate(${d.y},${d.x})`);
  }, []);

  const getLinks = useCallback(links => {
    return svgRef.current.selectAll("path.link").data(links, d => d.target.id);
  }, []);

  const getNodeExit = useCallback((node, duration, source) => {
    return node
      .exit()
      .transition()
      .duration(duration)
      .attr("transform", () => `translate(${source.y},${source.x})`)
      .remove();
  }, []);

  const update = useCallback(
    source => {
      if (!treeRef.current || !svgRef.current || !diagonalRef.current) return;

      const duration = 750;
      let i = 0;
      // Compute the new tree layout.
      const nodes = treeRef.current.nodes(rootRef.current).reverse();
      const links = treeRef.current.links(nodes);

      // Normalize for fixed-depth.
      nodes.forEach(treeNode => {
        // eslint-disable-next-line no-param-reassign
        treeNode.y = treeNode.depth * 180;
      });

      // Update the nodes
      const node = svgRef.current.selectAll("g.node").data(nodes, d => {
        // eslint-disable-next-line no-param-reassign,no-return-assign
        return d.id || (d.id = ++i);
      });

      // Enter any new nodes at the parent's previous position.
      const nodeEnter = getNodeEnter(node, source);
      nodeEnter
        .append("circle")
        .attr("r", 1e-6)
        // eslint-disable-next-line no-underscore-dangle
        .style("fill", d => (d._children ? "lightsteelblue" : "#fff"));
      nodeEnter
        .append("text")
        .attr("x", d => (d.isRoot ? -13 : 13))
        .attr("dy", ".35em")
        .attr("text-anchor", d => (d.isRoot ? "end" : "start"))
        .text(d => d.name)
        .style("fill-opacity", 1e-6);

      // Transition nodes to their new position.
      const nodeUpdate = getNodeUpdateTransition(node, duration);
      nodeUpdate
        .select("circle")
        .attr("r", 10)
        .style("fill", d => getCircleColor(d));
      nodeUpdate.select("text").style("fill-opacity", 1);

      // Transition exiting nodes to the parent's new position.
      const nodeExit = getNodeExit(node, duration, source);
      nodeExit.select("circle").attr("r", 1e-6);
      nodeExit.select("text").style("fill-opacity", 1e-6);

      // Update the links…
      const link = getLinks(links);
      // Enter any new links at the parent's previous position.
      link
        .enter()
        .insert("path", "g")
        .attr("class", "link")
        .attr("d", () => {
          const o = { x: source.x0, y: source.y0 };
          return diagonalRef.current({ source: o, target: o });
        });

      // Transition links to their new position.
      link
        .transition()
        .duration(duration)
        .attr("d", diagonalRef.current);

      // Transition exiting nodes to the parent's new position.
      link
        .exit()
        .transition()
        .duration(duration)
        .attr("d", () => {
          const o = { x: source.x, y: source.y };
          return diagonalRef.current({ source: o, target: o });
        })
        .remove();

      // Stash the old positions for transition.
      nodes.forEach(treeNode => {
        /* eslint-disable no-param-reassign */
        treeNode.x0 = treeNode.x;
        treeNode.y0 = treeNode.y;
      });
    },
    [getNodeEnter, getNodeUpdateTransition, getNodeExit, getCircleColor, getLinks]
  );

  const drawTree = useCallback(() => {
    if (!rootRef.current) return;

    document.getElementById("treeContainer").innerHTML = "";
    treeRef.current = d3.layout.tree().size([heightRef.current, widthRef.current]);
    diagonalRef.current = d3.svg.diagonal().projection(d => [d.y, d.x]);
    svgRef.current = d3
      .select("#treeContainer")
      .append("svg")
      .attr("width", widthRef.current + marginRef.current.right + marginRef.current.left)
      .attr("height", heightRef.current + marginRef.current.top + marginRef.current.bottom)
      .append("g")
      .attr("transform", `translate(${marginRef.current.left},${marginRef.current.top})`);

    update(rootRef.current);
    d3.select(window.self.frameElement).style("height", "500px");
  }, [update]);

  const setupTree = useCallback(() => {
    if (!tree || !tree.length) return;

    [rootRef.current] = tree;
    widthRef.current = 170 * depth;
    rootRef.current.x0 = heightRef.current / 2;
    rootRef.current.y0 = 0;
    drawTree();
  }, [tree, depth, drawTree]);

  useEffect(() => {
    setupTree();
  }, [setupTree]);

  return (
    <div>
      <div id="treeContainer" data-testid="rule-tree-container" />
      {errors.length && !isPreviewOnly ? (
        <ul>
          {errors.map((error, index) => (
            <li key={`${error.monitorAssessmentMeasure}-${error.outcome}-${index}`}>
              <span className="text text-danger">
                {`Please fix the "${error.outcome}" outcome of rule for: AM-${error.monitorAssessmentMeasure} - ${error.assessmentName}. It should contain interventions.`}
              </span>
            </li>
          ))}
        </ul>
      ) : null}
    </div>
  );
};

RulesTree.propTypes = {
  tree: PropTypes.array.isRequired,
  errors: PropTypes.array,
  depth: PropTypes.number,
  setSelectedAssessment: PropTypes.func,
  isPreviewOnly: PropTypes.bool
};

const RulesTreeWithData = ({ rootRule, connectedRules, setSelectedAssessment, isPreviewOnly }) => {
  if (!rootRule) return null;

  const { assessments } = useContext(StaticDataContext);
  const relatedAssessmentIds = Array.from(getRelatedAssessmentIds([rootRule, ...connectedRules]));

  const assessmentsWithShortenedNames = assessments
    .filter(ass => relatedAssessmentIds.includes(ass._id))
    .map(ass => ({
      ...ass,
      shortenedName: getAssessmentNameForTree(ass)
    }));

  const treeDisplayData = new TreeDisplayData(rootRule, connectedRules, assessmentsWithShortenedNames);
  const { tree = [], errors = [] } = treeDisplayData.getTreeDisplayData();
  const depth = tree.length > 0 ? getDepth(tree[0]) : 0;
  const connectedRulesIds = (connectedRules || []).map(rule => rule._id);

  return (
    <RulesTree
      tree={tree}
      rootRule={rootRule}
      connectedRules={connectedRules}
      connectedRulesIds={connectedRulesIds}
      errors={errors}
      depth={depth}
      setSelectedAssessment={setSelectedAssessment}
      isPreviewOnly={isPreviewOnly}
    />
  );
};

RulesTreeWithData.propTypes = {
  rootRule: PropTypes.object,
  connectedRules: PropTypes.array,
  setSelectedAssessment: PropTypes.func,
  isPreviewOnly: PropTypes.bool
};

export default memo(RulesTreeWithData, arePropsEqual);

function getDepth(obj) {
  let depth = 0;
  if (obj.children) {
    obj.children.forEach(d => {
      const tmpDepth = getDepth(d);
      if (tmpDepth > depth) {
        depth = tmpDepth;
      }
    });
  }
  return 1 + depth;
}
