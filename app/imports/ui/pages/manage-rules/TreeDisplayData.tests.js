/* eslint-disable no-use-before-define */
import { Assessments } from "/imports/api/assessments/assessments";
import { Rules } from "/imports/api/rules/rules";
import { Interventions } from "/imports/api/interventions/interventions";
import {
  assessments,
  expectedTreeDisplayData,
  interventions,
  rules
} from "../../../test-helpers/data/TreeDisplayData.testData";
import {
  getAssessmentNameForTree,
  getShortenedAssessmentName,
  getSortedOutcomes,
  TreeDisplayData,
  getRelatedAssessmentIds
} from "./TreeDisplayData";

describe("getSortedOutcomes", () => {
  it("should sort the outcomes from worst to best", () => {
    const outcomes = {
      at: {
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
      },
      above: {
        assessmentId: "bhkwFktTpjmag7EvF",
        interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      },
      below: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        interventionIds: []
      }
    };

    const expectedOutcomes = [
      {
        name: "below",
        properties: {
          assessmentId: "yxBaWDvbNjBJLcaHQ",
          interventionIds: []
        }
      },
      {
        name: "at",
        properties: {
          assessmentId: "ZtJxX6rjuoRwAqRFo",
          interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
        }
      },
      {
        name: "above",
        properties: {
          assessmentId: "bhkwFktTpjmag7EvF",
          interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
        }
      }
    ];

    expect(getSortedOutcomes(outcomes)).toMatchObject(expectedOutcomes);
  });
});

describe("getShortenedAssessmentName", () => {
  describe("should provide shortened assessment name", () => {
    const assessmentNamesToExpectedReturnValue = [
      ["Add 2-Digit Numbers with Regrouping", "Add 2-Digit No. w/ Regr"],
      ["Fact Families: Addition/Subtraction 0-20", "FF: Add/Subtr 0-20"],
      ["Fact Families: Add/Subtract 0-5", "FF: Add/Subtr 0-5"],
      ["Sums to 12", "Sums to 12"],
      ["Subtraction 0-20", "Subtr 0-20"],
      ["Multiplication 0-9", "Mult 0-9"],
      ["Subtract 2-digit Numbers without Regrouping", "Subtr 2-digit No. w/o Regr"],
      ["Quantity Comparison 9-19", "QC 9-19"],
      ["Division 5-9", "Div 5-9"],
      ["Add 2-Digit Numbers without Regrouping", "Add 2-Digit No. w/o Regr"],
      ["Substitute Whole Numbers to Solve Equations", "Subst Whole No. to Solve Eqs"],
      ["Identify Number - Draw Circles 1-20", "Id No. - Draw Circles 1-20"],
      ["Add, Subtract, Multiply, Divide with Exponents", "Add, Subtr, Mult, Div w/ Exponents"],
      ["Divide 2-Digit into 3-4 Digit with Remainders", "Div 2-Digit into 3-4 Digit w/ Rems"],
      ["Change Quantity of Dots to 10", "Change Qt of Dots to 10"],
      ["Translate Verbal Expressions into Math Equations", "Translate Verbal Exprs into Math Eqs"]
    ];
    assessmentNamesToExpectedReturnValue.forEach(pair => {
      it(`for ${pair[0]}`, () => {
        expect(getShortenedAssessmentName(pair[0])).toEqual(pair[1]);
      });
    });
  });
});

describe("getAssessmentNameForTree", () => {
  describe("should return assessment name in desired format: [AM-No.- Shortened Assessment Name] with max length of 24 letters", () => {
    const maxNameLength = 24;
    const assessmentsToExpectedValue = [
      [
        {
          name: "Fact Families: Add/Subtract 0-9",
          monitorAssessmentMeasure: "51"
        },
        "AM 51- FF: Add/Subtr 0-9"
      ],
      [{ name: "Subtraction 0-5", monitorAssessmentMeasure: "22" }, "AM 22- Subtr 0-5"],
      [
        {
          name: "Subtract 2-digit Numbers without Regrouping",
          monitorAssessmentMeasure: "30"
        },
        "AM 30- Subtr 2-digit N.."
      ],
      [
        {
          name: "Translate Verbal Expressions into Math Equations",
          monitorAssessmentMeasure: "80"
        },
        "AM 80- Translate Verba.."
      ]
    ];
    assessmentsToExpectedValue.forEach(pair => {
      it(`for AM-${pair[0].name}`, () => {
        const result = getAssessmentNameForTree(pair[0]);
        expect(result).toEqual(pair[1]);
        expect(result.length).toBeLessThanOrEqual(maxNameLength);
      });
    });
  });
});

describe("getTreeDisplayData", () => {
  beforeEach(async () => {
    await Rules.insertAsync(rules);
    await Assessments.insertAsync(assessments);
    await Interventions.insertAsync(interventions);
  });

  afterEach(async () => {
    await Rules.removeAsync({});
    await Assessments.removeAsync({});
    await Interventions.removeAsync({});
  });

  async function createTreeDisplayData(rootRule, otherRules) {
    const relatedAssessmentIds = getRelatedAssessmentIds([rootRule, ...otherRules]);
    const assessmentsData = (
      await Assessments.find({
        _id: { $in: Array.from(relatedAssessmentIds) }
      }).fetchAsync()
    ).map(ass => ({ ...ass, shortenedName: getAssessmentNameForTree(ass) }));

    return new TreeDisplayData(rootRule, otherRules, assessmentsData);
  }

  async function getRulesData() {
    const rootRule = (await Rules.find({}).fetchAsync()).find(rule => rule._id === rule.rootRuleId);
    const otherRules = await Rules.find({ _id: { $ne: rootRule._id } }).fetchAsync();
    return { rootRule, otherRules };
  }

  it("should build a structure for displaying decision tree based on available Rules, Assessments and Interventions", async () => {
    const { rootRule, otherRules } = await getRulesData();
    const treeDisplayData = await createTreeDisplayData(rootRule, otherRules);
    const result = treeDisplayData.getTreeDisplayData().tree;

    expect(result).toMatchObject(expectedTreeDisplayData);
  });

  it("should return errors if the displayed root rule is not self-contained", async () => {
    // WHEN THE RULE IS NOT SELF_CONTAINED IT MEANS IT IS MISSING RELATIONS TO OTHER TREE COMPONENTS AND THEREFORE MAY RESULT IN INFINITE LOOPS
    // WHEN BUILDING TREE
    const { rootRule } = await getRulesData();
    const outcomeToBreak = "above";
    const breakingRule = await Rules.findOneAsync({
      _id: { $ne: rootRule._id },
      [`outcomes.${outcomeToBreak}.assessmentId`]: rootRule.attributeValues.assessmentId
    });
    breakingRule.outcomes[outcomeToBreak].interventionIds = [];
    await Rules.updateAsync(breakingRule._id, { $set: breakingRule });

    // Get updated rules data after modification
    const { otherRules } = await getRulesData();
    const treeDisplayData = await createTreeDisplayData(rootRule, otherRules);
    const { errors } = treeDisplayData.getTreeDisplayData();

    const breakingRuleAssessment = await Assessments.findOneAsync({
      _id: breakingRule.attributeValues.assessmentId
    });
    const { name, monitorAssessmentMeasure } = breakingRuleAssessment;
    expect(errors).toMatchObject([
      {
        assessmentName: name,
        monitorAssessmentMeasure,
        outcome: outcomeToBreak
      }
    ]);
  });
});
