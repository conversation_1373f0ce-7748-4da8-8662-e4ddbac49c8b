import React from "react";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { <PERSON><PERSON> } from "react-bootstrap";
import keyBy from "lodash/keyBy";
import uniqBy from "lodash/uniqBy";
import get from "lodash/get";
import groupBy from "lodash/groupBy";

import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Assessments } from "/imports/api/assessments/assessments";
import { Rules } from "/imports/api/rules/rules";
import { Grades } from "/imports/api/grades/grades";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { Interventions } from "/imports/api/interventions/interventions";
import PageHeader from "../../components/page-header.jsx";
import RulesTree from "./rules-tree";
import { areSubscriptionsLoading, download, isHighSchoolGrade } from "../../utilities";
import Loading from "../../components/loading";
import { AssessmentTargetModifier } from "./assessment-target-modifier";
import { normalizeGrade } from "/imports/api/utilities/sortingHelpers/normalizeSortItem";
import { getCSV } from "../data-admin/upload/file-upload-utils";
import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";
import { translateBenchmarkPeriod } from "/imports/api/utilities/utilities";
import ManageGroupedAssessments from "./manage-grouped-assessments";

const defaultState = props => ({
  grade: props.grade || "",
  benchmarkPeriodId: props.benchmarkPeriodId || "",
  screeningAssignmentId: props.benchmarkAssessmentId || ""
});

class ManageRules extends React.Component {
  state = {
    selectedAssessment: null,
    selectedAssessmentDocument: null,
    selectedRuleDocument: null,
    isModifyingTargets: false,
    isUpdatingRules: false,
    ...defaultState(this.props)
  };

  getSummaryText() {
    const { selectedAssessment } = this.state;
    const { isPreviewOnly } = this.props;
    if (!selectedAssessment) {
      return null;
    }
    if (isPreviewOnly) {
      return (
        <div className="position-absolute" style={{ marginTop: "10px", left: "50%", transform: "translateX(-50%)" }}>
          {selectedAssessment.isComplete ? null : <span>Selected Node: </span>}
          <b>
            {selectedAssessment?.monitorAssessmentMeasure
              ? `AM ${selectedAssessment?.monitorAssessmentMeasure} - `
              : ""}
            {selectedAssessment?.fullName}
          </b>
        </div>
      );
    }

    const hasInterventions = !!(selectedAssessment.interventionIds && selectedAssessment.interventionIds.length);
    const { isRoot, isComplete } = selectedAssessment;
    let summaryText;
    if (hasInterventions) {
      summaryText = (
        <small>
          The selected component is an Intervention Protocol for <b>{selectedAssessment.fullName}</b>
        </small>
      );
    } else if (isRoot) {
      summaryText = (
        <small>
          The selected component: <b>{selectedAssessment.fullName}</b> is a root assessment
        </small>
      );
    } else if (isComplete) {
      summaryText = <small>The selected component indicates the Progress Monitoring was successfully completed</small>;
    } else {
      summaryText = (
        <small>
          The selected assessment: <b>{selectedAssessment.fullName}</b> is the
          <b>&nbsp;&quot;{selectedAssessment.outcome}&quot;</b>
          &nbsp;outcome of <b>{selectedAssessment.parent.fullName}</b>
        </small>
      );
    }
    return <div className="text-center">{summaryText}</div>;
  }

  setSelectedAssessment = selectedAssessment => {
    this.setState({
      selectedAssessment,
      selectedAssessmentDocument: Assessments.findOne(selectedAssessment.assessmentId),
      selectedRuleDocument: selectedAssessment.ruleId ? Rules.findOne(selectedAssessment.ruleId) : null
    });
  };

  setGrade = () => e => {
    this.updateFormValue(e.currentTarget.name, e.currentTarget.value, ["benchmarkPeriodId", "screeningAssignmentId"]);
  };

  setBenchmarkPeriod = () => e => {
    this.updateFormValue(e.currentTarget.name, e.currentTarget.value, ["screeningAssignmentId"]);
  };

  updateFormValue = (inputName, value, fieldsToReset = []) => {
    let updatedState = {};
    if (fieldsToReset.length) {
      fieldsToReset.forEach(field => {
        updatedState[field] = defaultState[field];
      });
      updatedState[inputName] = value;
    } else {
      updatedState = {
        ...this.state,
        [inputName]: value
      };
    }
    this.setState({
      ...updatedState,
      selectedAssessment: null,
      selectedAssessmentDocument: null,
      selectedRuleDocument: null
    });
  };

  getAvailableAssignments() {
    const screeningAssignment = this.props.screeningAssignments.find(
      sa => sa.grade === this.state.grade && sa.benchmarkPeriodId === this.state.benchmarkPeriodId
    );
    if (!screeningAssignment || !screeningAssignment.assessmentIds.length) {
      return [];
    }
    const availableAssessmentsCursor = Assessments.find({
      _id: { $in: screeningAssignment.assessmentIds }
    });
    const assessmentById = keyBy(availableAssessmentsCursor.fetch(), "_id");
    const sortedAssessments = [];
    screeningAssignment.assessmentIds.forEach(aId => {
      const assessment = assessmentById[aId];
      if (assessment) {
        sortedAssessments.push(assessment);
      }
    });
    return availableAssessmentsCursor.count()
      ? sortedAssessments.map(assessment => (
          <option key={assessment._id} value={assessment._id}>
            {assessment.name}
          </option>
        ))
      : [];
  }

  getRulesForRulesTree = () => {
    const query = {
      "attributeValues.assessmentId": this.state.screeningAssignmentId,
      "attributeValues.grade": this.state.grade,
      "attributeValues.benchmarkPeriod": BenchmarkPeriods.findOne(this.state.benchmarkPeriodId)?.label
    };
    const gradeRulesForAssessment = Rules.find(query).fetch();
    const rootRules = gradeRulesForAssessment.filter(rule => rule._id === rule.rootRuleId);
    const rootRule = null;
    const connectedRules = null;
    if (rootRules.length !== 1) {
      Alert.error(`Found: ${rootRules.length} rules. There should be one rule for the chosen screening assignment.`);
      return { rootRule, connectedRules };
    }
    const rootRuleId = rootRules[0]._id;
    return {
      rootRule: rootRules[0],
      connectedRules: Rules.find({
        rootRuleId,
        _id: { $ne: rootRuleId }
      }).fetch()
    };
  };

  addTargetToAssessment = (params, callback) => {
    Meteor.call("Assessments:addTargetToAssessment", params, err => {
      if (!err) {
        Alert.success(`Custom ${params.targetName} targets added successfully!`, { timeout: 5000 });
        this.setState({
          selectedAssessmentDocument: Assessments.findOne(params.assessmentId)
        });
        callback();
      }
    });
  };

  updateAssessmentTargets = ({ assessmentId, grade, targets }, callback) => {
    this.setState({ isModifyingTargets: true });
    Meteor.call("Assessments:updateTargets", { assessmentId, grade, targets }, err => {
      this.setState({ isModifyingTargets: false });
      if (!err) {
        Alert.success("Targets saved successfully!", { timeout: 5000 });
        this.setState({
          selectedAssessmentDocument: Assessments.findOne(assessmentId)
        });
        callback();
      } else if (err) {
        Alert.error(err.reason || "There was a problem modifying assessment target...", { timeout: 5000 });
      }
    });
  };

  removeTargetFromAssessment = assessmentType => e => {
    e.preventDefault();
    const {
      selectedAssessment: { assessmentId },
      grade
    } = this.state;
    Meteor.call("Assessments:removeTargetFromAssessment", { assessmentId, grade, targetName: assessmentType }, err => {
      if (!err) {
        Alert.success(`The ${assessmentType} target was successfully removed!`, { timeout: 5000 });
        this.setState({
          selectedAssessmentDocument: Assessments.findOne(assessmentId)
        });
      }
    });
  };

  updateRuleOutcomes = outcomeAssessments => {
    this.setState({ isUpdatingRules: true });
    Meteor.call(
      "Rules:updateRuleOutcomes",
      this.state.selectedAssessment.ruleId,
      outcomeAssessments,
      this.ruleUpdateCallback
    );
  };

  updateInterventionOutcomes = outcomeInterventions => {
    this.setState({ isUpdatingRules: true });
    Meteor.call(
      "Rules:updateOutcomeInterventions",
      this.state.selectedAssessment.ruleId,
      outcomeInterventions,
      this.ruleUpdateCallback
    );
  };

  addInterventionsToOutcome = (outcomeName, assessmentId) => e => {
    e.preventDefault();
    this.setState({ isUpdatingRules: true });
    Meteor.call(
      "Rules:addInterventionsToOutcome",
      {
        outcomeName,
        ruleId: this.state.selectedAssessment.ruleId,
        assessmentId
      },
      this.ruleUpdateCallback
    );
  };

  addOutcomeToRule = outcomeName => e => {
    e.preventDefault();
    this.setState({ isUpdatingRules: true });
    Meteor.call("Rules:addOutcomeToRule", this.state.selectedAssessment.ruleId, outcomeName, this.ruleUpdateCallback);
  };

  removeRuleOutcome = outcomeName => e => {
    e.preventDefault();
    this.setState({ isUpdatingRules: true });
    Meteor.call("Rules:removeRuleOutcome", this.state.selectedAssessment.ruleId, outcomeName, this.ruleUpdateCallback);
  };

  addRootRule = e => {
    e.preventDefault();
    const { grade, benchmarkPeriodId } = this.state;
    Meteor.call(
      "Rules:addRootRuleFor",
      {
        assessmentId: this.state.screeningAssignmentId,
        grade,
        benchmarkPeriodId
      },
      err => {
        if (!err) {
          Alert.success("The new root rule was successfully added! Please re-select the benchmark assessment", {
            timeout: 7000
          });
        } else {
          Alert.error(err.reason || "There was a problem adding a root rule provided benchmark assessment", {
            timeout: 5000
          });
        }
        this.setState({
          screeningAssignmentId: ""
        });
      }
    );
  };

  ruleUpdateCallback = err => {
    const stateToUpdate = { isUpdatingRules: false };
    if (err) {
      Alert.error(err.reason || "There was a problem with updating the chosen rule", { timeout: 5000 });
    } else {
      Alert.success("Rule successfully updated!", { timeout: 5000 });
      stateToUpdate.selectedRuleDocument = Rules.findOne(this.state.selectedAssessment.ruleId);
    }
    this.setState(stateToUpdate);
  };

  getBenchmarkPeriods() {
    return isHighSchoolGrade(this.state.grade)
      ? [this.props.benchmarkPeriods.find(bp => bp.name === "All")]
      : this.props.benchmarkPeriods.filter(bp => bp.name !== "All");
  }

  drawTree() {
    if (!this.state.grade || !this.state.benchmarkPeriodId || !this.state.screeningAssignmentId) {
      return null;
    }
    const { rootRule, connectedRules } = this.getRulesForRulesTree();
    if (!rootRule) {
      return (
        <React.Fragment>
          <Button className="btn btn-primary btn-info btn-center mt-1" name={"addRootRule"} onClick={this.addRootRule}>
            Add Root Rule for selected Screening Assignment
          </Button>
        </React.Fragment>
      );
    }
    return (
      <RulesTree
        rootRule={rootRule}
        connectedRules={connectedRules}
        setSelectedAssessment={this.setSelectedAssessment}
        isPreviewOnly={this.props.isPreviewOnly}
      />
    );
  }

  getInterventionOutcomeNames = ({ assessmentsById, interventionsById, ruleOutcome }) => {
    if (!ruleOutcome) {
      return {
        assessmentName: "No defined outcome",
        monitorAssessmentMeasure: "No defined outcome",
        joinedInterventionNames: "No defined outcome"
      };
    }
    const assessment = assessmentsById[ruleOutcome.assessmentId] || {};
    const assessmentName = assessment.name || "N/A";
    const monitorAssessmentMeasure = assessment.monitorAssessmentMeasure || "N/A";
    const joinedInterventionNames =
      (ruleOutcome.interventionIds &&
        ruleOutcome.interventionIds
          .map(interventionId => get(interventionsById[interventionId], "name", " - ").replace(/^.*?\s-\s/, ""))
          .join(", ")) ||
      "No interventions";
    return {
      assessmentName,
      monitorAssessmentMeasure,
      joinedInterventionNames
    };
  };

  prepareRulesWithAssessment = (parseInterventionOutcomes = false) => {
    const assessmentsById = keyBy(this.props.assessments, "_id");
    const interventionsById = keyBy(this.props.interventions, "_id");
    const rulesById = keyBy(this.props.rules, "_id");
    let rulesToUse = this.props.rules;
    if (parseInterventionOutcomes) {
      rulesToUse = this.parseAllTreeNodes();
    }
    let rulesWithAssessment = rulesToUse.map(rule => {
      const assessment = assessmentsById[rule.attributeValues.assessmentId];
      let dataObject = {
        ...rule,
        assessmentTargets: assessment.strands[0].scores[0].targets,
        monitorAssessmentMeasure: assessment.monitorAssessmentMeasure,
        assessmentName: assessment.name
      };

      if (parseInterventionOutcomes) {
        const rootRule = rulesById[rule.rootRuleId];
        const nameToUse = rootRule
          ? get(assessmentsById[rootRule.attributeValues.assessmentId], "name", "N/A")
          : get(assessmentsById[rule.attributeValues.assessmentId], "name", "N/A");
        const nodeIndexLabel =
          (rule.colIndex || rule.colIndex === 0) && (rule.colRowIndex || rule.colRowIndex === 0)
            ? `${rule.colIndex} - ${rule.colRowIndex}`
            : "N/A";
        dataObject = {
          ...dataObject,
          screeningAssignmentName: nameToUse,
          grade: rule.attributeValues.grade,
          assessmentTargets: assessment.strands[0].scores[0].targets,
          monitorAssessmentMeasure: assessment.monitorAssessmentMeasure,
          assessmentName: assessment.name,
          nodeIndexLabel,
          colIndex: rule.colIndex,
          colRowIndex: rule.colRowIndex,
          interventionOutcomes: {
            above: this.getInterventionOutcomeNames({
              assessmentsById,
              interventionsById,
              ruleOutcome: rule.outcomes.above
            }),
            at: this.getInterventionOutcomeNames({
              assessmentsById,
              interventionsById,
              ruleOutcome: rule.outcomes.at
            }),
            below: this.getInterventionOutcomeNames({
              assessmentsById,
              interventionsById,
              ruleOutcome: rule.outcomes.below
            })
          }
        };
      }
      return dataObject;
    });

    if (!parseInterventionOutcomes) {
      rulesWithAssessment = uniqBy(
        rulesWithAssessment,
        elem => `${elem.attributeValues.grade} ${elem.attributeValues.assessmentId}`
      );
    }

    rulesWithAssessment.sort((a, b) => {
      const gradeA = normalizeGrade(a.attributeValues.grade);
      const gradeB = normalizeGrade(b.attributeValues.grade);
      return (
        gradeA - gradeB ||
        a.monitorAssessmentMeasure.localeCompare(b.monitorAssessmentMeasure, undefined, {
          numeric: true
        })
      );
    });
    return rulesWithAssessment;
  };

  parseAllTreeNodes = () => {
    const allTrees = [];
    const screeningAssignmentAssessmentIdsByBenchmarkPeriod = this.props.screeningAssignments.reduce((a, c) => {
      if (!a[c.benchmarkPeriodId]) {
        // eslint-disable-next-line no-param-reassign
        a[c.benchmarkPeriodId] = [];
      }
      a[c.benchmarkPeriodId].push(...c.assessmentIds);
      return a;
    }, {});
    Object.entries(groupBy(this.props.rules, "rootRuleId")).forEach(([rootRuleId, connectedRules]) => {
      const currentTreeNodes = this.traverseTreeNodes(
        rootRuleId,
        connectedRules,
        screeningAssignmentAssessmentIdsByBenchmarkPeriod
      );
      allTrees.push(...currentTreeNodes);
    });
    Object.entries(screeningAssignmentAssessmentIdsByBenchmarkPeriod).forEach(([benchmarkPeriodId, assessmentIds]) => {
      const translatedBenchmarkPeriod = translateBenchmarkPeriod(benchmarkPeriodId);
      const treeAssessmentIds = allTrees
        .filter(
          at => at.attributeValues.benchmarkPeriod === translatedBenchmarkPeriod.label && at._id === at.rootRuleId
        )
        .map(at => at.attributeValues.assessmentId);
      const assessmentIdsNotIncluded = assessmentIds.filter(a => !treeAssessmentIds.includes(a));
      assessmentIdsNotIncluded.forEach(assessmentId => {
        const screeningAssignment = this.props.screeningAssignments.find(sa => sa.assessmentIds.includes(assessmentId));
        allTrees.push({
          attributeValues: {
            grade: screeningAssignment.grade,
            benchmarkPeriod: translatedBenchmarkPeriod.label,
            assessmentId
          },
          colIndex: 0,
          colRowIndex: 0,
          outcomes: {
            above: {},
            at: {},
            below: {}
          }
        });
      });
    });
    return allTrees;
  };

  getChildNodes = ({ parentNode, treeNodes, parsedNodes }) => {
    const childNodes = [];
    ["below", "at", "above"].forEach(outcome => {
      const outcomeAssessmentId = get(parentNode, `outcomes.${outcome}.assessmentId`);
      const filteredParsedNode =
        parsedNodes.filter(node => node.attributeValues.assessmentId === outcomeAssessmentId) || [];
      const rule = treeNodes.find(node => node.attributeValues.assessmentId === outcomeAssessmentId);
      if (
        !filteredParsedNode.length &&
        parentNode.attributeValues.assessmentId !== outcomeAssessmentId &&
        !childNodes.find(c => c && rule && c._id === rule._id)
      ) {
        childNodes.push(rule);
      }
    });

    return childNodes.filter(f => f);
  };

  traverseTreeNodes = (rootRuleId, treeNodes, screeningAssignmentAssessmentIdsByBenchmarkPeriod) => {
    const rootNode = treeNodes.find(t => t._id === rootRuleId);
    if (!rootNode) {
      return [];
    }
    const screeningAssignmentAssessmentIds =
      screeningAssignmentAssessmentIdsByBenchmarkPeriod[
        translateBenchmarkPeriod(rootNode.attributeValues.benchmarkPeriod).id
      ] || [];
    if (!screeningAssignmentAssessmentIds.includes(rootNode.attributeValues.assessmentId)) {
      return [];
    }
    rootNode.colIndex = 0;
    rootNode.colRowIndex = 0;
    const nodes = this.getChildNodes({ parentNode: rootNode, treeNodes, parsedNodes: [rootNode] });
    const parsedNodes = [rootNode];
    nodes.push(null);
    let colIndex = 1;
    let colRowIndex = 0;
    while (nodes.length) {
      const currentNode = nodes.shift();
      if (currentNode) {
        const childNodes = this.getChildNodes({ parentNode: currentNode, treeNodes, parsedNodes });
        currentNode.colIndex = colIndex;
        currentNode.colRowIndex = colRowIndex;
        colRowIndex += 1;
        parsedNodes.push(currentNode);
        // eslint-disable-next-line no-loop-func
        const childrenNodesWithIndexes = childNodes.map(node => ({ ...node, colIndex, colRowIndex }));
        nodes.push(...childrenNodesWithIndexes);
      } else {
        colIndex += 1;
        colRowIndex = 0;
        if (nodes.length) {
          nodes.push(null);
        }
      }
    }
    return parsedNodes;
  };

  exportTargetsCSV = () => {
    const rulesWithAssessment = this.prepareRulesWithAssessment();

    const data = rulesWithAssessment.map(ruleWithAssessment => {
      const targetsForPeriodAndType = {};
      const targetsByGrade = ruleWithAssessment.assessmentTargets.filter(
        target => target.grade === ruleWithAssessment.attributeValues.grade
      );

      const periods = ["Fall", "Winter", "Spring", "All"];
      const assessmentTypes = [undefined, "benchmark", "individual"];
      periods.forEach(period => {
        assessmentTypes.forEach(assessmentType => {
          const assessmentTypesObj = targetsByGrade.find(elem => elem.assessmentType === assessmentType);
          const periodObj = assessmentTypesObj && assessmentTypesObj.periods.find(elem => elem.name === period);
          const periodName = period === "All" ? "All Periods" : period;
          const periodAndTargetType = `${periodName} ${assessmentType || "default"}`;
          targetsForPeriodAndType[`${periodAndTargetType} instructional target`] = periodObj && periodObj.values[0];
          targetsForPeriodAndType[`${periodAndTargetType} mastery target`] = periodObj && periodObj.values[1];
        });
      });

      return {
        Grade: ruleWithAssessment.attributeValues.grade,
        "Assessment Measure": ruleWithAssessment.monitorAssessmentMeasure,
        "Assessment Name": ruleWithAssessment.assessmentName,
        ...targetsForPeriodAndType
      };
    });
    const hrefData = `data:application/octet-stream,${getCSV(data)}`;
    download({
      filename: `ruleTargetsData_${new Date().toISOString().slice(0, 10)}.csv`,
      hrefData
    });
  };

  exportOutcomeRulesCSV = () => {
    const rulesWithAssessment = this.prepareRulesWithAssessment(true);

    const data = rulesWithAssessment.map(ruleWithAssessment => {
      const seasonsByLabel = {
        "fall-period": "Fall",
        "winter-period": "Winter",
        "spring-period": "Spring",
        "all-periods": "All Periods"
      };

      const {
        attributeValues,
        monitorAssessmentMeasure,
        assessmentName,
        interventionOutcomes,
        screeningAssignmentName
      } = ruleWithAssessment;
      const targets = {};
      ["Below", "At", "Above"].forEach(target => {
        targets[`Target ${target} Assessment - Outcome Rule`] =
          interventionOutcomes[target.toLowerCase()].monitorAssessmentMeasure;
        targets[`Target ${target} - Outcome Rule Name`] = interventionOutcomes[target.toLowerCase()].assessmentName;
        targets[`Target ${target} - Interventions`] =
          interventionOutcomes[target.toLowerCase()].joinedInterventionNames;
      });

      return {
        Grade: attributeValues.grade,
        Season: seasonsByLabel[attributeValues.benchmarkPeriod],
        "Screening Assignment": screeningAssignmentName,
        colIndex: ruleWithAssessment.colIndex,
        colRowIndex: ruleWithAssessment.colRowIndex,
        "Node Number": ruleWithAssessment.nodeIndexLabel,
        "Assessment Measure": parseInt(monitorAssessmentMeasure),
        "Assessment Name": assessmentName,
        ...targets
      };
    });

    let sortedData = [];
    if (data && data.length) {
      sortedData = sortByPropertyFor({
        list: data,
        paths: ["Grade", "Season", "Screening Assignment", "colIndex", "colRowIndex"]
      });
    }

    sortedData = sortedData.map(d => {
      /* eslint-disable no-param-reassign */
      delete d.colIndex;
      delete d.colRowIndex;
      return d;
    });

    const hrefData = `data:application/octet-stream,${getCSV(sortedData)}`;
    download({
      filename: `outcomeRulesData_${new Date().toISOString().slice(0, 10)}.csv`,
      hrefData
    });
  };

  renderProgressMonitoringManagement = () => {
    const { isPreviewOnly } = this.props;
    return (
      <div className="card-box">
        {this.props.loading ? (
          <Loading />
        ) : (
          <React.Fragment>
            <div className="row">
              <div className="choose-rule-text col-md-3 offset-lg-1 text-end">
                <label>Choose rule to display:</label>
              </div>
              <div className="col-md-2 col-lg-1">
                <select
                  className="form-select form-select-sm"
                  type="select"
                  name="grade"
                  data-testid="grade-selector"
                  onChange={this.setGrade()}
                  value={this.state.grade || "Grade"}
                >
                  <option disabled>Grade</option>
                  {this.props.grades.map(grade => (
                    <option key={grade._id}>{grade._id}</option>
                  ))}
                </select>
              </div>
              <div className="col-md-4 col-lg-2">
                <select
                  className="form-select form-select-sm"
                  type="select"
                  name="benchmarkPeriodId"
                  data-testid="benchmarkPeriod-selector"
                  onChange={this.setBenchmarkPeriod()}
                  value={this.state.benchmarkPeriodId || "Benchmark Period"}
                >
                  <option disabled>Benchmark Period</option>
                  {this.getBenchmarkPeriods().map(bp => (
                    <option key={bp._id} value={bp._id}>
                      {bp.name}
                    </option>
                  ))}
                </select>
              </div>
              {this.state.benchmarkPeriodId && this.state.grade && (
                <div className="col-md-4 col-lg-3">
                  <select
                    className="form-select form-select-sm"
                    type="select"
                    name="screeningAssignmentId"
                    data-testid="screeningAssignment-selector"
                    onChange={e => this.updateFormValue(e.currentTarget.name, e.currentTarget.value)}
                    value={this.state.screeningAssignmentId || "Screening Assignment"}
                  >
                    <option disabled>Screening Assignment</option>
                    {this.getAvailableAssignments()}
                  </select>
                </div>
              )}
            </div>
            {this.getSummaryText()}
            {this.drawTree()}
            {this.state.selectedAssessment ? (
              <AssessmentTargetModifier
                selectedAssessment={this.state.selectedAssessment}
                selectedAssessmentDocument={this.state.selectedAssessmentDocument}
                grade={this.state.grade}
                benchmarkPeriodId={this.state.benchmarkPeriodId}
                benchmarkPeriods={this.props.benchmarkPeriods}
                interventions={this.props.interventions}
                isModifyingTargets={this.state.isModifyingTargets}
                isUpdatingRules={this.state.isUpdatingRules}
                addTargetToAssessment={this.addTargetToAssessment}
                updateAssessmentTargets={this.updateAssessmentTargets}
                updateRuleOutcomes={this.updateRuleOutcomes}
                updateInterventionOutcomes={this.updateInterventionOutcomes}
                addInterventionsToOutcome={this.addInterventionsToOutcome}
                addOutcomeToRule={this.addOutcomeToRule}
                removeRuleOutcome={this.removeRuleOutcome}
                removeTargetFromAssessment={this.removeTargetFromAssessment}
                selectedRuleDocument={this.state.selectedRuleDocument}
                isPreviewOnly={isPreviewOnly}
              />
            ) : null}
          </React.Fragment>
        )}
      </div>
    );
  };

  renderManageRules = () => {
    return (
      <div className="conFullScreen">
        <div className="relativeWrapper">
          <span className="buttonInPageHeader">
            <Button
              className="btn btn-primary btn-success"
              name="exportOutcomeCSV"
              data-testid="exportOutcomeCSV"
              onClick={this.exportOutcomeRulesCSV}
            >
              Export Outcome Rules
            </Button>{" "}
            <Button
              className="btn btn-primary btn-success"
              name="exportTargetsCSV"
              data-testid="exportTargetsCSV"
              onClick={this.exportTargetsCSV}
            >
              Export Targets
            </Button>
          </span>
          <PageHeader title={"Rule management"} description={"Setup the progress monitoring rules."} />
        </div>
        <div className="container">
          <ManageGroupedAssessments />
          <hr />
          {this.renderProgressMonitoringManagement()}
        </div>
      </div>
    );
  };

  renderPreviewManageRules = () => {
    return <div className="conFullScreen">{this.renderProgressMonitoringManagement()}</div>;
  };

  render() {
    return this.props.isPreviewOnly ? this.renderPreviewManageRules() : this.renderManageRules();
  }
}

ManageRules.propTypes = {
  screeningAssignments: PropTypes.array,
  assessments: PropTypes.array,
  rules: PropTypes.array,
  grades: PropTypes.array,
  benchmarkPeriods: PropTypes.array,
  interventions: PropTypes.array,
  loading: PropTypes.bool,
  isPreviewOnly: PropTypes.bool
};

ManageRules.defaultProps = {
  isPreviewOnly: false
};

export default withTracker(props => {
  const saHandle = Meteor.subscribe("ScreeningAssignments");
  const assessmentsHanlde = Meteor.subscribe("Assessments:ProgressMonitoringManagement");
  const rulesHandle = Meteor.subscribe("Rules:ProgressMonitoringManagement");
  const gradesHandle = Meteor.subscribe("Grades");
  const bpHandle = Meteor.subscribe("BenchmarkPeriods");
  const interventionsHandle = Meteor.subscribe("Interventions:ProgressMonitoring");
  const loading = areSubscriptionsLoading(
    saHandle,
    assessmentsHanlde,
    rulesHandle,
    gradesHandle,
    bpHandle,
    interventionsHandle
  );
  let screeningAssignments = [];
  let assessments = [];
  let rules = [];
  let grades = [];
  let benchmarkPeriods = [];
  let interventions = [];
  if (!loading) {
    screeningAssignments = ScreeningAssignments.find({}).fetch();
    assessments = Assessments.find({}).fetch();
    rules = Rules.find().fetch();
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    benchmarkPeriods = BenchmarkPeriods.find({}, { fields: { name: 1, label: 1 }, sort: { sortorder: 1 } }).fetch();
    interventions = Interventions.find({}).fetch();
  }
  return {
    ...props,
    screeningAssignments,
    assessments,
    rules,
    grades,
    benchmarkPeriods,
    interventions,
    loading
  };
})(ManageRules);

export { ManageRules as PureManageRules };
