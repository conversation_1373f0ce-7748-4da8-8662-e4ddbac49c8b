import React, { Component } from "react";
import { But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { AssessmentOutcomesRow } from "./assessment-outcomes-row";
import { indexToOutcomeName } from "./TreeDisplayData";

export class RuleOutcomesTable extends Component {
  render() {
    return (
      <table className="table table-bordered">
        <thead>
          <tr>
            <td className="col-md-2">Targets</td>
            <td className="col-md-5">Outcome Rule</td>
            <td className="col-md-5">Interventions</td>
          </tr>
        </thead>
        <tbody>
          {Object.values(this.props.selectedAssessment.children).map((properties, index) => (
            <AssessmentOutcomesRow
              key={`row-${indexToOutcomeName[index]}`}
              outcome={this.props.ruleOutcomes[indexToOutcomeName[index]]}
              outcomeName={indexToOutcomeName[index]}
              availableAssessments={this.props.availableAssessments}
              monitorAssessmentMeasure={properties.monitorAssessmentMeasure}
              handleRuleChange={this.props.handleRuleChange}
              interventionOutcomes={this.props.interventionOutcomes}
              availableInterventions={this.props.availableInterventions}
              handleInterventionsChange={this.props.handleInterventionsChange}
              addInterventionsToOutcome={this.props.addInterventionsToOutcome}
              addOutcomeToRule={this.props.addOutcomeToRule}
              removeRuleOutcome={this.props.removeRuleOutcome}
              isEnding={properties.isEnding}
              isPreviewOnly={this.props.isPreviewOnly}
            />
          ))}
        </tbody>
        {!this.props.isPreviewOnly ? (
          <tfoot>
            <tr>
              <td />
              <td>
                <Button
                  className="btn btn-primary pull-right"
                  name={"saveOutcomes"}
                  onClick={this.props.updateOutcomes}
                >
                  Save Outcomes!
                </Button>
              </td>
              <td>
                <Button
                  className="btn btn-primary pull-right"
                  name={"saveInterventions"}
                  onClick={this.props.updateInterventions}
                >
                  Save Interventions!
                </Button>
              </td>
            </tr>
          </tfoot>
        ) : null}
      </table>
    );
  }
}

RuleOutcomesTable.propTypes = {
  selectedAssessment: PropTypes.object,
  ruleOutcomes: PropTypes.object,
  interventionOutcomes: PropTypes.object,
  updateOutcomes: PropTypes.func,
  updateInterventions: PropTypes.func,
  handleRuleChange: PropTypes.func,
  addInterventionsToOutcome: PropTypes.func,
  handleInterventionsChange: PropTypes.func,
  addOutcomeToRule: PropTypes.func,
  removeRuleOutcome: PropTypes.func,
  availableInterventions: PropTypes.array,
  availableAssessments: PropTypes.array,
  isPreviewOnly: PropTypes.bool
};
