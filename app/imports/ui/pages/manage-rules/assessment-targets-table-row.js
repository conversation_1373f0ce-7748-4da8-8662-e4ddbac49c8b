import React from "react";
import PropTypes from "prop-types";

export function AssessmentTargetsTableRow(props) {
  return (
    <React.Fragment>
      <tr>
        <td>{props.periodTargetName}</td>
        <td>
          {props.isPreviewOnly ? (
            props.targets.instructionalTarget
          ) : (
            <input
              type="number"
              min="0"
              max="300"
              data-update-string={props.updateQueryString}
              name="instructionalTarget"
              value={props.targets.instructionalTarget}
              onChange={props.updateTarget}
            />
          )}
        </td>
        <td>
          {props.isPreviewOnly ? (
            props.targets.masteryTarget
          ) : (
            <input
              type="number"
              min="0"
              max="300"
              data-update-string={props.updateQueryString}
              name="masteryTarget"
              value={props.targets.masteryTarget}
              onChange={props.updateTarget}
            />
          )}
        </td>
      </tr>
    </React.Fragment>
  );
}

AssessmentTargetsTableRow.propTypes = {
  targets: PropTypes.object,
  updateTarget: PropTypes.func,
  updateQueryString: PropTypes.string,
  periodTargetName: PropTypes.string,
  isPreviewOnly: PropTypes.bool
};
