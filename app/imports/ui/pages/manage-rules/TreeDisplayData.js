import get from "lodash/get";

export const targetsSortOrder = {
  above: 2,
  at: 1,
  below: 0
};

export const indexToOutcomeName = {
  0: "below",
  1: "at",
  2: "above"
};

export class TreeDisplayData {
  constructor(rootRule, otherRules, assessments) {
    this.assessments = assessments;
    this.rootRule = rootRule;
    this.otherRules = otherRules;
    this.assessmentsProcessed = new Set();
    this.errors = [];
  }

  getChildNode(outcome, outcomeName, parentRuleId) {
    if (!outcome || !outcome.assessmentId) {
      return {
        name: "Passed...",
        outcome: outcomeName,
        isEnding: true,
        isComplete: true
      };
    }
    const assessment = this.assessments.find(ass => ass._id === outcome.assessmentId);
    const isEnding = !!(outcome.interventionIds && outcome.interventionIds.length);
    const shouldBeEnding = this.assessmentsProcessed.has(outcome.assessmentId);
    if (isEnding !== shouldBeEnding) {
      const parentRule = [this.rootRule, ...this.otherRules].find(rule => rule._id === parentRuleId);
      const ruleAssessmentId = parentRule.attributeValues.assessmentId;
      const { name: assessmentName, monitorAssessmentMeasure } = this.assessments.find(
        ass => ass._id === ruleAssessmentId
      );
      this.errors.push({
        assessmentName,
        monitorAssessmentMeasure,
        outcome: outcomeName
      });
    }
    if (isEnding || shouldBeEnding) {
      return {
        name: assessment.shortenedName,
        fullName: assessment.name,
        assessmentId: outcome.assessmentId,
        monitorAssessmentMeasure: assessment.monitorAssessmentMeasure,
        outcome: outcomeName,
        ruleId: null,
        isEnding: true,
        interventionIds: shouldBeEnding === isEnding ? outcome.interventionIds : []
      };
    }
    const rule = this.getRule(outcome);
    const sortedOutcomes = getSortedOutcomes(rule.outcomes);
    return this.getTreeComponent(assessment, sortedOutcomes, rule._id, outcomeName);
  }

  getRule(outcome) {
    return this.otherRules.find(r => r.attributeValues.assessmentId === outcome.assessmentId);
  }

  getTreeComponent(ruleAssessment, sortedOutcomes, ruleId, outcomeName = null) {
    this.assessmentsProcessed.add(ruleAssessment._id);
    return {
      name: ruleAssessment.shortenedName,
      fullName: ruleAssessment.name,
      assessmentId: ruleAssessment._id,
      monitorAssessmentMeasure: ruleAssessment.monitorAssessmentMeasure,
      ruleId,
      children: sortedOutcomes.map(outcome => this.getChildNode(outcome.properties, outcome.name, ruleId)),
      ...(outcomeName ? { outcome: outcomeName } : { isRoot: true })
    };
  }

  getTreeDisplayData() {
    const rootRuleAssessmentId = this.rootRule.attributeValues.assessmentId;
    const ruleAssessment = this.assessments.find(ass => ass._id === rootRuleAssessmentId);
    const sortedOutcomes = getSortedOutcomes(this.rootRule.outcomes);
    return {
      tree: [this.getTreeComponent(ruleAssessment, sortedOutcomes, this.rootRule._id)],
      errors: this.errors
    };
  }
}

export function getSortedOutcomes(outcomes) {
  return Object.entries(outcomes)
    .map(([name, properties]) => ({ name, properties }))
    .sort((a, b) => targetsSortOrder[a.name] - targetsSortOrder[b.name]);
}

export function getRelatedAssessmentIds(rules) {
  const relatedAssessmentIds = new Set();
  rules.forEach(rule => {
    relatedAssessmentIds.add(get(rule, "attributeValues.assessmentId", ""));
    relatedAssessmentIds.add(get(rule, "outcomes.above.assessmentId", ""));
    relatedAssessmentIds.add(get(rule, "outcomes.at.assessmentId", ""));
    relatedAssessmentIds.add(get(rule, "outcomes.below.assessmentId", ""));
  });
  return relatedAssessmentIds;
}

export function getShortenedAssessmentName(name) {
  const rules = [
    [/Fact Families/i, "FF"],
    [/Quantity Comparisons/i, "QC"],
    [/Quantity Comparison/i, "QC"],
    [/Quantity Compare/i, "QC"],
    [/Expressions/i, "Exprs"],
    [/Expression/i, "Expr"],
    [/Mixed Operations/i, "MO"],
    [/Compare/i, "Comp"],
    [/Regrouping/i, "Regr"],
    [/Identify/i, "Id"],
    [/Subtraction/i, "Subtr"],
    [/Subtract/i, "Subtr"],
    [/Addition/i, "Add"],
    [/Multiplication/i, "Mult"],
    [/Multiply/i, "Mult"],
    [/Division/i, "Div"],
    [/Divide/i, "Div"],
    [/Substitute/i, "Subst"],
    [/Remainders/i, "Rems"],
    [/Equations/i, "Eqs"],
    [/Equation/i, "Eq"],
    [/Numbers/i, "No."],
    [/Number/i, "No."],
    [/Quantity/i, "Qt"],
    [/ with /i, " w/ "],
    [/ without /i, " w/o "]
  ];

  return rules.reduce((acc, cv) => acc.replace(cv[0], cv[1]), name);
}

export function getAssessmentNameForTree(assessment) {
  const shortenedName = getShortenedAssessmentName(assessment.name);
  const nameWithMeasureAdded = `AM ${assessment.monitorAssessmentMeasure}- ${shortenedName}`;
  const maxDisplayLength = 24;
  if (nameWithMeasureAdded.length > maxDisplayLength) {
    const maxLengthWithoutDots = maxDisplayLength - 2;
    return nameWithMeasureAdded.slice(0, maxLengthWithoutDots).concat("..");
  }
  return nameWithMeasureAdded;
}
