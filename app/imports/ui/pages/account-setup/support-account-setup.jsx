import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import Select from "react-select";

import { Loading } from "../../components/loading.jsx";
import { isEmailValid } from "/imports/api/utilities/utilities";
import { getOrganizationNames } from "../../utilities";
import ConfirmModal from "../data-admin/confirm-modal";

const selectAllOption = {
  label: "Select All",
  value: "selectAll"
};

class SupportAccountSetup extends Component {
  state = {
    inviteSent: false,
    supportEmail: "",
    supportFirstName: "",
    supportLastName: "",
    selectedOrganizations: [],
    availableOrganizations: [],
    isProcessing: false,
    shouldShowNoSiteAccessModal: false
  };

  componentDidMount() {
    if (this.props.organizations.length) {
      this.setAvailableOrganizations();
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.organizations.length !== prevProps.organizations.length) {
      this.setAvailableOrganizations();
    }
  }

  setAvailableOrganizations() {
    this.setState({
      availableOrganizations: this.props.organizations.map(org => ({
        label: org.name,
        value: org._id
      }))
    });
  }

  goToDashboard = () => {
    this.props.history.push("/");
  };

  handleSendInvite = e => {
    e.preventDefault();
    if (!isEmailValid(this.state.supportEmail)) {
      return Alert.error("You must provide a valid email address to send an email invite to", { timeout: 5000 });
    }
    if (!this.state.selectedOrganizations.length) {
      return Alert.error("You must select at least one organization", { timeout: 5000 });
    }
    this.setState({ isProcessing: true });
    return Meteor.call("users:getSiteAccessByEmail", this.state.supportEmail.trim(), (err, siteAccess) => {
      if (!siteAccess || siteAccess.length) {
        this.createOrUpdateUser();
      } else if (siteAccess && !siteAccess.length) {
        this.setState({ shouldShowNoSiteAccessModal: true });
      }
    });
  };

  createOrUpdateUser = () => {
    const organizationIds = this.state.selectedOrganizations.map(org => org.value);
    Meteor.call(
      "users:createSupportUserAndSendEnrollmentLink",
      {
        email: this.state.supportEmail,
        firstName: this.state.supportFirstName,
        lastName: this.state.supportLastName,
        organizationIds
      },
      (error, result) => {
        if (!error) {
          this.setState({ inviteSent: result });
          this.inviteSentSucessDiv.scrollIntoView();
        } else {
          Alert.error(error.reason || error.error || "Error creating and/or sending email", { timeout: 5000 });
        }
        this.setState({ isProcessing: false });
      }
    );
  };

  closeModal = () => {
    this.setState({ shouldShowNoSiteAccessModal: false, isProcessing: false });
  };

  handleSupportEmailTextInputChange = event => this.setState({ supportEmail: event.target.value });

  handleSupportFirstNameTextInputChange = event => this.setState({ supportFirstName: event.target.value });

  handleSupportLastNameTextInputChange = event => this.setState({ supportLastName: event.target.value });

  handleOrganizationsChange = selected => {
    if (selected.length && selected[selected.length - 1].value === selectAllOption.value) {
      this.setState({
        selectedOrganizations: this.state.availableOrganizations
      });
    } else {
      this.setState({
        selectedOrganizations: selected
      });
    }
  };

  render() {
    let submitButtonText;
    if (this.state.isProcessing) {
      submitButtonText = "Processing...";
    } else if (this.state.inviteSent) {
      submitButtonText = "Back to Dashboard";
    } else {
      submitButtonText = "Send Invite Email";
    }
    return (
      <div className="conFullScreen medium-width">
        <div className="container add-support-screen">
          <h4 className="w7 text-center">Create Support Account</h4>
          <div className="create-support-text">
            <p>This page is to create a Support account.</p>
            <p>* Support users can view all the sites and classes from all organizations the same as coaches.</p>
            <p>* Support users can view the School Overview.</p>
            <p>
              * Support users can access the grade level Admin Dashboard, which includes summary notes and intervention
              statistics by class and grade.
            </p>
            <p>
              By completing the form below, the Support user will receive an email invitation to select a weblink (URL)
              in the email and to enter and verify a password for logging into SpringMath.
            </p>
          </div>
          <hr className="dark" />
          <div className="card-box">
            {this.props.loading ? (
              <Loading />
            ) : (
              <div>
                <form>
                  <div className="form-group">
                    <label htmlFor="txtSupportEmail">Support Email</label>
                    <input
                      id="txtSupportEmail"
                      type="text"
                      className="form-control"
                      onChange={this.handleSupportEmailTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtSupportFirstName">First Name</label>
                    <input
                      id="txtSupportFirstName"
                      type="text"
                      className="form-control"
                      onChange={this.handleSupportFirstNameTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtSupportLastName">Last Name</label>
                    <input
                      id="txtSupportLastName"
                      type="text"
                      className="form-control"
                      onChange={this.handleSupportLastNameTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="selectActiveOrganizations">Active Organizations</label>
                    <Select
                      id="selectActiveOrganizations"
                      value={this.state.selectedOrganizations}
                      isMulti
                      isSearchable={true}
                      name={"organizationsActive"}
                      options={[selectAllOption, ...this.state.availableOrganizations]}
                      className="basic-multi-select"
                      classNamePrefix="select"
                      onChange={this.handleOrganizationsChange}
                    />
                  </div>
                  <div className="form-group">
                    <button
                      ref={sb => {
                        this.submitButton = sb;
                      }}
                      className="btn btn-success"
                      onClick={this.state.inviteSent ? this.goToDashboard : this.handleSendInvite}
                      disabled={this.state.isProcessing}
                    >
                      {submitButtonText}
                    </button>
                  </div>
                </form>
                <div
                  ref={d => {
                    this.inviteSentSucessDiv = d;
                  }}
                >
                  {this.state.inviteSent && (
                    <div className="alert alert-success">
                      Invite Sent. Please contact the new user to inform them the invite has been sent.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <ConfirmModal
            showModal={this.state.shouldShowNoSiteAccessModal}
            confirmAction={this.createOrUpdateUser}
            onCloseModal={this.closeModal}
            bodyQuestion=""
            bodyText={
              <div>
                The user with email <strong>{this.state.supportEmail}</strong> already is in the system but is not
                assigned to this site. Would you like to assign them to this school?
              </div>
            }
            confirmText="Assign"
            cancelText="Cancel"
          />
        </div>
      </div>
    );
  }
}

SupportAccountSetup.propTypes = {
  loading: PropTypes.bool,
  organizations: PropTypes.array,
  history: PropTypes.object
};

const SupportAccountSetupContainer = withTracker(() => {
  const organizationsHandler = Meteor.subscribe("Organizations");
  const loading = !organizationsHandler.ready();
  let organizations = [];
  if (!loading) {
    organizations = getOrganizationNames();
  }
  return { loading, organizations };
})(SupportAccountSetup);

export default withRouter(SupportAccountSetupContainer);
