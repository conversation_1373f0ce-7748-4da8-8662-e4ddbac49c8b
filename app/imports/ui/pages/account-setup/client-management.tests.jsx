import React from "react";
import MockDate from "mockdate";
import { render, cleanup, waitFor, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import td from "testdouble";
import { Meteor } from "meteor/meteor";
import { PureClientManagement as ClientManagement } from "./client-management.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserSync: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id"),
  isSML: jest.fn(() => false)
}));

// eslint-disable-next-line no-unused-vars
let meteorCallSpy;

describe("ClientManagement", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
    // Mock Meteor methods and subscriptions
    Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
    Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  const idOfInactiveOrgWithStudents = "idOne";
  const idOfActiveOrgWithStudents = "idTwo";
  const idOfActiveOrgWithoutStudents = "idThree";
  const idOfInactiveOrgWithoutStudents = "idFour";
  const idOfActiveOrgWithStudents2 = "idFive";
  const idOfActiveOrgWithStudents3 = "idSix";
  const schoolYear = 2019;
  const orgs = [
    {
      _id: idOfInactiveOrgWithStudents,
      created: { on: 2 },
      name: "M",
      isActive: false
    },
    {
      _id: idOfActiveOrgWithStudents,
      created: { on: 1 },
      name: "Z",
      isActive: true
    },
    {
      _id: idOfActiveOrgWithoutStudents,
      created: { on: 3 },
      name: "ZZ",
      isActive: true
    },
    {
      _id: idOfInactiveOrgWithoutStudents,
      created: { on: 4 },
      name: "A",
      isActive: false
    },
    {
      _id: idOfActiveOrgWithStudents2,
      created: { on: 5 },
      name: "B",
      isActive: true
    },
    {
      _id: idOfActiveOrgWithStudents3,
      created: { on: 6 },
      name: "D",
      isActive: true
    }
  ];

  beforeEach(async () => {
    meteorCallSpy = td.replace(Meteor, "call");
    td.when(
      Meteor.call(
        "Students:getNumberOfActiveStudents",
        td.matchers.anything(),
        td.matchers.anything(),
        td.matchers.isA(Function)
      )
    ).thenDo((...args) => {
      const callback = args[args.length - 1];
      callback(null, [
        { _id: idOfInactiveOrgWithStudents, totalStudents: 1 },
        { _id: idOfActiveOrgWithStudents, totalStudents: 1 },
        { _id: idOfActiveOrgWithoutStudents, totalStudents: 0 },
        { _id: idOfInactiveOrgWithoutStudents, totalStudents: 0 },
        { _id: idOfActiveOrgWithStudents2, totalStudents: 10 },
        { _id: idOfActiveOrgWithStudents3, totalStudents: 5 }
      ]);
    });
    td.when(
      Meteor.call("StudentGroups:getAverageStatsPerOrg", td.matchers.isA(String), schoolYear, td.matchers.isA(Function))
    ).thenDo((...args) => {
      const callback = args[args.length - 1];
      callback(null, { percentScreened: 1, averageInterventionConsistency: 20, averageWeeksPerSkill: 1 });
    });
    td.when(Meteor.call("Organizations:getAllOrganizations", td.matchers.anything(), td.matchers.isA(Function))).thenDo(
      (...args) => {
        const callback = args[args.length - 1];
        callback(null, orgs);
      }
    );
  });
  afterEach(() => {
    cleanup();
    td.reset();
  });

  it("should display a loader when data is not ready yet", () => {
    td.reset();

    const { getByTestId } = render(<ClientManagement />);

    expect(getByTestId("loading-icon")).toBeVisible();
  });

  it("should display the clients in alphabetical order by default ", async () => {
    const { getAllByTestId } = renderWithRouter(<ClientManagement />);

    await waitFor(() => {
      const defaultSortedClients = getAllByTestId("orgLink");
      expect(defaultSortedClients[0]).toHaveTextContent("B");
      expect(defaultSortedClients[1]).toHaveTextContent("D");
      expect(defaultSortedClients[2]).toHaveTextContent("Z");
    });
  });

  it("should display the clients in chronological order when 'Sort Chronologically' radio is checked", async () => {
    const { getByTestId, getAllByTestId } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.superAdmin }
      }
    });

    await waitFor(() => {
      getByTestId("chronologically_radio").click();
      const resortedClients = getAllByTestId("orgLink");
      expect(resortedClients[0]).toHaveTextContent("Z");
      expect(resortedClients[1]).toHaveTextContent("B");
      expect(resortedClients[2]).toHaveTextContent("D");
    });
  });

  it("should not display empty organizations by default", async () => {
    const { getByTestId, queryByTestId } = renderWithRouter(<ClientManagement />);

    await waitFor(() => {
      expect(getByTestId(`${idOfActiveOrgWithStudents}_row`)).toBeVisible();
      expect(queryByTestId(`${idOfActiveOrgWithoutStudents}_row`)).not.toBeInTheDocument();
    });
  });

  it("should only display active clients by default", async () => {
    const { getByTestId, queryByTestId } = renderWithRouter(<ClientManagement />);

    await waitFor(() => {
      expect(queryByTestId(`${idOfInactiveOrgWithStudents}_row`)).not.toBeInTheDocument();
      expect(getByTestId(`${idOfActiveOrgWithStudents}_row`)).toBeVisible();
    });
  });

  it("should display the inactive clients when 'Show Inactive Clients' radio is checked", async () => {
    const { getByTestId } = renderWithRouter(<ClientManagement />);

    await waitFor(() => {
      getByTestId("showInactiveClients").click();

      expect(getByTestId(`${idOfInactiveOrgWithStudents}_row`)).toBeVisible();
      expect(getByTestId(`${idOfActiveOrgWithStudents}_row`)).toBeVisible();
    });
  });

  it("should display only Add Client, and nocAdd Support User, Add Universal Coach, Add Universal Data Admin, and Add Downloader buttons for Super Admin", async () => {
    const { getByTestId, queryByTestId } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.superAdmin }
      }
    });

    await waitFor(() => {
      expect(getByTestId("addClientButton")).toBeVisible();
      expect(queryByTestId("addSupportUserButton")).toBeNull();
      expect(queryByTestId("addUniversalCoachButton")).toBeNull();
      expect(queryByTestId("addUniversalDataAdminButton")).toBeNull();
      expect(queryByTestId("addDownloaderButton")).toBeNull();
    });
  });

  it("should not display Add Client, Add Support User, Add Universal Coach, Add Universal Data Admin, and Add Downloader buttons for Universal Coach", () => {
    const { queryByTestId } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.support }
      }
    });

    expect(queryByTestId("addClientButton")).toBeNull();
    expect(queryByTestId("addSupportUserButton")).toBeNull();
    expect(queryByTestId("addUniversalCoachButton")).toBeNull();
    expect(queryByTestId("addUniversalDataAdminButton")).toBeNull();
    expect(queryByTestId("addDownloaderButton")).toBeNull();
  });

  it("should display empty organizations when 'Hide Empty Organizations' checkbox is unchecked", async () => {
    const { getByTestId, queryByTestId } = renderWithRouter(<ClientManagement />);

    await waitFor(() => {
      getByTestId("hideEmptyOrgsCheckbox").click();

      expect(queryByTestId(`${idOfInactiveOrgWithStudents}_row`)).not.toBeInTheDocument();
      expect(getByTestId(`${idOfActiveOrgWithStudents}_row`)).toBeVisible();
      expect(getByTestId(`${idOfActiveOrgWithoutStudents}_row`)).toBeVisible();
    });
  });

  it("should display org stats columns for universal coach", async () => {
    const { getByText } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.support }
      }
    });
    await waitFor(() => {
      expect(getByText("% Screened")).toBeVisible();
      expect(getByText("Intervention Consistency")).toBeVisible();
      expect(getByText("Average Weeks Per Skill")).toBeVisible();
    });
  });

  it("should make possible deactivating the organization for superAdmins", async () => {
    const { getAllByTestId, getByText } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.superAdmin }
      }
    });

    fireEvent.click(getAllByTestId("deactivateOrganizationButton")[0]);
    await waitFor(() => expect(getByText("Yes, deactivate client")).toBeInTheDocument());
    fireEvent.click(getByText("Yes, deactivate client"));
    await waitFor(() => {
      td.verify(meteorCallSpy("deactivateOrganization", idOfActiveOrgWithStudents2, td.matchers.anything()));
    });
  });

  it("should make possible to reactivate the organization for superAdmins", async () => {
    const { getByTestId } = renderWithRouter(<ClientManagement />, {
      userContextValue: {
        currentSiteAccess: { role: ROLE_IDS.superAdmin }
      }
    });

    fireEvent.click(getByTestId("showInactiveClients"));
    fireEvent.click(getByTestId("reactivateOrganizationButton"));

    await waitFor(() => {
      td.verify(meteorCallSpy("reactivateOrganization", idOfInactiveOrgWithStudents, td.matchers.isA(Function)));
    });
  });
});
