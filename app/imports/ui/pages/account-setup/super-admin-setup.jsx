import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";
import ConfirmModal from "../data-admin/confirm-modal";

class SuperAdmin extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inviteSent: false,
      email: "",
      firstName: "",
      lastName: "",
      isProcessing: false,
      shouldShowNoSiteAccessModal: false
    };
  }

  goToDashboard = () => {
    this.props.history.push("/");
  };

  isSuperAdminEmailValid = email => {
    // eslint-disable-next-line no-useless-escape
    return /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+(?:\.[A-z0-9!#$%&'*+\/=?^_`{|}~-]+)*@(springmathaccelerate\.com)$/i.test(
      email
    );
  };

  handleSendInvite = e => {
    e.preventDefault();
    if (!this.isSuperAdminEmailValid(this.state.email)) {
      return Alert.error(
        "Invalid email. The Super Admin Email will only accept email addresses with a springmathaccelerate.com domain.",
        { timeout: 7000 }
      );
    }
    this.setState({ isProcessing: true });
    return Meteor.call("users:getSiteAccessByEmail", this.state.email.trim(), (err, siteAccess) => {
      if (!siteAccess || siteAccess.length) {
        this.createOrUpdateUser();
      } else if (siteAccess && !siteAccess.length) {
        this.setState({ shouldShowNoSiteAccessModal: true });
      }
    });
  };

  createOrUpdateUser = () => {
    Meteor.call(
      "users:createSuperAdminAndSendEnrollmentLink",
      {
        email: this.state.email,
        firstName: this.state.firstName,
        lastName: this.state.lastName
      },
      (error, result) => {
        if (!error) {
          this.setState({ inviteSent: result });
          this.inviteSentSucessDiv.scrollIntoView();
        } else {
          Alert.error(error.reason || error.error || "Error creating and/or sending email", { timeout: 5000 });
        }
        this.setState({ isProcessing: false });
      }
    );
  };

  handleTextInputChange = event => {
    const { name, value } = event.target;
    this.setState({ [name]: value });
  };

  closeModal = () => {
    this.setState({ shouldShowNoSiteAccessModal: false, isProcessing: false });
  };

  getSubmitButtonText = () => {
    if (this.state.isProcessing) {
      return "Processing...";
    }
    if (this.state.inviteSent) {
      return "Back to Dashboard";
    }
    return "Send Invite Email";
  };

  render() {
    return (
      <div className="conFullScreen medium-width">
        <div className="container add-support-screen">
          <h4 className="w7 text-center">Create Super Admin Account</h4>
          <div className="create-support-text">
            <p>This page is to create a Super Admin Account (for SWT employees only).</p>
            <p>
              Super Admins can perform all of the same duties as a customer Data Administrator, but can do so for all
              customers.
            </p>

            <p>Super Admins can:</p>
            <ul>
              <li>Add Clients, Add all types of users.</li>
              <li>Export Current Roster as CSV</li>
              <li>Export end-of-year roster from the previous year</li>
              <li>Import Records (upload student rosters)</li>
              <li>Manage Screenings, Classwide Interventions, Progress Monitoring</li>
            </ul>

            <p>Use the Manage UI to:</p>
            <ul>
              <li>Perform student operations (Move, Archive, Unarchive, Add, Upload, Edit)</li>
              <li>Clear screening scores</li>
              <li>Manage Classes & Teachers</li>
              <li>Add Classes & Teachers</li>
            </ul>

            <p>
              By completing the form below, the Super Admin will receive an email invitation to select a URL in the
              email, enter and verify a password for registering a user account.
            </p>
            <p>
              Note: the Super Admin Email will only accept email addresses with a <b>springmathaccelerate.com</b>{" "}
              domain.
            </p>
          </div>
          <hr className="dark" />
          <div className="card-box">
            <div>
              <form>
                <div className="form-group">
                  <label htmlFor="email">Super Admin Email</label>
                  <input
                    id="email"
                    name="email"
                    type="text"
                    className="form-control"
                    onChange={this.handleTextInputChange}
                    data-testid={"email"}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="firstName">First Name</label>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    className="form-control"
                    onChange={this.handleTextInputChange}
                    data-testid={"firstName"}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="lastName">Last Name</label>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    className="form-control"
                    onChange={this.handleTextInputChange}
                    data-testid={"lastName"}
                  />
                </div>
                <div className="form-group">
                  <button
                    ref={sb => {
                      this.submitButton = sb;
                    }}
                    className="btn btn-success"
                    onClick={this.state.inviteSent ? this.goToDashboard : this.handleSendInvite}
                    disabled={this.state.isProcessing}
                  >
                    {this.getSubmitButtonText()}
                  </button>
                </div>
              </form>
              <div
                ref={d => {
                  this.inviteSentSucessDiv = d;
                }}
              >
                {this.state.inviteSent && (
                  <div className="alert alert-success">
                    Invite Sent. Please contact the new user to inform them the invite has been sent.
                  </div>
                )}
              </div>
            </div>
          </div>
          <ConfirmModal
            showModal={this.state.shouldShowNoSiteAccessModal}
            confirmAction={this.createOrUpdateUser}
            onCloseModal={this.closeModal}
            bodyQuestion=""
            bodyText={
              <div>
                The user with email <strong>{this.state.email}</strong> already is in the system but is not assigned to
                this site. Would you like to assign them to this school?
              </div>
            }
            confirmText="Assign"
            cancelText="Cancel"
          />
        </div>
      </div>
    );
  }
}

SuperAdmin.propTypes = {
  loading: PropTypes.bool,
  history: PropTypes.object
};

const SuperAdminContainer = withRouter(SuperAdmin);

export default SuperAdminContainer;
