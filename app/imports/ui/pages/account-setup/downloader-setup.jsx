import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";

import { withTracker } from "meteor/react-meteor-data";
import { Loading } from "../../components/loading.jsx";
import { getMeteorUserSync, isEmailValid } from "/imports/api/utilities/utilities";
import ConfirmModal from "../data-admin/confirm-modal";

class DownloaderSetup extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inviteSent: false,
      downloaderEmail: "",
      downloaderFirstName: "",
      downloaderLastName: "",
      isProcessing: false,
      shouldShowNoSiteAccessModal: false
    };
  }

  goToDashboard = () => {
    this.props.history.push("/");
  };

  handleSendInvite = e => {
    e.preventDefault();
    if (!isEmailValid(this.state.downloaderEmail)) {
      return Alert.error("You must provide a valid email address to send an email invite to", { timeout: 5000 });
    }
    this.setState({ isProcessing: true });
    return Meteor.call("users:getSiteAccessByEmail", this.state.downloaderEmail.trim(), (err, siteAccess) => {
      if (!siteAccess || siteAccess.length) {
        this.createOrUpdateUser();
      } else if (siteAccess && !siteAccess.length) {
        this.setState({ shouldShowNoSiteAccessModal: true });
      }
    });
  };

  createOrUpdateUser = () => {
    Meteor.call(
      "users:createDownloaderAndSendEnrollmentLink",
      {
        email: this.state.downloaderEmail,
        firstName: this.state.downloaderFirstName,
        lastName: this.state.downloaderLastName
      },
      (error, result) => {
        if (!error) {
          this.setState({ inviteSent: result });
          this.inviteSentSuccessDiv.scrollIntoView();
        } else {
          Alert.error(error.reason || error.error || "Error creating and/or sending email", { timeout: 5000 });
        }
        this.setState({ isProcessing: false });
      }
    );
  };

  handleTextInputChange = event => {
    const { name, value } = event.target;
    this.setState({ [name]: value });
  };

  closeModal = () => {
    this.setState({ shouldShowNoSiteAccessModal: false, isProcessing: false });
  };

  getSubmitButtonText = () => {
    if (this.state.isProcessing) {
      return "Processing...";
    }
    if (this.state.inviteSent) {
      return "Back to Dashboard";
    }
    return "Send Invite Email";
  };

  render() {
    return (
      <div className="conFullScreen medium-width">
        <div className="container add-support-screen">
          <h4 className="w7 text-center">Create Downloader Account</h4>
          <div className="create-support-text">
            <p>This page is to create a Downloader account.</p>
            <p>* Downloader users can export users as CSV</p>
            <p>
              By completing the form below, the Downloader user will receive an email invitation to select a weblink
              (URL) in the email and to enter and verify a password for logging into SpringMath.
            </p>
          </div>
          <hr className="dark" />
          <div className="card-box">
            {this.props.loading ? (
              <Loading />
            ) : (
              <div>
                <form>
                  <div className="form-group">
                    <label htmlFor="txtDownloaderEmail">Downloader Email</label>
                    <input
                      id="txtDownloaderEmail"
                      name="downloaderEmail"
                      type="text"
                      className="form-control"
                      onChange={this.handleTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtDownloaderFirstName">First Name</label>
                    <input
                      id="txtDownloaderFirstName"
                      name="downloaderFirstName"
                      type="text"
                      className="form-control"
                      onChange={this.handleTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtDownloaderLastName">Last Name</label>
                    <input
                      id="txtDownloaderLastName"
                      name="downloaderLastName"
                      type="text"
                      className="form-control"
                      onChange={this.handleTextInputChange}
                    />
                  </div>
                  <div className="form-group">
                    <button
                      ref={sb => {
                        this.submitButton = sb;
                      }}
                      className="btn btn-success"
                      onClick={this.state.inviteSent ? this.goToDashboard : this.handleSendInvite}
                      disabled={this.state.isProcessing}
                    >
                      {this.getSubmitButtonText()}
                    </button>
                  </div>
                </form>
                <div
                  ref={d => {
                    this.inviteSentSuccessDiv = d;
                  }}
                >
                  {this.state.inviteSent && (
                    <div className="alert alert-success">
                      Invite Sent. Please contact the new user to inform them the invite has been sent.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <ConfirmModal
            showModal={this.state.shouldShowNoSiteAccessModal}
            confirmAction={this.createOrUpdateUser}
            onCloseModal={this.closeModal}
            bodyQuestion=""
            bodyText={
              <div>
                The user with email <strong>{this.state.downloaderEmail}</strong> already is in the system but is not
                assigned to this site. Would you like to assign them to this school?
              </div>
            }
            confirmText="Assign"
            cancelText="Cancel"
          />
        </div>
      </div>
    );
  }
}

DownloaderSetup.propTypes = {
  loading: PropTypes.bool,
  history: PropTypes.object
};

const DownloaderSetupContainer = withTracker(() => {
  const curUser = getMeteorUserSync();
  const loading = !curUser;
  return { loading };
})(withRouter(DownloaderSetup));

export default DownloaderSetupContainer;
