import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";

import { withTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import { Link } from "react-router-dom";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import { Loading } from "../../components/loading.jsx";
import {
  isEmailValid,
  capitalizeFirstLetter,
  isIdValid,
  userIdInvalidErrorMessage
} from "/imports/api/utilities/utilities";
import { areSubscriptionsLoading } from "../../utilities";
import ConfirmModal from "../data-admin/confirm-modal";

class DataAdminAccountSetup extends Component {
  state = {
    dataAdminSiteId: "allSites",
    dataAdminEmail: "",
    dataAdminLocalId: "",
    dataAdminFirstName: "",
    dataAdminLastName: "",
    isProcessing: false,
    shouldShowNoSiteAccessModal: false,
    actionTaken: undefined,
    submittedEmail: ""
  };

  handleSendInvite = e => {
    e.preventDefault();
    if (!isEmailValid(this.state.dataAdminEmail)) {
      return Alert.error("You must provide a valid email address to send an email invite to", { timeout: 5000 });
    }
    if (this.state.dataAdminLocalId.length && !isIdValid(this.state.dataAdminLocalId, true)) {
      return Alert.error(userIdInvalidErrorMessage, {
        timeout: 5000
      });
    }
    this.setState({ isProcessing: true });
    return Meteor.call("users:getSiteAccessByEmail", this.state.dataAdminEmail.trim(), (err, siteAccess) => {
      if (!siteAccess || siteAccess.length) {
        this.createOrUpdateUser();
      } else if (siteAccess && !siteAccess.length) {
        this.setState({ shouldShowNoSiteAccessModal: true });
      }
    });
  };

  createOrUpdateUser = () => {
    Meteor.call(
      "users:createDataAdminUserAndSendEnrollmentLink",
      {
        orgid: this.props.org._id,
        siteId: this.state.dataAdminSiteId,
        email: this.state.dataAdminEmail.trim(),
        localId: this.state.dataAdminLocalId.trim(),
        firstName: this.state.dataAdminFirstName.trim(),
        lastName: this.state.dataAdminLastName.trim()
      },
      (error, result) => {
        if (!error) {
          this.setState({
            dataAdminSiteId: "allSites",
            submittedEmail: this.state.dataAdminEmail,
            dataAdminEmail: "",
            dataAdminLocalId: "",
            dataAdminFirstName: "",
            dataAdminLastName: "",
            actionTaken: result.actionTaken
          });
        } else {
          Alert.error(error.reason || error.error || "Error creating and/or sending email", { timeout: 5000 });
        }
        this.setState({ isProcessing: false });
      }
    );
  };

  handleChange = e => {
    this.setState({ [e.target.name]: e.target.value });
  };

  closeModal = () => {
    this.setState({ shouldShowNoSiteAccessModal: false, isProcessing: false });
  };

  getInputField = (fieldName, isRequired = true) => (
    <input
      className="form-control"
      type="text"
      name={fieldName}
      data-testid={`txt${capitalizeFirstLetter(fieldName)}`}
      value={this.state[fieldName]}
      onChange={this.handleChange}
      required={isRequired}
    />
  );

  render() {
    return (
      <div className="conFullScreen medium-width">
        <div className="container add-user-screen">
          <h4 className="w7 text-center">{`If you're here, we've done good.`}</h4>
          <p className="text-center">This is the account setup screen.</p>
          <hr className="dark" />
          <p>{`Here's how it works:`}</p>
          <ol>
            <li>Complete the form below.</li>
            <li>{`The 'data admin' you enter will receive an email invite if you aren't using SSO exclusively.`}</li>
            <li>
              The invite allows them to enter their name, email, and gives instructions on setting up their
              district/school/class.
            </li>
            <li>
              You can observe their progress from our <Link to="/clients">client management screen</Link>.
            </li>
          </ol>
          <div className="card-box">
            {this.props.loading ? (
              <Loading />
            ) : (
              <div>
                <form onSubmit={this.handleSendInvite}>
                  <div className="form-group">
                    <label>District Name</label>
                    <input
                      type="text"
                      className="form-control"
                      data-testid="districtName"
                      readOnly={true}
                      value={this.props.org?.name}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtSite">Site</label>
                    <div>
                      <select
                        className="form-select"
                        name="dataAdminSiteId"
                        onChange={this.handleChange}
                        value={this.state.dataAdminSiteId || "allSites"}
                      >
                        <option value="allSites">All sites</option>
                        {this.props.sites.map(site => (
                          <option key={site._id} value={site._id}>
                            {site.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="form-group">
                    <label>Data Admin Email</label>
                    {this.getInputField("dataAdminEmail")}
                  </div>
                  <div className="form-group">
                    <label>Teacher/Local ID</label>
                    <small>(optional)</small>
                    {this.getInputField("dataAdminLocalId", false)}
                  </div>
                  <div className="form-group">
                    <label>First Name</label>
                    {this.getInputField("dataAdminFirstName")}
                  </div>
                  <div className="form-group">
                    <label>Last Name</label>
                    {this.getInputField("dataAdminLastName")}
                  </div>
                  <div className="form-group">
                    <button type="submit" className="btn btn-success" data-testid="submitButton">
                      Submit
                    </button>
                  </div>
                </form>
                <div>
                  {this.state.actionTaken === "created" ? (
                    <div className="alert alert-success" data-testid="createdUser">
                      User with email <strong>{this.state.submittedEmail}</strong> added. Please inform the user that
                      the invite has been sent if you aren&apos;t using SSO exclusively.
                    </div>
                  ) : null}
                  {this.state.actionTaken === "updated" ? (
                    <div className="alert alert-success" data-testid="updatedUser">
                      Existing user tied to this email <strong>{this.state.submittedEmail}</strong> was updated with
                      data admin role for this organization.
                    </div>
                  ) : null}
                  {this.state.actionTaken === null ? (
                    <div className="alert alert-warning" data-testid="noActionTaken">
                      No action was taken since existing user with this email{" "}
                      <strong>{this.state.submittedEmail}</strong> already has this role.
                    </div>
                  ) : null}
                </div>
              </div>
            )}
          </div>
          <ConfirmModal
            showModal={this.state.shouldShowNoSiteAccessModal}
            confirmAction={this.createOrUpdateUser}
            onCloseModal={this.closeModal}
            bodyQuestion=""
            bodyText={
              <div>
                The user with email <strong>{this.state.dataAdminEmail}</strong> already is in the system but is not
                assigned to this site. Would you like to assign them to this school?
              </div>
            }
            confirmText="Assign"
            cancelText="Cancel"
          />
        </div>
      </div>
    );
  }
}

DataAdminAccountSetup.propTypes = {
  loading: PropTypes.bool,
  org: PropTypes.object,
  sites: PropTypes.array
};

export default withTracker(params => {
  const subscription = Meteor.subscribe("Organizations", params.orgid);
  let loading = areSubscriptionsLoading(subscription);
  let org = {};
  let sites = [];
  if (!loading) {
    org = Organizations.findOne({ _id: params.orgid });
    const sitesHandler = Meteor.subscribe("Sites", org._id);
    loading = !sitesHandler.ready();
    sites = Sites.find({ orgid: params.orgid }, { fields: { name: 1 }, sort: { name: 1 } }).fetch();
  }
  return { loading, org, sites };
})(DataAdminAccountSetup);
