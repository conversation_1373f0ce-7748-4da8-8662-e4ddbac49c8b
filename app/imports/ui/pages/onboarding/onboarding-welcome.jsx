import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { withRouter } from "react-router-dom";
import { Button, Form } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import { Accounts } from "meteor/accounts-base";
import { getMeteorUserId, passwordRegex, passwordSpecMsg } from "/imports/api/utilities/utilities";

function OnboardingWelcome(props) {
  const [password, setPassword] = useState(undefined);
  const [confirmPassword, setConfirmPassword] = useState(undefined);
  const [firstName, setFirstName] = useState(null);
  const [lastName, setLastName] = useState(null);
  const [isFormValid, setIsFormValid] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [isBeingSubmitted, setIsBeingSubmitted] = useState(false);

  const errorContent = {
    empty: "Password cannot be empty.",
    mismatch: "Confirm password does not match password.",
    failedValidation: passwordSpecMsg
  };

  useEffect(() => {
    Meteor.call("user:getUserByEnrollTokenOrUserId", props.usrToken, getMeteorUserId(), (err, user) => {
      if (err) {
        setSubmitError(err?.message);
      } else {
        const { first, last } = user?.profile?.name || {};
        setFirstName(first);
        setLastName(last);
      }
    });
  }, []);

  useEffect(() => {
    setSubmitError("");
    if (password === confirmPassword && passwordRegex.test(password)) {
      setIsFormValid(true);
    } else {
      setIsFormValid(false);
    }
  }, [password, confirmPassword]);

  const getErrorStatusAndMessage = context => {
    if (password === undefined && context === "password") {
      return { isValid: false, message: "" };
    }
    if (confirmPassword === undefined && context === "confirmPassword") {
      return { isValid: false, message: "" };
    }
    if ((!password || !password?.length) && context === "password") {
      return { isValid: false, message: errorContent.empty };
    }
    if ((!confirmPassword || !confirmPassword.length) && context === "confirmPassword") {
      return { isValid: false, message: errorContent.empty };
    }
    if (!passwordRegex.test(password) && context !== "confirmPassword") {
      return {
        isValid: false,
        message: (
          <p
            className="m-0 text-black warningMessage"
            dangerouslySetInnerHTML={{ __html: errorContent.failedValidation }}
          />
        )
      };
    }
    if (password !== confirmPassword && context === "confirmPassword") {
      return { isValid: false, message: errorContent.mismatch };
    }
    return { isValid: true, message: "" };
  };

  const handleSubmit = event => {
    event.preventDefault();
    if (isFormValid) {
      setIsBeingSubmitted(true);
      event.stopPropagation();
      Accounts.resetPassword(props.usrToken, password, err => {
        if (err) {
          setIsBeingSubmitted(false);
          setSubmitError(
            err?.reason
              ? `The link used to set up your account has expired. <br /> Please contact data admin in your Organization to receive a new link.`
              : `Unable to change password: ${err}`
          );
        } else {
          Meteor.call("users:onboardingCompleted", { completed: true, firstName, lastName }, (error, data) => {
            if (!error) {
              Meteor.call("user:onPasswordChange", () => {
                Meteor.call("users:updateLoginData", () => {
                  props.history.push(data.isMFARequired ? "/logout" : "/");
                });
              });
            }
          });
        }
      });
    } else {
      setSubmitError("Password and Confirm Password cannot be empty.");
    }
  };

  const isPasswordValid = getErrorStatusAndMessage("password").isValid;
  const isConfirmPasswordValid = getErrorStatusAndMessage("confirmPassword").isValid;
  return (
    <div className="conFullScreen conOnBoarding narrow short password">
      <div className="container animated fadeInDown">
        <Form noValidate onSubmit={handleSubmit}>
          <h3 className="w9 text-center">Welcome to SpringMath</h3>
          <hr />
          <p>Since this is your first time logging in:</p>
          <p>What would you like your new password to be?</p>
          <Form.Group className="mb-3 next-steps p-t-10">
            <Form.Label>Password</Form.Label>
            <Form.Control
              id="txtPassword"
              type="password"
              placeholder="Password"
              value={password || ""}
              onChange={e => setPassword(e.currentTarget.value)}
              isInvalid={!isPasswordValid}
              isValid={isPasswordValid}
              required
            />
            <Form.Control.Feedback type="invalid">{getErrorStatusAndMessage("password").message}</Form.Control.Feedback>
            <Form.Label className="mt-2">Confirm Password</Form.Label>
            <Form.Control
              id="txtConfirmPassword"
              type="password"
              placeholder="Confirm Password"
              value={confirmPassword || ""}
              onChange={e => setConfirmPassword(e.currentTarget.value)}
              isInvalid={!isConfirmPasswordValid}
              isValid={isConfirmPasswordValid}
              required
            />
            <Form.Control.Feedback type="invalid">
              {getErrorStatusAndMessage("confirmPassword").message}
            </Form.Control.Feedback>
          </Form.Group>
          <hr />
          <p>Do we have the correct spelling of your name?</p>
          <Form.Group className="mb-3 next-steps p-t-10">
            <Form.Label>First Name</Form.Label>
            <Form.Control plaintext placeholder={firstName} readOnly />
            <Form.Label className="mt-2">Last Name</Form.Label>
            <Form.Control plaintext placeholder={lastName} readOnly />
          </Form.Group>
          <p>If not, please contact your Data Administrator for any corrections.</p>
          {submitError ? <p className="warningMessage" dangerouslySetInnerHTML={{ __html: submitError }} /> : null}
          <Button variant="primary" type="submit" disabled={isBeingSubmitted}>
            Set Password & Continue
          </Button>
        </Form>
      </div>
    </div>
  );
}

OnboardingWelcome.propTypes = {
  usrToken: PropTypes.string,
  history: PropTypes.object
};

export default withRouter(OnboardingWelcome);
