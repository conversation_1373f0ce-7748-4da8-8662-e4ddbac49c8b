import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withTracker } from "meteor/react-meteor-data";
import { withRouter } from "react-router-dom";
import { shouldUseDevMode, translateBenchmarkPeriod } from "/imports/api/utilities/utilities";
import { Assessments } from "/imports/api/assessments/assessments";
import { <PERSON><PERSON> } from "react-bootstrap";
import { Loading } from "../../components/loading.jsx";
import PrintMaterials from "../../components/dashboard/print-materials.jsx";
import ScoreEntryRowCell from "../../components/score-entry/score-entry-row-cell.jsx";
import InterventionMessage from "../../components/dashboard/intervention-message";
import { ScoreEntryContext } from "../../components/score-entry/score-entry-context";
import SkillVideoButton from "../../components/dashboard/skill-video-button";
import PreviewSkillTreeModal from "./preview-skill-tree-modal";
import TooltipWrapper from "../../components/tooltip-wrapper";

class FollowUpAssessment extends Component {
  static contextType = ScoreEntryContext;

  state = {
    isSavingScore: false,
    shouldDisplayPreviewSkillTreeModal: false
  };

  constructor(props) {
    super(props);
    this.enterScore = this.enterScore.bind(this);
  }

  enterScore(e) {
    e.preventDefault();
    const score = this.props.assessmentResult.scores.find(
      s => s.assessmentId === this.props.assessmentResult.individualSkills.assessmentId
    );
    const arrScoresPkg = [
      {
        scoreId: score._id,
        assessmentResultId: this.props.assessmentResult._id,
        number_correct: score.value,
        status: score.status
      }
    ];
    if (arrScoresPkg.every(asp => asp.status === "COMPLETE")) {
      this.context.clearUnusualHighScoreFields();
      this.setState({ isSavingScore: true });
      Meteor.call("AssessmentResults:updateScores", arrScoresPkg, this.props.siteId, err => {
        if (err) {
          // just let it go for now..
          this.setState({ isSavingScore: false });
        }
        Meteor.call(
          "saveScoreResult",
          {
            assessmentResultId: this.props.assessmentResult._id
          },
          (error, res) => {
            this.setState({ isSavingScore: false });
            if (error) {
              Alert.error(error.reason || "There was a problem while saving the score result", {
                timeout: 5000
              });
            }
            if (res && res.hasFollowUpPhaseEnded) {
              Meteor.call("assignStudentToSkillGroup", {
                siteId: this.props.siteId,
                studentId: this.props.studentInfo._id
              });
            }
          }
        );
      });
    }
  }

  renderScoreSubmitButton = () => {
    if (Object.keys(this.context.getUnusualHighScoreFields(this.props.assessmentResult._id)).length) {
      return (
        <button type="button" className="btn btn-default">
          <i className="fa fa-warning fa-right" />
          Fix Unusual High Score
        </button>
      );
    }

    const score = this.props.assessmentResult.scores.find(
      s => s.assessmentId === this.props.assessmentResult.individualSkills.assessmentId
    );

    if (score && score.status === "COMPLETE") {
      return (
        <button
          className="btn btn-success"
          data-testid="enterScoreBtn"
          disabled={!this.props.inActiveSchoolYear || this.state.isSavingScore}
          onClick={this.enterScore}
        >
          <i className="fa fa-save fa-right" />
          Save Results
        </button>
      );
    }

    return (
      <button type="button" className="btn btn-default">
        <i className="fa fa-warning fa-right" />
        Enter Scores to Continue
      </button>
    );
  };

  togglePreviewSkillTreeModal = () => {
    this.setState(state => ({ shouldDisplayPreviewSkillTreeModal: !state.shouldDisplayPreviewSkillTreeModal }));
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }
    const { monitorAssessmentMeasure: measureNumber, hasVideo, skillName } = this.props.followUpAssessment || {};
    const translatedBenchmarkPeriod = translateBenchmarkPeriod(this.props.assessmentResult.benchmarkPeriodId);
    const benchmarkPeriodName = (translatedBenchmarkPeriod && translatedBenchmarkPeriod.name) || "";
    const assessmentScoreLimit = {
      assessmentId: this.props.assessmentResult.individualSkills.assessmentId,
      limit: this.props.assessmentResult.individualSkills.assessmentTargets[1] * 5,
      at: this.props.assessmentResult.individualSkills.assessmentTargets[0]
    };
    const assessmentScore = this.props.assessmentResult.scores.find(
      sc => sc.assessmentId === this.props.followUpAssessment._id && sc.studentId === this.props.studentInfo._id
    );
    const studentFullName = `${this.props.studentInfo.identity.name.firstName} ${this.props.studentInfo.identity.name.lastName}`;

    const {
      assessmentTargets: [skillInstructional, skillMastery]
    } = this.props.assessmentResult.individualSkills;

    const {
      env: { CI }
    } = this.context;
    const shouldRenderAdditionalInfo = shouldUseDevMode(CI);

    return (
      <div
        className="skill-container individual pre-intervention clearfix"
        data-benchmark-period-name={benchmarkPeriodName}
        data-student-id={this.props.studentInfo._id}
        data-testid="individual-intervention-followup-skill"
      >
        <div className="skill-details">
          {this.props.studentInfo.currentSkill &&
          this.props.studentInfo.currentSkill.assessmentId &&
          this.props.studentInfo.currentSkill.message &&
          !this.props.studentInfo.currentSkill.message.dismissed ? (
            <InterventionMessage
              entityId={this.props.studentInfo._id}
              entityType="Student"
              message={this.props.studentInfo.currentSkill.message}
              name={this.props.studentInfo.identity.name}
            />
          ) : null}
          <p>
            <strong>
              {this.props.studentInfo.identity.name.firstName} {this.props.studentInfo.identity.name.lastName}{" "}
            </strong>
            is currently completing follow ups to their screening assessments.
          </p>
          <h3 className="w9 d-flex gap-3 vertical-align-middle">
            {this.props.assessmentName}
            {shouldRenderAdditionalInfo ? <span className="w5 font-18">{` (AM#${measureNumber})`}</span> : null}
            <div>
              <SkillVideoButton measureNumber={measureNumber} skillName={skillName} hasVideo={hasVideo} />
            </div>
          </h3>
          <div>
            <PrintMaterials
              assessmentMeasure={this.props.followUpAssessment.monitorAssessmentMeasure}
              assessmentId={this.props.followUpAssessment._id}
              benchmarkPeriodId={this.props.assessmentResult.benchmarkPeriodId}
              protocolMeasure={this.props.followUpAssessment.monitorAssessmentMeasure}
              interventionType=""
              materialsType="assessment"
              grade={this.props.studentInfo.grade}
              studentName={studentFullName}
              initialText="Generate Assessment"
              loadingText="Generating custom assessment. Just a moment please."
            />{" "}
            {shouldUseDevMode(CI, ["LOCAL", "DEV", "QA", "STAGE"]) ? (
              <React.Fragment>
                <Button variant="outline-blue" className="skill-button" onClick={this.togglePreviewSkillTreeModal}>
                  <TooltipWrapper
                    text={<i className="fa fa-sitemap cursor-pointer" />}
                    tooltipText={"Individual Progress Monitoring Tree Preview"}
                    placement="top"
                    customClassName=""
                    isClickTriggerEnabled={false}
                  />
                </Button>
                {this.state.shouldDisplayPreviewSkillTreeModal ? (
                  <PreviewSkillTreeModal
                    showModal={this.state.shouldDisplayPreviewSkillTreeModal}
                    onCloseModal={this.togglePreviewSkillTreeModal}
                    params={{
                      grade: this.props.studentInfo?.grade || "",
                      benchmarkPeriodId: this.props.assessmentResult?.benchmarkPeriodId || "",
                      benchmarkAssessmentId: this.props.assessmentResult.individualSkills?.benchmarkAssessmentId || "",
                      isPreviewOnly: true
                    }}
                  />
                ) : null}
              </React.Fragment>
            ) : null}
          </div>
        </div>
        <div className="skill-score-entry">
          {this.props.inActiveSchoolYear ? (
            <form className="form-horizontal text-center" autoComplete="off">
              <div className="form-group">
                <div className="col-sm-12 relativeWrapper">
                  {shouldRenderAdditionalInfo ? (
                    <small className="w6 debug-small">
                      At: ({skillInstructional}), Above: ({skillMastery})
                    </small>
                  ) : null}
                  <ScoreEntryRowCell
                    cellId={`${this.props.studentInfo._id}_drilldown`}
                    assessmentScoreLimit={assessmentScoreLimit}
                    assessmentResultId={this.props.assessmentResult._id}
                    assessmentScore={assessmentScore}
                    inActiveSchoolYear={this.props.inActiveSchoolYear}
                    isReadOnly={this.props.isReadOnly}
                    index={0}
                    rowScoreCount={1}
                    seleniumSelectorText="enterSkillScore"
                    siteId={this.props.siteId}
                  />
                </div>
                <div className="col-sm-12">{this.renderScoreSubmitButton()}</div>
              </div>
            </form>
          ) : null}
        </div>
      </div>
    );
  }
}

FollowUpAssessment.propTypes = {
  assessmentName: PropTypes.string,
  assessmentResult: PropTypes.shape({
    _id: PropTypes.string,
    benchmarkPeriodId: PropTypes.string,
    individualSkills: PropTypes.shape({
      assessmentId: PropTypes.string,
      benchmarkAssessmentId: PropTypes.string,
      assessmentTargets: PropTypes.array
    }),
    scores: PropTypes.arrayOf(
      PropTypes.shape({
        assessmentId: PropTypes.string
      })
    )
  }),
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  loading: PropTypes.bool,
  followUpAssessment: PropTypes.shape({
    _id: PropTypes.string,
    monitorAssessmentMeasure: PropTypes.string,
    hasVideo: PropTypes.bool
  }),
  studentInfo: PropTypes.shape({
    _id: PropTypes.string,
    grade: PropTypes.string,
    identity: PropTypes.shape({
      name: PropTypes.shape({
        firstName: PropTypes.string,
        lastName: PropTypes.string
      })
    }),
    currentSkill: PropTypes.shape({
      assessmentId: PropTypes.string,
      message: PropTypes.object
    })
  }),
  siteId: PropTypes.string,
  skillGroups: PropTypes.array,
  isFetchingSkillGroups: PropTypes.bool
};

const FollowUpAssessmentContainer = withRouter(
  withTracker(props => {
    let assessmentName = "";
    const followUpAssessment = Assessments.findOne({
      _id: props.assessmentResult.individualSkills.assessmentId
    });
    if (followUpAssessment) {
      ({ assessmentName } = props);
    }
    return {
      followUpAssessment,
      assessmentName,
      loading: !assessmentName || !followUpAssessment,
      siteId: props.match.params.siteId
    };
  })(FollowUpAssessment)
);

export default FollowUpAssessmentContainer;
