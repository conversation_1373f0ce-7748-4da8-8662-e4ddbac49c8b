import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";

import { withTracker } from "meteor/react-meteor-data";
import { ListGroup } from "react-bootstrap";
import { Loading } from "../../components/loading.jsx";

export class StudentGroupList extends Component {
  isActive = groupId => (this.props.match.params.studentGroupId === groupId ? " active" : "");

  render() {
    if (this.props.loading) return <Loading />;
    return (
      <div className="col-sm-3 col-md-2">
        <ListGroup className="student-group-list">
          {this.props.studentGroups.map(({ _id, name, siteId, orgid: orgId }) => (
            <Link
              className={`${this.isActive(_id)} list-group-item`}
              to={`/${orgId}/site/${siteId}/student-groups/${_id}`}
              key={_id}
            >
              {name}
            </Link>
          ))}
        </ListGroup>
      </div>
    );
  }
}

StudentGroupList.propTypes = {
  loading: PropTypes.bool,
  studentGroups: PropTypes.array,
  match: PropTypes.object
};

export default withTracker(() => ({}))(withRouter(StudentGroupList));
