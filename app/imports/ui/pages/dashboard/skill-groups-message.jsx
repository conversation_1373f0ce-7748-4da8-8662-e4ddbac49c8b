import React, { Component } from "react";
import PropTypes from "prop-types";

export default class SkillGroupsMessage extends Component {
  render() {
    if (!this.props.skillGroups || !this.props.skillGroups.length || this.props.isFetchingSkillGroups) {
      return null;
    }

    return (
      <div>
        <div className="conInterventionMessageNotice">
          <div className="conInterventionMessageNotice-Heading">
            <h2>
              There is more than one student working on the same Individual Intervention skill this week. To see small
              group options
            </h2>
            <button className="btn btn-primary btn-xs pull-right" onClick={this.props.openInterventionSkillGroupsModal}>
              click here
            </button>
          </div>
        </div>
      </div>
    );
  }
}

SkillGroupsMessage.propTypes = {
  skillGroups: PropTypes.array,
  isFetchingSkillGroups: PropTypes.bool,
  openInterventionSkillGroupsModal: PropTypes.func
};
