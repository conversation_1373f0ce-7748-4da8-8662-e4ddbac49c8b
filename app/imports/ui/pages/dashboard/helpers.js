import moment from "moment";
import { getNextBMPeriod } from "/imports/api/benchmarkPeriods/methods";
import { get } from "lodash";
import { situations as navs, situations } from "../../../api/utilities/utilities";

function getNextBenchmarkPeriodDate(benchmarkPeriodDateObject, customDate) {
  const { month, day } = benchmarkPeriodDateObject;
  const currentDate = customDate ? moment(customDate) : moment();
  const year =
    currentDate.month() < month - 1 || (currentDate.month() === month - 1 && currentDate.day() <= day)
      ? currentDate.year()
      : currentDate.year() + 1;
  return moment({ year, month: month - 1, day });
}

export function calculateScreeningWindowNotice({
  isInActiveSchoolYear,
  isReadOnly,
  currentSituation,
  currentAssessmentResults,
  benchmarkPeriod,
  studentGroup,
  activeNavName,
  customDate,
  benchmarkPeriodsGroupId,
  isTestOrg,
  benchmarkPeriods
}) {
  let newScreeningWindowNotice = { show: false };

  if (!isInActiveSchoolYear || isReadOnly) {
    return {
      show: false,
      shouldClear: true
    };
  }

  const nextBMPeriod = getNextBMPeriod({
    customDate,
    benchmarkPeriodsGroupId,
    isTestOrg,
    benchmarkPeriods
  });

  const nextBMPeriodStartDate = getNextBenchmarkPeriodDate(
    get(nextBMPeriod.startDate, benchmarkPeriodsGroupId, nextBMPeriod.startDate.default),
    customDate
  );

  const daysBeforeScreeningWindow = nextBMPeriodStartDate.diff(customDate ? moment(customDate) : moment(), "d");

  // Check if we should show the upcoming screening window notice (14-day warning)
  if (daysBeforeScreeningWindow > 0 && daysBeforeScreeningWindow <= 14) {
    newScreeningWindowNotice = {
      show: true,
      name: nextBMPeriod.name,
      componentName: "screening-window-notice",
      showThreeDayWarning: daysBeforeScreeningWindow <= 3,
      daysBeforeScreeningWindow,
      studentGroup
    };
  }
  // Check if we should show the screening window started notice
  else if (
    (currentSituation & situations.needsScreening ||
      (currentSituation & situations.screening &&
        currentAssessmentResults &&
        currentAssessmentResults.some(
          ar => ar.type === "benchmark" && ar.benchmarkPeriodId === benchmarkPeriod._id
        ))) &&
    activeNavName !== navs.screening &&
    activeNavName
  ) {
    newScreeningWindowNotice = {
      show: true,
      name: benchmarkPeriod.name,
      componentName: "screening-window-started",
      screeningContinues:
        currentAssessmentResults &&
        currentAssessmentResults.some(ar => ar.type === "benchmark" && ar.benchmarkPeriodId === benchmarkPeriod._id),
      daysBeforeScreeningWindow,
      studentGroup
    };
  }
  // Default case - hide the notice
  else {
    newScreeningWindowNotice = {
      show: false,
      shouldClear: true
    };
  }

  return newScreeningWindowNotice;
}
