import { assert } from "chai";

import React from "react";
import { shallow } from "enzyme";

import DashboardContainer from "./dashboard.jsx";

describe("Dashboard UI", () => {
  describe("Render", () => {
    it("render", () => {
      // Verify that the method does what we expected
      const dashboardComponent = shallow(<DashboardContainer studentGroupId="test-group-id" siteId="test-site-id" />);
      assert.isDefined(dashboardComponent, "dashboardComponent did not render");
    });
  });
});
