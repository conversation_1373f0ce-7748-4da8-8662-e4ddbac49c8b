import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
/*
SPRIN-1505
If there are students who are both at the same level (acquisition or fluency building)
and who are working on the same skill group (now relying on GroupedAssessments) we need to provide
the users a message that tells them that a grouping is available (let's call this a match).

StudentsBySkill
{
    studentsBelowInstructionalTarget: [], // Acquisition
    studentsBelowMasteryTarget: [], // Fluency
    studentsWithoutSkillHistory: [], // No Progress
    skillName: ga.skillName,
    assessmentGroupId: ga._id, // Reference to GroupedAssessments collection, example below
    siteId
}

GroupedAssessments
{
    "_id" : "6440990bb96ca20007bd96a5",
    "skillName" : "Subtraction",
    "assessmentMeasures" : [ "40", "53", "62", "82" ]
}
 */
export default class InterventionSkillGroupsModal extends Component {
  close = () => {
    this.props.closeModal();
  };

  render() {
    const { skillGroups } = this.props;
    return (
      <Modal show={this.props.showModal} onHide={this.close} dialogClassName="modal-lg modal-xl" backdrop="static">
        <ModalHeader>
          <h3 className="w9">Suggested Small Groups</h3>
        </ModalHeader>

        <ModalBody>
          <div className="modal-container">
            <div className="row">
              <div className="col-sm-2">Last/ First</div>
              <div className="col-sm-1 text-center">Grade</div>
              <div className="col-sm-3 text-center">Class</div>
              <div className="col-sm-4 text-center">Intervention</div>
              <div className="col-sm-2 text-center">Instructional Level</div>
            </div>
            {skillGroups.map((skillGroup, index) => {
              return (
                <React.Fragment key={skillGroup.skillName}>
                  <hr className="my-1 hr-dark" />
                  {skillGroup.students.map((student, i) => (
                    <div key={`studentRow_${index}_${i}`} className="row m-b-10" data-testid="groupingRow">
                      <div className="col-sm-2 skill-group-cell-overflow" data-testid={`nameGroupingRowCell-${i}`}>
                        {student.lastName}, {student.firstName}
                      </div>
                      <div
                        className="col-sm-1 skill-group-cell-overflow text-center"
                        data-testid={`gradeGroupingRowCell-${i}`}
                      >
                        {student.grade}
                      </div>
                      <div
                        className="col-sm-3 skill-group-cell-overflow text-center"
                        data-testid={`classNameGroupingRowCell-${i}`}
                      >
                        {student.studentGroupName}
                      </div>
                      <div
                        className="col-sm-4 skill-group-cell-overflow text-center"
                        data-testid={`interventionGroupingRowCell-${i}`}
                      >
                        {student.interventionName}
                      </div>
                      <div className="col-sm-2 text-center" data-testid={`skillGroupingRowCell-${i}`}>
                        {student.skillProgress}
                      </div>
                    </div>
                  ))}
                </React.Fragment>
              );
            })}
          </div>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

InterventionSkillGroupsModal.propTypes = {
  showModal: PropTypes.bool,
  skillGroups: PropTypes.array,
  closeModal: PropTypes.func
};
