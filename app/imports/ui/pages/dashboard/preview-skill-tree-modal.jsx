import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import ManageRules from "../manage-rules/manage-rules";

export default class PreviewSkillTreeModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  render() {
    const { showModal, params } = this.props;
    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName="modal-95"
        backdrop={true}
        data-testid="previewSkillTreeModal"
      >
        <ModalHeader className="align-content-center justify-content-center">
          <div className="text-center">
            <h3>Progress Monitoring</h3>
          </div>
        </ModalHeader>
        <ModalBody>
          <ManageRules {...params} />
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

PreviewSkillTreeModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  cancelText: PropTypes.string,
  params: PropTypes.object
};
