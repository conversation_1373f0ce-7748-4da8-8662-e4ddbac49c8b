import React from "react";
import PropTypes from "prop-types";
import { SCORE_INCREASING_METRIC_THRESHOLD } from "/imports/api/constants";

const ProgramEvaluationIndividualInterventionRow = props => {
  const getAverageScoresIncreasing = () => {
    return props.averageScoresIncreasing !== undefined ? `${props.averageScoresIncreasing.toFixed(0)}%` : "N/A";
  };

  return (
    <div className="row row-margin-fixed rowIndvSummary classwideSummary" data-testid="rowIndvSummary">
      <div className="col-1 text-center">{Number.isNaN(Number(props.grade)) ? props.grade : Number(props.grade)}</div>
      <div className="col-3 text-center">{props.numNeeding}</div>
      <div className="col-3 text-center">{props.numGetting}</div>
      <div className="col-3 text-center">{props.numCompleting}</div>
      <div
        className={`col-2 text-center ${
          props.numGetting && props.averageScoresIncreasing < SCORE_INCREASING_METRIC_THRESHOLD ? "text-danger" : ""
        }`}
      >
        {!props.numGetting ? "N/A" : getAverageScoresIncreasing()}
      </div>
    </div>
  );
};

ProgramEvaluationIndividualInterventionRow.propTypes = {
  averageScoresIncreasing: PropTypes.number,
  numNeeding: PropTypes.number,
  numGetting: PropTypes.number,
  numCompleting: PropTypes.number,
  grade: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};

export default ProgramEvaluationIndividualInterventionRow;
