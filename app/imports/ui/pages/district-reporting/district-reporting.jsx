import React, { useContext, useEffect, useState } from "react";
import { <PERSON>, with<PERSON>outer } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import { chunk, keyBy, intersection } from "lodash";

import { SCORE_INCREASING_METRIC_THRESHOLD } from "/imports/api/constants";
import Loading from "../../components/loading";
import { isOnPrintPage, openPrintWindow } from "../../utilities";
import * as reporting from "../program-evaluation/reporting-data-methods";
import BarGraph from "./bar-graph";
import { getValuesByKey } from "../../../api/utilities/utilities";
import {
  getAverageNumberOfSkillsCompletedByGradeData,
  getIndividualInterventionImplementationForSchools,
  getSkillProgressByGrade,
  renderNoScoresBanner,
  renderSeasonToSeason
} from "../program-evaluation/reporting-data-methods";
import DetailTable from "../student-detail/skillProgress";
import BarGrowthGraph from "./bar-growth-graph";
import { GrowthChartLegend } from "../admin-view/growth-chart-legend";
import { getGrowthChartMarkup } from "../student-groups/growth-chart-wrapper";
import { normalizeGrade } from "../../../api/utilities/sortingHelpers/normalizeSortItem";
import { getNumberOfDigitsForSeries } from "../../../api/districtReporting/helpers";
import { getGrowthGraphSubtitle } from "../program-evaluation/helpers";
import { OrganizationContext } from "../../../contexts/OrganizationContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";

const loadingMessage = "Please wait while your data is compiled. This can take up to a minute.";

function scroll(idToScrollTo) {
  document.getElementById(idToScrollTo).scrollIntoView({ behavior: "smooth" });
}

const DistrictReporting = props => {
  const [isGeneratingPrintout, setIsGeneratingPrintout] = useState(false);
  const isPrinting = isOnPrintPage();
  const hasAnyActiveData = !!props.schools?.find(s => s.numberOfStudents && s.numberOfGroups);

  useEffect(() => {
    window.onmessage = event => {
      if (event.data === "printScheduled") {
        setIsGeneratingPrintout(true);
      } else if (event.data === "printWindowClosed") {
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
        setIsGeneratingPrintout(false);
      }
    };
  }, []);

  const renderTitle = () => {
    return (
      <div className="text-center">
        <h4>
          District Reporting for {props.orgName} ({`${props.schoolYear - 1}-${props.schoolYear % 100}`})
        </h4>
      </div>
    );
  };

  const getBreakPageClassBasedOnThreshold = threshold => {
    return props.schools.length > threshold ? "page-break-before" : "";
  };

  const printPage = () => {
    const printURL = `/${props.orgid}/print/DistrictReporting?orgid=${props.orgid}&schoolYear=${props.schoolYear}`;
    openPrintWindow(printURL);
  };

  const renderSchoolCard = school => {
    if (school.numberOfStudents && school.numberOfGroups) {
      return (
        <div className="card-body">
          <p className="card-text">
            <strong>Number of students: </strong> {school.numberOfStudents || 0}
          </p>
          <p className="card-text">
            <strong>Number of classes/groups: </strong> {school.numberOfGroups || 0}
          </p>
          {school.completedAssessmentResults.length ? null : (
            <p className="card-text">No data has been saved for this school</p>
          )}
        </div>
      );
    }

    return <p className="card-body card-text">No Active Data</p>;
  };

  const chunkSchoolCards = schools => {
    const result = [];
    let currentIndex = 0;
    const firstPageChunkSize = 8;
    const pageChunkSize = 10;

    const firstChunk = schools.slice(currentIndex, currentIndex + firstPageChunkSize);
    result.push(firstChunk);
    currentIndex += firstPageChunkSize;

    while (currentIndex < schools.length) {
      const schoolChunk = schools.slice(currentIndex, currentIndex + pageChunkSize);
      result.push(schoolChunk);
      currentIndex += pageChunkSize;
    }

    return result;
  };

  const renderSection1 = () => {
    return (
      <div id="district-reporting-schools" className="scroll-margin-with-sticky-menu">
        <p>
          This report is designed to provide you with aggregated data for your district for the year you have selected.
          The information provided is designed to help you review how well your district implemented SpringMath and its
          impact upon your students’ results. Drilling down to the school specific reports that are available within
          SpringMath will help you to answer questions that arise.
        </p>
        <h5>Schools included:</h5>
        {isPrinting ? (
          chunkSchoolCards(props.schools).map((schoolsChunk, i) => (
            <div
              key={`schoolCardGroup_${i}`}
              className={`d-grid gap-2 district-reporting-school-card-group${i !== 0 ? " page-break-before" : ""}`}
            >
              {schoolsChunk.map((school, index) => (
                <div className="card" key={`schoolCard_${i}_${index}`}>
                  <div className="card-header">{school.name}</div>
                  {renderSchoolCard(school)}
                </div>
              ))}
            </div>
          ))
        ) : (
          <div className="d-grid gap-2 district-reporting-school-card-group">
            {props.schools?.map((school, index) => (
              <div className="card" key={`schoolCard_${index}`}>
                <div className="card-header">{school.name}</div>
                {renderSchoolCard(school)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const chunkNestedDataForSchools = (screeningData, chunkSize) => {
    if (screeningData[0].data.length < chunkSize) {
      return [screeningData];
    }

    const screeningDataBySeasonName = keyBy(screeningData, "name");

    const chunksBySeasonName = {};
    const finalChunks = [];

    Object.entries(screeningDataBySeasonName).forEach(([seasonName, seasonElement]) => {
      chunksBySeasonName[seasonName] = chunk(seasonElement.data, chunkSize);
    });

    // NOTE(fmazur) - We only need indexes to iterate with
    Object.values(chunksBySeasonName)[0].forEach((c, index) => {
      finalChunks.push(
        Object.keys(chunksBySeasonName).map(seasonName => {
          return {
            ...screeningDataBySeasonName[seasonName],
            data: chunksBySeasonName[seasonName][index]
          };
        })
      );
    });
    return finalChunks;
  };

  const renderSection2 = () => {
    const screeningDataForSchools = reporting.getScreeningDataForSchools({
      schoolsInOrg: props.schools,
      schoolYear: props.schoolYear,
      benchmarkPeriodsGroupId: props.benchmarkPeriodsGroupId,
      bmPeriods: props.bmPeriods
    });
    const schoolNameById = getValuesByKey(props.schools, "_id", "name");

    const chunkSize = isPrinting ? Math.min(13, props.schools.length) : props.schools.length;
    const dataChunks = isPrinting
      ? chunkNestedDataForSchools(screeningDataForSchools, chunkSize)
      : [screeningDataForSchools];

    return (
      <div id="district-reporting-screening-participation" className="page-break-before scroll-margin-with-sticky-menu">
        <h5>
          <strong>Screening Participation:</strong>
        </h5>
        {dataChunks.map((dataChunk, index) => {
          const maxNumberOfDigits = getNumberOfDigitsForSeries(dataChunk);
          return (
            <div className={`${index !== 0 ? "page-break-before" : ""}`} key={`section2_graph_${index}`}>
              <BarGraph
                data={dataChunk}
                chartName={`Percent of Students in Schools Screened by Grade & Season${
                  dataChunks.length > 1 ? ` ${index + 1}/${dataChunks.length}` : ""
                }`}
                type="Column"
                chartId={`districtReporting_screeningUseBySchool_${index}`}
                options={reporting.getChartOptions({
                  height: dataChunk[0].data.length * 60,
                  shouldUseSecondaryLabel: true,
                  subtitleText: `N = the number of students enrolled when screening was completed <br /> No screening data for high school classes`,
                  dangerThreshold: 95,
                  categories: dataChunk[0].data.map(d => schoolNameById[d.schoolId]),
                  nOffset: -35 - 5 * maxNumberOfDigits,
                  categoryOffset: -40 - 5 * maxNumberOfDigits,
                  seriesAlternateCategoryLabel: "Winter",
                  noDataMessage: "No active data",
                  animation: false
                })}
              />
            </div>
          );
        })}
      </div>
    );
  };

  const renderClasswideImplementation = () => {
    const classwideProficiencyDataForSchools = reporting.getClasswideProficiencyDataForSchools({
      schoolsInOrg: props.schools,
      ruleAssessmentIdsByGrade: props.ruleAssessmentIdsByGrade
    });

    const schoolNameById = getValuesByKey(props.schools, "_id", "name");
    const chunkSize = isPrinting ? Math.min(13, props.schools.length) : props.schools.length;
    const dataChunks = isPrinting
      ? chunkNestedDataForSchools(classwideProficiencyDataForSchools, chunkSize)
      : [classwideProficiencyDataForSchools];

    return (
      <div
        id="district-reporting-classwide-interventions"
        className={`${getBreakPageClassBasedOnThreshold(5)} scroll-margin-with-sticky-menu`}
      >
        <h5>
          <strong>Classwide Interventions Implementation</strong>
        </h5>
        {dataChunks.map((dataChunk, index) => {
          return (
            <div className={`${index !== 0 ? "page-break-before" : ""}`} key={`section3_bar_graph_${index}`}>
              <BarGraph
                data={dataChunk}
                chartName={`Classwide Intervention Use by School${
                  dataChunks.length > 1 ? ` ${index + 1}/${dataChunks.length}` : ""
                }`}
                chartId={`districtReporting_classwideUseBySchool_${index}`}
                options={reporting.getChartOptions({
                  height: dataChunk[0].data.length * 60,
                  customHeightOffset: isPrinting ? 160 : 120,
                  categories: dataChunk[0].data.map(d => schoolNameById[d.schoolId]),
                  categoryOffset: -5,
                  categoryOffsetY: isPrinting ? -20 : 0,
                  seriesAlternateCategoryLabel: "% of Skills Mastered",
                  noDataMessage: "No Classwide Data",
                  animation: false,
                  legendLayout: isPrinting ? "vertical" : "horizontal"
                })}
              />
            </div>
          );
        })}
      </div>
    );
  };

  const chunkDataForClasswideInterventionProgress = (gradeData, chunkSize) => {
    const chunkedPlotData = chunk(gradeData.plotData[0].data, chunkSize);
    // const chunkedCategories = chunk(gradeData.categories, chunkSize);
    const chunkedData = [];
    // NOTE(fmazur) - Potential issue when schools have the same name but different id
    const schoolNameById = getValuesByKey(props.schools, "_id", "name");
    chunkedPlotData.forEach((plotDataSet, index) => {
      chunkedData.push({
        categories: plotDataSet.map(dataElement => schoolNameById[dataElement.schoolId]),
        plotData: [
          {
            ...gradeData.plotData,
            data: plotDataSet
          }
        ],
        numberOfClasswideSkills: gradeData.numberOfClasswideSkills,
        currentChunk: index + 1,
        totalChunks: plotDataSet.length
      });
    });
    return chunkedData;
  };

  function getDataSetsPerPage(classwideProgressGradeData) {
    const pages = [];

    classwideProgressGradeData.forEach(([grade, gradeData]) => {
      const dataChunks = chunkDataForClasswideInterventionProgress(gradeData, 18);

      dataChunks.forEach(dataChunk => {
        const chunkWithGrade = { ...dataChunk, grade };
        const lastPage = pages[pages.length - 1];
        const canAddToLastPage =
          lastPage &&
          lastPage.reduce((acc, item) => acc + item.categories.length, 0) + dataChunk.categories.length <=
            (lastPage.length === 2 ? 12 : 14) &&
          lastPage.length < 3;

        if (canAddToLastPage) {
          lastPage.push(chunkWithGrade);
        } else {
          // NOTE(fmazur) - Create a new page with the current chunk if it can't fit in the last page
          pages.push([chunkWithGrade]);
        }
      });
    });
    return pages;
  }

  const renderMedianNumberOfSkillsCompleted = () => {
    const schoolNameById = getValuesByKey(props.schools, "_id", "name");
    const averageNumberOfSkillsCompletedByGradeData = getAverageNumberOfSkillsCompletedByGradeData({
      schoolsInOrg: props.schools,
      ruleAssessmentIdsByGrade: props.ruleAssessmentIdsByGrade,
      schoolNameById
    });

    const sortedClasswideProgressGradeData = Object.entries(averageNumberOfSkillsCompletedByGradeData).sort(
      ([a], [b]) => {
        return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
      }
    );

    const dataSetsPerPage = getDataSetsPerPage(sortedClasswideProgressGradeData);

    const getOptions = gradeData => {
      const scaleMax = gradeData.numberOfClasswideSkills + 2;
      return reporting.getChartOptions({
        height: gradeData.categories.length * 45,
        shouldUseSecondaryLabel: false,
        categories: gradeData.categories,
        subtitleText: "Red vertical line indicates the total number of classwide intervention skills",
        subtitleOffset: -30,
        categoryOffset: -10,
        shouldHidePercentInLabel: true,
        min: 0,
        max: scaleMax,
        shouldUseEvent: true,
        numberOfClasswideSkills: gradeData.numberOfClasswideSkills,
        isLegendEnabled: false,
        tickInterval: 1,
        categoryOffsetY: -35
      });
    };
    const titlePrefix = "Classwide Intervention Progress - Grade";
    return (
      <div className="page-break-before">
        <h5>
          <strong>Classwide Interventions Progress</strong>
        </h5>
        {isPrinting
          ? dataSetsPerPage.map((pageData, pageIndex) => {
              return (
                <div key={`cwip_${pageIndex}`} className={`${pageIndex !== 0 ? "page-break-before" : ""}`}>
                  {pageData.map((gradeData, gradeIndex) => {
                    const { grade } = gradeData;
                    const chartPartLabel =
                      gradeData.totalChunks > 1 ? ` ${gradeData.currentChunk}/${gradeData.totalChunks}` : "";
                    const chartTitle = `
                          <div>
                            <span>${titlePrefix} ${grade}${chartPartLabel}</span>
                            <br />
                            <small class="font-13">(Median number of skills completed)</small>
                          </div>
                        `;
                    return (
                      <div key={`districtReporting_classwideInterventionProgress_${pageIndex}_${grade}_${gradeIndex}`}>
                        <BarGraph
                          chartName={chartTitle}
                          chartId={`classwideInterventionProgress_${pageIndex}_${grade}_${gradeIndex}`}
                          options={getOptions(gradeData)}
                          data={gradeData.plotData}
                        />
                      </div>
                    );
                  })}
                </div>
              );
            })
          : sortedClasswideProgressGradeData.map(([grade, gradeData], gradeIndex) => {
              const chartTitle = `
                          <div> 
                            <span>${titlePrefix} ${grade}</span> 
                            <br /> 
                            <small class="font-13">(Median number of skills completed)</small> 
                          </div> 
                        `;
              return gradeData.plotData.length ? (
                <div key={`gradeCWIP_${grade}`} className={`${gradeIndex !== 0 ? "page-break-before" : ""}`}>
                  <div key={`districtReporting_classwideInterventionProgress_${grade}_${gradeIndex}`}>
                    <BarGraph
                      chartName={chartTitle}
                      chartId={`classwideInterventionProgress_${grade}_${gradeIndex}`}
                      options={getOptions(gradeData)}
                      data={gradeData.plotData}
                    />
                  </div>
                </div>
              ) : (
                renderNoScoresBanner({ isPrinting })
              );
            })}
      </div>
    );
  };

  const renderAveragePercentOfStudentsByInstructionalRangeWhenMasteryAchieved = () => {
    const skillProgressByGrade = getSkillProgressByGrade({
      schoolsInOrg: props.schools,
      assessmentNameById: props.assessmentNameById,
      classwideRules: props.classwideRules
    });
    const printingFontSizeClassName = isPrinting ? "font-13" : "";
    return (
      <div className="page-break-before">
        {Object.values(skillProgressByGrade).flat(1).length
          ? Object.entries(skillProgressByGrade).map(([grade, rowData], index) => {
              const shouldRenderTable = rowData?.find(rd => rd.columns.filter(Boolean)?.length);
              return (
                <div key={grade} className={index !== 0 ? "page-break-before" : ""}>
                  <div>
                    <div className="d-flex flex-row justify-content-between">
                      <h5 className={printingFontSizeClassName}>Grade {grade}</h5>
                      <h5 className={printingFontSizeClassName}>
                        Percent of Students in Each Category when Mastery was Reached in Classwide Intervention
                      </h5>
                    </div>
                    {shouldRenderTable ? (
                      <p className={printingFontSizeClassName}>
                        Small classes with alternative mastery criteria are included in these calculations
                      </p>
                    ) : null}
                  </div>

                  {rowData.length && shouldRenderTable ? (
                    <DetailTable key={`skillProgress${index}`} rowData={rowData} componentContext={"compactTable"} />
                  ) : (
                    renderNoScoresBanner({
                      message: "Classwide Intervention Scores not yet submitted for any student group in this grade",
                      isPrinting
                    })
                  )}
                  {!isPrinting ? <br /> : null}
                </div>
              );
            })
          : renderNoScoresBanner({ isPrinting })}
      </div>
    );
  };

  const renderSection3 = () => {
    return (
      <React.Fragment>
        {renderClasswideImplementation()}
        {renderMedianNumberOfSkillsCompleted()}
        {!isPrinting ? <hr /> : null}
        {renderAveragePercentOfStudentsByInstructionalRangeWhenMasteryAchieved()}
      </React.Fragment>
    );
  };

  const renderSection5 = () => {
    const shouldDisplaySeasonToSeason =
      intersection(
        Object.keys(props.growthDataBySchoolYearByGrade).map(s => parseInt(s)),
        [props.schoolYear - 1, props.schoolYear]
      ).length === 2;
    const fallToFallDataByGrade = {};
    const winterToWinterDataByGrade = {};
    /*
        {
          2024: {
            01: {
              winterToSpring: { fall: {}, winter...}
            }
          },
          2023: ...
        }
     */
    const customColorsByMeasurePeriod = {
      fall: "#ed8b00",
      winter: "#003349",
      spring: "#B6C1CB",
      classwide: "#565294"
    };

    Object.entries(props.growthDataBySchoolYearByGrade).forEach(([schoolYear, growthByGrade]) => {
      Object.entries(growthByGrade).forEach(([grade, growthSet]) => {
        fallToFallDataByGrade[grade] = fallToFallDataByGrade[grade] ?? [];
        winterToWinterDataByGrade[grade] = winterToWinterDataByGrade[grade] ?? [];
        const fallData = { categories: [], scores: [], schoolYear };
        const winterData = { categories: [], scores: [], schoolYear };
        const { chartItems } = getGrowthChartMarkup("all", growthSet, true, grade, customColorsByMeasurePeriod);
        for (let i = 0; i < chartItems.categories.length; i += 1) {
          if (chartItems.categories[i]?.measurePeriod === "fall") {
            fallData.categories.push(chartItems.categories[i]);
            fallData.scores.push(chartItems.scores[i]);
          }
          if (chartItems.categories[i]?.measurePeriod === "winter") {
            winterData.categories.push(chartItems.categories[i]);
            winterData.scores.push(chartItems.scores[i]);
          }
        }
        fallToFallDataByGrade[grade].push(fallData);
        winterToWinterDataByGrade[grade].push(winterData);
      });
    });

    const hasAnyGrowthDataToDisplay = !!Object.values(props.growthDataBySchoolYearByGrade || {})
      .map(el => Object.values(el || {}))
      .flat(2).length;

    if (!hasAnyGrowthDataToDisplay) {
      return null;
    }

    return (
      <div id="district-reporting-growth" className="page-break-before scroll-margin-with-sticky-menu">
        <h5>Seasonal Growth</h5>
        {Object.entries(props.growthDataBySchoolYearByGrade?.[props.schoolYear] || {})
          .sort(([a], [b]) => {
            return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
          })
          .map(([grade, growthSet], index) => {
            const { chartId, chartItems } = getGrowthChartMarkup(
              "all",
              growthSet,
              true,
              grade,
              customColorsByMeasurePeriod
            );

            let subtitleText = "";
            if (grade !== "HS") {
              subtitleText = getGrowthGraphSubtitle(chartItems.scores);
            }
            const hasAnyStudentsBeenAssessed = !!chartItems.categories.filter(Boolean).find(item => item.n !== 0);

            return (
              <div key={`growth_section_${index}`} className={index > 0 ? "page-break-before" : ""}>
                <h6 className="text-center">Seasonal Growth for Grade {grade}</h6>
                {hasAnyStudentsBeenAssessed ? (
                  <div className="text-center font-13">
                    <small>(Percent of students at or above the instructional target at each timepoint)</small>
                  </div>
                ) : null}
                {hasAnyStudentsBeenAssessed ? (
                  <React.Fragment>
                    <GrowthChartLegend comparisonPeriod={"all"} />
                    <BarGrowthGraph
                      chartName={""}
                      chartId={chartId}
                      data={[{ showInLegend: false, data: chartItems.scores }]}
                      options={{
                        categories: chartItems.categories,
                        height: chartItems.categories.length * (isPrinting ? 45 : 30),
                        categoryWidth: "600px",
                        usePlotLines: true,
                        subtitleText
                      }}
                    />
                  </React.Fragment>
                ) : (
                  renderNoScoresBanner({ message: "No screening data available", isPrinting })
                )}
                <br />
              </div>
            );
          })}
        {shouldDisplaySeasonToSeason ? (
          <React.Fragment>
            <h5 className="page-break-before">Fall to Fall growth by year and grade</h5>
            {renderSeasonToSeason({
              seasonData: fallToFallDataByGrade,
              graphContext: "Fall to Fall",
              schoolYear: props.schoolYear,
              isPrinting
            })}
            <h5 className="page-break-before">Winter to Winter growth by year and grade</h5>
            {renderSeasonToSeason({
              seasonData: winterToWinterDataByGrade,
              graphContext: "Winter to Winter",
              schoolYear: props.schoolYear,
              isPrinting
            })}
          </React.Fragment>
        ) : null}
      </div>
    );
  };

  const getAverageScoresIncreasing = averageScoresIncreasing => {
    return averageScoresIncreasing !== undefined ? `${averageScoresIncreasing.toFixed(0)}%` : "N/A";
  };

  const renderIndividualInterventionUseBySchool = (rowChunk, chunkIndex) => {
    return (
      <div
        key={`individualInterventionUseBySchool_${chunkIndex}`}
        className={chunkIndex !== 0 ? "page-break-before" : ""}
      >
        <div className="text-center">Individual Intervention use by School</div>
        <div>
          <div className="tblIntvSummaryTable" data-testid={"districtReporting_individualInterventionTable"}>
            <div className="row row-margin-fixed rowIndvSummaryHeading">
              <div className="col-4 header">School</div>
              <div className="col-2 header">Individual Interventions Recommended</div>
              <div className="col-2 header">Individual Interventions Started</div>
              <div className="col-2 header">Individual Interventions Completed</div>
              <div className="col-2 header">% of Scores Increasing</div>
            </div>
            <div className={`individual-interventions-group ${isPrinting ? "font-13" : ""}`}>
              {rowChunk.map(row => (
                <div
                  key={row._id}
                  className="row row-margin-fixed rowIndvSummary classwideSummary"
                  data-testid="rowIndvSummary"
                >
                  <div className="col-4">{row.name}</div>
                  <div className="col-2 text-center">{row.numNeeding}</div>
                  <div className="col-2 text-center">{row.numGetting}</div>
                  <div className="col-2 text-center">{row.numCompleting}</div>
                  <div
                    className={`col-2 text-center ${
                      row.numGetting && row.averageScoresIncreasing < SCORE_INCREASING_METRIC_THRESHOLD
                        ? "text-danger"
                        : ""
                    }`}
                  >
                    {!row.numGetting ? "N/A" : getAverageScoresIncreasing(row.averageScoresIncreasing)}
                  </div>
                </div>
              ))}
            </div>
            <small className="font-13">
              Started = At least one score has been saved in the individual intervention. <br /> Completed = Student has
              reached mastery performance on at least one of the screening skills in the season in which the scores are
              being reported (fall, winter or spring). <br />
              Individual interventions scheduled by coaches are not included in the recommendations column.
            </small>
          </div>
        </div>
      </div>
    );
  };

  const renderIndividualInterventionImplementation = () => {
    const rows = getIndividualInterventionImplementationForSchools({ schools: props.schools });
    const schoolsPerPage = 24;
    const rowChunks = isPrinting ? chunk(rows, schoolsPerPage) : [rows];
    return (
      <div
        id="district-reporting-individual-interventions"
        className="page-break-before scroll-margin-with-sticky-menu"
      >
        <h5>
          <strong>Individual Intervention Implementation</strong>
        </h5>
        {rowChunks.map((rowChunk, chunkIndex) => renderIndividualInterventionUseBySchool(rowChunk, chunkIndex))}
      </div>
    );
  };

  const renderSection4 = () => {
    return renderIndividualInterventionImplementation();
  };

  const renderSpringProficiencyBySchool = wasSectionRenderedByContext => {
    const {
      districtCategoriesByGrade,
      districtSeriesByGrade,
      districtAssessmentName,
      stateAssessmentName,
      stateSeriesByGrade,
      stateCategoriesByGrade
    } = props.externalProficiencyDataForSchools.springData;

    let wasAnyDistrictRendered = false;
    let wasAnyStateRendered = false;

    return (
      <React.Fragment>
        {Object.entries(districtSeriesByGrade).map(([grade, data], index) => {
          const maxNumberOfDigits = getNumberOfDigitsForSeries(data);
          if (districtAssessmentName === "N/A") {
            if (Object.entries(districtSeriesByGrade).length - 1 === index && !wasAnyDistrictRendered) {
              return (
                <div key={`spring_district_district_no_scores`}>
                  <h6 className="text-center">District Percent Proficient on External Measure - Spring</h6>
                  {renderNoScoresBanner({ isPrinting })}
                </div>
              );
            }
            return null;
          }
          if (!wasAnyDistrictRendered) {
            wasSectionRenderedByContext.spring = true;
            wasAnyDistrictRendered = true;
          }

          return (
            <div key={`spring_district_${grade}`} className={index > 0 ? "page-break-before" : ""}>
              <h6 className="text-center">
                <strong>Grade {grade}</strong> - District Percent Proficient on External Measure{" "}
                <strong>{districtAssessmentName}</strong> - Spring
              </h6>
              <BarGraph
                data={data}
                chartName={""}
                chartId={`districtReporting_proficientExternalMeasureDistrictSpring_${grade}`}
                options={reporting.getChartOptions({
                  height: districtCategoriesByGrade[grade].length * 80,
                  shouldUseSecondaryLabel: true,
                  categories: districtCategoriesByGrade[grade],
                  nOffset: -35 - 5 * maxNumberOfDigits,
                  categoryOffset: -40 - 5 * maxNumberOfDigits,
                  categoryOffsetY: -40
                })}
              />
            </div>
          );
        })}
        {!isPrinting ? <hr className="hr-dark" /> : null}
        {Object.entries(stateSeriesByGrade).map(([grade, data], index) => {
          if (stateAssessmentName === "N/A") {
            if (Object.entries(stateSeriesByGrade).length - 1 === index && !wasAnyStateRendered) {
              return (
                <div
                  key={`spring_district_state_no_scores`}
                  className={wasAnyDistrictRendered ? "page-break-before" : ""}
                >
                  <h6 className="text-center">State Percent Proficient on External Measure - Spring</h6>
                  {renderNoScoresBanner({ isPrinting })}
                </div>
              );
            }
            return null;
          }
          if (!wasAnyStateRendered) {
            wasSectionRenderedByContext.spring = true;
            wasAnyStateRendered = true;
          }

          const maxNumberOfDigits = getNumberOfDigitsForSeries(data);
          return (
            <div key={`spring_state_${grade}`} className="page-break-before">
              <h6 className="text-center">
                <strong>Grade {grade}</strong> - State Percent Proficient on External Measure{" "}
                <strong>{stateAssessmentName}</strong> - Spring
              </h6>
              <BarGraph
                data={data}
                chartName={""}
                chartId={`stateReporting_proficientExternalMeasureDistrictSpring_${grade}`}
                options={reporting.getChartOptions({
                  height: stateCategoriesByGrade[grade].length * 80,
                  shouldUseSecondaryLabel: true,
                  categories: stateCategoriesByGrade[grade],
                  nOffset: -35 - 5 * maxNumberOfDigits,
                  categoryOffset: -40 - 5 * maxNumberOfDigits,
                  categoryOffsetY: -40
                })}
              />
            </div>
          );
        })}
      </React.Fragment>
    );
  };

  const renderFallToSpringProficiencyBySchool = wasSectionRenderedByContext => {
    const {
      districtSeriesByGrade,
      districtCategoriesByGrade,
      districtAssessmentName
    } = props.externalProficiencyDataForSchools.fallToSpringData;
    let wasAnyFallToSpringRendered = false;
    return Object.entries(districtSeriesByGrade)
      .sort(([a], [b]) => (normalizeGrade(a) < normalizeGrade(b) ? -1 : 1))
      .map(([grade, series], index) => {
        if (districtAssessmentName === "N/A") {
          if (Object.entries(districtSeriesByGrade).length - 1 === index && !wasAnyFallToSpringRendered) {
            return (
              <div
                key={`fall_to_spring_no_scores`}
                className={wasSectionRenderedByContext.spring ? "page-break-before" : ""}
              >
                <h6 className="text-center">District Percent Proficient on External Measure - Fall to Spring</h6>
                {renderNoScoresBanner({ isPrinting })}
              </div>
            );
          }
          return null;
        }
        if (!wasAnyFallToSpringRendered) {
          wasSectionRenderedByContext.fallToSpring = true;
          wasAnyFallToSpringRendered = true;
        }

        const maxNumberOfDigits = getNumberOfDigitsForSeries(series);
        const categories = districtCategoriesByGrade[grade];
        return (
          <div key={`fallToSpring_${grade}`} className={wasSectionRenderedByContext.spring ? "page-break-before" : ""}>
            <h6 className="text-center">
              <strong>Grade {grade}</strong> - District Percent Proficient on External Measure{" "}
              <strong>{districtAssessmentName}</strong> - Fall to Spring
            </h6>
            <BarGraph
              data={series}
              chartName={""}
              chartId={`districtReporting_proficientExternalMeasureDistrictFallToSpring_${grade}`}
              options={reporting.getChartOptions({
                height: categories.length * 55,
                shouldUseSecondaryLabel: true,
                categories,
                nOffset: -35 - 5 * maxNumberOfDigits,
                categoryOffset: -40 - 5 * maxNumberOfDigits,
                categoryOffsetY: -40
              })}
            />
          </div>
        );
      });
  };

  const renderSpringProficiencyByYearBySchool = wasSectionRenderedByContext => {
    const {
      districtSeriesByGrade,
      districtCategoriesByGrade,
      districtAssessmentName,
      stateSeriesByGrade,
      stateCategoriesByGrade,
      stateAssessmentName
    } = props.externalProficiencyDataForSchools.springByYearData;

    let wasAnyDistrictSpringByYearRendered = false;
    let wasAnyStateSpringByYearRendered = false;
    return (
      <div>
        {Object.entries(districtSeriesByGrade).length ? (
          Object.entries(districtSeriesByGrade)
            .sort(([a], [b]) => (normalizeGrade(a) < normalizeGrade(b) ? -1 : 1))
            .map(([grade, series], index) => {
              if (districtAssessmentName === "N/A") {
                if (Object.entries(districtSeriesByGrade).length - 1 === index && !wasAnyDistrictSpringByYearRendered) {
                  return (
                    <div
                      key={`districtSpringByYear_no_scores`}
                      className={wasAnyStateSpringByYearRendered ? "page-break-before" : ""}
                    >
                      <h6 className="text-center">District Percent Proficient on External Measure - Spring by Year</h6>
                      {renderNoScoresBanner({ isPrinting })}
                    </div>
                  );
                }
                return null;
              }
              if (!wasAnyDistrictSpringByYearRendered) {
                wasSectionRenderedByContext.springByYear = true;
                wasAnyDistrictSpringByYearRendered = true;
              }
              const maxNumberOfDigits = getNumberOfDigitsForSeries(series);
              const categories = districtCategoriesByGrade[grade];
              return (
                <div key={`districtSpringByYear_${grade}`} className="page-break-before">
                  <h6 className="text-center">
                    <strong>Grade {grade}</strong> - District Percent Proficient on External Measure{" "}
                    <strong>{districtAssessmentName}</strong> - Spring by Year
                  </h6>
                  <BarGraph
                    data={series}
                    chartName={""}
                    chartId={`districtReporting_proficientExternalMeasureDistrictSpringByYear_${grade}`}
                    options={reporting.getChartOptions({
                      height: categories.length * 80,
                      shouldUseSecondaryLabel: true,
                      categories,
                      nOffset: -35 - 5 * maxNumberOfDigits,
                      categoryOffset: -40 - 5 * maxNumberOfDigits,
                      categoryOffsetY: -40
                    })}
                  />
                </div>
              );
            })
        ) : (
          <div
            key={`districtSpringByYear_no_scores`}
            className={wasAnyStateSpringByYearRendered ? "page-break-before" : ""}
          >
            <h6 className="text-center">District Percent Proficient on External Measure - Spring by Year</h6>
            {renderNoScoresBanner({ isPrinting })}
          </div>
        )}
        {!isPrinting ? <hr className="hr-dark" /> : null}
        {Object.entries(stateSeriesByGrade).length ? (
          Object.entries(stateSeriesByGrade)
            .sort(([a], [b]) => (normalizeGrade(a) < normalizeGrade(b) ? -1 : 1))
            .map(([grade, series], index) => {
              if (stateAssessmentName === "N/A") {
                if (Object.entries(stateSeriesByGrade).length - 1 === index && !wasAnyStateSpringByYearRendered) {
                  return (
                    <div
                      key={`stateSpringByYear_no_scores`}
                      className={wasSectionRenderedByContext.fallToSpring ? "page-break-before" : ""}
                    >
                      <h6 className="text-center">State Percent Proficient on External Measure - Spring by Year</h6>
                      {renderNoScoresBanner({ isPrinting })}
                    </div>
                  );
                }
                return null;
              }
              if (!wasAnyStateSpringByYearRendered) {
                wasSectionRenderedByContext.springByYear = true;
                wasAnyStateSpringByYearRendered = true;
              }

              const maxNumberOfDigits = getNumberOfDigitsForSeries(series);
              const categories = stateCategoriesByGrade[grade];
              return (
                <div
                  key={`stateSpringByYear_${grade}`}
                  className={wasSectionRenderedByContext.fallToSpring ? "page-break-before" : ""}
                >
                  <h6 className="text-center">
                    <strong>Grade {grade}</strong> - State Percent Proficient on External Measure{" "}
                    <strong>{stateAssessmentName}</strong> - Spring by Year
                  </h6>
                  <BarGraph
                    data={series}
                    chartName={""}
                    chartId={`districtReporting_proficientExternalMeasureStateSpringByYear_${grade}`}
                    options={reporting.getChartOptions({
                      height: categories.length * 80,
                      shouldUseSecondaryLabel: true,
                      categories,
                      nOffset: -35 - 5 * maxNumberOfDigits,
                      categoryOffset: -40 - 5 * maxNumberOfDigits,
                      categoryOffsetY: -40
                    })}
                  />
                </div>
              );
            })
        ) : (
          <div
            key={`stateSpringByYear_no_scores`}
            className={wasSectionRenderedByContext.fallToSpring ? "page-break-before" : ""}
          >
            <h6 className="text-center">State Percent Proficient on External Measure - Spring by Year</h6>
            {renderNoScoresBanner({ isPrinting })}
          </div>
        )}
      </div>
    );
  };

  const renderSection6 = () => {
    const wasSectionRenderedByContext = {
      spring: false,
      fallToSpring: false,
      springByYear: false
    };
    return (
      <div id="district-reporting-external-growth" className="page-break-before scroll-margin-with-sticky-menu">
        <h5>
          <strong>Growth on External Measures</strong>
        </h5>
        {renderSpringProficiencyBySchool(wasSectionRenderedByContext)}
        {!isPrinting ? <hr className="hr-dark" /> : null}
        {renderFallToSpringProficiencyBySchool(wasSectionRenderedByContext)}
        {!isPrinting ? <hr className="hr-dark" /> : null}
        {renderSpringProficiencyByYearBySchool(wasSectionRenderedByContext)}
      </div>
    );
  };

  const navigateBack = () => {
    props.history.goBack();
  };

  return (
    <div>
      {isPrinting ? null : (
        <div className="sticky-menu container-fluid">
          <span className="position-absolute nav-button-left">
            <button className="btn btn-success" onClick={navigateBack}>
              <i className="fa fa-chevron-left" /> Go Back
            </button>
          </span>
          <span className="position-absolute nav-button-right">
            <button className="btn btn-success" onClick={printPage} disabled={isGeneratingPrintout}>
              <i className="fa fa-print" /> {isGeneratingPrintout ? "Preparing printout..." : "Print This Page"}
            </button>
          </span>
          <ul className="nav nav-pills middle-sub-nav d-flex justify-content-center">
            <li>
              <Link to={"#"} replace onClick={() => scroll("district-reporting-schools")}>
                Schools
              </Link>
            </li>
            {hasAnyActiveData ? (
              <React.Fragment>
                <li>
                  <Link to={"#"} replace onClick={() => scroll("district-reporting-screening-participation")}>
                    Screening Participation
                  </Link>
                </li>
                <li>
                  <Link to={"#"} replace onClick={() => scroll("district-reporting-classwide-interventions")}>
                    Classwide Interventions
                  </Link>
                </li>
                <li>
                  <Link to={"#"} replace onClick={() => scroll("district-reporting-individual-interventions")}>
                    Individual Interventions
                  </Link>
                </li>
                <li>
                  <Link to={"#"} replace onClick={() => scroll("district-reporting-growth")}>
                    Growth
                  </Link>
                </li>
                <li>
                  <Link to={"#"} replace onClick={() => scroll("district-reporting-external-growth")}>
                    External Growth
                  </Link>
                </li>
              </React.Fragment>
            ) : null}
          </ul>
        </div>
      )}
      <div className={isPrinting ? "" : "conFullScreen"}>
        <div className={`${isPrinting ? "" : "container district-reporting-screen"}`}>
          {renderTitle()}
          {renderSection1()}
          {!isPrinting ? <hr className="hr-dark" /> : null}
          {hasAnyActiveData ? (
            <React.Fragment>
              {renderSection2()}
              {!isPrinting ? <hr className="hr-dark" /> : null}
              {renderSection3()}
              {!isPrinting ? <hr className="hr-dark" /> : null}
              {renderSection4()}
              {!isPrinting ? <hr className="hr-dark" /> : null}
              {renderSection5()}
              {!isPrinting ? <hr className="hr-dark" /> : null}
              {renderSection6()}
            </React.Fragment>
          ) : (
            renderNoScoresBanner({ isPrinting })
          )}
        </div>
      </div>
    </div>
  );
};

DistrictReporting.propTypes = {
  orgName: PropTypes.string,
  orgid: PropTypes.string,
  schools: PropTypes.array,
  classwideRules: PropTypes.array,
  growthDataBySchoolYearByGrade: PropTypes.object,
  externalProficiencyDataForSchools: PropTypes.object,
  ruleAssessmentIdsByGrade: PropTypes.object,
  assessmentNameById: PropTypes.object,
  history: PropTypes.object,
  schoolYear: PropTypes.number,
  benchmarkPeriodsGroupId: PropTypes.string,
  bmPeriods: PropTypes.array
};

const DistrictReportingDataWrapper = props => {
  const [isLoading, setIsLoading] = useState(true);
  const [districtReportingDataSet, setDistrictReportingDataSet] = useState(null);
  const { orgId } = useContext(OrganizationContext);
  const { schoolYear } = useContext(SchoolYearContext);

  useEffect(() => {
    setIsLoading(true);
    if (orgId && schoolYear) {
      Meteor.call("getDistrictReportingDataSet", orgId, schoolYear, (err, resp) => {
        if (!err) {
          setDistrictReportingDataSet(resp);
        }
        setIsLoading(false);
      });
    }
  }, [orgId, schoolYear]);

  if (isLoading) {
    return <Loading message={loadingMessage} />;
  }
  const mergedProps = { ...props, ...(districtReportingDataSet || {}), isLoading, schoolYear, orgid: orgId };
  return <DistrictReporting {...mergedProps} />;
};

DistrictReportingDataWrapper.propTypes = {
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

const DistrictReportingDataWrapperWithRouter = withRouter(DistrictReportingDataWrapper);

export default DistrictReportingDataWrapperWithRouter;
