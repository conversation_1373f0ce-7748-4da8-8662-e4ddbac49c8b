import React, { useEffect, useRef } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";

import { isOnPrintPage } from "../../utilities";

/*
Data structure:
// Bar group
[
	// nth Bar In group
	{
		name: "" // Name of the bar tooltip,
		color: "",
		// Every data element should correspond by index to category
		data: [
			{
				y: 0 // Bar value,
				n: 0 // (optional) n= label triggered by options.useSecondaryLabel,
				isNA: true // Whether display N/A
				noData: 0 // Whether display custom message
			}, ...
		]
	}, ...
]
*/

/*
  Options:
    animation: boolean,
    categories: array of strings,
    categoryOffset: number,
    categoryOffsetY: number,
    customHeightOffset: number,
    dangerThreshold: number,
    height: number,
    legendLayout: string,
    isLegendEnabled: boolean,
    max: number,
    min: number,
    noDataMessage: string,
    nOffset: number,
    seriesAlternateCategoryLabel: string,
    shouldHidePercentInLabel: boolean,
    shouldUseEvent: boolean,
    shouldUseSecondaryLabel: boolean,
    subtitleText: string,
    subtitleOffset: number,
    xAxisTitle: string,
    yAxisTitle: string
 */

const getMasteryPoints = (that, options) => {
  const customPoints = [];
  const { points } = that.series[0];
  const { numberOfClasswideSkills } = options;
  points.forEach(point => {
    const width = 2;
    const height = point.pointWidth;
    const x = that.plotLeft + (that.plotBox.width / options.max) * numberOfClasswideSkills - width;
    const y = point.shapeArgs.x + that.plotTop;
    customPoints.push(
      that.renderer
        .rect(x, y, width, height)
        .attr({ fill: "red", zIndex: 3 })
        .add()
    );
  });
  return customPoints;
};

const getEventsToRender = options => {
  return {
    events: {
      load() {
        this.customPoints = [];
      },
      render() {
        // NOTE(fmazur) - remove all custom points before redrawing to avoid duplicates
        this.customPoints.forEach(point => {
          point.destroy();
        });
        this.customPoints = getMasteryPoints(this, options);
      }
    }
  };
};

const BarGraph = ({ chartName, chartId, data, options }) => {
  const chartRef = useRef(null);

  const updateChart = () => {
    const isPrinting = isOnPrintPage();
    const xAxisHeight = options.height;
    const heightOffset = options.customHeightOffset || (options.subtitleText?.length ? 160 : 120);
    chartRef.current = new Highcharts.Chart(chartId, {
      title: {
        text: chartName || ""
      },
      chart: {
        type: "bar",
        height: xAxisHeight + heightOffset,
        ...(options.shouldUseEvent ? getEventsToRender(options) : {})
      },
      accessibility: {
        enabled: false
      },
      subtitle: {
        text: options.subtitleText || "",
        style: {
          color: "#000"
        },
        verticalAlign: "bottom",
        y: options.subtitleOffset || 10
      },
      credits: {
        enabled: false
      },
      yAxis: {
        min: options.min || 0,
        max: options.max || 100,
        title: {
          // NOTE(fmazur) - avoid using default y/x
          text: options.yAxisTitle?.length > 2 ? options.yAxisTitle : ""
        },
        labels: {
          style: {
            fontSize: isPrinting ? "6px" : "9px",
            ...(isPrinting ? { color: "#000" } : {})
          },
          y: options.categoryOffsetY || 0
        },
        ...(options.tickInterval ? { tickInterval: options.tickInterval } : {})
      },
      legend: {
        layout: options.legendLayout || "horizontal",
        enabled: options.isLegendEnabled,
        floating: false,
        maxHeight: heightOffset,
        align: "center",
        verticalAlign: "top",
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      xAxis: {
        type: "category",
        height: xAxisHeight,
        labels: {
          autoRotationLimit: 0,
          style: {
            whiteSpace: "normal",
            width: "200px",
            fontSize: "10px",
            ...(isPrinting ? { color: "#000" } : {})
          },
          x: options.categoryOffset || 0
        },
        // Names of bar groups
        categories: options.categories
      },
      plotOptions: {
        bar: {
          maxPointWidth: 20,
          groupPadding: 0.07,
          pointPadding: 0.05
        },
        series: {
          animation: options.animation ?? true,
          dataLabels: [
            {
              inside: false,
              enabled: true,
              y: -1,
              allowOverlap: true,
              formatter() {
                if (this.point.isHS || this.point.noData || this.point.noScreeningData) {
                  return "";
                }
                if (this.point.isNA || this.point.y === undefined) {
                  return "N/A";
                }
                const { y } = this.point;
                const pointText = `${Number.isInteger(y) ? y : y.toFixed(1)}${
                  options.shouldHidePercentInLabel ? "" : "%"
                }`;
                if (options.dangerThreshold && y < options.dangerThreshold) {
                  if (isPrinting) {
                    return `<span style="text-decoration: underline">${pointText}</span>`;
                  }
                  return `<span style="color: red;">${pointText}</span>`;
                }
                return pointText;
              },
              rotation: 0,
              style: {
                fontSize: isPrinting ? "7px" : "9px"
              }
            },
            {
              overflow: "allow",
              align: "left",
              style: {
                textOutline: "0px"
              },
              color: isPrinting ? "#000" : "#797979",
              inside: true,
              enabled: true,
              crop: false,
              formatter() {
                if (this.point.isHS && this.point.series.userOptions.name === options.seriesAlternateCategoryLabel) {
                  return "No screening is necessary for high schools";
                }
                if (
                  !this.point.noData &&
                  this.point.noScreeningData &&
                  this.point.series.userOptions.name === options.seriesAlternateCategoryLabel
                ) {
                  return "No screening data";
                }
                if (this.point.noData && this.point.series.userOptions.name === options.seriesAlternateCategoryLabel) {
                  return options.noDataMessage || "No data";
                }
                return "";
              }
            },
            options.shouldUseSecondaryLabel
              ? {
                  overflow: "allow",
                  align: "left",
                  color: "#000",
                  inside: true,
                  enabled: true,
                  crop: false,
                  formatter() {
                    if (this.point.isNA) {
                      return "";
                    }

                    const { n } = this.point;
                    return n ? `(n=${n})` : "";
                  },
                  style: {
                    fontSize: isPrinting ? "7px" : "9px",
                    ...(isPrinting ? { textOutline: "0px" } : {})
                  },
                  x: options.nOffset
                }
              : {}
          ]
        }
      },
      tooltip: {
        formatter() {
          const { series, isNA, isHS, noData, noScreeningData, y } = this.point;
          if (noData || noScreeningData) {
            return false;
          }
          return `<span style="font-size:11px">${series.userOptions.name}</span><br><b>${
            isNA || isHS
              ? "N/A"
              : `${Number.isInteger(y) ? y : y.toFixed(2)}${options.shouldHidePercentInLabel ? "" : "%"}`
          }</b>`;
        }
      },
      series: data
    });
  };

  useEffect(() => {
    updateChart();
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [chartName, chartId, data, options]);

  return <div id={chartId} />;
};

BarGraph.propTypes = {
  chartName: PropTypes.string.isRequired,
  chartId: PropTypes.string,
  options: PropTypes.object,
  data: PropTypes.array,
  shouldUseSecondaryLabel: PropTypes.bool
};

BarGraph.defaultProps = {
  shouldUseSecondaryLabel: false
};

export default BarGraph;
