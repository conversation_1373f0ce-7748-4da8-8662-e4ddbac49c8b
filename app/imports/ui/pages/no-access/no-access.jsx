import React from "react";
import { Link, useHistory } from "react-router-dom";

export default function NoAccess() {
  const history = useHistory();

  function handleBackButton() {
    history.go(-2);
  }

  return (
    <div>
      <div className="information-box mt-1 text-center">
        <p>You don&apos;t have access to this page.</p>
        <div className="d-flex gap-1 justify-content-center">
          <button onClick={handleBackButton} className="btn btn-outline-secondary">
            Back
          </button>
          <Link to="/" className="btn btn-outline-primary">
            Return to Home Page
          </Link>
        </div>
      </div>
    </div>
  );
}
