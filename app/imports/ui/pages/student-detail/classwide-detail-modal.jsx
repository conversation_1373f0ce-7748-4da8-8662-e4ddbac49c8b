import React, { useEffect, useState, Component } from "react";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import { <PERSON>dal, ModalBody, ModalHeader, <PERSON>ton } from "react-bootstrap";
import Loading from "../../components/loading";
import DetailTable from "./skillProgress";

class ClasswideDetailModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  renderModalHeaderTitle = () => {
    if (this.props.componentContext === "groupDetail") {
      return "Student Skill Progress";
    }
    if (this.props.componentContext === "gradeDetail") {
      return "Skill Progress by Class";
    }
    return "Detail Modal";
  };

  render() {
    const { showModal } = this.props;
    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName="modal-95"
        backdrop={true}
        data-testid="classwide-detail-modal"
      >
        <ModalHeader className="align-content-center justify-content-center">
          <div className="text-center">
            <h3>{this.renderModalHeaderTitle()}</h3>
          </div>
        </ModalHeader>
        <ModalBody>
          {Object.keys(this.props.rowData || {}).length ? (
            <DetailTable
              rowData={this.props.rowData}
              summaryAll={this.props.summaryAll}
              componentContext={this.props.componentContext}
            />
          ) : (
            <div className="alert alert-info text-center">No data found</div>
          )}
        </ModalBody>

        <div className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
            Close
          </Button>
        </div>
      </Modal>
    );
  }
}

ClasswideDetailModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  rowData: PropTypes.object,
  summaryAll: PropTypes.object,
  componentContext: PropTypes.string
};

export default function ClasswideDetailModalDataWrapper(params) {
  const [loading, setLoading] = useState(true);
  const [resp, setResp] = useState({});

  useEffect(() => {
    setLoading(true);
    if (params.componentContext === "groupDetail") {
      Meteor.call(
        "getIndividualStudentDetailData",
        { studentGroupId: params.studentGroupId, shouldIncludeAllSkills: false },
        (err, r) => {
          if (!err) {
            setResp(r || {});
          }
          setLoading(false);
        }
      );
    } else if (params.componentContext === "gradeDetail") {
      Meteor.call(
        "getGradeDetailData",
        { siteId: params.siteId, grade: params.grade, schoolYear: params.schoolYear },
        (err, r) => {
          if (!err) {
            setResp(r || {});
          }
          setLoading(false);
        }
      );
    }
  }, []);

  if (loading) {
    return (
      <span className="btn btn-default">
        <Loading inline={true} message="Preparing Data..." />
      </span>
    );
  }

  return <ClasswideDetailModal {...params} rowData={resp.rowData} summaryAll={resp.summaryAll} />;
}
