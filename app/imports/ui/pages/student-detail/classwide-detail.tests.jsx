import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import React from "react";
import { shallow } from "enzyme";
import { PureClasswideDetail } from "./classwide-detail.jsx";
import ClasswideInterventionProgress from "../../components/student-detail/classwide-intervention-progress.jsx";

if (Meteor.isClient) {
  describe("Classwide-Detail UI", () => {
    describe("Render", () => {
      let PureClasswideDetailComponent;
      beforeEach(() => {
        PureClasswideDetailComponent = shallow(<PureClasswideDetail studentGroup={{}} students={[]} />);
      });

      it("renders the primary component", () => {
        // Verify that the method does what we expected
        assert.isDefined(PureClasswideDetailComponent, "ClasswideDetailComponent did not render");
      });
    });

    describe("ClasswideInterventionProgress Component", () => {
      const studentGroup = {
        history: [
          {
            type: "classwide"
          }
        ]
      };

      it("is rendered as a child component", () => {
        const PureClasswideDetailComponent = shallow(<PureClasswideDetail studentGroup={studentGroup} students={[]} />);
        const ClasswideInterventionProgressComponent = PureClasswideDetailComponent.find(ClasswideInterventionProgress);
        assert.isOk(ClasswideInterventionProgressComponent.length, "");
      });
    });
  });
}
