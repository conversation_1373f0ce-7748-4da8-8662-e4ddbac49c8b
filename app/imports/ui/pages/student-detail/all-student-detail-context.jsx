import React, { useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { groupBy } from "lodash";
import { useTracker } from "meteor/react-meteor-data";
import { areSubscriptionsLoading } from "../../utilities";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { getPrintOptionsFrom } from "./student-detail";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

function AllStudentDetailContext(props) {
  const { studentGroupId, studentGroup, studentsInStudentGroup } = useContext(StudentGroupContext);
  const { benchmarkWindows } = useContext(SiteContext);
  const { grade } = studentGroup || {};
  const { assessments, screeningAssignments } = useContext(StaticDataContext);
  const { schoolYear } = useContext(SchoolYearContext);

  const trackerData = useTracker(() => {
    if (!grade) {
      return { loading: true };
    }
    let allClasswideScores = [];
    let classwideBenchmarkScores = [];
    let classwideInterventionScores = [];
    let individualInterventionScoresByStudentId = {};
    let otherStudentGroupsByStudentId = {};
    let otherStudentAssessmentResults = [];
    let studentEnrollments = [];
    let studentIdsInGroup = [];
    let studentEnrollmentsByStudentId = {};

    const assessmentResultsSub = Meteor.subscribe(
      "AssessmentResultsForStudentGroup",
      studentGroupId,
      studentGroup?.orgid
    );
    const individualRulesHandle = Meteor.subscribe("Rules:IndividualRootRulesByGrade", grade);
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroupId);
    const enrollmentsHandle = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);
    const studentGroupsSub = Meteor.subscribe(
      "StudentGroupsAssociatedWithUser",
      schoolYear,
      studentGroup?.siteId,
      studentGroup?.orgid
    ); // NOTE(fmazur) - for finding other groups

    const loading = areSubscriptionsLoading(
      individualRulesHandle,
      rulesSub,
      enrollmentsHandle,
      studentGroupsSub,
      assessmentResultsSub
    );

    if (!loading) {
      studentIdsInGroup = studentsInStudentGroup.map(s => s._id) || [];
      allClasswideScores = (studentGroup.history || []).filter(sgh => !!sgh.whenEnded);
      const assessmentResultIdsOfAllClasswideScores = allClasswideScores.map(
        ({ assessmentResultId }) => assessmentResultId
      );
      const assessmentResults = AssessmentResults.find({
        _id: { $nin: assessmentResultIdsOfAllClasswideScores },
        status: "COMPLETED",
        type: { $in: ["classwide", "benchmark"] }
      }).fetch();
      otherStudentAssessmentResults = reformatAssessmentResults(assessmentResults);
      studentEnrollments = StudentGroupEnrollments.find().fetch();
      classwideInterventionScores = (studentGroup.history || []).filter(
        sgh => sgh.type === "classwide" && sgh.whenEnded
      );
      classwideBenchmarkScores = (studentGroup.history || []).filter(sgh => sgh.type === "benchmark" && sgh.whenEnded);
      individualInterventionScoresByStudentId = Object.values(studentsInStudentGroup).reduce((acc, student) => {
        acc[student._id] = (student.history || []).filter(sh => !!sh.whenEnded);
        return acc;
      }, {});
      studentEnrollmentsByStudentId = groupBy(studentEnrollments, "studentId");
      otherStudentGroupsByStudentId = Object.entries(studentEnrollmentsByStudentId).reduce(
        (acc, [studentId, enrollments]) => {
          acc[studentId] = StudentGroups.find(
            { _id: { $in: enrollments.map(enr => enr.studentGroupId) } },
            { fields: { name: 1 } }
          ).fetch();
          return acc;
        },
        {}
      );
    }

    const {
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults
    } = getPrintOptionsFrom(props);

    return {
      ...props,
      loading,
      allClasswideScores,
      benchmarkWindows,
      classwideBenchmarkScores,
      classwideInterventionScores,
      individualInterventionScoresByStudentId,
      otherResults: otherStudentAssessmentResults,
      otherStudentGroupsByStudentId,
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults,
      schoolYear,
      studentEnrollmentsByStudentId,
      studentGroup,
      studentIdsInGroup,
      studentsInGroup: studentsInStudentGroup,
      selectedSkillAssessmentId: props.selectedSkillAssessmentId,
      selectedSkillIndex: props.selectedSkillIndex,
      screeningAssignments,
      assessments
    };
  }, [
    props.selectedSkillIndex,
    props.selectedSkillIndex,
    studentGroupId,
    studentGroup,
    studentsInStudentGroup,
    benchmarkWindows,
    grade,
    schoolYear
  ]);

  if (!props.children) {
    return null;
  }

  // Handle single child or multiple children
  if (React.isValidElement(props.children)) {
    return React.cloneElement(props.children, trackerData);
  }

  // Handle multiple children
  return React.Children.map(props.children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, trackerData);
    }
    return child;
  });
}

AllStudentDetailContext.propTypes = {
  loading: PropTypes.bool,
  children: PropTypes.node,
  selectedSkillAssessmentId: PropTypes.string,
  selectedSkillIndex: PropTypes.number
};

export default function allStudentDetailContainerWithContext(WrappedComponent) {
  return function AllStudentDetailContainer(props) {
    return <AllStudentDetailContext {...props}>{WrappedComponent}</AllStudentDetailContext>;
  };
}
