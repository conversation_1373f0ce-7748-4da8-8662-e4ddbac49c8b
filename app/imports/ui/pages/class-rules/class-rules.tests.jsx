import { Meteor } from "meteor/meteor";
import React from "react";
import { cleanup, fireEvent, render, within } from "@testing-library/react";
import "@testing-library/jest-dom";
import { PureClassRules as ClassRules } from "./class-rules";
import {
  getMultipleAssessmentsClassRulesData,
  getMultipleAssessmentsClassRulesDataForGrades,
  getSingleAssessmentClassRulesData
} from "../../../test-helpers/data/class-rules-test-helpers";

jest.mock("../data-admin/upload/file-upload-utils", () => ({
  ...jest.requireActual("../data-admin/upload/file-upload-utils"),
  getCSV: jest.fn()
}));

const fileUploadUtils = require("../data-admin/upload/file-upload-utils");

describe("ClassRules", () => {
  let meteorSpy;
  beforeEach(() => {
    meteorSpy = jest.spyOn(Meteor, "call");
  });
  afterEach(() => {
    cleanup();
    meteorSpy.mockRestore();
  });
  describe("component", () => {
    it("should show loader when getting data", () => {
      const { getByText } = render(<ClassRules loading={true} />);

      expect(getByText("Getting Rules")).toBeVisible();
    });

    it("should display classwide tree with rule names and targets data for all available grades", () => {
      const {
        grade,
        grades,
        assessmentId,
        rules,
        assessmentName,
        instructionalTarget,
        masteryTarget,
        assessments
      } = getSingleAssessmentClassRulesData();

      const { getByTestId } = render(<ClassRules grades={grades} rules={rules} assessments={assessments} />);
      getByTestId("editClassRules").click();
      getByTestId("confirm-modal-btn").click();
      const indexNumber = 1;
      const { getByText, getByDisplayValue } = within(getByTestId(`${indexNumber}_${grade}_${assessmentId}`));
      expect(getByText(assessmentName)).toBeVisible();
      expect(getByDisplayValue(instructionalTarget.toString())).toBeVisible(); // CONVERTING TO STRING IS NECESSARY TO PERFORM A LOOKUP IN DOM
      expect(getByDisplayValue(masteryTarget.toString())).toBeVisible();
    });

    it("should allow removing a classwide rule from grade's classwide tree", () => {
      const { grades, assessmentId, rules, assessments, ruleId } = getSingleAssessmentClassRulesData();
      const { getByTestId } = render(<ClassRules grades={grades} rules={rules} assessments={assessments} />);
      getByTestId("editClassRules").click();
      getByTestId("confirm-modal-btn").click();
      getByTestId("removeRule").click();

      expect(meteorSpy).toHaveBeenCalledTimes(1);
      expect(meteorSpy).toHaveBeenCalledWith("Rules:removeSkill", ruleId, assessmentId, expect.any(Function));
    });

    it("should not allow manipulating rules position in classwide tree when there is only a single rule", () => {
      const { grades, rules, assessments } = getSingleAssessmentClassRulesData();

      const { queryByTestId } = render(<ClassRules grades={grades} rules={rules} assessments={assessments} />);

      expect(queryByTestId("moveRuleUp")).toBeNull();
      expect(queryByTestId("moveRuleDown")).toBeNull();
    });

    it("should allow manipulating rules position in classwide tree when there is more than one rule", () => {
      const { grade, grades, middleAssessmentId, ruleId, rules, assessments } = getMultipleAssessmentsClassRulesData();
      const { getByTestId: getByTestIdParent, getAllByTestId } = render(
        <ClassRules grades={grades} rules={rules} assessments={assessments} />
      );
      getByTestIdParent("editClassRules").click();
      getByTestIdParent("confirm-modal-btn").click();
      const middleAssessmentIndex = 2;
      const { getByTestId } = within(getByTestIdParent(`${middleAssessmentIndex}_${grade}_${middleAssessmentId}`));
      getByTestId("moveRuleUp").click();
      expect(meteorSpy).toHaveBeenLastCalledWith("Rules:moveSkillUp", ruleId, middleAssessmentId, expect.any(Function));

      getByTestId("moveRuleDown").click();
      expect(meteorSpy).toHaveBeenLastCalledWith(
        "Rules:moveSkillDown",
        ruleId,
        middleAssessmentId,
        expect.any(Function)
      );

      // Verifying the total number of expected mobility buttons
      // there shouldn't be the Move Up button on top Rule and there shouldn't be the Move Down button on bottom Rule
      expect(getAllByTestId("moveRuleUp")).toHaveLength(2);
      expect(getAllByTestId("moveRuleDown")).toHaveLength(2);
      expect(getAllByTestId("removeRule")).toHaveLength(3);
    });

    it("should allow adding an assessment to grade's classwide rules", async () => {
      const { grade, grades, assessments, ruleId, rules } = getSingleAssessmentClassRulesData();
      const newAssessmentId = "newAssessmentId";
      const newAssessment = {
        _id: newAssessmentId,
        name: "New Assessment",
        associatedGrades: [grade],
        monitorAssessmentMeasure: "124"
      };
      const { getByPlaceholderText, getByText, getByTestId } = render(
        <ClassRules grades={grades} rules={rules} assessments={[...assessments, newAssessment]} />
      );
      getByTestId("editClassRules").click();
      getByTestId("confirm-modal-btn").click();

      fireEvent.focus(getByPlaceholderText(`Click to add assessment`));

      getByText("AM 124 - New Assessment").click();
      expect(meteorSpy).toHaveBeenLastCalledWith("Rules:addSkill", ruleId, { assessmentId: newAssessmentId });
    });

    it("should allow modifying each rule's assessment targets", () => {
      const { grade, grades, assessmentId, rules, assessments } = getSingleAssessmentClassRulesData();
      const { getByTestId: parentGetByTestId } = render(
        <ClassRules grades={grades} rules={rules} assessments={assessments} />
      );
      parentGetByTestId("editClassRules").click();
      parentGetByTestId("confirm-modal-btn").click();
      const indexNumber = 1;
      const assessmentTestId = `${indexNumber}_${grade}_${assessmentId}`;
      const instructionalTarget = 33;
      const masteryTarget = 66;
      const { getByTestId, getByText } = within(parentGetByTestId(assessmentTestId));
      fireEvent.change(getByTestId(`${assessmentTestId}_Instructional`), { target: { value: instructionalTarget } });
      fireEvent.change(getByTestId(`${assessmentTestId}_Mastery`), { target: { value: masteryTarget } });

      getByText("Save").click();

      expect(meteorSpy).toHaveBeenLastCalledWith(
        "Assessments:updateOrCreateClasswideTargets",
        {
          instructionalTarget,
          masteryTarget,
          assessmentId,
          gradeId: grade
        },
        expect.any(Function) // callback
      );
    });

    it("should keep the correct targets when assessments change their positions in rules", () => {
      // THIS CHECKS WHETHER LIST ELEMENTS HAVE A UNIQUE KEY WHICH MAY CAUSE ISSUES WHEN DONE IMPROPERLY
      const {
        grade,
        grades,
        middleAssessmentId,
        topAssessmentId,
        bottomAssessmentId,
        ruleId,
        rules,
        assessments
      } = getMultipleAssessmentsClassRulesData();
      const { getByTestId, rerender } = render(<ClassRules grades={grades} rules={rules} assessments={assessments} />);
      getByTestId("editClassRules").click();
      getByTestId("confirm-modal-btn").click();
      const middleAssessmentInitialIndex = 2;
      const middleAssessmentInstructionalTargetValue = getByTestId(
        `${middleAssessmentInitialIndex}_${grade}_${middleAssessmentId}_Instructional`
      ).value;
      // updated rules with middle assessment moved to the top
      const rulesWithModifiedOrder = [
        {
          _id: ruleId,
          grade,
          skills: [
            {
              assessmentId: middleAssessmentId,
              interventions: []
            },
            {
              assessmentId: topAssessmentId,
              interventions: []
            },
            {
              assessmentId: bottomAssessmentId,
              interventions: []
            }
          ]
        }
      ];

      rerender(<ClassRules grades={grades} rules={rulesWithModifiedOrder} assessments={assessments} />);

      // THE VALUE SHOULD NOT BE CHANGED JUST BECAUSE ASSESSMENT POSITION IN RULE CHANGED
      const middleAssessmentUpdatedIndex = 1;
      expect(getByTestId(`${middleAssessmentUpdatedIndex}_${grade}_${middleAssessmentId}_Instructional`).value).toEqual(
        middleAssessmentInstructionalTargetValue
      );
    });

    it("should generate valid object for CSV", () => {
      const { grades, rules, assessments } = getMultipleAssessmentsClassRulesDataForGrades([
        { grade: "K", am: 31 },
        { grade: "5", am: 11 },
        { grade: "HS", am: 22 }
      ]);

      const { getByTestId } = render(<ClassRules grades={grades} rules={rules} assessments={assessments} />);
      getByTestId("exportTargetsCSV").click();
      expect(fileUploadUtils.getCSV).toHaveBeenCalledTimes(1);
      expect(fileUploadUtils.getCSV).toHaveBeenCalledWith([
        {
          "AM #": "34",
          "Assessment Name": "Count",
          Grade: "K",
          "Instructional Target": 1,
          "Mastery Target": 2,
          "Order within grade": 1
        },
        {
          "AM #": "32",
          "Assessment Name": "Circle",
          Grade: "K",
          "Instructional Target": 3,
          "Mastery Target": 4,
          "Order within grade": 2
        },
        {
          "AM #": "33",
          "Assessment Name": "Write",
          Grade: "K",
          "Instructional Target": 5,
          "Mastery Target": 6,
          "Order within grade": 3
        },
        {
          "AM #": "34",
          "Assessment Name": "Count",
          Grade: "5",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 1
        },
        {
          "AM #": "32",
          "Assessment Name": "Circle",
          Grade: "5",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 2
        },
        {
          "AM #": "33",
          "Assessment Name": "Write",
          Grade: "5",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 3
        },
        {
          "AM #": "34",
          "Assessment Name": "Count",
          Grade: "HS",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 1
        },
        {
          "AM #": "32",
          "Assessment Name": "Circle",
          Grade: "HS",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 2
        },
        {
          "AM #": "33",
          "Assessment Name": "Write",
          Grade: "HS",
          "Instructional Target": 0,
          "Mastery Target": 0,
          "Order within grade": 3
        }
      ]);
    });
  });
});
