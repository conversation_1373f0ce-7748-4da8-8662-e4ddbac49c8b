import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import get from "lodash/get";
import { <PERSON><PERSON> } from "react-bootstrap";

import PageHeader from "../../components/page-header.jsx";
import { Rules } from "/imports/api/rules/rules";
import { Grades } from "/imports/api/grades/grades";
import { Assessments } from "/imports/api/assessments/assessments";
import { Loading } from "../../components/loading.jsx";
import { ClassRuleRow } from "./class-rule-row";
import { AddAssessmentInput } from "./add-assessment-input";
import { getCSV } from "../data-admin/upload/file-upload-utils";
import { download } from "../../utilities";
import ConfirmModal from "../data-admin/confirm-modal";

function isLastRule(skillLength, index) {
  return skillLength > 0 && skillLength - 1 === index;
}

function isFirstSkill(skillLength, index) {
  return skillLength > 0 && index === 0;
}

export class ClassRules extends Component {
  state = {
    isInEditMode: false,
    shouldDisplayModal: false
  };

  getAssessmentData(id, grade) {
    const assessment = this.getAssessmentById(id);
    const allTargets = get(assessment, "strands[0].scores[0].targets", []);
    const gradeTargets = allTargets.filter(t => t.grade === grade);
    let classwideTargets = gradeTargets.find(gt => gt.assessmentType === "classwide");
    if (!classwideTargets) {
      classwideTargets = gradeTargets.find(gt => !gt.assessmentType);
    }
    return {
      name: get(assessment, "name", "Name not found"),
      am: get(assessment, "monitorAssessmentMeasure", "N/A"),
      instructionalTarget: get(classwideTargets, "periods[0].values[0]", 0),
      masteryTarget: get(classwideTargets, "periods[0].values[1]", 0)
    };
  }

  getAssessmentById(id) {
    return this.props.assessments.find(ass => ass._id === id);
  }

  moveSkillUp = event => {
    const { assessmentId, ruleId } = event.target.dataset;
    Meteor.call("Rules:moveSkillUp", ruleId, assessmentId, (err, resp) => {
      if (!err && resp) {
        Alert.success("Classwide skill order updated!", { timeout: 2000 });
      }
    });
  };

  moveSkillDown = event => {
    const { assessmentId, ruleId } = event.target.dataset;
    Meteor.call("Rules:moveSkillDown", ruleId, assessmentId, (err, resp) => {
      if (!err && resp) {
        Alert.success("Classwide skill order updated!", { timeout: 2000 });
      }
    });
  };

  removeRule = event => {
    const { ruleId, assessmentId } = event.target.dataset;
    Meteor.call("Rules:removeSkill", ruleId, assessmentId, (err, resp) => {
      if (!err && resp) {
        Alert.success("Classwide skill removed!", { timeout: 2000 });
      }
    });
  };

  updateClasswideTargets = ({ assessmentId, gradeId, instructionalTarget, masteryTarget }, callback) => {
    Meteor.call(
      "Assessments:updateOrCreateClasswideTargets",
      { assessmentId, gradeId, instructionalTarget, masteryTarget },
      err => {
        if (err) {
          Alert.error(err.reason || "There was a problem updating target", {
            timeout: 5000
          });
        } else {
          Alert.success("Classwide target updated!", {
            timeout: 3000
          });
        }
        callback();
      }
    );
  };

  exportTargetsCSV = () => {
    const data = [];
    this.props.grades.forEach(
      grade =>
        this.props.rules &&
        this.props.rules
          .filter(rule => rule.grade === grade._id)
          .forEach(rule => {
            rule.skills.forEach((skill, index) => {
              const { name, am, instructionalTarget, masteryTarget } = this.getAssessmentData(
                skill.assessmentId,
                grade._id
              );
              data.push({
                Grade: grade.display,
                "Order within grade": index + 1,
                "AM #": am,
                "Assessment Name": name,
                "Instructional Target": instructionalTarget,
                "Mastery Target": masteryTarget
              });
            });
          })
    );

    const hrefData = `data:application/octet-stream,${getCSV(data)}`;
    download({
      filename: `classRulesTargetsData_${new Date().toISOString().slice(0, 10)}.csv`,
      hrefData
    });
  };

  updateSkillVideos = () => {
    Meteor.call("Assessments:updateAvailableVideoList", err => {
      if (!err) {
        Alert.success(`Successfully updated Video Skill List`);
      } else {
        Alert.error(err?.message || `There was an issue when updating Video Skill List`);
      }
    });
  };

  enableEditMode = () => {
    this.setState({
      shouldDisplayModal: false,
      isInEditMode: true
    });
  };

  showModal = () => {
    if (this.state.isInEditMode) {
      this.setState({ isInEditMode: false });
    } else {
      this.setState({ shouldDisplayModal: true });
    }
  };

  closeModal = () => {
    this.setState({ shouldDisplayModal: false });
  };

  render() {
    if (this.props.loading) {
      return <Loading message="Getting Rules" />;
    }
    const { assessments, rules } = this.props;
    return (
      <div className="conFullScreen">
        <div className="relativeWrapper">
          <div className="buttonInPageHeader d-flex gap-1">
            <Button
              className="btn btn-primary btn-success"
              name="updateSkillVideos"
              data-testid="updateSkillVideos"
              onClick={this.updateSkillVideos}
            >
              Update Skill Videos
            </Button>
            <Button
              className="btn btn-primary btn-success"
              name="exportTargetsCSV"
              data-testid="exportTargetsCSV"
              onClick={this.exportTargetsCSV}
            >
              Export Targets
            </Button>
          </div>
          <PageHeader title={"Class Rules"} description={"Setup the class wide rules."} />
        </div>
        <div className="container">
          <div className="card-box">
            <div className="d-flex flex-row">
              <div className="col-md-1">Grade</div>
              <div>
                <Button
                  className="btn btn-primary btn-success"
                  name="editClassRules"
                  data-testid="editClassRules"
                  onClick={this.showModal}
                >
                  {this.state.isInEditMode ? "Cancel Edit Mode" : "Edit Class Rules"}
                </Button>
              </div>
            </div>
            {this.props.grades.map(grade => {
              return (
                <div key={grade._id} className="row">
                  <div>
                    <hr />
                  </div>
                  <div className="col-md-1">{grade.display}</div>
                  <div key={`${grade._id}`} className="col-md-11">
                    {this.state.isInEditMode ? (
                      <AddAssessmentInput grade={grade} assessments={assessments} rules={rules} />
                    ) : null}
                    <div className="row class-rules-container-header">
                      <div className="col-md-1">Order</div>
                      <div className="col-md-1">AM Number</div>
                      <div className="col-md-5">Assessment Name</div>
                      <div className="col-md-1">Instructional Target</div>
                      <div className="col-md-1">Mastery Target</div>
                      <div className="col-md-1" />
                    </div>
                    {rules?.map(rule => {
                      if (rule.grade !== grade._id) {
                        return null;
                      }
                      return rule.skills.map((skill, index) => {
                        const { name, am, instructionalTarget, masteryTarget } = this.getAssessmentData(
                          skill.assessmentId,
                          grade._id
                        );
                        const isLast = isLastRule(rule.skills.length, index);
                        const isFirst = isFirstSkill(rule.skills.length, index);
                        return (
                          <ClassRuleRow
                            key={`${rule._id}_${grade._id}_${index}_${skill.assessmentId}`}
                            assessmentId={skill.assessmentId}
                            gradeId={grade._id}
                            indexNumber={index + 1}
                            am={am}
                            name={name}
                            instructionalTarget={instructionalTarget}
                            masteryTarget={masteryTarget}
                            ruleId={rule._id}
                            isLast={isLast}
                            isFirst={isFirst}
                            removeRule={this.removeRule}
                            moveSkillDown={this.moveSkillDown}
                            moveSkillUp={this.moveSkillUp}
                            updateClasswideTargets={this.updateClasswideTargets}
                            isInEditMode={this.state.isInEditMode}
                          />
                        );
                      });
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <ConfirmModal
          showModal={this.state.shouldDisplayModal}
          bodyText={
            <div className="text-danger">
              Modifying Classwide rules during school year will break student group history!
            </div>
          }
          confirmAction={this.enableEditMode}
          onCloseModal={this.closeModal}
        />
      </div>
    );
  }
}

ClassRules.propTypes = {
  loading: PropTypes.bool,
  rules: PropTypes.array,
  grades: PropTypes.array,
  assessments: PropTypes.array
};

export default withTracker(() => {
  const crHandle = Meteor.subscribe("ClasswideRules");
  const gHandle = Meteor.subscribe("Grades");
  const aHandle = Meteor.subscribe("Assessments");
  const loading = !crHandle.ready() || !gHandle.ready() || !aHandle.ready();
  let rules = [];
  let grades = [];
  let assessments = [];
  if (!loading) {
    rules = Rules.find().fetch();
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    assessments = Assessments.find({ monitorAssessmentMeasure: { $exists: true } }, { sort: { name: 1 } }).fetch();
  }
  return { loading, rules, grades, assessments };
})(ClassRules);

export { ClassRules as PureClassRules };
