import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import Loading from "../../components/loading";

export class ClassRuleRow extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      initialInstructional: parseInt(props.instructionalTarget),
      initialMastery: parseInt(props.masteryTarget),
      instructional: null,
      mastery: null,
      isUpdating: false
    };
  }

  saveTargets = event => {
    event.preventDefault();
    const instructionalTarget = parseInt(this.state.instructional || this.state.initialInstructional);
    const masteryTarget = parseInt(this.state.mastery || this.state.initialMastery);
    const { assessmentId, gradeId } = this.props;
    this.setState({
      isUpdating: true
    });
    this.props.updateClasswideTargets(
      {
        instructionalTarget,
        masteryTarget,
        assessmentId,
        gradeId
      },
      () => {
        this.setState({
          initialInstructional: instructionalTarget,
          initialMastery: masteryTarget,
          isUpdating: false
        });
      }
    );
  };

  handleInputChange = event => {
    const newValue = event.target.value;
    this.setState({ [event.target.name]: parseInt(newValue) });
  };

  renderSaveButton = () => {
    if (!this.props.isInEditMode) {
      return null;
    }

    if (this.state.isUpdating) {
      return <Loading inline={true} />;
    }
    const { initialInstructional, initialMastery, instructional, mastery } = this.state;

    if (
      (instructional !== null && initialInstructional !== instructional && mastery === null) ||
      (mastery !== null && initialMastery !== mastery && instructional === null) ||
      (instructional !== null && initialInstructional !== instructional) ||
      (mastery !== null && initialMastery !== mastery)
    ) {
      return (
        <button className="btn btn-success btn-xs" onClick={this.saveTargets}>
          Save
        </button>
      );
    }
    return null;
  };

  render() {
    const {
      assessmentId,
      gradeId,
      indexNumber,
      am,
      name,
      removeRule,
      ruleId,
      isInEditMode,
      instructionalTarget,
      masteryTarget,
      isFirst,
      isLast
    } = this.props;
    const testId = `${indexNumber}_${gradeId}_${assessmentId}`;

    return (
      <div className="class-rules-container" data-testid={testId}>
        <div className="row">
          <div className="col-md-1">{indexNumber}.</div>
          <div className="col-md-1">{am}</div>
          <div className="col-md-5 class-rules-assessment-name">{name}</div>
          <div className="col-md-1">
            {isInEditMode ? (
              <input
                type="number"
                min="0"
                max="300"
                className="form-control"
                name="instructional"
                value={this.state.instructional === null ? instructionalTarget : this.state.instructional}
                onChange={this.handleInputChange}
                data-testid={`${testId}_Instructional`}
              />
            ) : (
              instructionalTarget
            )}
          </div>
          <div className="col-md-1">
            {isInEditMode ? (
              <input
                type="number"
                min="0"
                max="300"
                className="form-control"
                name="mastery"
                value={this.state.mastery === null ? masteryTarget : this.state.mastery}
                onChange={this.handleInputChange}
                data-testid={`${testId}_Mastery`}
              />
            ) : (
              masteryTarget
            )}
          </div>
          <div className="col-md-1">{this.renderSaveButton()}</div>
          <div className="col-md-2">
            {isInEditMode ? (
              <React.Fragment>
                <i
                  className="class-rule-icon fa fa-trash text-danger"
                  aria-hidden="true"
                  onClick={removeRule}
                  data-rule-id={ruleId}
                  data-assessment-id={assessmentId}
                  data-testid="removeRule"
                />
                {isLast ? null : (
                  <i
                    className="class-rule-icon fa fa-arrow-down"
                    aria-hidden="true"
                    onClick={this.props.moveSkillDown}
                    data-rule-id={ruleId}
                    data-assessment-id={assessmentId}
                    data-testid="moveRuleDown"
                  />
                )}
                {isFirst ? null : (
                  <i
                    className="class-rule-icon fa fa-arrow-up"
                    aria-hidden="true"
                    onClick={this.props.moveSkillUp}
                    data-rule-id={ruleId}
                    data-assessment-id={assessmentId}
                    data-testid="moveRuleUp"
                  />
                )}
              </React.Fragment>
            ) : null}
          </div>
        </div>
      </div>
    );
  }
}

ClassRuleRow.propTypes = {
  assessmentId: PropTypes.string,
  gradeId: PropTypes.string,
  indexNumber: PropTypes.number,
  am: PropTypes.string,
  name: PropTypes.string,
  instructionalTarget: PropTypes.number,
  masteryTarget: PropTypes.number,
  ruleId: PropTypes.string,
  isLast: PropTypes.bool,
  isFirst: PropTypes.any,
  removeRule: PropTypes.func,
  moveSkillDown: PropTypes.func,
  moveSkillUp: PropTypes.func,
  updateClasswideTargets: PropTypes.func,
  isInEditMode: PropTypes.bool
};
