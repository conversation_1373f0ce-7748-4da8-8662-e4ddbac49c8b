import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { Link } from "react-router-dom";
import { Loading } from "../../components/loading";
import { download } from "../../utilities";

export default class GetAssessmentPrintout extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fetchingPrintMaterials: false,
      printMaterialsStatus: "",
      printMaterials: ""
    };
  }

  handlePrintAssessments = () => {
    this.setState({ printMaterialsStatus: "LOADING" });
    const monitorAssessmentMeasures = [this.props.assessment.monitorAssessmentMeasure];
    console.assert(!monitorAssessmentMeasures.some(v => typeof v === "undefined"));

    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: "SCREENING",
        assessmentIds: [this.props.assessment._id],
        assessmentMeasureIds: monitorAssessmentMeasures, // yes, it should be passed twice :)
        protocolMeasureIds: monitorAssessmentMeasures,
        studentGrade: this.props.grade,
        studentName: "",
        studentGroupId: "",
        payloadType: "assessment",
        benchmarkPeriodId: this.props.assessment.benchmarkPeriodId
      },
      (err, resp) => {
        if (err) {
          Alert.error(`${err.error}: ${err.reason}`, { timeout: 3000 });
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "ERROR"
          });
        } else {
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "",
            printMaterials: resp
          });
          this.downloadPdf();
        }
      }
    );

    this.setState({
      fetchingPrintMaterials: true
    });
  };

  downloadPdf = () => {
    const data = `data:application/pdf;base64,${encodeURIComponent(this.state.printMaterials)}`;
    const filename = `springmath-screening${this.props.assessment._id}.pdf`;
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      const byteCharacters = atob(this.state.printMaterials);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "application/pdf" });
      window.navigator.msSaveOrOpenBlob(blob, filename);
    } else {
      download({ filename, hrefData: data });
    }
  };

  render() {
    let background = "";
    let state = null;
    const printStatus = this.state.printMaterialsStatus;
    if (printStatus === "SUCCESS") {
      background = " list-group-item-success";
      state = <i className="fa fa-download fa-2x" />;
    } else if (printStatus === "LOADING") {
      background = " list-group-item-warning";
      state = <Loading inline />;
    } else if (printStatus === "ERROR") {
      background = " list-group-item-danger";
    }

    return (
      <Link
        to="#"
        className={`list-group-item assessment-printout-item ${background}`}
        onClick={this.handlePrintAssessments}
        data-testid="assessment-screening"
      >
        <span className="badge assessment-printout-badge">{state}</span>
        <span className="m-r-15">{this.props.ordinal}</span>
        {this.props.assessment.name}
      </Link>
    );
  }
}

GetAssessmentPrintout.propTypes = {
  assessment: PropTypes.shape({
    _id: PropTypes.string,
    name: PropTypes.string,
    benchmarkName: PropTypes.string,
    benchmarkPeriodId: PropTypes.string,
    monitorAssessmentMeasure: PropTypes.string
  }),
  grade: PropTypes.string,
  ordinal: PropTypes.number
};
