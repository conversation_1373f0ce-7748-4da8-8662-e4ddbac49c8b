import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON>out<PERSON> } from "react-router-dom";
import Alert from "react-s-alert";

import { passwordRegex, passwordSpecMsg } from "/imports/api/utilities/utilities";

class ResetPassword extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showWarning: false
    };
    this.setUserPassword = this.setUserPassword.bind(this);
  }

  setErrorMessage(msg) {
    const errMsg = document.getElementById("pswdErrorMsg");
    this.setState({
      showWarning: true
    });
    errMsg.innerHTML = msg;
    document.getElementById("txtPassword").select();
  }

  componentDidMount() {
    const elem = document.getElementById("txtPassword");
    if (elem) {
      elem.focus();
    }
  }

  setUserPassword(event) {
    event.preventDefault();
    const { usrToken: token } = this.props;
    const pw = this.passwordInput.value;
    const cpw = this.passwordConfirmInput.value;
    if (pw !== cpw || pw === "" || cpw === "") {
      this.setErrorMessage("New passwords do not match, try again.");
      return;
    }
    if (!passwordRegex.test(pw)) {
      this.setErrorMessage(passwordSpecMsg);
      return;
    }
    Meteor.call("resetUserPassword", token, pw, (err, resp) => {
      if (err?.reason === "Token expired") {
        this.setErrorMessage(
          `The link used to reset your password has expired or a new link was created. <br /> Please try the '<a href="/login" style="text-decoration: underline;">forgot password</a>' again.`
        );
        return;
      }
      if (err) {
        this.setErrorMessage(err.reason || err.message);
        return;
      }
      Meteor.call("user:onPasswordChange", resp?.id, () => {
        Meteor.call("users:updateLoginData", { userId: resp?.id }, () => {
          this.props.history.push("/logout");
          Alert.success("Successfully updated password");
        });
      });
    });
  }

  render() {
    return (
      <div className="conFullScreen conOnBoarding medium-width short password">
        <div className="container animated fadeInDown">
          <div className="content clearfix">
            <h3 className="w9">Welcome to SpringMath</h3>
            <p>Use this form to reset your password:</p>
            <hr />
            <form id="login" className="asd">
              <section>
                <p id="pswdErrorMsg" className={this.state.showWarning ? "warningMessage" : ""}>
                  What would you like your new password to be?
                </p>
                <div className="next-steps">
                  <div className="form-group">
                    <label className="pswdForm" htmlFor="txtPassword">
                      New Password
                    </label>
                    <input
                      id="txtPassword"
                      className="form-control"
                      ref={pi => {
                        this.passwordInput = pi;
                      }}
                      type="password"
                      required={true}
                      name="password"
                    />
                    <label className="pswdForm" htmlFor="txtConfirmPassword">
                      Confirm New Password
                    </label>
                    <input
                      id="txtConfirmPassword"
                      className="form-control"
                      ref={pci => {
                        this.passwordConfirmInput = pci;
                      }}
                      type="password"
                      required={true}
                      name="password"
                    />
                  </div>
                </div>
                <button className="btn btn-success btn-custom waves-light pull-right" onClick={this.setUserPassword}>
                  Reset Password
                </button>
              </section>
            </form>
          </div>
        </div>
      </div>
    );
  }
}

ResetPassword.propTypes = {
  usrToken: PropTypes.string,
  history: PropTypes.object
};

export default withRouter(ResetPassword);
