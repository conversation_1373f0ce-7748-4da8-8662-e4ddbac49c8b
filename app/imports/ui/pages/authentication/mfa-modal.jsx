import { <PERSON><PERSON><PERSON> } from "buffer";
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { useHistory } from "react-router-dom";
import { Accounts } from "meteor/accounts-base";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>dalFooter } from "react-bootstrap";

function MfaModal(props) {
  const history = useHistory();

  const [MFAuri, setMFAuri] = useState("");
  const [MFACode, setMFACode] = useState("");
  const [qrCode, setQrCode] = useState("");
  const [MFASecret, setMFASecret] = useState("");
  const [showSecretSection, setShowSecretSection] = useState(false);

  const generateQRCode = () => {
    Accounts.generate2faActivationQrCode("SpringMath", (codeError, result) => {
      if (codeError) {
        Alert.error(codeError.message || "Error while generating activation code");
        return;
      }
      const { svg, uri, secret } = result;
      setQrCode(Buffer.from(svg).toString("base64"));
      setMFAuri(uri);
      setMFASecret(secret);
    });
  };

  const onMFACodeChange = e => {
    const code = e.target.value;
    setMFACode(code);
  };

  const enableMFA = e => {
    e.preventDefault();
    Accounts.enableUser2fa(MFACode, error => {
      if (error) {
        Alert.error(error.reason || "Error while enabling MFA");
      } else {
        if (props.handleMFALogin) {
          props.handleMFALogin(MFACode);
        } else {
          Alert.success("Successfully linked MFA");
        }
        props.closeModal();
      }
    });
  };

  const handleSubmit = e => {
    if (props.isMFAEnabled) {
      props.handleMFALogin(MFACode);
    } else {
      enableMFA(e);
    }
  };

  const handleCodeInputKey = e => {
    if (!MFACode.length || e.key !== "Enter") {
      return;
    }
    enableMFA(e);
  };

  const handleEnterKeySubmit = e => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  useEffect(() => {
    if (!props.isMFAEnabled) {
      generateQRCode();
    }
  }, []);

  const renderContent = () => {
    const stepStyles = {
      display: "flex",
      alignItems: "flex-start",
      gap: "15px",
      marginBottom: "1rem"
    };

    const stepNumberStyles = {
      background: "#007bff",
      color: "white",
      width: "32px",
      height: "32px",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontWeight: "bold",
      fontSize: "16px",
      flexShrink: 0,
      marginTop: "2px"
    };

    const stepContentStyles = {
      flex: 1,
      lineHeight: "1.5"
    };

    const secretCodeStyles = {
      background: "#f8f9fa",
      padding: "8px 12px",
      borderRadius: "4px",
      fontFamily: "monospace",
      fontSize: "14px",
      border: "1px solid #dee2e6",
      display: "inline-block",
      marginTop: "5px",
      letterSpacing: "2px"
    };

    const renderSupportContent = () => {
      return (
        <div>
          Please contact <span className="link-primary"><EMAIL></span> with any questions.
        </div>
      );
    };

    return (
      <div className="text-center">
        {qrCode && MFAuri && (
          <div>
            {props.shouldShowMoreInfo && (
              <div className="small alert alert-danger">
                <div>You must enable MFA on your account in order to access this organization</div>
                <button className="btn btn-primary mt-2" onClick={() => history.go(-2)}>
                  Back
                </button>
              </div>
            )}
            <div style={{ textAlign: "left", maxWidth: "750px", margin: "0 auto" }}>
              <div style={stepStyles}>
                <span style={stepNumberStyles}>1</span>
                <div style={stepContentStyles}>
                  <span style={{ fontSize: "24px", fontWeight: "bold" }}>Grab your phone for this step.</span>
                  <br />
                  Open an authenticator app on your phone (Google or Microsoft Authenticator, etc). Please install one
                  from your app store if you haven&apos;t installed one yet.
                </div>
              </div>

              <div style={stepStyles}>
                <span style={stepNumberStyles}>2</span>
                <div style={stepContentStyles}>
                  <strong>Scan this QR code</strong> with your authenticator app:
                </div>
              </div>

              <div className="mb-3 text-center">
                <img width="200" src={`data:image/svg+xml;base64,${qrCode}`} />
              </div>

              <div className="mb-3 text-center">
                <a
                  href="#"
                  onClick={e => {
                    e.preventDefault();
                    setShowSecretSection(!showSecretSection);
                  }}
                  style={{ color: "#007bff", textDecoration: "none", fontSize: "14px" }}
                >
                  Unable to scan? {showSecretSection ? "Hide alternatives" : "Show alternatives"}
                </a>
              </div>

              {showSecretSection && (
                <div
                  style={{
                    background: "#f8f9fa",
                    border: "1px solid #dee2e6",
                    borderRadius: "8px",
                    padding: "20px",
                    marginBottom: "20px",
                    textAlign: "center"
                  }}
                >
                  <div className="mb-3" style={{ fontSize: "14px", color: "#495057", textAlign: "left" }}>
                    <strong>Note:</strong> To scan the QR code above, use the <strong>+</strong> icon in your
                    authenticator app to open the camera.
                  </div>
                  <div className="mb-3" style={{ fontSize: "14px", color: "#495057", textAlign: "left" }}>
                    <strong>If that doesn&apos;t work:</strong> Manually enter this secret key in your authenticator app
                    instead of scanning the QR code:
                    <br />
                    <div className="text-center">
                      <code style={secretCodeStyles}>{MFASecret}</code>
                    </div>
                  </div>
                  <p> {renderSupportContent()}</p>
                </div>
              )}

              <div style={stepStyles}>
                <span style={stepNumberStyles}>3</span>
                <div style={stepContentStyles}>
                  <strong>Enter the 6-digit code</strong> from your authenticator app below to complete setup.
                  <br />
                  <small style={{ color: "#6c757d" }}>
                    After setup, you&apos;ll enter a new code from this app each time you login.
                  </small>
                </div>
              </div>
            </div>
          </div>
        )}
        {props.isMFAEnabled && (
          <div style={{ textAlign: "left", maxWidth: "750px", margin: "0 auto 0 auto" }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexDirection: "column"
              }}
            >
              <span style={{ fontSize: "20px" }}>
                Open your authenticator app <strong>on your phone</strong> and enter the 6-digit code below.
              </span>
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  setShowSecretSection(!showSecretSection);
                }}
                style={{
                  color: "#007bff",
                  textDecoration: "none",
                  fontSize: "14px",
                  marginLeft: "15px",
                  flexShrink: 0
                }}
              >
                Need help?
              </a>
            </div>
            {showSecretSection && (
              <div
                style={{
                  marginBottom: "15px",
                  padding: "15px",
                  background: "#f8f9fa",
                  border: "1px solid #dee2e6",
                  borderRadius: "8px",
                  color: "#6c757d",
                  textAlign: "center"
                }}
              >
                {renderSupportContent()}
              </div>
            )}
          </div>
        )}
        <input
          className="mt-3 form-control font-18 w-50 d-inline-block"
          type="text"
          placeholder="MFA Code (e.g. 123456)"
          onChange={onMFACodeChange}
          onKeyUp={handleCodeInputKey}
          onKeyDown={handleEnterKeySubmit}
          autoFocus={true}
        />
      </div>
    );
  };

  return (
    <Modal show={props.showModal} onHide={props.closeModal} backdrop="static" size="lg">
      <ModalHeader>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div className="flex-grow-1">
            <div className="d-flex justify-content-between align-items-center">
              <h3 className="m-0">{props.isMFAEnabled ? "Enter MFA Code" : "Set Up Multi Factor Authentication"}</h3>
              {!props.isMFAEnabled && (
                <div
                  style={{
                    fontSize: "13px",
                    backgroundColor: "#d1ecf1",
                    color: "#0c5460",
                    padding: "8px 12px",
                    borderRadius: "4px",
                    border: "1px solid #bee5eb",
                    marginLeft: "20px",
                    fontWeight: "500"
                  }}
                >
                  SpringMath uses MFA to ensure all your data remains secure.
                </div>
              )}
            </div>
          </div>
        </div>
        <button className="btn btn-close-custom red-hover" onClick={props.closeModal}>
          &times;
        </button>
      </ModalHeader>
      <ModalBody>{renderContent()}</ModalBody>
      <ModalFooter>
        <div className="text-center w-100">
          <button className="btn btn-success" disabled={!MFACode.trim().length} onClick={handleSubmit}>
            Submit
          </button>
        </div>
      </ModalFooter>
    </Modal>
  );
}

MfaModal.propTypes = {
  handleMFALogin: PropTypes.func,
  showModal: PropTypes.func.required,
  closeModal: PropTypes.func.required,
  isMFAEnabled: PropTypes.bool,
  shouldShowMoreInfo: PropTypes.bool
};

export default MfaModal;
