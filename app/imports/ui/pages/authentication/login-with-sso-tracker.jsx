/* eslint-disable react/no-string-refs */
// eslint-disable-next-line no-unused-vars
import React, { useState, useRef } from "react";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import { getMeteorUserId, getQueryString } from "/imports/api/utilities/utilities";
import { loginWithazureAdB2c } from "/imports/api/ssoAzureAdB2c/client/azureAdB2cClient";

function loginWithSSO(props, callbackStart, callbackSuccess, callbackError) {
  if (getMeteorUserId()) {
    // user already logged in logout and signin with SSO
    Meteor.logout(() => {
      localStorage.removeItem("lastSiteIdWithGrades");
      localStorage.removeItem("lastRouteWithGrades");
      localStorage.removeItem("orgid");
      localStorage.removeItem("ssoLogIn");
    });
  }
  if (!getMeteorUserId() && !Meteor.loggingIn() && !Meteor.loggingOut()) {
    const domainHint = getQueryString("domain_hint");
    const options = { domain_hint: domainHint };
    if (ServiceConfiguration.configurations.find({ service: "azureAdB2c" }).count() > 0) {
      callbackStart();
      loginWithazureAdB2c(options, err => {
        if (err) {
          callbackError(err);
          Alert.error(`SSO Error ${err.reason || err.message}`, {
            onClose() {
              Alert.closeAll();
            }
          });
        } else {
          Meteor.call("users:setSelectedSchoolYear", () => {
            Meteor.call("users:updateLoginData");
            callbackSuccess();
            localStorage.setItem("ssoLogIn", true);
            props.history.push("/", { from: "login" });
          });
        }
      });
    }
  }
}

export default function loginWithSsoTracker(props) {
  const triggerOnceRef = useRef(false);

  const [loggingIn, setLoggingIn] = useState(false);
  const [error, setError] = useState();

  // Use useTracker to reactively check if services are configured
  const servicesConfigured = useTracker(() => {
    return Accounts.loginServicesConfigured();
  }, []);

  const setInProgress = () => {
    triggerOnceRef.current = true;
    setLoggingIn(true);
  };
  const setSuccess = () => {
    setLoggingIn(false);
    setError(null);
  };
  const setFail = e => {
    setLoggingIn(false);
    setError(e);
  };

  if (!error && !loggingIn && !triggerOnceRef.current && servicesConfigured) {
    loginWithSSO(props, setInProgress, setSuccess, setFail);
  }

  return {
    login: () => loginWithSSO(props, setInProgress, setSuccess, setFail),
    error,
    loggingIn
  };
}
