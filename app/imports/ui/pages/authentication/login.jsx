/* eslint-disable react/no-string-refs */
import React, { Component } from "react";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Accounts } from "meteor/accounts-base";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import { debounce } from "lodash";
import { removeHtmlScript } from "/imports/api/utilities/utilities";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import MfaModal from "./mfa-modal";

class Login extends Component {
  state = {
    email: "",
    password: "",
    isSSOOnlyOrg: false,
    isDataAdmin: false,
    isMFARequired: false,
    isMFAEnabled: false,
    ssoRedirectUrl: "",
    shouldDisplayMfaModal: false
  };

  componentDidMount() {
    removeHtmlScript("launcher"); // remove ze-snippet
    removeHtmlScript("webWidget"); // remove ze-snippet
    removeHtmlScript("ze-snippet"); // remove ze-snippet
    delete window.zE;
    delete window.zEACLoaded;
    delete window.zESettings;
    delete window.zEWebpackACJsonp;
    delete window.zEmbed;
    this.debounceGetUserAuthInfoByEmail = debounce(() => {
      this.getUserAuthInfoByEmail();
    }, 500);

    localStorage.removeItem("Meteor.userId");
    localStorage.removeItem("Meteor.loginToken");
    localStorage.removeItem("Meteor.loginTokenExpires");
  }

  componentWillUnmount() {
    this.debounceGetUserAuthInfoByEmail.flush();
  }

  onEmailChange = e => {
    const email = e.target.value;
    this.setState({ email }, this.debounceGetUserAuthInfoByEmail(e));
  };

  onPasswordChange = e => {
    const password = e.target.value;
    this.setState({ password });
  };

  getUserAuthInfoByEmail = (callback = () => {}) => {
    const email = this.state.email.trim();
    Meteor.call("users:getUserAuthInfoByEmail", email, (err, resp) => {
      if (err) {
        Alert.error(err);
      } else {
        const { isSSOOnlyOrg, isDataAdmin = false, ssoPortalUrl = "", isMFARequired = false, isMFAEnabled = false } =
          resp || {};
        localStorage.setItem("isMFARequired", isMFARequired ? "true" : "");
        this.setState({ isSSOOnlyOrg, isDataAdmin, isMFARequired, isMFAEnabled });
        if ((!isSSOOnlyOrg || isDataAdmin) && email.length) {
          callback();
        } else {
          this.setState({ ssoRedirectUrl: ssoPortalUrl });
        }
      }
    });
  };

  handleSuccessfulLogin = () => {
    Meteor.call("users:setSelectedSchoolYear", () => {
      Meteor.call("users:updateLoginData");
      localStorage.setItem("ssoLogIn", false);
      this.props.history.push("/", { from: "login" });
    });
  };

  login(event) {
    event.preventDefault();

    const email = this.state.email.trim();
    const password = this.state.password.trim();
    incWaitingOn(1, "Logging in...");
    this.getUserAuthInfoByEmail(() =>
      Meteor.loginWithPassword(email, password, err => {
        decWaitingOn();
        if (!err) {
          if (this.state.isMFARequired) {
            Meteor.call("users:updateActivityStamp");
            this.displayMfaModal();
            return;
          }
          this.handleSuccessfulLogin();
          return;
        }
        if (err.error !== "no-2fa-code") {
          Meteor.call("users:updateLoginData", { failedLoginEmail: email });
          Alert.error("Wrong user name or password.", {
            onClose() {
              Alert.closeAll();
            }
          });
        } else {
          this.displayMfaModal();
        }
      })
    );
  }

  handleMFALogin = code => {
    const email = this.state.email.trim();
    const password = this.state.password.trim();

    Meteor.loginWithPasswordAnd2faCode(email, password, code, error => {
      if (error) {
        Alert.error("Invalid MFA code");
      } else {
        this.handleSuccessfulLogin();
      }
    });
  };

  forgotPassword(event) {
    event.preventDefault();
    const email = this.state.email.trim();
    if (!email) {
      Alert.error('Please enter your email and then click the "Forgot your password" link.', {
        position: "top",
        effect: "jelly",
        offset: 50
      });
    } else {
      this.getUserAuthInfoByEmail(() =>
        Accounts.forgotPassword({ email }, err => {
          if (err) {
            Alert.error("Error, unable to reset with given email address", {
              position: "top",
              effect: "jelly",
              offset: 50
            });
          } else {
            Alert.success(`Reset password email sent for account: ${email}`, {
              position: "top",
              effect: "jelly",
              offset: 50
            });
          }
        })
      );
    }
  }

  displayMfaModal = () => {
    this.setState({ shouldDisplayMfaModal: true });
  };

  closeMfaModal = () => {
    this.setState({ shouldDisplayMfaModal: false });
  };

  render() {
    const isSSOOnlyOrgAndDataAdmin = this.state.isSSOOnlyOrg && this.state.isDataAdmin;
    const isSSOOnlyOrgAndNotDataAdmin = this.state.isSSOOnlyOrg && !this.state.isDataAdmin;

    return (
      <div className="conLogin">
        <h4 className="text-center w7">
          <Link to="#" className="logo logo-lg">
            <i className="md md-equalizer" /> <span>SpringMath</span>{" "}
          </Link>
        </h4>

        <form className="form-horizontal m-t-20" onSubmit={this.login.bind(this)} id="login">
          <div className="form-group">
            <label>Email</label>
            <input
              className="form-control"
              type="text"
              required=""
              name="emailAddress"
              placeholder="Email"
              value={this.state.email}
              onChange={this.onEmailChange}
            />
            <i className="md md-account-circle form-control-feedback l-h-34" />
          </div>

          <div className={`form-group ${isSSOOnlyOrgAndNotDataAdmin ? "d-none" : ""}`}>
            <label>Password</label>
            <input
              className="form-control"
              type="password"
              required=""
              name="password"
              value={this.state.password}
              placeholder="Password"
              onChange={this.onPasswordChange}
            />
            <i className="md md-vpn-key form-control-feedback l-h-34" />
          </div>

          {!isSSOOnlyOrgAndNotDataAdmin ? (
            <div className="form-group  m-t-20">
              <Link
                to="/"
                onClick={this.forgotPassword.bind(this)}
                className="btn btn-link text-start text-muted"
                data-testid="login-forgot-password-link"
              >
                Forgot your password?
              </Link>
              <button
                id="btnLogin"
                className="btn btn-success btn-custom w-md waves-effect waves-light pull-right"
                type="submit"
                disabled={this.state.email.trim().length === 0 || this.state.password.trim().length === 0}
              >
                Log In
              </button>
            </div>
          ) : (
            <div className="text-center">
              {this.state.ssoRedirectUrl ? (
                <a href={this.state.ssoRedirectUrl} className="btn btn-default text-center">
                  Please use your district SSO portal
                </a>
              ) : (
                <div className="alert alert-info">Please use your district SSO portal</div>
              )}
            </div>
          )}
          {isSSOOnlyOrgAndDataAdmin && (
            <div className="text-center">
              <div className="alert alert-info">
                The &apos;Use SSO Only&apos; options is enabled. Only Data Admin users, like you, are able to login
                using an Email and Password.
              </div>
            </div>
          )}
        </form>
        {this.state.shouldDisplayMfaModal && (
          <MfaModal
            showModal={this.state.shouldDisplayMfaModal}
            closeModal={this.closeMfaModal}
            isMFAEnabled={this.state.isMFAEnabled}
            handleMFALogin={this.handleMFALogin}
          />
        )}
      </div>
    );
  }
}

Login.propTypes = {
  history: PropTypes.object
};

export default withRouter(Login);
