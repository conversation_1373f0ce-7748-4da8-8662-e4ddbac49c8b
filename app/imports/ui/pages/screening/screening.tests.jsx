import { assert } from "chai";
import React from "react";
import { shallow } from "enzyme";
import td from "testdouble";
import "@testing-library/jest-dom";

import { Meteor } from "meteor/meteor";
import { cleanup, waitFor } from "@testing-library/react";
import Screening, { PureScreening } from "./screening.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserSync: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id"),
  isSML: jest.fn(() => false)
}));

describe("Screening UI", () => {
  const org = {
    schoolYearBoundary: {
      month: 6,
      day: 31
    }
  };
  beforeEach(() => {
    td.replace(Meteor, "call");
    // Mock Meteor methods and subscriptions
    Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
    Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
  });
  afterEach(() => {
    cleanup();
    td.reset();
  });
  describe("Render", () => {
    it("render", () => {
      // Verify that the method does what we expected
      const screeningComponent = shallow(<Screening studentGroup={{ _id: "test" }} org={org} />);
      assert.isDefined(screeningComponent, "screeningComponent did not render");
    });
  });
  describe("when there is no active subcomponent", () => {
    const testStudentGroup = {
      _id: "testStudentGroupId",
      siteId: "someSiteId",
      grade: "123"
    };
    describe("when there is an active screening", () => {
      const currentBMPScreeningStatus = {
        hasScreening: true,
        completed: false,
        periodId: "8S52Gz5o85hRkECgq"
      };
      it("should display the continue screening area and button", async () => {
        const { _id, siteId, grade } = testStudentGroup;
        td.when(
          Meteor.call("ScreeningHelpers:currentBMPScreeningStatus", { studentGroupId: _id, siteId, grade })
        ).thenCallback(null, currentBMPScreeningStatus);

        const { getByText } = renderWithRouter(
          <PureScreening studentGroup={testStudentGroup} loading={false} org={org} inActiveSchoolYear={true} />
        );

        await waitFor(() => {
          expect(getByText("Continue", { selector: "a", exact: false })).toBeVisible();
        });
      });
    });
    describe("when there is not an active screening and no history for this period", () => {
      const currentBMPScreeningStatus = {
        hasScreening: false,
        completed: false
      };
      it("should display the begin screening area and button", async () => {
        const { _id, siteId, grade } = testStudentGroup;
        td.when(
          Meteor.call("ScreeningHelpers:currentBMPScreeningStatus", { studentGroupId: _id, siteId, grade })
        ).thenCallback(null, currentBMPScreeningStatus);

        const { getByText } = renderWithRouter(<PureScreening studentGroup={testStudentGroup} loading={false} />);

        await waitFor(() => {
          expect(getByText("Begin", { selector: "button", exact: false })).toBeVisible();
        });
      });
    });
  });
});
