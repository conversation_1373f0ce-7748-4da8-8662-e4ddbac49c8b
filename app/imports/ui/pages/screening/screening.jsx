import React, { useState, useEffect, useCallback, useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useHistory } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";

import NewScreening from "./new-screening.jsx";
import ScreeningResults from "./screening-results.jsx";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { Loading } from "../../components/loading.jsx";
import * as utils from "/imports/api/utilities/utilities";
import getCurrentBMPScreeningStatus from "/imports/api/helpers/getCurrentBMPScreeningStatus";
import FirstScreening from "./first-screening";
import ScreeningDashboard from "./screening-dashboard";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

export function getBenchmarkPeriodLabel(benchmarkPeriodId) {
  return utils.capitalizeFirstLetter(utils.translateBenchmarkPeriod(benchmarkPeriodId).name);
}

export function startNewClasswide(studentGroup, history, currentBenchmarkPeriod) {
  const { siteId, orgid, grade } = studentGroup;
  const studentGroupId = studentGroup._id;
  const opts = {
    studentGroupId,
    siteId,
    benchmarkPeriodId: grade === "HS" ? "allPeriods" : currentBenchmarkPeriod && currentBenchmarkPeriod._id
  };
  Meteor.call("startClasswideIntervention", opts, error => {
    if (error) {
      utils.ninjalog.error({
        msg: `error setting up new screening period: ${error}`
      });
    } else {
      history.push(`/${orgid}/site/${siteId}/student-groups/${studentGroupId}/${utils.dashboardNavs.classwide}`);
    }
  });
}

function Screening(props) {
  const { schoolYear, currentBenchmarkPeriod } = useContext(SchoolYearContext);
  const [currentBMPScreeningStatus, setCurrentBMPScreeningStatus] = useState(null);
  const [previousIncompleteScreenings, setPreviousIncompleteScreenings] = useState([]);
  const history = useHistory();

  const getPreviousIncompleteScreeningBenchmarkPeriodIds = useCallback((studentGroupId, grade) => {
    Meteor.call(
      "ScreeningHelpers:getPreviousIncompleteScreeningBenchmarkPeriodIds",
      studentGroupId,
      grade,
      (err, resp) => {
        if (err) {
          Alert.error("Error fetching incomplete screenings");
        } else {
          setPreviousIncompleteScreenings(resp);
        }
      }
    );
  }, []);

  useEffect(() => {
    if (props.studentGroup && !currentBMPScreeningStatus) {
      getCurrentBMPScreeningStatus(props.studentGroup, (err, resp) => {
        setCurrentBMPScreeningStatus(resp);
      });
      getPreviousIncompleteScreeningBenchmarkPeriodIds(props.studentGroup._id, props.studentGroup.grade);
    }
  }, [props.studentGroup, currentBMPScreeningStatus, getPreviousIncompleteScreeningBenchmarkPeriodIds]);

  // Handle props changes (replaces UNSAFE_componentWillReceiveProps)
  useEffect(() => {
    if (props.studentGroup && !currentBMPScreeningStatus) {
      getCurrentBMPScreeningStatus(props.studentGroup, (err, resp) => {
        setCurrentBMPScreeningStatus(resp);
      });
      getPreviousIncompleteScreeningBenchmarkPeriodIds(props.studentGroup._id, props.studentGroup.grade);
    }
  }, [props.studentGroup, currentBMPScreeningStatus, getPreviousIncompleteScreeningBenchmarkPeriodIds]);

  const startNewScreening = useCallback(() => {
    const { siteId, grade, orgid, _id: studentGroupId } = props.studentGroup;
    const opts = {
      studentGroupId,
      siteId,
      grade,
      orgid
    };
    Meteor.call("setUpNewScreeningPeriod", opts, (error, results) => {
      if (error) {
        utils.ninjalog.error({
          msg: `error setting up new screening period: ${error}`
        });
      }
      if (results) {
        history.push(`/${orgid}/site/${siteId}/student-groups/${studentGroupId}/${utils.dashboardNavs.screening}/form`);
      }
    });
  }, [props.studentGroup, history]);

  const formatCurrentPeriod = useCallback(() => {
    if (!currentBMPScreeningStatus) {
      return null;
    }
    return utils.capitalizeFirstLetter(utils.translateBenchmarkPeriod(currentBMPScreeningStatus.periodId).name);
  }, [currentBMPScreeningStatus]);

  const formatScreeningTitle = useCallback(
    (currentPeriod = formatCurrentPeriod()) => {
      return `${currentPeriod} ${utils.getFormattedSchoolYear(
        (currentBMPScreeningStatus && currentBMPScreeningStatus.schoolYear) || schoolYear
      )} screening`;
    },
    [currentBMPScreeningStatus, formatCurrentPeriod]
  );

  const renderScreeningComponent = useCallback(
    (
      activeComponent,
      studentGroup,
      inActiveSchoolYear,
      screeningBenchmarkPeriodId,
      bmpScreeningStatus,
      assessmentResultId,
      isReadOnly,
      hasScreeningHistory
    ) => {
      if (activeComponent === "ScreeningForm") {
        return (
          <NewScreening
            studentGroupId={studentGroup._id}
            inActiveSchoolYear={inActiveSchoolYear}
            isReadOnly={isReadOnly}
            screeningBenchmarkPeriodId={screeningBenchmarkPeriodId || bmpScreeningStatus.periodId}
          />
        );
      }
      if (activeComponent === "ScreeningResults" && assessmentResultId) {
        return (
          <ScreeningResults
            assessmentResultId={assessmentResultId}
            studentGroupId={studentGroup._id}
            siteId={studentGroup.siteId}
            students={props.students}
            orgid={studentGroup.orgid}
            history={history}
          />
        );
      }
      const canManageScreenings = inActiveSchoolYear && !isReadOnly;
      const shouldSeeScreeningSummary =
        hasScreeningHistory ||
        previousIncompleteScreenings.length ||
        (bmpScreeningStatus.hasScreening && !bmpScreeningStatus.completed);
      if (shouldSeeScreeningSummary) {
        return (
          <ScreeningDashboard
            canManageScreenings={canManageScreenings}
            currentBMPScreeningStatus={bmpScreeningStatus}
            formatScreeningTitle={formatScreeningTitle}
            startNewScreening={startNewScreening}
            studentGroup={studentGroup}
            previousIncompleteScreenings={previousIncompleteScreenings}
            benchmarkPeriods={props.benchmarkPeriods}
          />
        );
      }
      return (
        <FirstScreening
          studentGroup={studentGroup}
          inActiveSchoolYear={inActiveSchoolYear}
          startNewClasswide={() => startNewClasswide(studentGroup, history, currentBenchmarkPeriod)}
          canManageScreenings={canManageScreenings}
          startNewScreening={startNewScreening}
        />
      );
    },
    [
      props.loading,
      props.students,
      props.activeComponent,
      props.benchmarkPeriods,
      previousIncompleteScreenings,
      formatScreeningTitle,
      startNewScreening,
      history
    ]
  );

  const {
    studentGroup,
    inActiveSchoolYear,
    activeComponent,
    assessmentResultId,
    isReadOnly,
    screeningBenchmarkPeriodId,
    loading
  } = props;

  const hasScreeningHistory = studentGroup?.history && studentGroup.history.some(h => h.type === "benchmark");

  if (loading || !currentBMPScreeningStatus) {
    return <Loading />;
  }

  return (
    <React.Fragment>
      <div data-testid="screeningContent" />
      {renderScreeningComponent(
        activeComponent,
        studentGroup,
        inActiveSchoolYear,
        screeningBenchmarkPeriodId,
        currentBMPScreeningStatus,
        assessmentResultId,
        isReadOnly,
        hasScreeningHistory
      )}
    </React.Fragment>
  );
}

Screening.propTypes = {
  activeComponent: PropTypes.string,
  assessmentResultId: PropTypes.string,
  benchmarkPeriods: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  loading: PropTypes.bool,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  user: PropTypes.object,
  screeningBenchmarkPeriodId: PropTypes.string
};

// Data Container
function ScreeningWithTracker({ studentGroup, ...otherProps }) {
  const trackerData = useTracker(() => {
    const user = getMeteorUserSync();
    const benchmarkPeriodsSub = Meteor.subscribe("BenchmarkPeriods");
    const screeningAssignmentsSub = Meteor.subscribe("ScreeningAssignments");
    const studentGroupEnrollmentsSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroup._id);
    const bmwSub = Meteor.subscribe("BenchmarkWindowsForStudentGroupAndPeriod", studentGroup._id);

    const loading =
      !benchmarkPeriodsSub.ready() ||
      !screeningAssignmentsSub.ready() ||
      !studentGroupEnrollmentsSub.ready() ||
      !bmwSub.ready();

    const benchmarkPeriods = BenchmarkPeriods.find().fetch();

    return {
      benchmarkPeriods,
      studentGroup,
      loading,
      user
    };
  }, [studentGroup._id]);

  return <Screening {...otherProps} {...trackerData} />;
}

ScreeningWithTracker.propTypes = {
  studentGroup: PropTypes.object.isRequired
};

export default ScreeningWithTracker;
export { Screening as PureScreening }; // for testing
