import { assert } from "chai";

import React from "react";
import { shallow } from "enzyme";

import ScreeningResults from "./screening-results.jsx";

describe("ScreeningResults UI", () => {
  describe("Render", () => {
    let screeningResultsComponent;

    beforeEach(() => {
      const studentGroupId = "test_student_group_2";
      const year = 2017;
      const period = "fall-period";
      const assessmentResult = {
        benchmarkPeriodId: "testBenchmarkPeriodId"
      };
      const siteId = "testSiteId";
      screeningResultsComponent = shallow(
        <ScreeningResults params={{ studentGroupId, year, period, assessmentResult, siteId }} />
      );
    });

    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(screeningResultsComponent, "screeningResultsComponent did not render");
    });
  });
});
