import _ from "lodash";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

export default class SuggestedStudentPairingsModal extends Component {
  close = () => {
    this.props.closeModal();
  };

  render() {
    const { studentResults, showModal, hasNewPairings, getNewPairings } = this.props;
    const maxGroupSize = Math.ceil(studentResults.length / 2);
    const shouldStudentWorkWithTeacher = studentResults.length % 2 === 1;
    const [firstGroup = [], secondGroup = []] = _.chunk(studentResults, maxGroupSize);

    return (
      <Modal show={showModal} onHide={this.close} backdrop="static">
        <ModalHeader>
          <div className="d-flex flex-row justify-content-between w-100">
            <h3 className="w9">Suggested Student Pairings</h3>
            {hasNewPairings ? (
              <button className="btn btn-primary" onClick={getNewPairings}>
                New Pairs
              </button>
            ) : null}
          </div>
        </ModalHeader>

        <ModalBody>
          <table className="table">
            <tbody>
              {firstGroup.map((datum, index) => (
                <tr key={index} data-testid={`studentPair_${index}`}>
                  <td>
                    {datum.lastName}, {datum.firstName}
                  </td>
                  <td>
                    {secondGroup[index] ? (
                      <span>
                        {secondGroup[index].lastName}, {secondGroup[index].firstName}
                      </span>
                    ) : (
                      "Teacher"
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {shouldStudentWorkWithTeacher && (
            <div className="alert alert-info">We recommend rotating the student working with the teacher.</div>
          )}
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

SuggestedStudentPairingsModal.propTypes = {
  showModal: PropTypes.bool,
  studentResults: PropTypes.array,
  closeModal: PropTypes.func,
  hasNewPairings: PropTypes.bool,
  getNewPairings: PropTypes.func
};
