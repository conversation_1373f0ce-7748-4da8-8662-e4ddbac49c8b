import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withTracker } from "meteor/react-meteor-data";
import map from "lodash/map";
import sortBy from "lodash/sortBy";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>, Button } from "react-bootstrap";
import { withRouter } from "react-router-dom";
import * as utils from "/imports/api/utilities/utilities";
import ScreeningRow from "../../components/score-entry/score-entry-row";
import { Students } from "/imports/api/students/students";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Assessments } from "/imports/api/assessments/assessments";
import { getScoreTargets } from "/imports/api/assessments/methods";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { Loading } from "../../components/loading";
import ActiveSchoolYearMessage from "../../components/ActiveSchoolYearMessage";
import { downloadPdf, renderQuickActionButtons } from "/imports/ui/utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { ScoreEntryContext } from "../../components/score-entry/score-entry-context";
import { shouldUseDevMode } from "/imports/api/utilities/utilities";
import SkillVideoButton from "../../components/dashboard/skill-video-button";

class NewScreening extends Component {
  static contextType = ScoreEntryContext;

  constructor(props) {
    super(props);
    this.state = {
      fetchingPrintMaterials: false,
      printMaterialsStatus: "",
      bsModalShow: false,
      sortBy: "lastFirst",
      errorCalculatingAssessmentResults: false
    };
    this.showModal = this.showModal.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.viewResults = this.viewResults.bind(this);
    this.handlePrintAssessments = this.handlePrintAssessments.bind(this);
    this.renderPrintButton = this.renderPrintButton.bind(this);
  }

  handlePrintAssessments(assessments, grade, benchmarkPeriodId) {
    const assessmentIds = assessments.map(a => a._id);
    const vendorIds = assessments.map(a => a.monitorAssessmentMeasure);

    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: "SCREENING",
        assessmentIds,
        assessmentMeasureIds: vendorIds,
        protocolMeasureIds: vendorIds,
        studentGrade: grade,
        studentName: "", // studentName
        studentGroupId: this.props.studentGroupId,
        payloadType: "assessment",
        benchmarkPeriodId
      },
      (err, resp) => {
        if (err) {
          Alert.error(`${err.error}: ${err.reason}`, { timeout: 3000 });
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "ERROR"
          });
        } else {
          const fileName = `springmath-screening-${this.props.resultsDoc._id}.pdf`;
          downloadPdf(resp, fileName);
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "SUCCESS"
          });
        }
      }
    );

    this.setState({
      fetchingPrintMaterials: true
    });
  }

  showModal() {
    this.setState({
      bsModalShow: true
    });
  }

  hideModal() {
    this.setState({
      bsModalShow: false
    });
  }

  viewResults() {
    if (!this.props.resultsDoc) {
      this.setState({ errorCalculatingAssessmentResults: true });
      return null;
    }

    const resultsDocId = this.props.resultsDoc._id;
    Meteor.call("calculateClasswideScreeningResults", resultsDocId, (err, res) => {
      if (err) {
        this.setState({ errorCalculatingAssessmentResults: true });
      } else if (res) {
        this.hideModal();
        this.setState({ errorCalculatingAssessmentResults: false });
        this.props.history.push(
          `/${this.props.studentGroup.orgid}/site/${this.props.studentGroup.siteId}/student-groups/${this.props.studentGroup._id}/${utils.dashboardNavs.screening}/results/${resultsDocId}`
        );
      }
    });
    return null;
  }

  get sortByString() {
    return this.state.sortBy === "lastFirst" ? "Last, First" : "First / Last";
  }

  toggleSorting = () => {
    this.setState(prevState => ({
      sortBy: prevState.sortBy === "lastFirst" ? "firstLast" : "lastFirst"
    }));
  };

  getSortingValue = student => {
    const { firstName, lastName } = student.identity.name;
    return this.state.sortBy === "lastFirst" ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
  };

  renderStudents(currentStudentScreeningStatuses) {
    return sortBy(this.props.students, this.getSortingValue).reduce((a, student, index) => {
      const assessmentScores = this.props.resultsDoc.scores
        .filter(sc => sc.studentId === student._id)
        .sort(
          (fst, snd) =>
            this.props.resultsDoc.assessmentIds.indexOf(fst._id) - this.props.resultsDoc.assessmentIds.indexOf(snd._id)
        );
      if (assessmentScores.length > 0) {
        const currentStudentScreeningStatus = currentStudentScreeningStatuses.find(c => c.studentId === student._id);

        a.push(
          <ScreeningRow
            studentIndex={index}
            student={student}
            assessmentResultId={this.props.resultsDoc._id}
            assessmentScores={assessmentScores}
            assessmentScoreLimits={this.props.assessmentScoreLimits}
            currentStudentScreeningStatus={currentStudentScreeningStatus}
            inActiveSchoolYear={this.props.inActiveSchoolYear}
            isReadOnly={this.props.isReadOnly}
            key={student._id}
            isSML={this.props.isSML}
            sortBy={this.state.sortBy}
            loaded
          />
        );
      }
      return a;
    }, []);
  }

  handleDownloadPdf = () => {
    const { assessments, studentGrade, resultsDoc } = this.props;
    if (
      assessments &&
      assessments.length > 0 &&
      assessments.length < 10 &&
      studentGrade &&
      resultsDoc &&
      resultsDoc?.status !== "COMPLETED"
    ) {
      this.handlePrintAssessments(assessments, studentGrade, resultsDoc?.benchmarkPeriodId);
    }
  };

  renderPrintButton() {
    if (this.state.printMaterialsStatus !== "ERROR") {
      return (
        <button className="btn btn-success" onClick={this.handleDownloadPdf}>
          Print Your Assessments
        </button>
      );
    }
    return (
      <button
        className="btn btn-danger"
        disabled={!this.props.inActiveSchoolYear}
        onClick={() => this.setState({ printMaterialsStatus: "" })}
      >
        Printing Error, try again
      </button>
    );
  }

  getInstructionalTarget(assessment) {
    const instructionalTargetIndex = 0;
    return getScoreTargets({
      assessment,
      grade: this.props.studentGrade,
      benchmarkPeriodId: this.props.resultsDoc.benchmarkPeriodId,
      assessmentType: "benchmark"
    })[instructionalTargetIndex];
  }

  getSubmitScreeningButton = () => {
    const studentIdsInGroup = this.props.students.map(s => s._id);
    const studentScores = this.props.resultsDoc.scores.filter(score => studentIdsInGroup.includes(score.studentId));
    const areAllStudentsAbsent = studentScores.every(sc => sc.status === "CANCELLED");
    const isScreeningCompleted =
      !areAllStudentsAbsent && studentScores.every(score => ["COMPLETE", "CANCELLED"].includes(score.status));
    if (Object.keys(this.context.getUnusualHighScoreFields(this.props.resultsDoc._id)).length) {
      return (
        <p className="text-end m0 float-end">
          <a className="btn btn-default disabled" name="button">
            <i className="fa fa-warning fa-left" />
            Fix Unusual High Scores
          </a>
        </p>
      );
    }

    if (isScreeningCompleted) {
      return (
        <p className="text-end m0 float-end">
          <button
            onClick={this.props.isSML ? this.viewResults : this.showModal}
            className="btn btn-success"
            name="button"
            disabled={!this.props.inActiveSchoolYear}
          >
            View Results <i className="fa fa-bar-chart fa-right" />
          </button>
        </p>
      );
    }

    return (
      <p className="text-end m0 float-end">
        <a className="btn btn-default disabled" name="button">
          <i className="fa fa-warning fa-left" />
          {this.props.isSML ? "Continue" : "Screen All Students to Continue"}
        </a>
      </p>
    );
  };

  renderLoadingMessage = () => {
    const { shouldShowLoadingMessage } = this.props;
    return shouldShowLoadingMessage ? (
      <p className="alert alert-info col-lg-8 col-lg-offset-2 m-t-10">
        Screening form is not available for this student group yet.
        <br />
        You might have to go back to the previous page if this is taking too long.
      </p>
    ) : null;
  };

  render() {
    if (this.props.loading) {
      return <Loading message={this.renderLoadingMessage()} />;
    }

    const currentStudentScreeningStatuses = map(this.props.students, s => {
      const studentAssScores = this.props.resultsDoc.scores.filter(sc => sc.studentId === s._id);
      const someScoresEntered = studentAssScores.some(sc => ["COMPLETE", "CANCELLED"].includes(sc.status));
      const allScoresEntered = studentAssScores.every(sc => sc.status === "COMPLETE");
      const allScoresCancelled = studentAssScores.every(sc => sc.status === "CANCELLED");

      return {
        studentId: s._id,
        someScoresEntered,
        allScoresEntered,
        allScoresCancelled
      };
    });

    const {
      env: { CI }
    } = this.context;
    const shouldRenderQuickActionButtons = shouldUseDevMode(CI);

    const numStudentsCompletedScreening = currentStudentScreeningStatuses.filter(c => c.someScoresEntered).length;

    const numStudentsMissingScores = currentStudentScreeningStatuses.filter(c => !c.allScoresEntered).length;

    return (
      <div>
        {this.props.isSML ? (
          <p className="text-center">
            We need to give your student a screening assessment to <br />
            determine if they need an intervention
          </p>
        ) : (
          <p className="text-center">
            {`Screen each student to see who's on track and who might need intervention.`} <br />
            Once screening is complete we will show you your results.
          </p>
        )}

        <div className="conPrintScreenAssessments">
          {this.state.fetchingPrintMaterials ? (
            <div className="print-cta">
              <i className="fa fa-print" />
              <Loading message="Building Custom Assessments. This will take just a moment." block />
            </div>
          ) : (
            <div className="print-cta">
              <i className="fa fa-print" />
              {this.renderPrintButton()}
              <h6 className="w7 m-b-0">
                <small>After screening, please enter scores below.</small>
              </h6>
            </div>
          )}
        </div>
        <ActiveSchoolYearMessage inActiveSchoolYear={this.props.inActiveSchoolYear} />
        <div className="screening-progress" data-testid="input-screening-score-page">
          {this.props.isSML ? null : (
            <h5 className="w7 m-b-0 text-success float-start">
              <em>
                {numStudentsCompletedScreening} of {this.props.students.length}
                &nbsp;
                <small>Students Accounted For</small>
              </em>
            </h5>
          )}

          {this.getSubmitScreeningButton()}
          <table className="table screening-table">
            <thead>
              <tr>
                <th>{shouldRenderQuickActionButtons ? renderQuickActionButtons("start") : " "}</th>
                <th className="split-header-top">
                  <ul className="screening-cols">
                    {this.props.assessments.map((assessment, index) => (
                      <li key={assessment._id} data-testid="screening-cols-measure">
                        <u>
                          <strong>Measure {index + 1}</strong>
                        </u>
                        <br />
                        {assessment.name}
                      </li>
                    ))}
                    <li>&nbsp;</li>
                  </ul>
                </th>
              </tr>
              <tr className="screening-student-table-header">
                <th className="screening-student-sort">
                  {this.props.isSML ? null : (
                    <React.Fragment>
                      Sort by:{" "}
                      <u role="button" onClick={this.toggleSorting} data-testid="new-screening-sort-by">
                        {this.sortByString}
                      </u>
                    </React.Fragment>
                  )}
                </th>
                <th className="split-header-bottom">
                  <ul className="screening-cols">
                    {this.props.assessments.map(assessment => (
                      <li key={assessment._id}>
                        {assessment.hasVideo ? (
                          <div className="d-flex mb-1">
                            <SkillVideoButton
                              measureNumber={assessment.monitorAssessmentMeasure}
                              skillName={assessment.name}
                              hasVideo={assessment.hasVideo}
                            />
                          </div>
                        ) : null}
                        <div className="d-flex">
                          (Instructional
                          <br />
                          target = {this.getInstructionalTarget(assessment)})
                        </div>
                      </li>
                    ))}
                    <li>&nbsp;</li>
                  </ul>
                </th>
              </tr>
            </thead>
            <tbody>{this.renderStudents(currentStudentScreeningStatuses)}</tbody>
          </table>
        </div>

        <ButtonToolbar>
          <Modal show={this.state.bsModalShow} onHide={this.hideModal} dialogClassName="notice" backdrop="static">
            <Modal.Body>
              <i className="fa fa-2x fa-warning text-warning" />
              <h1 className="text-warning">Important</h1>
              <p>
                Please carefully review your scores before proceeding. You will not be able to edit or add additional
                scores after selecting &quot;Continue to results&quot;
              </p>
              <p>
                <small>
                  {numStudentsMissingScores
                    ? `You have ${numStudentsMissingScores} ${
                        numStudentsMissingScores > 1 ? "students" : "student"
                      } without complete scores.`
                    : null}
                </small>
              </p>
              {this.state.errorCalculatingAssessmentResults ? (
                <div className="alert alert-danger">Error calculating results. Please refresh page and try again.</div>
              ) : null}
            </Modal.Body>
            <Modal.Footer>
              <Button variant="danger" className="me-auto" onClick={this.hideModal}>
                Cancel
              </Button>
              <Button variant="success" onClick={this.viewResults}>
                Continue to results
                <i className="fa fa-chevron-right fa-sm fa-right" />
                <i className="fa fa-chevron-right fa-sm" />
              </Button>
            </Modal.Footer>
          </Modal>
        </ButtonToolbar>
      </div>
    );
  }
}

NewScreening.propTypes = {
  assessments: PropTypes.array,
  assessmentScoreLimits: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  loading: PropTypes.bool.isRequired,
  resultsDoc: PropTypes.object,
  studentGrade: PropTypes.string,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  screeningBenchmarkPeriodId: PropTypes.string,
  students: PropTypes.array,
  isSML: PropTypes.bool,
  history: PropTypes.object,
  shouldShowLoadingMessage: PropTypes.bool
};

// Data Container
export default withRouter(
  withTracker(({ studentGroupId, screeningBenchmarkPeriodId, history }) => {
    // const sgHandle = Meteor.subscribe('StudentGroupsAssociatedWithUser');
    const sgeHandle = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);
    const sHandle = Meteor.subscribe("StudentsInStudentGroup", studentGroupId); // Students Collection
    const assHandle = Meteor.subscribe("Assessments"); // Assessment Collection

    let students;
    let studentGroup;
    let assessments = [];
    let studentGrade;
    let resultsDoc;
    let assessmentScoreLimits = [];
    let shouldShowLoadingMessage = false;

    studentGroup = StudentGroups.findOne({ _id: studentGroupId });
    const assessmentResultsSub = Meteor.subscribe(
      "AssessmentResults:FindByIds",
      (studentGroup && studentGroup.currentAssessmentResultIds) || []
    );
    let loading = !sgeHandle.ready() || !sHandle.ready() || !assHandle.ready() || !assessmentResultsSub.ready();

    if (!loading) {
      studentGroup = StudentGroups.findOne({ _id: studentGroupId });

      studentGrade = studentGroup.grade;
      const activeBenchmarkAssessments = AssessmentResults.find({ type: "benchmark" }).fetch();
      if (activeBenchmarkAssessments.length) {
        resultsDoc = screeningBenchmarkPeriodId
          ? activeBenchmarkAssessments.find(ass => ass.benchmarkPeriodId === screeningBenchmarkPeriodId)
          : activeBenchmarkAssessments[0];
        const studentIdsInResultDocScores = resultsDoc.scores.map(score => score.studentId);
        students = Students.find({}, { sort: { "identity.name.lastName": 1 } })
          .fetch()
          .filter(s => studentIdsInResultDocScores.includes(s._id));

        // If there is already a resultsDoc, reroute to view results page
        if (resultsDoc.status === "COMPLETED") {
          history.push(
            `/${studentGroup.orgid}/site/${studentGroup.siteId}/student-groups/${studentGroup._id}/${utils.dashboardNavs.screening}/results/${resultsDoc._id}`
          );
        }
        assessments = Assessments.find({ _id: { $in: resultsDoc.assessmentIds } })
          .fetch()
          .sort((a, b) => resultsDoc.assessmentIds.indexOf(a._id) - resultsDoc.assessmentIds.indexOf(b._id));
        assessmentScoreLimits = assessments.map(a => {
          const targets = getScoreTargets({
            assessment: a,
            grade: studentGrade,
            benchmarkPeriodId: resultsDoc.benchmarkPeriodId,
            assessmentType: "benchmark"
          });
          return {
            assessmentId: a._id,
            limit: targets[1] * 5,
            at: targets[0]
          };
        });
      } else {
        loading = true;
        shouldShowLoadingMessage = true;
      }
    }
    const currentOrg = Organizations.findOne();
    const isSML = currentOrg && !!currentOrg.isSelfEnrollee;
    return {
      assessments,
      assessmentScoreLimits,
      loading,
      resultsDoc,
      studentGrade,
      studentGroup,
      studentGroupId,
      students,
      isSML,
      shouldShowLoadingMessage
    };
  })(NewScreening)
);
