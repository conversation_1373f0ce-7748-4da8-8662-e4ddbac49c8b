import React from "react";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withRouter } from "react-router-dom";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import Loading from "../../components/loading";
import StudentsUpload from "./students-upload";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";

class StudentUploadWrapper extends React.Component {
  handleStudentUploadSubmit = data => {
    Meteor.call(
      "RosterImports:insertStudentRoster",
      {
        data: { ...data, source: "CSV-Students" },
        orgid: this.props.studentGroup.orgid,
        siteId: this.props.studentGroup.siteId,
        studentGroupId: this.props.studentGroup._id
      },
      err => {
        if (err) {
          decWaitingOn();
        } else {
          decWaitingOn();
          this.props.history.push(
            `/data-admin/manage-group/students/${this.props.studentGroup.orgid}/site/${this.props.studentGroup.siteId}/${this.props.studentGroup._id}`
          );
        }
      }
    );
    incWaitingOn(1, "Inserting into the db!");
  };

  render() {
    return this.props.loading ? (
      <Loading />
    ) : (
      <StudentsUpload
        studentGroup={this.props.studentGroup}
        uploadStudents={this.handleStudentUploadSubmit}
        isNewGroup
      />
    );
  }
}

export default withTracker(props => {
  const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", props.orgid);
  const loading = !studentGroupsHandler.ready();
  let studentGroup = {};
  if (!loading) {
    studentGroup = StudentGroups.findOne(props.studentGroupId);
  }
  return { studentGroup, loading };
})(withRouter(StudentUploadWrapper));

StudentUploadWrapper.propTypes = {
  loading: PropTypes.bool.isRequired,
  studentGroup: PropTypes.object,
  history: PropTypes.object
};
