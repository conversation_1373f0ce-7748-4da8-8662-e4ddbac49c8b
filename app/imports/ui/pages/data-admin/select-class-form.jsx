import React from "react";
import PropTypes from "prop-types";
import { DropdownButton, Dropdown, ButtonGroup } from "react-bootstrap";

const gradesSortOrder = {
  K: 1,
  "01": 2,
  "02": 3,
  "03": 4,
  "04": 5,
  "05": 6,
  "06": 7,
  "07": 8,
  "08": 9,
  HS: 10
};

const SelectClassForm = ({
  studentGroups,
  currentStudentGroup,
  teacherType,
  onSelectCurrentStudentGroup,
  onSelectTeacherType
}) => {
  const renderStudentGroups = () =>
    studentGroups
      .sort((a, b) => gradesSortOrder[a.grade] - gradesSortOrder[b.grade])
      .map(group => (
        <Dropdown.Item eventKey={group._id} key={group._id}>
          {group.name}
        </Dropdown.Item>
      ));

  const selectStudentGroup = selectedStudentGroupId => {
    onSelectCurrentStudentGroup(studentGroups.find(group => group._id === selectedStudentGroupId));
  };

  const selectTeacherType = selectedTeacherType => {
    onSelectTeacherType(selectedTeacherType);
  };

  return (
    <div>
      <h3>Select Class</h3>
      <div className="row mb-1">
        <div className="col-sm-4">
          <small>Class Name</small>
          <br />
          <ButtonGroup className="d-grid">
            <DropdownButton
              variant="default"
              size="sm"
              title={currentStudentGroup.name}
              className="d-grid no-caret"
              align="end"
              id="selectClass"
              onSelect={selectStudentGroup}
            >
              {renderStudentGroups()}
            </DropdownButton>
          </ButtonGroup>
        </div>
        <div className="col-sm-3">
          <small>Section ID</small>
          <br />
          <input
            disabled
            value={currentStudentGroup.sectionId}
            className="form-control input-sm"
            placeholder="Section ID"
          />
        </div>
        <div className="col-sm-2">
          <small>Grade</small>
          <br />
          <input disabled value={currentStudentGroup.grade} className="form-control input-sm" placeholder="Grade" />
        </div>
        <div className="col-sm-3">
          <small>Primary or Secondary</small>
          <br />
          <ButtonGroup className="d-grid">
            <DropdownButton
              variant="default"
              size="sm"
              title={teacherType || "Select teacher type"}
              className="d-grid no-caret"
              align="end"
              id="selectClass"
              onSelect={selectTeacherType}
            >
              <Dropdown.Item eventKey="Primary">Primary</Dropdown.Item>
              <Dropdown.Item eventKey="Secondary">Secondary</Dropdown.Item>
            </DropdownButton>
          </ButtonGroup>
        </div>
      </div>
    </div>
  );
};

SelectClassForm.propTypes = {
  currentStudentGroup: PropTypes.object.isRequired,
  studentGroups: PropTypes.array.isRequired,
  teacherType: PropTypes.string,
  onSelectTeacherType: PropTypes.func.isRequired,
  onSelectCurrentStudentGroup: PropTypes.func.isRequired
};

export default SelectClassForm;
