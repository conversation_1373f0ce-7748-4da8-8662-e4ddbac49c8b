import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { groupBy, get } from "lodash";
import Alert from "react-s-alert";
import Select from "react-select";
import { Link } from "react-router-dom";

import ManageStudentsRow from "./manage-students-row";
import Loading from "../../components/loading";
import Header from "../../components/admin-view/admin-header";
import { isSingleSchoolDataAdmin } from "./utilities";

export default class SearchStudents extends Component {
  constructor(props) {
    super(props);
    this.state = {
      lastName: "",
      firstName: "",
      localId: "",
      stateId: "",
      foundStudents: [],
      studentEnrollments: [],
      studentGroups: [],
      isSearching: false,
      selectedSites: [],
      availableSites: []
    };
  }

  componentDidMount() {
    // Fetch only when in org search
    if (!this.props.siteId) {
      Meteor.call("Sites:getSitesInOrg", this.props.orgid, (err, sites) => {
        this.setState({
          availableSites: sites.map(s => ({
            label: s.name,
            value: s._id
          }))
        });
      });
    }
  }

  getStudents = () => {
    const { orgid, siteId } = this.props;
    const { firstName, lastName, localId, stateId, selectedSites, availableSites } = this.state;
    const allSiteIdsInOrg = availableSites.map(s => s.value);
    const parsedSiteIds = [siteId, ...selectedSites.map(s => s.value)].filter(f => f);
    const siteIds = !parsedSiteIds.length ? allSiteIdsInOrg : parsedSiteIds;
    Meteor.call(
      "Students:searchStudents",
      { orgid, siteIds, firstName, lastName, localId, stateId },
      (err, results) => {
        if (!err) {
          this.setState({
            foundStudents: results.students,
            studentEnrollments: results.studentEnrollments,
            studentGroups: results.studentGroups,
            isSearching: false
          });
        } else {
          this.setState({ isSearching: false });
          Alert.error(err.message, { timeout: 10000 });
        }
      }
    );
    this.setState({
      isSearching: true,
      foundStudents: [],
      studentEnrollments: [],
      studentGroups: []
    });
  };

  setSearchValue = event => {
    this.setState({ [event.target.name]: event.target.value });
  };

  getStudentData = enrollementStudentId =>
    this.state.foundStudents.find(student => student._id === enrollementStudentId);

  getStudentEnrollment = studentId =>
    this.state.studentEnrollments.find(enrollment => enrollment.studentId === studentId);

  getArchivedStudents = () => this.state.foundStudents.filter(student => !this.getStudentEnrollment(student._id));

  sentenceCase = string => string.charAt(0).toUpperCase() + string.slice(1);

  handleSitesChange = selectedSites => {
    this.setState({ selectedSites });
  };

  getInputField = fieldName => (
    <input
      className="form-control"
      ref={fieldName}
      type="text"
      required=""
      name={fieldName}
      placeholder=""
      data-testid={`student${this.sentenceCase(fieldName)}`}
      value={this.state[fieldName]}
      onChange={this.setSearchValue}
    />
  );

  render() {
    const sitesById = groupBy(this.state.availableSites, "value");
    return (
      <div className="workspace-container">
        <div className="d-flex mt-1">
          {!isSingleSchoolDataAdmin() ? (
            <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${this.props.orgid}`}>
              <i className="fa fa-caret-left" /> Back to All Schools
            </Link>
          ) : null}
          {!this.props.shouldSearchInOrg ? (
            <Link
              className="btn btn-outline-blue btn-xs"
              to={`/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}`}
            >
              <i className="fa fa-caret-left" /> Back to School
            </Link>
          ) : null}
        </div>

        <Header keyLabel="schoolOverview" headerTitle="Student Search" />

        <div className="card-box-wrapper mt-2">
          <div>
            {this.props.shouldSearchInOrg ? (
              <div className="row flex-container p-3">
                <div className="col-12 mb-2">
                  <label>Site(s)</label>
                  <Select
                    id="selectedSites"
                    value={this.state.selectedSites}
                    isMulti
                    isSearchable={true}
                    name={"selectedSites"}
                    options={this.state.availableSites}
                    className="basic-multi-select"
                    classNamePrefix="select"
                    placeholder="Search in all sites or select specific ones"
                    onChange={this.handleSitesChange}
                  />
                </div>
              </div>
            ) : null}
            <div className="row flex-container p-3">
              <div className="col-3">
                <label>Last Name</label>
                {this.getInputField("lastName")}
              </div>
              <div className="col-3">
                <label>First Name</label>
                {this.getInputField("firstName")}
              </div>
              <div className="col-2">
                <label>Local ID</label>
                {this.getInputField("localId")}
              </div>
              <div className="col-2">
                <label>State ID</label>
                {this.getInputField("stateId")}
              </div>
              <div className="col-2 pull-bottom d-grid">
                {this.state.isSearching ? (
                  <Loading inline={true} />
                ) : (
                  <button
                    onClick={this.getStudents}
                    className="btn btn-outline-primary btn-wide btn-sm"
                    data-testid="studentSearchButton"
                  >
                    Search
                  </button>
                )}
              </div>
            </div>
            <div className="main-content mt-2">
              <div className="p-2">
                <table className="table table-condensed">
                  <thead className="">
                    <tr>
                      <th>Name: Last, First</th>
                      <th>Class</th>
                      {this.props.shouldSearchInOrg ? <th>Site</th> : null}
                      <th>Grade</th>
                      <th>Date of Birth</th>
                      <th>Local ID</th>
                      <th>State ID</th>
                    </tr>
                  </thead>
                  <tbody data-testid="student-search-rows" className="scroll-y">
                    {this.state.studentEnrollments.map(enrollment => (
                      <ManageStudentsRow
                        key={enrollment._id}
                        student={this.getStudentData(enrollment.studentId)}
                        studentGroup={this.state.studentGroups.find(
                          studentGroup => studentGroup._id === enrollment.studentGroupId
                        )}
                        showCheckbox={false}
                        siteId={enrollment.siteId}
                        siteName={get(sitesById[enrollment.siteId], "0.label")}
                        orgid={enrollment.orgid}
                        isStudentSearch={true}
                        isActive={enrollment.isActive}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SearchStudents.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  shouldSearchInOrg: PropTypes.bool
};
