import React, { useState, useCallback, useEffect } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { isEmailValid } from "/imports/api/utilities/utilities";

const AddTeacherForm = ({ teacher, onChange, orgid }) => {
  const [errors, setErrors] = useState({
    lastName: null,
    firstName: null,
    localId: null,
    email: null
  });

  const isFormValid = () => Object.values(errors).every(e => e === null || e === "");

  const handleChange = field => e => {
    const { value } = e.target;
    onChange({ [field]: value }, isFormValid());
  };

  const updateIsFormValid = useCallback(() => {
    onChange({}, isFormValid());
  }, [errors, onChange]);

  const setError = (field, error) => {
    setErrors(prevErrors => ({
      ...prevErrors,
      [field]: error
    }));
  };

  useEffect(() => {
    updateIsFormValid();
  }, [errors]);

  const clearError = field => () => {
    setError(field, "");
  };

  const validateLength = field => e => {
    const targetValue = e.target.value;
    if (!targetValue.trim().length) {
      setError(field, `${e.target.placeholder} cannot be empty`);
      return false;
    }
    clearError(field)();
    return true;
  };

  const validateEmail = () => e => {
    const { email } = teacher;
    const emailField = "email";
    if (!validateLength(emailField)(e)) {
      return;
    }
    if (isEmailValid(email)) {
      Meteor.call("checkIfEmailAlreadyExists", email, orgid, (err, resp) => {
        if (resp) {
          const errorMessage = `Email already exists for ${resp.profile.name.last}, ${resp.profile.name.first}${
            resp.isArchived ? ", \n  Use the manage class tab to unarchive this teacher." : ""
          }`;
          setError(emailField, errorMessage);
        } else {
          clearError(emailField)();
        }
      });
    } else {
      setError(emailField, "Email is not valid");
    }
  };

  const renderErrorMessage = field => {
    const error = errors[field];
    if (error?.length) {
      return <span className="help-block help-block-override text-danger animated bounceIn">{error}</span>;
    }
    return null;
  };

  const firstNameField = "firstName";
  const lastNameField = "lastName";
  const localIdField = "localId";
  const emailField = "email";

  return (
    <div>
      <h4>Add Teacher</h4>
      <div className="row mb-1">
        <div className="col-sm-4">
          <small>Last Name</small>
          <br />
          <input
            onChange={handleChange(lastNameField)}
            value={teacher.lastName}
            onFocus={clearError(lastNameField)}
            onBlur={validateLength(lastNameField)}
            className="form-control input-sm m-b-5"
            placeholder="Last name"
          />
          {renderErrorMessage(lastNameField)}
        </div>
        <div className="col-sm-3">
          <small>First Name</small>
          <br />
          <input
            onChange={handleChange(firstNameField)}
            value={teacher.firstName}
            onFocus={clearError(firstNameField)}
            onBlur={validateLength(firstNameField)}
            className="form-control input-sm m-b-5"
            placeholder="First name"
          />
          {renderErrorMessage(firstNameField)}
        </div>
        <div className="col-sm-2">
          <small>Teacher ID</small>
          <br />
          <input
            onChange={handleChange(localIdField)}
            value={teacher.localId}
            onBlur={validateLength(localIdField)}
            onFocus={clearError(localIdField)}
            className="form-control input-sm m-b-5"
            placeholder="Teacher ID"
          />
          {renderErrorMessage(localIdField)}
        </div>
        <div className="col-sm-3">
          <small>Email</small>
          <br />
          <input
            onChange={handleChange(emailField)}
            value={teacher.email}
            onBlur={validateEmail()}
            onFocus={clearError(emailField)}
            className="form-control input-sm m-b-5"
            placeholder="Email"
          />
          {renderErrorMessage(emailField)}
        </div>
      </div>
    </div>
  );
};

AddTeacherForm.propTypes = {
  teacher: PropTypes.object.isRequired,
  onChange: PropTypes.func,
  orgid: PropTypes.string.isRequired
};

AddTeacherForm.defaultProps = {
  onChange: () => {}
};

export default AddTeacherForm;
