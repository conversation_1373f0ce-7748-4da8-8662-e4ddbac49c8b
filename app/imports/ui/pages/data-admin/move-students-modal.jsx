import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>, ListGroup, ListGroupItem } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import { useHistory } from "react-router-dom";

import { Sites } from "/imports/api/sites/sites";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../routing/AppDataContext";
import { getMeteorUserSync, sortByGradeAndName } from "/imports/api/utilities/utilities";
import { isSingleSchoolDataAdmin } from "./utilities";
import { SiteContext } from "/imports/contexts/SiteContext";

const MoveStudentsModal = ({
  showModal: showModalProp,
  studentGroup,
  students,
  siteId,
  orgid,
  sites,
  schoolYear,
  onCloseModal,
  onMoveStudents
}) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState(siteId || null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [studentGroups, setStudentGroups] = useState([]);

  const history = useHistory();
  const appDataContext = useContext(AppDataContext);
  const { refetchStudentGroupsInSite } = useContext(SiteContext);

  const fetchStudentGroups = siteIdParam => {
    const isManageStudentPage = appDataContext?.routeName === "data-admin-manage-group";

    const studentGroupsQuery = {
      siteId: siteIdParam,
      schoolYear
    };

    if (isManageStudentPage && studentGroup) {
      studentGroupsQuery._id = { $ne: studentGroup._id };
    }

    return StudentGroups.find(studentGroupsQuery)
      .fetch()
      .sort(sortByGradeAndName);
  };

  useEffect(() => {
    if (selectedSchool) {
      const groups = fetchStudentGroups(selectedSchool);
      setStudentGroups(groups);
    }
  }, [selectedSchool, schoolYear, studentGroup, appDataContext?.routeName]);

  useEffect(() => {
    if (showModalProp) {
      setShowModal(true);
    }
  }, [showModalProp]);

  const close = () => {
    setShowModal(false);
    onCloseModal();
  };

  const selectSchool = e => {
    const selectedSchoolValue = e.currentTarget.value;
    const groups = fetchStudentGroups(selectedSchoolValue);

    setSelectedSchool(selectedSchoolValue);
    setStudentGroups(groups);
    setSelectedGroup(null);
  };

  const selectGroup = e => {
    setSelectedGroup(e.currentTarget.value);
  };

  const moveStudents = () => {
    const isUnarchiveStudentPage = appDataContext?.routeName === "data-admin-unarchive";
    Meteor.call(
      "moveStudentsBetweenGroups",
      {
        students,
        previousGroupId: isUnarchiveStudentPage ? "" : studentGroup?._id,
        nextGroupId: selectedGroup,
        isUnarchiveOperation: isUnarchiveStudentPage
      },
      (error, result) => {
        refetchStudentGroupsInSite();
        const { wasGroupDeactivated } = result || {};
        if (wasGroupDeactivated) {
          history.push(`/data-admin/manage-group/students/${orgid}/site/${siteId}`);
        }
        if (error) {
          console.log(error);
          Alert.error("There was a problem while moving students", {
            timeout: 3000
          });
        } else {
          Alert.success("Students have been moved successfully", {
            timeout: 3000
          });
        }
        onMoveStudents();
      }
    );
    close();
  };

  return (
    <Modal
      show={showModal}
      size="lg"
      onHide={close}
      dialogClassName="move-students-modal"
      data-testid="moveStudentsModal"
      backdrop="static"
    >
      <ModalHeader>
        <h3 className="w9">Move Students</h3>
      </ModalHeader>

      <ModalBody>
        <div className="row small p-2">
          <div className="col-5">Student</div>
          <div className="col-2">Grade</div>
          <div className="col-3">Date of Birth</div>
          <div className="col-2">Local ID</div>
        </div>
        <ListGroup className="student-list max-h-30">
          {students.map(student => (
            <ListGroupItem key={student._id} className="p-2">
              <div className="row small">
                <div className="col-5">
                  <b>
                    {student.identity.name.lastName}, {student.identity.name.firstName}
                  </b>
                </div>
                <div className="col-2">{student.grade}</div>
                <div className="col-3">{student.demographic.birthDate || ""}</div>
                <div className="col-2">{student.identity.identification.localId}</div>
              </div>
            </ListGroupItem>
          ))}
        </ListGroup>

        <h5>Select the Class/Group to move these students to.</h5>

        {!isSingleSchoolDataAdmin() ? (
          <div className="row p-2">
            <div className="col-12 d-flex align-items-center">
              <label className="control-label me-2 small">School</label>
              <select className="form-select" value={selectedSchool} onChange={selectSchool}>
                {sites.map(site => (
                  <option key={site._id} value={site._id}>
                    {site.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        ) : null}

        <div className="form-group">
          <ListGroup className="student-group-list max-h-30">
            {studentGroups.map(({ _id, grade, name }) => (
              <ListGroupItem key={_id} className="p-2 small">
                <label>
                  <input
                    type="radio"
                    className="me-2"
                    name="selectedGroup"
                    value={_id}
                    data-testid={name}
                    onChange={selectGroup}
                  />
                  {name} <small>Grade: {grade}</small>
                </label>
              </ListGroupItem>
            ))}
          </ListGroup>
        </div>
      </ModalBody>

      <ModalFooter className="d-flex justify-content-center">
        <Button variant="success" onClick={moveStudents} disabled={!selectedGroup} data-testid="submitMoveStudents">
          Move Students
        </Button>
        <Button variant="default" onClick={close}>
          Cancel
        </Button>
      </ModalFooter>
    </Modal>
  );
};

MoveStudentsModal.propTypes = {
  showModal: PropTypes.bool,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  sites: PropTypes.array,
  schoolYear: PropTypes.number,
  onCloseModal: PropTypes.func,
  onMoveStudents: PropTypes.func
};

MoveStudentsModal.defaultProps = {
  onCloseModal: () => {},
  onMoveStudents: () => {}
};

const MoveStudentsModalWithTracker = props => {
  const { orgid, siteId, schoolYear: contextSchoolYear } = props;
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(contextSchoolYear);

  useEffect(() => {
    if (!contextSchoolYear) {
      const user = getMeteorUserSync() || {};
      utils.getCurrentSchoolYear(user, orgid).then(year => {
        setResolvedSchoolYear(year);
      });
    }
  }, [contextSchoolYear, orgid]);

  const { sites, loading } = useTracker(() => {
    const sitesHandler = Meteor.subscribe("Sites", orgid, siteId);
    const isLoading = !sitesHandler.ready();

    const sitesData =
      isLoading || !resolvedSchoolYear
        ? []
        : Sites.find({ schoolYear: resolvedSchoolYear }, { fields: { name: 1 } }).fetch();

    return {
      sites: sitesData,
      loading: isLoading
    };
  }, [orgid, siteId, resolvedSchoolYear]);

  if (loading || !resolvedSchoolYear) {
    return null; // or a loading spinner
  }

  return <MoveStudentsModal {...props} sites={sites} schoolYear={resolvedSchoolYear} orgid={orgid} />;
};

MoveStudentsModalWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  schoolYear: PropTypes.number
};

export default MoveStudentsModalWithTracker;
