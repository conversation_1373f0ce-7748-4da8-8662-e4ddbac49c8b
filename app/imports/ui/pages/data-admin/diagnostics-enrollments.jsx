import React, { useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";

import { Loading } from "/imports/ui/components/loading";

export function DiagnosticsEnrollments({ fetchFunction, id }) {
  const [enrollments, setEnrollments] = useState(null);
  const [isFetchingEnrollments, setIsFetchingEnrollments] = useState(false);

  const fetchEnrollments = () => {
    setIsFetchingEnrollments(true);
    fetchFunction(id, (data = []) => {
      setIsFetchingEnrollments(false);
      setEnrollments(data);
    });
  };

  const renderEnrollments = () => {
    if (isFetchingEnrollments) {
      return (
        <div className="text-center">
          <Loading inline message="Fetching enrollments..." />
        </div>
      );
    }

    if (!enrollments) {
      return (
        <div className="text-center">
          <Button onClick={fetchEnrollments}>Get Enrollments</Button>
        </div>
      );
    }

    if (enrollments.length) {
      return (
        <div>
          <h5>Enrollments API Data:</h5>
          <textarea disabled className="form-control mt-1" rows={20} value={JSON.stringify(enrollments, null, 4)} />
        </div>
      );
    }

    return (
      <div>
        <h5>Enrollments API Data:</h5>
        <div className="alert alert-info mt-1 text-center">No Enrollments found</div>
      </div>
    );
  };

  return renderEnrollments();
}

DiagnosticsEnrollments.propTypes = {
  fetchFunction: PropTypes.func,
  id: PropTypes.string
};
