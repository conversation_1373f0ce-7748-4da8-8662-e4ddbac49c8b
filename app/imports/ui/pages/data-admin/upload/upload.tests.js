import { assert } from "chai";
import { shallow } from "enzyme";
import sinon from "sinon";
import React from "react";
import <PERSON> from "papaparse";
import { LoadingCounter } from "/imports/api/loadingCounter/loadingCounter";

import { RosterImportItems } from "/imports/api/rosterImportItems/rosterImportItems";

import Upload from "./upload.jsx";

describe("Upload UI", () => {
  let papaParseStub;
  let loadingCounterStub;
  // let fileUploadLineItemsStub;
  beforeEach(async () => {
    if (typeof LoadingCounter.findOneAsync.restore === "function") {
      await LoadingCounter.findOneAsync.restore();
    }
    if (typeof Papa.parse.restore === "function") {
      Papa.parse.restore();
    }
    if (typeof RosterImportItems.validate.restore === "function") {
      RosterImportItems.validate.restore();
    }
    // fileUploadLineItemsStub = sinon.stub(RosterImportItems, "validate");
    loadingCounterStub = sinon.stub(LoadingCounter, "findOneAsync").callsFake(() => ({}));
    papaParseStub = sinon.stub(Papa, "parse");
  });
  afterAll(() => {
    if (papaParseStub) {
      papaParseStub.resetBehavior();
    }
    if (loadingCounterStub) {
      loadingCounterStub.restore();
    }
  });
  describe("Render", () => {
    it("renders", () => {
      // Verify that the method does what we expected
      const uploadComponent = shallow(<Upload />);
      assert.isDefined(uploadComponent, "uploadComponent did not render");
    });
    describe("Results Area", () => {
      it("does not display result area when no files are dropped", () => {
        const uploadComponent = shallow(<Upload />);
        const resultsArea = uploadComponent.find("#resultsArea");
        assert.equal(resultsArea.length, 0);
      });
    });
  });
  // TODO get rid of "skip"
  // describe("OnDrop", () => {
  //   let uploadComponent;
  //   let dropEvent;
  //   const file = {};
  //   let results = {};
  //   beforeEach(
  //     inFiber(() => {
  //       uploadComponent = mount(<Upload />);
  //       dropEvent = {
  //         // preventDefault: () => true,
  //         // dataTransfer: {
  //         //   files: [{
  //         //     preview: 'blob:http%3A//localhost%3A3000/f40b9ccb-68fc-4672-aa02-d826aec0262e',
  //         //   }],
  //         // },
  //       };
  //       papaParseStub.callsFake((files, opts) => {
  //         // run the completion function
  //         opts.complete(results, file);
  //         return true;
  //       });
  //       results = {
  //         meta: { fields: "fields!" },
  //         data: "data!",
  //         fileName: "fileName!",
  //         errors: ["error!"]
  //       };
  //     })
  //   );
  //   it(
  //     "calls onDrop",
  //     inFiber(() => {
  //       // don't do anything else here, otherwise stub out validation
  //       papaParseStub.callsFake(() => true);
  //       uploadComponent.find("#dz1").simulate("drop", dropEvent);
  //       assert.isTrue(papaParseStub.called, "papa parse was not called only once, or was not called at all");
  //     })
  //   );
  //   it(
  //     "displays no parsable data message if there is no data and no errors",
  //     inFiber(() => {
  //       results = {
  //         meta: { fields: "fields!" },
  //         data: [],
  //         fileName: "fileName!",
  //         errors: []
  //       };
  //       uploadComponent.find("#dz1").simulate("drop", dropEvent);
  //       const resultsArea = uploadComponent.find("#resultsArea");
  //       const dataWarningMessage = resultsArea.find("p");
  //       assert.include(dataWarningMessage.text(), "The upload tool found no parsable data.");
  //     })
  //   );
  //   // TODO get rid of "skip"
  //   describe("displays errors in results area", () => {
  //     describe("when csv column headers are correct", () => {
  //       beforeEach(() => {
  //         const lineItemObj = RosterImportItemsHelpers.generateObj();
  //         results = {
  //           meta: { fields: Object.getOwnPropertyNames(lineItemObj.data) },
  //           data: ["data!"],
  //           fileName: "fileName!"
  //         };
  //       });
  //       each(range(1, 11), i => {
  //         it(`should display ${i} error(s) if the parse resulted in ${i} errors`, done => {
  //           const errorList = map(range(1, i + 1), j => ({
  //             message: `error ${j}`,
  //             row: j
  //           }));
  //           results.errors = errorList;
  //           uploadComponent.find("#dz1").simulate("drop", dropEvent);
  //           setTimeout(() => {
  //             const resultsArea = uploadComponent.find("#resultsArea");
  //             const errors = resultsArea.find(".alert-danger");
  //             assert.equal(errors.nodes.length, i);
  //             done();
  //           });
  //         });
  //       });
  //     });
  //   });
  //   // TODO get rid of "skip"
  //   describe("Completed with data and no errors", () => {
  //     it("has a resultsArea div with a congratulations message (h2)", done => {
  //       let congratulationsHeader;
  //       fileUploadLineItemsStub.callsFake(() => true);
  //       const lineItemObj = RosterImportItemsHelpers.generateObj();
  //       results = {
  //         meta: { fields: Object.getOwnPropertyNames(lineItemObj.data) },
  //         data: ["data1!"],
  //         fileName: "fileName!",
  //         errors: []
  //       };
  //       uploadComponent.find("#dz1").simulate("drop", dropEvent);
  //       setTimeout(() => {
  //         const resultsArea = uploadComponent.find("#resultsArea");
  //         congratulationsHeader = resultsArea.find("#congratulationsHeader");
  //         assert.include(congratulationsHeader.text(), "Congratulations!");
  //         done();
  //       }, 200);
  //     });
  //   });
  // });
});
