import Papa from "papaparse";

export function getCSV(data) {
  return encodeURIComponent(Papa.unparse(data, { delimiter: ",", newline: "\r\n" }));
}

export const requiredFieldsExample = {
  DistrictID: "3999",
  DistrictName: "Anytown ISD",
  SchoolID: "0158",
  SchoolName: "Sunny Slope Elementary",
  TeacherID: "1457",
  TeacherLastName: "Forest",
  TeacherFirstName: "Jeanette E",
  TeacherEmail: "<EMAIL>",
  ClassName: "BEHAVIORS 2",
  ClassSectionID: 2,
  StudentLocalID: "685123",
  StudentStateID: "3999000685555",
  StudentLastName: "Anderson",
  StudentFirstName: "Gail",
  StudentBirthDate: "2005-01-03",
  SpringMathGrade: "2"
};

export const assessmentScoresUploadRequiredFieldsExample = {
  StudentLocalID: "312",
  StudentStateID: "5432",
  StudentLastName: "<PERSON>",
  StudentFirstName: "Gail",
  AssessmentYear: "2020",
  StateAssessmentName: "Assessment 1",
  StateAssessmentScaleScore: "624",
  StateAssessmentProficient: "Yes",
  StateAssessmentPercentileScore: "50",
  DistrictAssessmentName: "District Assessment 1",
  DistrictAssessmentFallScaleScore: "655",
  DistrictAssessmentFallProficient: "Yes",
  DistrictAssessmentSpringScaleScore: "111",
  DistrictAssessmentSpringProficient: "No"
};

const textAreaLineBreak = "\n--------------------------------------------------------------------\n";

function getMissingGroupsErrorText(groupErrors) {
  return groupErrors.missingStudentGroups
    .map(
      (missingGroup, index) =>
        `${index + 1}.\tClass Section ID: ${missingGroup.classSectionID}\n\tSchool ID: ${
          missingGroup.schoolID
        }\n\tDistrict ID: ${missingGroup.districtID}\n\tSpringMath Grade: ${
          missingGroup.springMathGrade
        }\n\tClass Name: ${missingGroup.className}\n`
    )
    .join("\n");
}

function getChangingGradeErrorText(groupErrors) {
  return groupErrors.groupsChangingGrade
    .map(
      (changingGradeGroup, index) =>
        `${index + 1}.\t${changingGradeGroup.className} (classSectionID: ${
          changingGradeGroup.classSectionID
        } in School: ${changingGradeGroup.schoolID}) changed SpringMath Grade from: ${
          changingGradeGroup.originalSpringMathGrade
        } to: ${changingGradeGroup.newSpringMathGrade}\n`
    )
    .join("\n");
}

function getMissingGroupError(groupErrors) {
  return [
    "It appears that your roster is missing the class that is currently active in the SpringMath database:\n\n",
    getMissingGroupsErrorText(groupErrors),
    "\nImporting this roster would archive existing classrooms."
  ];
}

function getChangingGradeError(shouldAddLineBreak, groupErrors) {
  return [
    shouldAddLineBreak ? textAreaLineBreak : "",
    "\nIt appears that your roster is attempting to change grade of the class that is currently active in the SpringMath database:\n\n",
    getChangingGradeErrorText(groupErrors),
    "\nThis cannot be achieved via file import. To fix this you can bring back the original group's grade or you can use Manage Class functionality in Data Admin panel to change the group's grade."
  ];
}

export function getFormattedStudentGroupErrors(groupErrors) {
  let errors = "";
  if (groupErrors.missingStudentGroups && groupErrors.missingStudentGroups.length) {
    errors = errors.concat(...getMissingGroupError(groupErrors));
  }
  if (groupErrors.groupsChangingGrade && groupErrors.groupsChangingGrade.length) {
    const shouldAddLineBreak = errors.length ? textAreaLineBreak : "";
    errors = errors.concat(...getChangingGradeError(shouldAddLineBreak, groupErrors));
  }
  return errors;
}
