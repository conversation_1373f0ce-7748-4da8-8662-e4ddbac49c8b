import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { get } from "lodash";
import Select from "react-select";

import Loading from "../../components/loading";
import { GradeTranslations } from "./grade-translations";
import { UserRoleIdentifiers } from "./user-role-identifiers";

export class RosterSettings extends Component {
  getRosteringTypeName() {
    const rosteringLabels = {
      rosterEdFi: "Ed-Fi",
      rosterOR: "OneRoster"
    };

    return rosteringLabels[get(this.props, "rosteringType", "")] || "rostering";
  }

  state = {
    apiUrl: get(this.props, "rosteringSettings.apiUrl", ""),
    authUrl: get(this.props, "rosteringSettings.authUrl", ""),
    clientId: get(this.props, "rosteringSettings.clientId", ""),
    clientSecret: get(this.props, "rosteringSettings.clientSecret", ""),
    shouldUseScopes: get(this.props, "rosteringSettings.shouldUseScopes", false),
    shouldUseSequentialRequests: get(this.props, "rosteringSettings.shouldUseSequentialRequests", false),
    limit: {
      value: get(this.props, "rosteringSettings.limit", 500),
      label: get(this.props, "rosteringSettings.limit", 500)
    },
    wasLimitChanged: false,
    schoolYear: get(this.props, "rosteringSettings.schoolYear", ""),
    shouldUseYearSpecificMode: get(this.props, "rosteringSettings.schoolYear", "").length > 0,
    testConnectionResult: null,
    isFetching: false
  };

  messageMap = {
    200: {
      message: `You were successfully connected to provided ${this.getRosteringTypeName()} endpoint.`,
      type: "success"
    },
    404: {
      message: "There was a problem connecting to the provided URL. Please check the API URL and try again."
    },
    500: {
      message: `The provided instance of ${this.getRosteringTypeName()} appears to be having technical difficulties. Please try again later.`
    },
    400: {
      message: `There was a problem connecting to the ${this.getRosteringTypeName()} server. The credentials might be wrong. Please verify the Client ID and Client Secret and try again.`
    },
    401: {
      message: `There was a problem connecting to the ${this.getRosteringTypeName()} server. The credentials might be wrong. Please verify the Client ID and Client Secret and try again.`
    },
    403: {
      message: `SpringMath was able to connect to ${this.getRosteringTypeName()}.  However, it was not able to access the needed ${this.getRosteringTypeName()} endpoint. Check the permissions for the provided Client Id in ${this.getRosteringTypeName()} and try again.`
    },
    0: {
      message: `SpringMath is not able to reach out to ${this.getRosteringTypeName()} at this time due to an internal server error. Please check your API URL and credentials and try again.`
    },
    1: {
      message: `SpringMath connected to the provided ${this.getRosteringTypeName()} connection. However, there is no data available in ${this.getRosteringTypeName()}. If this is expected this warning can be ignored.`,
      type: "warning"
    },
    default: {
      message: `There was an error connecting to ${this.getRosteringTypeName()}. Please check the API URL, Client Id, and Client Secret and try again.`
    }
  };

  componentDidUpdate(prevProps) {
    if (!prevProps.rosteringSettings && this.props.rosteringSettings) {
      const { apiUrl, clientId, clientSecret } = this.props.rosteringSettings;
      this.setState({ apiUrl, clientId, clientSecret });
    }
  }

  handleInputChange = event => {
    if (typeof event?.value === "number") {
      this.setState({ limit: event, wasLimitChanged: true });
    } else {
      const { target } = event;
      const value = target.type === "checkbox" ? target.checked : target.value;
      if (target.name === "shouldUseSequentialRequests" && !this.state.wasLimitChanged) {
        const limitValue = target.checked ? 5000 : 500;
        this.setState({ [target.name]: value, limit: { value: limitValue, label: limitValue } });
      } else {
        this.setState({ [target.name]: value });
      }
    }
  };

  handleMessage = ({ err, res }) => {
    const statusCode = err ? err.error : res.statusCode;
    const { message, type = "error" } = this.messageMap[statusCode] || this.messageMap.default;
    this.setState({
      testConnectionResult: { type, message }
    });
  };

  saveRosteringSettings = event => {
    event.preventDefault();
    const { orgid } = this.props;
    const {
      apiUrl,
      authUrl,
      clientId,
      clientSecret,
      shouldUseScopes,
      shouldUseSequentialRequests,
      limit,
      schoolYear: inputSchoolYear,
      shouldUseYearSpecificMode
    } = this.state;
    const schoolYear = shouldUseYearSpecificMode ? inputSchoolYear : "";
    this.setState({ isFetching: true, testConnectionResult: null });
    Meteor.call(
      "Organizations:saveRosteringSettings",
      {
        orgid,
        apiUrl,
        authUrl,
        clientId,
        clientSecret,
        shouldUseScopes,
        shouldUseSequentialRequests,
        limit: limit.value,
        schoolYear
      },
      (err, res) => {
        this.setState({ isFetching: false, wasLimitChanged: false });
        if (res) {
          this.handleMessage({ err, res });
          if (res.saveResult) {
            Alert.success("Rostering settings saved successfully.", {
              timeout: 3000
            });
          } else {
            Alert.error("Organization with provided id not found.", {
              timeout: 3000
            });
          }
        } else if (err.details === "external") {
          this.handleMessage({ err, res });
        } else {
          this.handleMessage({ res: { statusCode: 200 } });
          Alert.error(`Error saving rostering settings! ${err.message}`);
        }
      }
    );
  };

  testConnection = event => {
    event.preventDefault();
    const {
      apiUrl,
      clientId,
      clientSecret,
      authUrl,
      shouldUseScopes,
      schoolYear: inputSchoolYear,
      shouldUseYearSpecificMode
    } = this.state;
    const schoolYear = shouldUseYearSpecificMode ? inputSchoolYear : "";
    const { orgid } = this.props;
    this.setState({ isFetching: true, testConnectionResult: null });
    Meteor.call(
      "testExternalRosteringAPIConnection",
      { orgid, apiUrl, authUrl, clientId, clientSecret, shouldUseScopes, schoolYear },
      (err, res) => {
        this.setState({ isFetching: false });
        if (res) {
          this.handleMessage({ err, res });
        } else if (err.details === "external") {
          this.handleMessage({ err, res });
        } else {
          Alert.error(err.message);
        }
      }
    );
  };

  renderTestConnectionResult = ({ message, type }) => {
    const alertType = type === "error" ? "danger" : type;
    return <div className={`alert alert-${alertType}`}>{message}</div>;
  };

  render() {
    const {
      clientSecret,
      isFetching,
      clientId,
      shouldUseScopes,
      shouldUseSequentialRequests,
      limit,
      schoolYear,
      shouldUseYearSpecificMode,
      testConnectionResult,
      apiUrl,
      authUrl
    } = this.state;

    return (
      <React.Fragment>
        <form className="form-horizontal">
          <div className="align-items-center d-flex form-group">
            <label className="col-sm-3 text-end">API Url:</label>
            <div className="col-sm-9">
              <input
                type="text"
                onChange={this.handleInputChange}
                required="required"
                name="apiUrl"
                value={apiUrl}
                className="form-control"
              />
            </div>
          </div>
          <div className="align-items-center d-flex form-group">
            <label className="col-sm-3 text-end">Custom OAuth Url (optional): </label>
            <div className="col-sm-9">
              <input
                type="text"
                onChange={this.handleInputChange}
                required="required"
                name="authUrl"
                value={authUrl}
                className="form-control"
              />
            </div>
          </div>
          <div className="align-items-center d-flex form-group">
            <label className="col-sm-3 text-end">Client ID:</label>
            <div className="col-sm-9">
              <input
                type="text"
                onChange={this.handleInputChange}
                required="required"
                name="clientId"
                value={clientId}
                className="form-control"
              />
            </div>
          </div>
          <div className="align-items-center d-flex form-group">
            <label className="col-sm-3 text-end">Client Secret:</label>
            <div className="col-sm-9">
              <input
                type="password"
                onChange={this.handleInputChange}
                required="required"
                name="clientSecret"
                value={clientSecret}
                className="form-control"
              />
            </div>
          </div>
          {this.props.rosteringType === "rosterOR" && (
            <React.Fragment>
              <div className="align-items-center d-flex form-group">
                <label className="col-sm-3 text-end">Use API Scopes (optional):</label>
                <div className="col-sm-1">
                  <input
                    className="checkbox-md"
                    type="checkbox"
                    onChange={this.handleInputChange}
                    value={shouldUseScopes}
                    defaultChecked={shouldUseScopes}
                    name="shouldUseScopes"
                  />
                </div>
                <label className="col-sm-3 text-end">Use Sequential Requests: </label>
                <div className="col-sm-1">
                  <input
                    className="checkbox-md"
                    type="checkbox"
                    onChange={this.handleInputChange}
                    value={shouldUseSequentialRequests}
                    defaultChecked={shouldUseSequentialRequests}
                    name="shouldUseSequentialRequests"
                  />
                </div>
                <label className="col-sm-2 text-end">Limit: </label>
                <Select
                  className="col-sm-2 text-start react-select-container"
                  classNamePrefix="react-select"
                  defaultValue={limit?.value ? limit : { value: 500, label: 500 }}
                  isClearable={false}
                  isSearchable={false}
                  name="limit"
                  value={limit}
                  options={[
                    { value: 500, label: 500 },
                    { value: 1000, label: 1000 },
                    { value: 5000, label: 5000 },
                    { value: 10000, label: 10000 },
                    { value: 20000, label: 20000 },
                    { value: 50000, label: 50000 }
                  ]}
                  onChange={this.handleInputChange}
                />
              </div>
            </React.Fragment>
          )}

          {this.props.rosteringType === "rosterEdFi" && (
            <React.Fragment>
              <div className="align-items-center d-flex form-group">
                <label className="col-sm-3 text-end">Use Year-Specific Mode:</label>
                <div className="col-sm-9">
                  <input
                    className="checkbox-md"
                    type="checkbox"
                    onChange={this.handleInputChange}
                    value={shouldUseYearSpecificMode}
                    defaultChecked={shouldUseYearSpecificMode}
                    name="shouldUseYearSpecificMode"
                  />
                </div>
              </div>
              {shouldUseYearSpecificMode && (
                <div className="align-items-center d-flex form-group">
                  <label className="col-sm-3 text-end">School Year:</label>
                  <div className="col-sm-9">
                    <input
                      type="text"
                      onChange={this.handleInputChange}
                      required="required"
                      name="schoolYear"
                      value={schoolYear}
                      className="form-control"
                    />
                  </div>
                </div>
              )}
            </React.Fragment>
          )}
          <div className="form-group">
            {isFetching ? (
              <Loading />
            ) : (
              <div className="row">
                <div className="col-sm-6 d-grid">
                  <button className="btn btn-default" onClick={this.testConnection}>
                    Test Connection
                  </button>
                </div>

                <div className="col-sm-6 d-grid">
                  <button className="btn btn-primary" onClick={this.saveRosteringSettings}>
                    Save
                  </button>
                </div>
              </div>
            )}
          </div>
        </form>
        {testConnectionResult && this.renderTestConnectionResult(testConnectionResult)}

        <hr />

        {this.props.rosteringType === "rosterOR" && (
          <UserRoleIdentifiers
            orgid={this.props.orgid}
            customIdentifiersByRole={this.props.rosteringSettings?.userIdentifiers}
          />
        )}
        <GradeTranslations
          orgid={this.props.orgid}
          customTranslationsByGrade={this.props.rosteringSettings?.translations}
        />
      </React.Fragment>
    );
  }
}

RosterSettings.propTypes = {
  orgid: PropTypes.string,
  rosteringSettings: PropTypes.object,
  rosteringType: PropTypes.string
};
