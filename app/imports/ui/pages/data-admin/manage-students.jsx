import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import _ from "lodash";
import Alert from "react-s-alert";
import { useHistory } from "react-router-dom";

import { Loading } from "/imports/ui/components/loading";
import MoveStudentsModal from "./move-students-modal";
import ConfirmModal from "./confirm-modal";
import AddStudentForm from "./add-student-form";
import StudentsUpload from "./students-upload";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import ManageStudentsRow from "./manage-students-row";
import ManageStudentsButtons from "./manage-students-buttons";
import { AppDataContext } from "../../routing/AppDataContext";
import { isExternalRostering } from "../../utilities";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";
import { UserContext } from "/imports/contexts/UserContext";
import { ROLE_IDS } from "/tests/cypress/support/common/constants";
import { SiteContext } from "/imports/contexts/SiteContext";

// TODO(fmazur) - investigate selectedStudents being undefined when changing sorting
function ManageStudents(props) {
  const history = useHistory();
  const { grades } = useContext(StaticDataContext);
  const { userRoles } = useContext(UserContext);
  const { refetchStudentGroupsInSite } = useContext(SiteContext);
  const [state, setState] = useState({
    selectedStudents: [],
    sortBy: "lastFirst",
    isMoveStudentsModalOpen: false,
    isArchiveStudentsModalOpen: false,
    isAddStudentsFormOpen: false,
    isEditingStudents: false,
    rostering: "rosterUpload"
  });

  useEffect(() => {
    const isSuperAdminOrUniversalDataAdmin = userRoles.find(
      role => role === ROLE_IDS.universalDataAdmin || role === ROLE_IDS.superAdmin
    );
    Meteor.call("Organizations:getOrganizationFieldValues", props.orgid, ["rostering"], (err, resp) => {
      if (!err) {
        if (resp.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin) {
          // SuperAdmin or UniversalDataAdmin needs access to import when organization has blocked roster imports
          setState(prevState => ({ ...prevState, rostering: "rosterImport" }));
        } else {
          setState(prevState => ({ ...prevState, rostering: resp.rostering }));
        }
      }
    });
  }, []);

  useEffect(() => {
    setState(prevState => ({
      ...prevState,
      isAddStudentsFormOpen: !props.students?.length || !props.studentGroup?._id
    }));
  }, [props.students?.length, props.studentGroup?._id, state.sortBy]);

  const moveStudents = () => {
    setState(prevState => ({ ...prevState, isMoveStudentsModalOpen: true }));
  };

  const setEditingStudents = () => {
    setState(prevState => ({ ...prevState, isEditingStudents: true }));
  };

  const cancelEditingStudents = () => {
    setState(prevState => ({ ...prevState, isEditingStudents: false }));
  };

  const closeMoveStudentsModal = () => {
    setState(prevState => ({ ...prevState, isMoveStudentsModalOpen: false }));
  };

  const clearSelectedStudents = () => {
    setState(prevState => ({ ...prevState, selectedStudents: [] }));
  };

  const closeArchiveStudentsModal = () => {
    setState(prevState => ({ ...prevState, isArchiveStudentsModalOpen: false }));
  };

  const archiveStudents = () => {
    Meteor.call(
      "archiveStudents",
      {
        studentIds: state.selectedStudents,
        studentGroupId: props.studentGroup._id
      },
      (error, { wasGroupDeactivated }) => {
        if (error) {
          console.log(`archiveStudents error: ${error}`);
          Alert.error("There was a problem while archiving students", {
            timeout: 3000
          });
        } else {
          refetchStudentGroupsInSite();
          Alert.closeAll();
          Alert.success("Students have been archived successfully", {
            timeout: 3000
          });
        }
        clearSelectedStudents();
        closeArchiveStudentsModal();
        if (wasGroupDeactivated) {
          history.push(`/data-admin/manage-group/students/${props.orgid}/site/${props.siteId}`);
        }
      }
    );
  };

  const openArchiveStudentsModal = () => {
    setState(prevState => ({ ...prevState, isArchiveStudentsModalOpen: true }));
  };

  const showAddStudentForm = () => {
    setState(prevState => ({ ...prevState, isAddStudentsFormOpen: true }));
  };

  const hideAddStudentForm = () => {
    setState(prevState => ({ ...prevState, isAddStudentsFormOpen: false }));
  };

  const showUploadStudents = () => {
    setState(prevState => ({ ...prevState, isUploadStudentsOpen: true }));
  };

  const hideUploadStudents = () => {
    setState(prevState => ({ ...prevState, isUploadStudentsOpen: false }));
  };

  const toggleSelectedStudents = studentId => e => {
    const { selectedStudents = [] } = state;
    if (e.currentTarget.checked) {
      if (!selectedStudents.includes(studentId)) {
        selectedStudents.push(studentId);
      }
    } else {
      _.pull(selectedStudents, studentId);
    }

    setState(prevState => ({ ...prevState, selectedStudents }));
  };

  const toggleSelectAllStudents = () => {
    if (state.selectedStudents?.length === props.students.length) {
      setState(prevState => ({ ...prevState, selectedStudents: [] }));
    } else {
      const allStudentsIds = props.students.map(student => student._id);
      setState(prevState => ({ ...prevState, selectedStudents: allStudentsIds }));
    }
  };

  const toggleSorting = () => {
    setState(prevState => ({
      sortBy: prevState.sortBy === "lastFirst" ? "firstLast" : "lastFirst"
    }));
  };

  const getSortingValue = student =>
    state.sortBy === "lastFirst" ? student.identity.name.lastName : student.identity.name.firstName;

  const handleStudentUploadSubmit = data => {
    Meteor.call(
      "RosterImports:insertStudentRoster",
      {
        data: { ...data, source: "CSV-Students" },
        orgid: props.orgid,
        siteId: props.siteId,
        studentGroupId: props.studentGroup._id
      },
      err => {
        if (err) {
          decWaitingOn();
        } else {
          decWaitingOn();
          hideUploadStudents();
        }
      }
    );
    incWaitingOn(1, "Inserting into the db!");
  };

  const isUnarchivePage = () => props.manageView === "unarchive";

  const renderHeader = () => (
    <thead>
      <tr>
        <th>
          {props.isExternalRostering ? null : (
            <input
              className="me-2"
              type="checkbox"
              checked={state.selectedStudents?.length === props.students.length}
              onChange={toggleSelectAllStudents}
              data-testid="select-all-students-checkbox"
            />
          )}
          Sort by:{" "}
          <u role="button" onClick={toggleSorting}>
            {state.sortBy === "lastFirst" ? "Last, First" : "First, Last"}
          </u>
        </th>
        {isUnarchivePage() ? <th>Most Recent Grade</th> : <th>Student Grade</th>}
        <th>Date of Birth</th>
        <th>Local ID</th>
        <th>State ID</th>
        <th />
      </tr>
    </thead>
  );

  const updateStudentData = studentData => {
    Meteor.call("updateStudentData", props.studentGroup._id, studentData, (err, res) => {
      if (res) {
        Alert.success("Student has been updated successfully", {
          timeout: 3000
        });
      }
      if (err) {
        Alert.error(err.reason || "Error updating the Student", {
          timeout: 3000
        });
      }
    });
  };

  const renderStudents = () =>
    _.sortBy(props.students, getSortingValue).map(student => (
      <ManageStudentsRow
        key={student._id}
        student={student}
        orgid={props.orgid}
        studentGroup={props.studentGroup}
        selected={state.selectedStudents?.includes(student._id)}
        selectStudent={toggleSelectedStudents(student._id)}
        showCheckbox={!props.isExternalRostering}
        isEditingStudents={state.isEditingStudents}
        updateStudentData={updateStudentData}
        isUnarchivePage={isUnarchivePage()}
      />
    ));

  const { loading } = props;
  if (loading) {
    return <Loading />;
  }

  const { manageView, orgid, siteId } = props;
  const {
    selectedStudents = [],
    isUploadStudentsOpen,
    rostering,
    isEditingStudents,
    isAddStudentsFormOpen,
    isArchiveStudentsModalOpen,
    isMoveStudentsModalOpen
  } = state;
  const currentlySelectedStudents = props.students.filter(student => selectedStudents?.includes(student._id));
  const isManageStudentPage = manageView === "students";
  const isOnUnarchivePage = isUnarchivePage();

  return (
    <div data-testid="manage-students-buttons">
      {props.isExternalRostering ? null : (
        <ManageStudentsButtons
          isManageStudentsPage={isManageStudentPage}
          isUploadStudentsOpen={isUploadStudentsOpen}
          selectedStudents={currentlySelectedStudents}
          showUploadStudents={showUploadStudents}
          hideUploadStudents={hideUploadStudents}
          showAddStudentForm={showAddStudentForm}
          openArchiveStudentsModal={openArchiveStudentsModal}
          moveStudents={moveStudents}
          isEditingStudents={isEditingStudents}
          setEditingStudents={setEditingStudents}
          cancelEditingStudents={cancelEditingStudents}
          rostering={rostering}
        />
      )}
      <div className="main-content mt-2">
        <div className="p-3">
          {isUploadStudentsOpen ? (
            <StudentsUpload studentGroup={props.studentGroup} uploadStudents={handleStudentUploadSubmit} />
          ) : (
            <table className="table table-condensed">
              {renderHeader()}
              <tbody>
                {!isExternalRostering(state.rostering) && !isOnUnarchivePage && isAddStudentsFormOpen ? (
                  <AddStudentForm studentGroup={props.studentGroup} onCancel={hideAddStudentForm} grades={grades} />
                ) : null}
                {renderStudents()}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {isMoveStudentsModalOpen && (
        <AppDataContext.Consumer>
          {({ schoolYear }) => (
            <MoveStudentsModal
              showModal={isMoveStudentsModalOpen}
              students={currentlySelectedStudents}
              studentGroup={props.studentGroup}
              orgid={orgid}
              siteId={siteId}
              onCloseModal={closeMoveStudentsModal}
              onMoveStudents={clearSelectedStudents}
              schoolYear={schoolYear}
            />
          )}
        </AppDataContext.Consumer>
      )}

      <ConfirmModal
        showModal={isArchiveStudentsModalOpen}
        onCloseModal={closeArchiveStudentsModal}
        confirmAction={archiveStudents}
        headerText="Are you sure you want to archive these students?"
        bodytext="The students will be unenrolled from this student group."
        confirmText="Yes, archive students"
      />
    </div>
  );
}

ManageStudents.propTypes = {
  loading: PropTypes.bool,
  isExternalRostering: PropTypes.bool,
  students: PropTypes.array,
  studentGroup: PropTypes.object,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  manageView: PropTypes.string
};

export default ManageStudents;
