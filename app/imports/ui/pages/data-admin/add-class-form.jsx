import get from "lodash/get";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import React, { useContext, useState } from "react";
import { withRouter } from "react-router-dom";
import Alert from "react-s-alert";
import Select from "react-select";
import StudentUploadWrapper from "./add-class-student-upload-wrapper";
import AddTeacherForm from "./add-teacher-form";
import { getTeacherLabel } from "./utilities";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { UserContext } from "/imports/contexts/UserContext";
import { getReactSelectCustomHeightStyle } from "/imports/ui/utilities";
import { ROLE_IDS } from "/tests/cypress/support/common/constants";

const STEPS = {
  addClass: 1,
  uploadStudents: 2
};

const getDefaultGrade = () => {
  return "01";
};

const AddClassForm = props => {
  const { userRoles } = useContext(UserContext);
  const { org } = useContext(OrganizationContext);

  const defaultGrade = getDefaultGrade();
  const [newClass, setNewClass] = useState({
    name: "",
    sectionId: "",
    grade: get(props.grades, "[0].display", defaultGrade),
    teacher: ""
  });
  const [newTeacher, setNewTeacher] = useState({ lastName: "", firstName: "", localId: "", email: "" });
  const [isTeacherFormValid, setIsTeacherFormValid] = useState(false);
  const [isNewTeacher, setIsNewTeacher] = useState(false);
  const [isSavingForm, setIsSavingForm] = useState(false);
  const [step, setStep] = useState(STEPS.addClass);
  const [studentGroupId, setStudentGroupId] = useState(null);

  const isAddTeacherFormValid = () => Object.values(newTeacher).every(value => value.trim().length);

  const isAddClassFormValid = () => {
    const isNewClassValid = Object.values(newClass).every(value => value.trim().length);
    return isNewClassValid && (!isNewTeacher || (isNewTeacher && isAddTeacherFormValid() && isTeacherFormValid));
  };

  const resetNewClass = () => {
    setNewClass({ name: "", sectionId: "", grade: getDefaultGrade(), teacher: "" });
  };

  const resetNewTeacher = () => {
    setIsNewTeacher(false);
    setNewTeacher({ lastName: "", firstName: "", localId: "", email: "" });
  };

  const onChangeNewClass = field => e => {
    const updatedNewClass = { ...newClass, [field]: e.target.value };
    setNewClass(updatedNewClass);
  };

  const onChangeTeacher = e => {
    const updatedNewClass = { ...newClass, teacher: e.value };
    const isNewTeacherValue = e.value === "add_new_teacher";
    setIsNewTeacher(isNewTeacherValue);
    setNewClass(updatedNewClass);
    setIsTeacherFormValid(!isNewTeacherValue);
  };

  const goToNextStep = () => {
    setStep(STEPS.uploadStudents);
    resetNewClass();
    resetNewTeacher();
  };

  const goToManageStudentsPage = (newStudentGroupId = studentGroupId) => {
    props.history.push(`/data-admin/manage-group/students/${props.orgid}/site/${props.siteId}/${newStudentGroupId}`);
  };

  const saveNewClass = () => {
    if (!isTeacherFormValid) {
      return Alert.error("Please fix teacher form before proceeding", { timeout: 3000 });
    }
    const { orgid, siteId } = props;
    const studentGroup = {};
    const teacher = {};
    Object.entries(newClass).forEach(([key, value]) => {
      studentGroup[key] = value.trim();
    });
    if (isNewTeacher) {
      Object.entries(newTeacher).forEach(([key, value]) => {
        teacher[key] = value.trim();
      });
    }

    setIsSavingForm(true);

    Meteor.call("addClassToSite", { studentGroup, teacher, orgid, siteId }, (error, result) => {
      if (error) {
        Alert.error(error.reason || "There was a problem while adding a new class", {
          timeout: 5000
        });
        setIsSavingForm(false);
        return;
      }
      Alert.success("Class has been added successfully", {
        timeout: 3000
      });
      setStudentGroupId(result);

      const isSuperAdminOrUniversalDataAdmin =
        userRoles.includes(ROLE_IDS.universalDataAdmin) || userRoles.includes(ROLE_IDS.superAdmin);

      const shouldUseRosterImport =
        org.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin ? true : org.rostering === "rosterImport";

      if (shouldUseRosterImport) {
        goToNextStep();
      } else {
        goToManageStudentsPage(result);
      }

      setIsSavingForm(false);
    });
    return true;
  };

  const onTeacherChange = (teacher, isTeacherFormValidValue = false) => {
    const updatedNewTeacher = { ...newTeacher, ...teacher };
    setNewTeacher(updatedNewTeacher);
    setIsTeacherFormValid(isTeacherFormValidValue);
  };

  const handleCancel = () => {
    props.history.push(`/data-admin/manage-group/students/${props.orgid}/site/${props.siteId}`);
  };

  // Determine rostering display text
  const isSuperAdminOrUniversalDataAdmin =
    userRoles.includes(ROLE_IDS.universalDataAdmin) || userRoles.includes(ROLE_IDS.superAdmin);

  // Always default to showing "Upload" for universalDataAdmin/superAdmin users
  // This ensures the correct display even if org context is not loaded yet
  let shouldUseRosterImport = isSuperAdminOrUniversalDataAdmin;

  // Override based on org rostering setting if available
  if (org?.rostering) {
    shouldUseRosterImport =
      org.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin ? true : org.rostering === "rosterImport";
  }

  return (
    <div className="p-3">
      <div className="text-center">
        <div className="wizard">
          <a className={step === 1 ? "current" : ""}>
            <span className="badge rounded-pill bg-secondary">1</span> Add class & teacher
          </a>
          <a className={step === 2 ? "current" : ""}>
            <span className="badge rounded-pill bg-secondary">2</span>{" "}
            {shouldUseRosterImport ? `Upload` : `Manually add`} students
          </a>
        </div>
      </div>

      {step === STEPS.addClass ? (
        <React.Fragment>
          <div className="row mb-3">
            <div className="col-sm-5">
              <small>Class Name</small>
              <br />
              <input
                onChange={onChangeNewClass("name")}
                value={newClass.name}
                className="form-control form-control-sm"
                placeholder="Class Name"
              />
            </div>
            <div className="col-sm-3">
              <small>Class Section ID</small>
              <br />
              <input
                onChange={onChangeNewClass("sectionId")}
                value={newClass.sectionId}
                className="form-control form-control-sm"
                placeholder="Class Section ID"
              />
            </div>
            <div className="col-sm-1">
              <small>Grade</small>
              <br />
              <select
                id="select-grade"
                className="form-select form-select-sm"
                value={newClass.grade}
                onChange={onChangeNewClass("grade")}
              >
                {props.grades.map(grade => (
                  <option key={grade._id} value={grade.display}>
                    {grade.display}
                  </option>
                ))}
              </select>
            </div>
            <div className="col-sm-3">
              <small>Teacher</small>
              <br />
              <Select
                className="form-control form-select-sm"
                classNamePrefix="react-select"
                value={{
                  value: newClass.teacher,
                  label:
                    newClass.teacher === "add_new_teacher"
                      ? "Add new teacher"
                      : getTeacherLabel(newClass.teacher, props.teachers)
                }}
                id="select-teacher"
                onChange={onChangeTeacher}
                styles={getReactSelectCustomHeightStyle(21)}
                options={[
                  { value: "add_new_teacher", label: "Add new teacher" },
                  ...props.teachers.map(teacher => ({
                    value: teacher._id,
                    label: getTeacherLabel(teacher._id, props.teachers)
                  }))
                ]}
              />
            </div>
          </div>
          {isNewTeacher && (
            <div className="card card-header mb-3">
              <AddTeacherForm teacher={newTeacher} onChange={onTeacherChange} orgid={props.orgid} />
            </div>
          )}
          <fieldset className="form-group">
            <div className="row justify-content-between">
              <div className="col-4">
                <button type="button" className="btn btn-danger form-control" onClick={handleCancel}>
                  Cancel
                </button>
              </div>
              <div className="col-4">
                <button
                  type="submit"
                  className="btn btn-primary form-control"
                  onClick={saveNewClass}
                  disabled={!isAddClassFormValid() || isSavingForm}
                >
                  {isSavingForm ? "Saving..." : "Next"}
                </button>
              </div>
            </div>
          </fieldset>
        </React.Fragment>
      ) : (
        <StudentUploadWrapper orgid={props.orgid} studentGroupId={studentGroupId} />
      )}
    </div>
  );
};

AddClassForm.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  teachers: PropTypes.array,
  grades: PropTypes.array,
  history: PropTypes.object
};

export default withRouter(AddClassForm);
