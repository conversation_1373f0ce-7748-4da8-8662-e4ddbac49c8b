import React, { Component } from "react";
import PropTypes from "prop-types";
import { Form, Row } from "react-bootstrap";
import { Formik } from "formik";
import { get, isEmpty, set, startCase, unset } from "lodash";

import { getDefaultSchoolBreakValues, isDateRangeInvalid } from "/imports/api/utilities/utilities";
import { schoolBreaksSchema } from "/imports/api/organizations/organizations";
import { FieldError } from "./field-error";
import { isDateValid } from "./utilities";

function areDatesValidOrEmpty(date1, date2 = []) {
  return (
    (!date1.month && !date1.day && !date2.month && !date2.day) ||
    (isDateValid(date1.month, date1.day) && isDateValid(date2.month, date2.day))
  );
}

function validateDateRange(startDateObject, endDateObject, errors, errorFieldName) {
  const MIN_RANGE_ERROR = "Minimum of 1 week is required";
  if (isDateRangeInvalid(startDateObject, endDateObject)) {
    // eslint-disable-next-line no-param-reassign
    set(errors, errorFieldName, MIN_RANGE_ERROR);
  } else if (get(errors, errorFieldName) === MIN_RANGE_ERROR) {
    unset(errors, errorFieldName);
    const [schoolBreakName] = errorFieldName.split(".");
    if (isEmpty(errors[schoolBreakName])) {
      // eslint-disable-next-line no-param-reassign
      delete errors[schoolBreakName];
    }
  }
}

export class SchoolBreaksForm extends Component {
  formProps;

  componentDidMount() {
    this.props.setValidateSchoolBreaksFunction(this.validateSchoolBreaks);
  }

  validateSchoolBreaks = (formProps = this.formProps) => {
    const { values } = formProps;

    const winterStart = { month: values.winter.startMonth, day: values.winter.startDay };
    const winterEnd = { month: values.winter.endMonth, day: values.winter.endDay };
    const springStart = { month: values.spring.startMonth, day: values.spring.startDay };
    const springEnd = { month: values.spring.endMonth, day: values.spring.endDay };
    const otherStart = { month: values.other.startMonth, day: values.other.startDay };
    const otherEnd = { month: values.other.endMonth, day: values.other.endDay };

    const areWinterDatesValid = areDatesValidOrEmpty(winterStart, winterEnd);
    const areSpringDatesValid = areDatesValidOrEmpty(springStart, springEnd);
    const areOtherDatesValid = areDatesValidOrEmpty(otherStart, otherEnd);

    validateDateRange(winterStart, winterEnd, formProps.errors, "winter.endDay");
    validateDateRange(springStart, springEnd, formProps.errors, "spring.endDay");
    validateDateRange(otherStart, otherEnd, formProps.errors, "other.endDay");

    const areDatesValid =
      areWinterDatesValid && areSpringDatesValid && areOtherDatesValid && !Object.keys(formProps.errors).length;
    this.props.setAreSchoolBreakDatesValid(areDatesValid);

    return { schoolBreaks: values, areDatesValid };
  };

  renderSchoolBreaksRow = (schoolBreakName, formProps) => {
    const startMonthFieldName = `${schoolBreakName}.startMonth`;
    const startDayFieldName = `${schoolBreakName}.startDay`;
    const endMonthFieldName = `${schoolBreakName}.endMonth`;
    const endDayFieldName = `${schoolBreakName}.endDay`;

    const { values, handleChange, errors } = formProps;

    const startMonthError = get(errors, startMonthFieldName);
    const startDayError = get(errors, startDayFieldName);
    const endMonthError = get(errors, endMonthFieldName);
    const endDayError = get(errors, endDayFieldName);

    return (
      <tr key={schoolBreakName}>
        <td>{startCase(schoolBreakName)} Break</td>
        <td>
          <Form.Group className="position-relative">
            <Form.Control
              type="number"
              size="sm"
              className="d-inline w-25"
              placeholder="Month"
              min="1"
              max="12"
              name={startMonthFieldName}
              value={get(values, startMonthFieldName)}
              onChange={handleChange}
              isInvalid={!!startMonthError}
            />
            <FieldError>{startMonthError}</FieldError>
            {" / "}
            <Form.Control
              type="number"
              size="sm"
              className="d-inline w-25"
              placeholder="Day"
              min="1"
              max="31"
              name={startDayFieldName}
              value={get(values, startDayFieldName)}
              onChange={handleChange}
              isInvalid={!!startDayError}
            />
            <FieldError>{startDayError}</FieldError>
          </Form.Group>
        </td>
        <td>
          <Form.Group className="position-relative">
            <Form.Control
              type="number"
              size="sm"
              className="d-inline w-25"
              placeholder="Month"
              min="1"
              max="12"
              name={endMonthFieldName}
              value={get(values, endMonthFieldName)}
              onChange={handleChange}
              isInvalid={!!endMonthError}
            />
            <FieldError>{endMonthError}</FieldError>
            {" / "}
            <Form.Control
              type="number"
              size="sm"
              className="d-inline w-25"
              placeholder="Day"
              min="1"
              max="31"
              name={endDayFieldName}
              value={get(values, endDayFieldName)}
              onChange={handleChange}
              isInvalid={!!endDayError}
            />
            <FieldError>{endDayError}</FieldError>
          </Form.Group>
        </td>
      </tr>
    );
  };

  render() {
    const { org } = this.props;
    const { schoolBreaks } = org;

    const schoolBreakNames = ["winter", "spring", "other"];
    const initialValues = schoolBreaks || getDefaultSchoolBreakValues();

    return (
      <Formik validationSchema={schoolBreaksSchema} initialValues={initialValues}>
        {formProps => {
          const { areDatesValid } = this.validateSchoolBreaks(formProps);
          this.formProps = formProps;

          return (
            <React.Fragment>
              <Row>
                <table className="table table-condensed">
                  <thead>
                    <tr>
                      <th className="col-4">Name</th>
                      <th className="col-4">Start</th>
                      <th className="col-4">End</th>
                    </tr>
                  </thead>
                  <tbody className="l-h-34">
                    {schoolBreakNames.map(schoolBreakName => this.renderSchoolBreaksRow(schoolBreakName, formProps))}
                  </tbody>
                </table>
                <div className={`alert alert-danger${areDatesValid ? " invisible" : ""}`}>
                  Both Start and End dates for each School Break have to be set correctly or empty
                </div>
              </Row>
            </React.Fragment>
          );
        }}
      </Formik>
    );
  }
}

SchoolBreaksForm.propTypes = {
  org: PropTypes.object,
  setValidateSchoolBreaksFunction: PropTypes.func,
  setAreSchoolBreakDatesValid: PropTypes.func
};
