import React, { useState, useEffect, useRef } from "react";
import Dual<PERSON><PERSON><PERSON><PERSON> from "react-dual-listbox";
import { Meteor } from "meteor/meteor";
import Al<PERSON> from "react-s-alert";
import { <PERSON><PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import { get, groupBy, isEqual } from "lodash";

import "react-dual-listbox/lib/react-dual-listbox.css";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import Loading from "/imports/ui/components/loading";

import FileUploadErrors from "../upload/file-upload-errors";
import { validateAndSetSubtotals } from "../upload/validation-helpers";
import { getRosteringTypeLabel, getSchoolNumberComparison } from "/imports/ui/utilities";
import { getValuesByKey, studentsInMultipleClassesInfo } from "/imports/api/utilities/utilities";
import {
  areAnyStudentGroupsUnmatchedInSites,
  areSitesMatching,
  mapToGroupedOptions,
  mapToOptions,
  onOptionChange,
  renderComponentWithLoaderAndRetryComponent
} from "./utilities";
import { DataProviderForRosteringFilters, setUnique } from "./dataFetching";
import { MissingDataComponent } from "./helper-components/missing-data";
import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";
import { normalizeRosterImportItems } from "/imports/api/rosterImportItems/normalizeRosterImportItems";
import rosterImportItemsHelpers from "/imports/api/rosterImportItems/methods";
import MergeDataModal from "/imports/api/rosterImports/merge-data-modal";
import ExternalRosteringInfoModal from "../external-rostering-info-modal";
import ConfirmModal from "../confirm-modal";

const renderApplyButton = ({ isFilterApplied, onClick, isDisabled }) => {
  return (
    <div className="d-grid">
      {isFilterApplied ? (
        <button className="btn btn-success mt-1" disabled={isDisabled} onClick={onClick}>
          Applied <i className="fa fa-check-circle-o" aria-hidden="true"></i>
        </button>
      ) : (
        <button className="btn btn-default mt-1" disabled={isDisabled} onClick={onClick}>
          Apply
        </button>
      )}
    </div>
  );
};

// eslint-disable-next-line react/display-name
export const RosterFiltering = React.memo(({ rosteringType, rosteringSettings, orgid, orgName }) => {
  const lastFilterRef = useRef();
  const resultsRef = useRef();

  const rosteringSettingsSchools = get(rosteringSettings, "filters.schools", []);
  const rosteringSettingsTeachers = get(rosteringSettings, "filters.teachers", []);
  const rosteringSettingsClasses = get(rosteringSettings, "filters.classes", []);

  const shouldIgnoreEnrollmentStartDateDefault = get(rosteringSettings, "shouldIgnoreEnrollmentStartDate", false);
  const shouldIgnoreEnrollmentEndDateDefault = get(rosteringSettings, "shouldIgnoreEnrollmentEndDate", false);

  const [dataProviderForRosteringFilters, setDataProviderForRosteringFilters] = useState(null);

  const [allowMultipleGradeLevels, setAllowMultipleGradeLevels] = useState(null);

  const [isFilterFetchingEnabled, setIsFilterFetchingEnabled] = useState(false);
  const [isFetchingTeachers, setIsFetchingTeachers] = useState(false);
  const [isFetchingClasses, setIsFetchingClasses] = useState(false);

  const [availableSchools, setAvailableSchools] = useState(null);
  const [selectedSchools, setSelectedSchools] = useState(rosteringSettingsSchools);
  const [schoolsError, setSchoolsError] = useState(false);

  const [availableTeachers, setAvailableTeachers] = useState(null);
  const [compositeClasses, setCompositeClasses] = useState([]);
  const [selectedTeachers, setSelectedTeachers] = useState(rosteringSettingsTeachers);
  const [teachersError, setTeachersError] = useState(false);

  const [availableClasses, setAvailableClasses] = useState(null);
  const [selectedClasses, setSelectedClasses] = useState(rosteringSettingsClasses);
  const [classesError, setClassesError] = useState(false);

  const [classRosters, setClassRosters] = useState(null);
  const [isFetchingClassRosters, setIsFetchingClassRosters] = useState(null);
  const [loadingMessage, setLoadingMessage] = useState("");

  const [shouldShowConfirmModal, setShouldShowConfirmModal] = useState(false);
  const [rosteringDataChangePercentage, setRosteringDataChangePercentage] = useState(0);
  const [importErrors, setImportErrors] = useState(null);
  const [hasUploadFailed, setHasUploadFailed] = useState(null);

  const [isSchoolFilterAppliedLocally, setIsSchoolFilterAppliedLocally] = useState(!!rosteringSettingsSchools.length);
  const [isTeacherFilterAppliedLocally, setIsTeacherFilterAppliedLocally] = useState(
    !!rosteringSettingsTeachers.length
  );

  const [shouldIgnoreEnrollmentStartDate, setShouldIgnoreEnrollmentStartDate] = useState(
    shouldIgnoreEnrollmentStartDateDefault
  );
  const [shouldIgnoreEnrollmentEndDate, setShouldIgnoreEnrollmentEndDate] = useState(
    shouldIgnoreEnrollmentEndDateDefault
  );
  const [isMergeDataModalOpen, setIsMergeDataModalOpen] = useState(false);
  const [isNewSchoolsModalOpen, setIsNewSchoolsModalOpen] = useState(false);
  const [newSchoolsToBeAdded, setNewSchoolsToBeAdded] = useState([]);
  const [finalImportData, setFinalImportData] = useState({});

  const [missingSavedSchools, setMissingSavedSchools] = useState([]);
  const [missingSavedTeachers, setMissingSavedTeachers] = useState([]);
  const [missingSavedClasses, setMissingSavedClasses] = useState([]);

  const [missingComponentProps, setMissingComponentProps] = useState({ schools: [], teachers: [], classes: [] });

  const [isExternalRosteringInfoModalOpen, setIsExternalRosteringInfoModalOpen] = useState(false);

  const getAvailableSelectedSchoolItems = () =>
    availableSchools?.length
      ? selectedSchools.filter(val => availableSchools.find(school => school.schoolId === val))
      : [];
  const getAvailableSelectedTeacherItems = () =>
    availableTeachers?.length
      ? selectedTeachers.filter(val => availableTeachers.find(teacher => teacher._id === val))
      : [];
  const getAvailableSelectedClassItems = () =>
    availableClasses?.length
      ? selectedClasses.filter(val => availableClasses.find(studentGroup => studentGroup._id === val))
      : [];

  // HELPERS
  const scrollToLastFilter = () => {
    lastFilterRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  const scrollToResults = () => {
    resultsRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const hasClassRostersToDisplay = () => {
    return classRosters.length && classRosters.filter(c => Object.keys(c).length).length;
  };

  const toggleIgnoreEnrollmentStartDate = () => {
    const value = !shouldIgnoreEnrollmentStartDate;
    setShouldIgnoreEnrollmentStartDate(value);
    Meteor.call(
      "Organizations:updateOrganizationFieldValue",
      orgid,
      `rosteringSettings.shouldIgnoreEnrollmentStartDate`,
      value,
      err => {
        if (!err) {
          Alert.success(`Successfully updated "Ignore Enrollment Start Date" setting`);
        }
      }
    );
  };

  const toggleIgnoreEnrollmentEndDate = () => {
    const value = !shouldIgnoreEnrollmentEndDate;
    setShouldIgnoreEnrollmentEndDate(value);
    Meteor.call(
      "Organizations:updateOrganizationFieldValue",
      orgid,
      `rosteringSettings.shouldIgnoreEnrollmentEndDate`,
      value,
      err => {
        if (!err) {
          Alert.success(`Successfully updated "Ignore Enrollment End Date" setting`);
        }
      }
    );
  };

  const renderClassRosters = () => {
    if (!hasClassRostersToDisplay()) {
      return <div className="alert alert-info text-center">No data available for import</div>;
    }
    return (
      <div className="panel-body">
        <div className="row table-responsive">
          <table className="table table-condensed table-striped table-bordered table-layout-fixed table-with-ellipsis">
            <thead>
              <tr>
                <th className="col-3">School</th>
                <th className="col-5">Class</th>
                <th className="col-2">Teacher</th>
                <th className="col-1">Grade</th>
                <th className="col-1"># Kids</th>
              </tr>
            </thead>
            <tbody>
              {sortByPropertyFor({
                list: classRosters,
                paths: ["schoolName", "grade", "className", "teacherName"],
                order: 1
              }).map(roster => (
                <tr key={`${roster._id}_${roster.teacherName}`}>
                  <td>
                    <div>{roster.schoolName}</div>
                  </td>
                  <td>
                    <div>{roster.className}</div>
                  </td>
                  <td>
                    <div>{roster.teacherName}</div>
                  </td>
                  <td>
                    <div>{roster.grade}</div>
                  </td>
                  <td>
                    <div>{roster.numberOfKids}</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  async function getOrganization() {
    Meteor.call("Organizations:getOrganizationFieldValues", orgid, ["allowMultipleGradeLevels"], (err, resp) => {
      if (!err && resp) {
        setAllowMultipleGradeLevels(resp.allowMultipleGradeLevels);
      }
    });
  }

  const fetchSchools = () => {
    setIsFilterFetchingEnabled(true);
    dataProviderForRosteringFilters.getSchools({ setAvailableSchools, setSchoolsError, setMissingSavedSchools });
  };

  const onApplySchoolsFilter = () => {
    setIsFetchingTeachers(true);
    dataProviderForRosteringFilters.getTeachers({
      selectedSchools,
      availableSchools,
      selectedTeachers,
      setAvailableTeachers,
      setSelectedTeachers,
      setTeachersError,
      setMissingSavedTeachers,
      callback: () => setIsFetchingTeachers(false)
    });
    setIsSchoolFilterAppliedLocally(true);
    setIsTeacherFilterAppliedLocally(false);
  };

  const onApplyTeacherFilter = () => {
    setIsFetchingClasses(true);
    dataProviderForRosteringFilters.getClasses({
      setAvailableClasses,
      setClassesError,
      availableTeachers,
      selectedTeachers,
      setCompositeClasses,
      selectedSchools,
      setSelectedClasses,
      selectedClasses,
      setMissingSavedClasses,
      shouldIgnoreEnrollmentStartDate,
      shouldIgnoreEnrollmentEndDate,
      callback: () => setIsFetchingClasses(false)
    });
    setIsTeacherFilterAppliedLocally(true);
  };

  const saveFilters = () => {
    const filters = {
      schools: getAvailableSelectedSchoolItems(),
      teachers: getAvailableSelectedTeacherItems(),
      classes: getAvailableSelectedClassItems()
    };
    Meteor.call(
      "Organizations:saveRosterFilters",
      {
        orgid,
        filters
      },
      (err, res) => {
        if (err) {
          Alert.error(err.message);
        } else if (res) {
          setMissingComponentProps({ schools: [], teachers: [], classes: [] });
          Alert.success("Roster filters saved successfully.", {
            timeout: 3000
          });
        }
      }
    );
  };

  const prepareDataForMerging = importData => {
    const normalizedData = importData.map(el => {
      return normalizeRosterImportItems(rosterImportItemsHelpers.createFromCSVDataRow(el).data);
    });
    return normalizedData.reduce((data, dataItem) => {
      if (!data[dataItem.schoolID]) {
        // eslint-disable-next-line no-param-reassign
        data[dataItem.schoolID] = {
          stateInformation: {
            districtNumber: dataItem.districtID,
            schoolNumber: dataItem.schoolID,
            localSchoolNumber: dataItem.schoolID
          },
          schoolName: dataItem.schoolName,
          siteIdToUpdate: selectedSchools[dataItem.schoolID],
          studentGroups: []
        };
      }

      if (!data[dataItem.schoolID].studentGroups.find(sg => sg.classSectionId === dataItem.classSectionID))
        data[dataItem.schoolID].studentGroups.push({
          courseName: dataItem.courseName,
          classSectionId: dataItem.classSectionID
        });
      return data;
    }, {});
  };

  const insertRosters = params => {
    const { data, validationErrors, importData } = params;
    let { hasValidationErrors } = params;
    Meteor.call(
      "RosterImports:insertRoster",
      { data, validationErrors, itemCount: importData.length },
      orgid,
      (err, result) => {
        if (err) {
          decWaitingOn();
          setImportErrors([]);
          setHasUploadFailed(true);
          Alert.error("No valid roster data to import - please see errors");
        } else {
          decWaitingOn();
          if (result.hasValidationErrors) {
            setImportErrors([]);
            setHasUploadFailed(true);
          }
          hasValidationErrors ||= result.hasValidationErrors;
          const successMessage = `Successfully imported ${
            hasValidationErrors ? "part of " : ""
          }the roster to the SpringMath${hasValidationErrors ? " - please see errors" : ""}`;
          Alert.success(successMessage);
        }
        scrollToResults();
      }
    );
  };

  // eslint-disable-next-line no-unused-vars
  const importClassRosters = ({ data: importData, ncesBySchoolId }) => {
    setImportErrors(null);
    setHasUploadFailed(null);
    validateAndSetSubtotals({
      data: importData,
      isCSV: false,
      callback: ({ errors: validationErrors, failedRows }) => {
        const hasValidationErrors = validationErrors.length > 0;
        setImportErrors(hasValidationErrors ? validationErrors : null);
        const data = importData.filter((datum, index) => !failedRows.includes(index));
        if (data.length || (!data.length && !failedRows.length)) {
          if (Object.keys(ncesBySchoolId).length) {
            incWaitingOn(1, "Processing data...");
            Meteor.call("sites:convertSchoolIdsToNCES", ncesBySchoolId, orgid, error => {
              decWaitingOn(1);
              if (error) {
                Alert.error(error.message || "There was an issue with converting school IDs to NCES IDs");
              } else {
                incWaitingOn(1, "Inserting into the db!");
                insertRosters({ data, validationErrors, importData, hasValidationErrors });
              }
            });
          } else {
            incWaitingOn(1, "Inserting into the db!");
            insertRosters({ data, validationErrors, importData, hasValidationErrors });
          }
        } else {
          Alert.error("No valid roster data to import - please see errors");
          scrollToResults();
        }
      }
    });
  };

  function importFullRoster({
    importData,
    parsedClassRosters,
    ncesBySchoolId,
    existingSites = [],
    shouldCheckNewSchools = true,
    siteIdAssignmentBySchoolId
  }) {
    let schoolNumbersToBeAdded = [];

    if (shouldCheckNewSchools) {
      const { schoolNumbersToBeAdded: unmatchedSchools } = getSchoolNumberComparison(importData, existingSites);
      schoolNumbersToBeAdded = unmatchedSchools.filter(n => !siteIdAssignmentBySchoolId?.[n]);
    }

    if (schoolNumbersToBeAdded.length && shouldCheckNewSchools) {
      setNewSchoolsToBeAdded(
        schoolNumbersToBeAdded
          .map(n => {
            const foundItem = importData.find(d => d.SchoolID === n);
            return foundItem ? `${foundItem.SchoolName} (${foundItem.SchoolID})` : null;
          })
          .filter(Boolean)
      );
      setIsNewSchoolsModalOpen(true);
    } else {
      importClassRosters({ data: importData, ncesBySchoolId });
      setIsFetchingClassRosters(false);
      setIsNewSchoolsModalOpen(false);
      setClassRosters(parsedClassRosters);
      scrollToResults();
    }
  }

  function prepareDataAndImport(siteIdAssignmentBySchoolId, sectionIdAssignmentByClassSectionId) {
    incWaitingOn(1, "Preparing data for import!");
    Meteor.call(
      "RosterImports:prepareDataForMerging",
      orgid,
      siteIdAssignmentBySchoolId,
      sectionIdAssignmentByClassSectionId,
      finalImportData.importData?.[0]?.DistrictID,
      () => {
        decWaitingOn();
        setIsMergeDataModalOpen(false);
        const { importData, parsedClassRosters, ncesBySchoolId, existingSites } = finalImportData;
        importFullRoster({ importData, parsedClassRosters, ncesBySchoolId, existingSites, siteIdAssignmentBySchoolId });
      }
    );
  }

  function importRosteringData({ importData, parsedClassRosters, ncesBySchoolId }) {
    let existingSites = [];
    let existingStudentGroupsBySiteId = {};

    Meteor.call("Sites:getSitesInOrg", orgid, (err, sites) => {
      if (!err) {
        existingSites = sites;
      }
      if (!existingSites.length) {
        importFullRoster({ importData, parsedClassRosters, ncesBySchoolId, existingSites });
      } else {
        Meteor.call("StudentGroups:getGroupsInOrg", orgid, (e, studentGroups) => {
          if (!e) {
            existingStudentGroupsBySiteId = groupBy(studentGroups, "siteId");

            const preparedData = prepareDataForMerging(importData, sites);
            setFinalImportData({
              importData,
              preparedData,
              existingSites,
              existingStudentGroupsBySiteId,
              isFullFetch: true,
              parsedClassRosters,
              ncesBySchoolId
            });

            const {
              matchedExistingSchoolNumbers,
              importDataSchoolNumbersByExistingSchoolNumbers
            } = getSchoolNumberComparison(importData, existingSites);

            const areAnyStudentGroupsUnmatched = areAnyStudentGroupsUnmatchedInSites({
              importData,
              existingSites,
              studentGroups,
              importDataSchoolNumbersByExistingSchoolNumbers
            });
            const areSitesUnmatched = !areSitesMatching(
              matchedExistingSchoolNumbers,
              importData.map(i => i.SchoolID)
            );
            const shouldDisplayMergeModal = areSitesUnmatched || areAnyStudentGroupsUnmatched;

            if (!shouldDisplayMergeModal) {
              importFullRoster({ importData, parsedClassRosters, ncesBySchoolId, existingSites });
            } else {
              setIsMergeDataModalOpen(true);
            }
          } else {
            Alert.error("There was a problem during import process");
          }
        });
      }
    });
  }

  async function fetchClassRosters(isFullFetch = false) {
    setImportErrors(null);
    setIsFetchingClassRosters(true);
    setLoadingMessage("");
    const filteredAvailableClasses = availableClasses.filter(availableClass =>
      selectedClasses.includes(availableClass._id)
    );

    const importData = [];
    const ncesBySchoolId = {};
    let parsedClassRosters;
    try {
      let enrollments = [];
      let students = [];

      if (rosteringType === "rosterOR") {
        setLoadingMessage("Fetching enrollments...");
        enrollments = await dataProviderForRosteringFilters.getEnrollmentsInSchools(selectedSchools);

        setLoadingMessage("Fetching students...");
        const studentEnrollments = enrollments.filter(e => e.role === "student");
        students = await dataProviderForRosteringFilters.getStudentsInSchools({
          classIds: selectedClasses,
          schoolIds: selectedSchools,
          enrollments: studentEnrollments
        });
      } else {
        setLoadingMessage("Fetching students...");
        students = await dataProviderForRosteringFilters.getEdFiStudentsInSchools(
          filteredAvailableClasses.map(c => c._id)
        );
      }

      setLoadingMessage("Fetching rosters...");
      parsedClassRosters = await dataProviderForRosteringFilters.getRoster({
        compositeClasses,
        filteredAvailableClasses,
        availableSchools,
        availableTeachers,
        selectedSchools,
        rosteringSettings,
        isFullFetch,
        importData,
        ncesBySchoolId,
        allowMultipleGradeLevels,
        enrollments,
        students,
        shouldIgnoreEnrollmentStartDate,
        shouldIgnoreEnrollmentEndDate
      });
    } catch (e) {
      console.error("getRoster error:", e);
      Alert.error(e.reason || e.message || e.toString());
    }

    if (isFullFetch) {
      setLoadingMessage("Calculating roster import data change percentage...");
      Meteor.call(
        "RosterImports:getCurrentRosterImportDataChangePercentageAndThreshold",
        orgid,
        importData.length,
        (error, { dataChangePercentage, rosteringThreshold }) => {
          if (error) {
            Alert.error("There was a problem calculating roster import data change percentage");
          } else {
            setFinalImportData({ importData, parsedClassRosters, ncesBySchoolId });
            if (dataChangePercentage >= rosteringThreshold * 100) {
              setRosteringDataChangePercentage(dataChangePercentage);
              setShouldShowConfirmModal(true);
            } else {
              setLoadingMessage("Importing roster data...");
              importRosteringData({ importData, parsedClassRosters, ncesBySchoolId });
            }
          }
        }
      );
    }

    setIsFetchingClassRosters(false);
    setLoadingMessage("");
    setClassRosters(parsedClassRosters);
    scrollToResults();
  }

  function confirmImportWithLargeDataChange() {
    const { importData, parsedClassRosters, ncesBySchoolId } = finalImportData;
    importRosteringData({ importData, parsedClassRosters, ncesBySchoolId });
  }

  const showExternalRosteringInfoModal = () => {
    setIsExternalRosteringInfoModalOpen(true);
  };

  const closeExternalRosteringInfoModal = () => {
    setIsExternalRosteringInfoModalOpen(false);
  };

  const isSchoolFilterApplied = isEqual(selectedSchools, rosteringSettingsSchools) && !!selectedSchools.length;
  const isTeacherFilterApplied = isEqual(selectedTeachers, rosteringSettingsTeachers) && !!selectedTeachers.length;
  const isClassesFilterApplied = isEqual(selectedClasses, rosteringSettingsClasses) && !!selectedClasses.length;

  useEffect(() => {
    const dataProvider = new DataProviderForRosteringFilters({ rosteringSettings, rosteringType, orgid, orgName });
    setDataProviderForRosteringFilters(dataProvider);
    getOrganization();
  }, []);

  // Scroll Page
  useEffect(() => {
    scrollToLastFilter();
  }, [availableSchools, availableTeachers, availableClasses, isFetchingClassRosters]);

  useEffect(() => {
    if (availableSchools?.length) {
      setSelectedSchools(selectedSchools.filter(val => availableSchools.find(school => school.schoolId === val)));
    }
  }, [availableSchools]);

  useEffect(() => {
    if (availableClasses?.length) {
      setSelectedClasses(
        selectedClasses.filter(val => availableClasses.find(studentGroup => studentGroup._id === val))
      );
    }
  }, [availableClasses]);

  useEffect(() => {
    if (availableTeachers?.length) {
      setSelectedTeachers(selectedTeachers.filter(val => availableTeachers.find(teacher => teacher._id === val)));
    }
  }, [availableTeachers]);

  function areSelectedTeachersEqualToTeachersFromFilters() {
    return isEqual(
      selectedTeachers,
      rosteringSettings?.filters?.teachers.filter(val => availableTeachers?.find(teacher => teacher._id === val))
    );
  }

  useEffect(() => {
    if (
      availableTeachers?.length &&
      selectedTeachers?.length &&
      rosteringSettings?.filters &&
      areSelectedTeachersEqualToTeachersFromFilters()
    ) {
      onApplyTeacherFilter();
    }
  }, [selectedTeachers, shouldIgnoreEnrollmentStartDate, shouldIgnoreEnrollmentEndDate]);

  useEffect(() => {
    if (
      availableSchools?.length &&
      selectedSchools?.length &&
      rosteringSettings?.filters &&
      isSchoolFilterAppliedLocally
    ) {
      onApplySchoolsFilter();
    }
  }, [selectedSchools]);

  useEffect(() => {
    if (!isEqual(selectedSchools, rosteringSettings?.filters?.schools)) {
      setIsSchoolFilterAppliedLocally(false);
      setIsFetchingTeachers(false);
    }
  }, [selectedSchools]);

  useEffect(() => {
    setIsTeacherFilterAppliedLocally(areSelectedTeachersEqualToTeachersFromFilters());
  }, [selectedTeachers]);

  useEffect(() => {
    Meteor.call(
      "getNamesForMissingFilterIds",
      {
        orgid,
        schoolIds: missingSavedSchools,
        teacherIds: missingSavedTeachers,
        classesIds: missingSavedClasses
      },
      (err, res) => {
        setMissingComponentProps(res);
      }
    );
  }, [missingSavedSchools, missingSavedTeachers, missingSavedClasses]);

  const shouldShowMissingItemsWarning =
    !!missingComponentProps.schools.length ||
    !!missingComponentProps.teachers.length ||
    !!missingComponentProps.classes.length;

  const shouldShowStudentsInMultipleClassesInfo = (importErrors || []).some(importError =>
    importError.includes("More than one classSectionID is present for studentLocalID")
  );

  if (shouldShowStudentsInMultipleClassesInfo && !importErrors.includes(studentsInMultipleClassesInfo)) {
    importErrors.unshift(studentsInMultipleClassesInfo);
  }

  const rosteringDataChangePercentageText = `${
    Number.isInteger(rosteringDataChangePercentage)
      ? rosteringDataChangePercentage
      : rosteringDataChangePercentage.toFixed(1)
  }%`;

  return (
    <React.Fragment>
      {shouldShowMissingItemsWarning && (
        <MissingDataComponent
          schools={missingComponentProps.schools}
          teachers={missingComponentProps.teachers}
          classes={missingComponentProps.classes}
        />
      )}

      <Button
        size="xs"
        variant="outline-blue"
        className="skill-button pull-right"
        onClick={showExternalRosteringInfoModal}
      >
        <i className="fa fa-info-circle cursor-pointer" />
      </Button>

      {isExternalRosteringInfoModalOpen && (
        <ExternalRosteringInfoModal onCloseModal={closeExternalRosteringInfoModal} />
      )}

      <div className="row">
        <div className="col-6">
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                className="m-r-10"
                name="shouldIgnoreEnrollmentStartDate"
                checked={shouldIgnoreEnrollmentStartDate}
                onClick={toggleIgnoreEnrollmentStartDate}
              />
              Ignore Enrollment Start Date
            </label>
          </div>
        </div>

        <div className="col-6">
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                className="m-r-10"
                name="shouldIgnoreEnrollmentStartDate"
                checked={shouldIgnoreEnrollmentEndDate}
                onClick={toggleIgnoreEnrollmentEndDate}
              />
              Ignore Enrollment End Date
            </label>
          </div>
        </div>
      </div>

      {!isFilterFetchingEnabled ? (
        <div className="text-center">
          <button className="btn btn-purple" onClick={fetchSchools} disabled={isFilterFetchingEnabled}>
            Fetch Rostering Filters
          </button>
        </div>
      ) : (
        <div className="form-group">
          <label htmlFor="selectSchools">Schools</label>
          {renderComponentWithLoaderAndRetryComponent({
            condition: availableSchools,
            childComponent: (
              <>
                <DualListBox
                  options={mapToOptions(availableSchools, {
                    value: "schoolId",
                    label: "nameOfInstitution",
                    shouldLabelIncludeValue: true
                  })}
                  selected={selectedSchools}
                  onChange={onOptionChange(setSelectedSchools)}
                  canFilter
                  className="select-with-ellipsis"
                />
                {renderApplyButton({
                  isFilterApplied: isSchoolFilterApplied,
                  onClick: onApplySchoolsFilter,
                  isDisabled: !selectedSchools.length || isFetchingTeachers
                })}
              </>
            ),
            error: schoolsError,
            onError: fetchSchools,
            loadingMessage: "Fetching schools..."
          })}
        </div>
      )}
      {!!availableSchools?.length &&
        (isSchoolFilterAppliedLocally || isFetchingTeachers) &&
        !!getAvailableSelectedSchoolItems().length && (
          <div className="form-group">
            <label htmlFor="selectTeachers">Teachers</label>
            {renderComponentWithLoaderAndRetryComponent({
              condition: availableTeachers,
              childComponent: (
                <>
                  <DualListBox
                    options={mapToGroupedOptions(availableTeachers, {
                      groupHeaderData: getValuesByKey(
                        (availableSchools || []).filter(({ schoolId }) => selectedSchools.includes(schoolId)),
                        "schoolId",
                        "name"
                      ),
                      groupHeaderField: "schoolId"
                    })}
                    selected={selectedTeachers}
                    onChange={onOptionChange(arg => setUnique(setSelectedTeachers, arg))}
                    canFilter
                    className="select-with-ellipsis"
                  />
                  {renderApplyButton({
                    isFilterApplied: isTeacherFilterApplied,
                    onClick: onApplyTeacherFilter,
                    isDisabled: !selectedTeachers.length || isFetchingClasses
                  })}
                </>
              ),
              error: teachersError,
              onError: onApplySchoolsFilter,
              loadingMessage: "Fetching teachers..."
            })}
          </div>
        )}
      <div ref={lastFilterRef} />
      {!!availableTeachers?.length &&
        isSchoolFilterAppliedLocally &&
        !!getAvailableSelectedSchoolItems().length &&
        (isTeacherFilterAppliedLocally || isFetchingClasses) &&
        !!getAvailableSelectedTeacherItems().length && (
          <div className="form-group">
            <label htmlFor="selectTeachers">Classes</label>
            {renderComponentWithLoaderAndRetryComponent({
              condition: availableClasses,
              childComponent: (
                <>
                  <DualListBox
                    options={mapToGroupedOptions(availableClasses, {
                      label: "classFullName",
                      groupHeaderData: getValuesByKey(availableTeachers, "_id", "name", false, false, "_id"),
                      groupHeaderField: "teacherId"
                    })}
                    selected={selectedClasses}
                    onChange={onOptionChange(arg => setUnique(setSelectedClasses, arg))}
                    canFilter
                    className="select-with-ellipsis"
                  />
                  <div className="d-grid gap-1 mt-1">
                    <button className="btn btn-purple" onClick={() => fetchClassRosters(false)}>
                      Preview
                    </button>
                    {isClassesFilterApplied && isSchoolFilterApplied && isTeacherFilterApplied ? (
                      <button className="btn btn-primary" onClick={saveFilters}>
                        Filters Saved <i className="fa fa-check-circle-o" aria-hidden="true"></i>
                      </button>
                    ) : (
                      <button className="btn btn-primary" onClick={saveFilters}>
                        Save Filters
                      </button>
                    )}
                    <button className="btn btn-success" onClick={() => fetchClassRosters(true)}>
                      Import Now
                    </button>
                  </div>
                </>
              ),
              error: classesError,
              onError: onApplyTeacherFilter,
              loadingMessage: "Fetching classes..."
            })}
            <div ref={resultsRef} />
            {(importErrors || hasUploadFailed) && (
              <FileUploadErrors
                validationErrors={importErrors}
                hasUploadFailed={hasUploadFailed}
                fileName={`${getRosteringTypeLabel(rosteringType)} upload`}
                orgid={orgid}
              />
            )}
            {isFetchingClassRosters ? (
              <Loading message={loadingMessage} />
            ) : (
              classRosters && (
                <div className="m-t-30">
                  <h2>Class List ({hasClassRostersToDisplay() ? classRosters.length : 0})</h2>
                  {renderClassRosters()}
                </div>
              )
            )}
            <ConfirmModal
              showModal={shouldShowConfirmModal}
              confirmAction={confirmImportWithLargeDataChange}
              onCloseModal={() => setShouldShowConfirmModal(false)}
              bodyQuestion=""
              bodyText={
                <div>{rosteringDataChangePercentageText} of the roster will be removed. Do you want to continue?</div>
              }
              confirmText="Yes"
              cancelText="No"
            />
            {isMergeDataModalOpen ? (
              <MergeDataModal
                onCloseModal={() => {
                  setIsFetchingClassRosters(false);
                  setIsMergeDataModalOpen(false);
                }}
                confirmAction={prepareDataAndImport}
                confirmText="Apply"
                cancelText="Abort"
                showModal={true}
                size="lg"
                rosteringData={finalImportData.preparedData}
                existingSites={finalImportData.existingSites}
                existingStudentGroupsBySiteId={finalImportData.existingStudentGroupsBySiteId}
                orgid={orgid}
              />
            ) : null}
          </div>
        )}
      {isNewSchoolsModalOpen ? (
        <ConfirmModal
          showModal={isNewSchoolsModalOpen}
          headerText="New Schools Detected"
          bodyText={
            <div>
              <strong>The following school(s) will be created in SpringMath:</strong>
              {newSchoolsToBeAdded.map((schoolLabel, index) => (
                <p key={`newSchool_${index}`}>{schoolLabel}</p>
              ))}
            </div>
          }
          bodyQuestion=""
          confirmText="Proceed with Import"
          confirmButtonClassName="btn-default"
          cancelText="Abort Import"
          cancelButtonClassName="btn-danger"
          confirmAction={() =>
            importFullRoster({
              importData: finalImportData.importData,
              parsedClassRosters: finalImportData.parsedClassRosters,
              ncesBySchoolId: finalImportData.ncesBySchoolId,
              shouldCheckNewSchools: false
            })
          }
          onCloseModal={() => setIsNewSchoolsModalOpen(false)}
        />
      ) : null}
    </React.Fragment>
  );
});

RosterFiltering.propTypes = {
  orgid: PropTypes.string,
  orgName: PropTypes.string,
  rosteringSettings: PropTypes.object,
  rosteringType: PropTypes.string
};
