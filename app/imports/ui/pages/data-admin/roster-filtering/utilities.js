import React from "react";
import { difference, get, groupBy, invert, sortBy } from "lodash";
import { RetryComponent } from "./helper-components/retry-component";
import Loading from "../../../components/loading";
import { getValuesByKey } from "/imports/api/utilities/utilities";

export const onOptionChange = (setFunction, stateSetFunctionsToReset = []) => e => {
  setFunction(e);
  stateSetFunctionsToReset.forEach(stateSetFunction => stateSetFunction([]));
};

export const mapToOptions = (objects, { label = "name", value = "_id", shouldLabelIncludeValue = false } = {}) => {
  if (!objects || !Array.isArray(objects)) {
    return [];
  }
  return sortBy(
    objects.map(object => {
      let optionLabel = object[label];
      if (shouldLabelIncludeValue) {
        optionLabel += ` (${object[value]})`;
      }
      return {
        label: optionLabel,
        value: object[value]
      };
    }),
    "label"
  );
};

export const mapToGroupedOptions = (
  objects,
  { label = "name", value = "_id", groupHeaderData = {}, groupHeaderField = "" } = {}
) => {
  if (!objects || !Array.isArray(objects)) {
    return [];
  }
  const options = sortBy(
    objects.map(object => {
      return {
        label: object[label],
        value: object[value],
        header: get(object, groupHeaderField)
      };
    }),
    "label"
  );

  const optionsByHeaderField = groupBy(options, "header");
  return Object.keys(optionsByHeaderField).map(header => {
    return {
      label: groupHeaderData[header],
      options: optionsByHeaderField[header]
    };
  });
};

export const renderComponentWithLoaderAndRetryComponent = ({
  condition,
  childComponent,
  error,
  onError,
  loadingMessage = ""
}) => {
  if (error) {
    return <RetryComponent onClick={onError} />;
  }
  return condition ? childComponent : <Loading message={loadingMessage} />;
};

export function areAnyStudentGroupsUnmatchedInSites({
  importData,
  existingSites,
  studentGroups,
  importDataSchoolNumbersByExistingSchoolNumbers
}) {
  const existingSiteIdsBySchoolNumber = getValuesByKey(existingSites, "stateInformation.schoolNumber", "_id");
  const existingSectionIdsBySiteId = getValuesByKey(
    studentGroups.filter(sg => sg.isActive),
    "siteId",
    "sectionId",
    true,
    true
  );
  const importSectionIdsBySchoolNumber = getValuesByKey(importData, "SchoolID", "ClassSectionID", true, true);
  const importedSchoolNumbersByExisting = invert(importDataSchoolNumbersByExistingSchoolNumbers);

  return Object.entries(importSectionIdsBySchoolNumber).some(([schoolNumber, sectionIds]) => {
    const siteId = existingSiteIdsBySchoolNumber[importedSchoolNumbersByExisting[schoolNumber] || schoolNumber];
    const existingSectionIds = existingSectionIdsBySiteId[siteId];
    return (
      difference(existingSectionIds, sectionIds).length > 0 && difference(sectionIds, existingSectionIds).length > 0
    );
  });
}

export function areSitesMatching(existingSchoolNumbers = [], importedSchoolNumbers) {
  return !(
    difference(existingSchoolNumbers, importedSchoolNumbers).length &&
    difference(importedSchoolNumbers, existingSchoolNumbers).length
  );
}
