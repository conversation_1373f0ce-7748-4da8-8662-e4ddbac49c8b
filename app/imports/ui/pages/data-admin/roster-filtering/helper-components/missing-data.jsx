import React from "react";
import { Alert } from "react-bootstrap";
import PropTypes from "prop-types";

export const MissingDataComponent = ({ schools = [], teachers = [], classes = [] }) => (
  <div>
    <Alert variant="warning" className="alert-warning-dark alert-warning-flex" data-testid="missingDataWarning">
      <div className="alert-item-center">
        <i className="fa fa-warning fa-lg" />
        Some of the previously selected items are no longer present in the rostering system. To acknowledge that these
        changes were intended save filters and this message will be automatically dismissed.
        <br />
        Missing items:
        <br />
        {!!schools.length && (
          <div>
            Schools:
            <br />{" "}
            {schools.map(elem => (
              <div key={`missingSchools${elem.id}`}>{`ID ${elem.id} ${elem.name || "*No Name Found*"}`}</div>
            ))}
          </div>
        )}
        {!!teachers.length && (
          <div>
            Teachers:
            <br />
            {teachers.map(elem => (
              <div key={`missingTeachers${elem.id}`}>{`ID ${elem.id} ${elem.name || "*No Name Found*"}`}</div>
            ))}
          </div>
        )}
        {!!classes.length && (
          <div>
            Classes:
            <br />
            {classes.map(elem => (
              <div key={`missingClasses${elem.id}`}>{`ID ${elem.id} ${elem.name || "*No Name Found*"}`}</div>
            ))}
          </div>
        )}
      </div>
    </Alert>
  </div>
);

MissingDataComponent.propTypes = {
  schools: PropTypes.array,
  teachers: PropTypes.array,
  classes: PropTypes.array
};
