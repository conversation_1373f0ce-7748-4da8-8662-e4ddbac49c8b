import { areAnyStudentGroupsUnmatchedInSites, areSitesMatching } from "./utilities";

describe("areSitesMatching", () => {
  it("should return true when all sites are matching", () => {
    const existingSchoolNumber = ["60", "61"];
    const importedSchoolNumber = ["61", "60"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(true);
  });
  it("should return true when there are no existing sites", () => {
    const existingSchoolNumber = [];
    const importedSchoolNumber = ["61", "60"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(true);
  });
  it("should return true when there are all existing sites matching imported ones and there are more imported than matching", () => {
    const existingSchoolNumber = ["60", "61"];
    const importedSchoolNumber = ["61", "60", "62"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(true);
  });
  it("should return true when there are all existing sites matching imported ones and there are more existing than matching", () => {
    const existingSchoolNumber = ["60", "61", "62"];
    const importedSchoolNumber = ["61", "60"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(true);
  });
  it("should return false when not all sites are matching", () => {
    const existingSchoolNumber = ["60", "61"];
    const importedSchoolNumber = ["61", "63"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(false);
  });
  it("should return false when there are no matching sites", () => {
    const existingSchoolNumber = ["60", "62"];
    const importedSchoolNumber = ["61", "63"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(false);
  });
  it("should return false when there are more existing sites than imported and only some of imported match", () => {
    const existingSchoolNumber = ["60", "61", "62"];
    const importedSchoolNumber = ["61", "63"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(false);
  });
  it("should return false when there are more sites imported than existing and no sites are matching", () => {
    const existingSchoolNumber = ["60", "61", "62"];
    const importedSchoolNumber = ["63", "64", "65", "66"];
    expect(areSitesMatching(existingSchoolNumber, importedSchoolNumber)).toBe(false);
  });
});

describe("areAnyStudentGroupsUnmatchedInSites", () => {
  function generateImportData(studentGroups = []) {
    return studentGroups.map(([SchoolID, ClassSectionID]) => ({
      SchoolID,
      ClassSectionID
    }));
  }
  function generateExistingSites(schools = []) {
    return schools.map(([_id, schoolNumber]) => ({
      _id,
      stateInformation: {
        schoolNumber
      }
    }));
  }
  function generateStudentGroups(studentGroups = []) {
    return studentGroups.map(([siteId, sectionId]) => ({
      siteId,
      isActive: true,
      sectionId
    }));
  }

  const importData = generateImportData([
    ["60", "50"],
    ["61", "51"]
  ]);
  it("should return false when all groups are matching", () => {
    const existingSites = generateExistingSites([
      ["first", "60"],
      ["second", "61"]
    ]);
    const studentGroups = generateStudentGroups([
      ["first", "50"],
      ["second", "51"]
    ]);
    const importDataSchoolNumbersByExistingSchoolNumbers = {};

    expect(
      areAnyStudentGroupsUnmatchedInSites({
        importData,
        existingSites,
        studentGroups,
        importDataSchoolNumbersByExistingSchoolNumbers
      })
    ).toBe(false);
  });
  it("should return false when there are no existing groups", () => {
    const existingSites = generateExistingSites([
      ["first", "60"],
      ["second", "61"]
    ]);
    const studentGroups = [];
    const importDataSchoolNumbersByExistingSchoolNumbers = {};

    expect(
      areAnyStudentGroupsUnmatchedInSites({
        importData,
        existingSites,
        studentGroups,
        importDataSchoolNumbersByExistingSchoolNumbers
      })
    ).toBe(false);
  });
  it("should return true when some groups are not matching", () => {
    const existingSites = generateExistingSites([
      ["first", "60"],
      ["second", "61"]
    ]);
    const studentGroups = generateStudentGroups([
      ["first", "50"],
      ["second", "52"]
    ]);
    const importDataSchoolNumbersByExistingSchoolNumbers = {};

    expect(
      areAnyStudentGroupsUnmatchedInSites({
        importData,
        existingSites,
        studentGroups,
        importDataSchoolNumbersByExistingSchoolNumbers
      })
    ).toBe(true);
  });
  it("should return true when all groups are not matching", () => {
    const existingSites = generateExistingSites([
      ["first", "60"],
      ["second", "61"]
    ]);
    const studentGroups = generateStudentGroups([
      ["first", "53"],
      ["second", "52"]
    ]);
    const importDataSchoolNumbersByExistingSchoolNumbers = {};

    expect(
      areAnyStudentGroupsUnmatchedInSites({
        importData,
        existingSites,
        studentGroups,
        importDataSchoolNumbersByExistingSchoolNumbers
      })
    ).toBe(true);
  });
  it("should return false when there are no existing sites", () => {
    const existingSites = [];
    const studentGroups = generateStudentGroups([
      ["first", "53"],
      ["second", "52"]
    ]);
    const importDataSchoolNumbersByExistingSchoolNumbers = {};

    expect(
      areAnyStudentGroupsUnmatchedInSites({
        importData,
        existingSites,
        studentGroups,
        importDataSchoolNumbersByExistingSchoolNumbers
      })
    ).toBe(false);
  });
});
