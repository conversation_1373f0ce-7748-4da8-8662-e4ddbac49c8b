import React, { useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { Link } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import isEmpty from "lodash/isEmpty";

import { Loading } from "/imports/ui/components/loading";
import Header from "/imports/ui/components/admin-view/admin-header";

import ManageStudents from "./manage-students.jsx";
import ManageScores from "./manage-scores.jsx";

import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Students } from "/imports/api/students/students";
import { Users } from "/imports/api/users/users";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import ManageGroupDetails from "./manage-group-details";
import { isExternalRostering } from "../../utilities";
import { isSingleSchoolDataAdmin } from "./utilities";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext.jsx";
import { StaticDataContext } from "../../../contexts/StaticDataContext";
import { OrganizationContext } from "../../../contexts/OrganizationContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";

const ManageGroup = ({
  loading,
  studentGroup,
  studentGroupId,
  students,
  headerTitle,
  headerSubtitle,
  additionalHeaderInfo,
  manageView,
  orgid,
  siteId,
  groupDemographics,
  schoolYear,
  allStudentGroupsForOrg,
  rostering
}) => {
  const getRouteLinkClassName = viewName => (manageView === viewName ? "active" : "");

  const getRouteLink = viewName => {
    if (studentGroup) {
      switch (viewName) {
        case "class":
          return `/data-admin/manage-group/class/${orgid}/site/${siteId}/${studentGroup._id}`;
        case "students":
        case "manage-scores":
        default:
          return `/data-admin/manage-group/${viewName}/${orgid}/site/${siteId}/${studentGroup._id}`;
      }
    }
    return "";
  };

  const renderView = () => {
    switch (manageView) {
      case "manage-scores":
        return (
          <ManageScores
            studentGroupId={studentGroupId || studentGroup._id}
            siteId={siteId}
            orgid={orgid}
            schoolYear={schoolYear}
          />
        );
      case "class":
        return (
          <ManageGroupDetails
            studentGroup={studentGroup}
            studentGroupId={studentGroupId}
            siteId={siteId}
            orgid={orgid}
            allStudentGroupsForOrg={allStudentGroupsForOrg}
            isExternalRostering={isExternalRostering(rostering)}
          />
        );
      case "students":
      default:
        return (
          <ManageStudents
            students={students}
            studentGroup={studentGroup}
            siteId={siteId}
            orgid={orgid}
            manageView={manageView}
            isExternalRostering={isExternalRostering(rostering)}
            loading={loading}
          />
        );
    }
  };

  if (loading && !studentGroup._id) {
    return (
      <div className="workspace-container">
        <Loading message="manage-group" />
      </div>
    );
  }

  return (
    <div className="workspace-container">
      <div className="d-flex justify-content-between mt-1">
        {!isSingleSchoolDataAdmin() ? (
          <Link className="btn btn-outline-blue btn-xs" to={`/data-admin/dashboard/${orgid}`}>
            <i className="fa fa-caret-left" /> Back to All Schools
          </Link>
        ) : (
          <div />
        )}
        <div className="align-self-center justify-self-end d-flex gap-1">
          <Link
            className="btn btn-outline-blue btn-xs"
            to={`/data-admin/manage-school/search-students/${orgid}/site/${siteId}`}
          >
            Student Search
          </Link>
          {isExternalRostering(rostering) ? null : (
            <Link
              className="btn btn-outline-blue btn-xs"
              to={`/data-admin/manage-school/unarchive/${orgid}/site/${siteId}`}
              data-testid="manage-school-unarchive"
            >
              Unarchive
            </Link>
          )}
          {isExternalRostering(rostering) ? null : (
            <Link
              className="btn btn-outline-blue btn-xs"
              to={`/data-admin/manage-school/add-class-and-teacher/${orgid}/site/${siteId}`}
            >
              Add Class & Teacher
            </Link>
          )}
        </div>
      </div>
      <Header
        keyLabel="groupOverview"
        headerTitle={headerTitle}
        headerSubtitle={headerSubtitle}
        additionalHeaderInfo={additionalHeaderInfo}
        headerStats={groupDemographics}
      />
      <div className="card-box-wrapper mt-2">
        <ul
          className="nav nav-pills middle-sub-nav middle-sub-nav-compact mb-0"
          data-testid="manage-group-nav-route-items"
        >
          <li>
            <Link
              className={getRouteLinkClassName("students")}
              to={getRouteLink("students")}
              data-testid="manage-group-students"
            >
              Students
            </Link>
          </li>
          <li>
            <Link
              className={getRouteLinkClassName("manage-scores")}
              to={getRouteLink("manage-scores")}
              data-testid="manage-group-manage-scores"
            >
              Manage Scores
            </Link>
          </li>
          <li>
            <Link
              className={getRouteLinkClassName("class")}
              to={getRouteLink("class")}
              data-testid="manage-group-manage-class"
            >
              Manage Class
            </Link>
          </li>
        </ul>
        {renderView()}
      </div>
    </div>
  );
};

ManageGroup.propTypes = {
  loading: PropTypes.bool,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  students: PropTypes.array,
  headerTitle: PropTypes.string,
  headerSubtitle: PropTypes.string,
  additionalHeaderInfo: PropTypes.string,
  manageView: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  groupDemographics: PropTypes.array,
  grades: PropTypes.array.isRequired,
  siteName: PropTypes.string,
  rostering: PropTypes.string,
  schoolYear: PropTypes.number,
  allStudentGroupsForOrg: PropTypes.array
};

const ManageGroupWithTracker = props => {
  const { orgid, siteId, studentGroupId, manageView } = props;
  const { schoolYear } = useContext(SchoolYearContext) || {};
  const { grades } = useContext(StaticDataContext);
  const { org } = useContext(OrganizationContext);
  const { site, studentGroupsInSite } = useContext(SiteContext);
  const { studentGroup: contextStudentGroup } = useContext(StudentGroupContext);
  const { rostering } = org || {};

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    const isManageStudentPage = manageView === "students";
    const studentGroupEnrollmentsHandler = Meteor.subscribe("StudentGroupEnrollments:PerSite", {
      orgid,
      siteId,
      shouldPublishActive: isManageStudentPage
    });
    const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", orgid);
    const studentsHandler = Meteor.subscribe("Students:PerSiteInYear", {
      orgid,
      siteId,
      schoolYear
    });
    const usersHandler = Meteor.subscribe("Users", { siteId, orgid });

    const loading =
      !studentGroupEnrollmentsHandler.ready() ||
      !studentGroupsHandler.ready() ||
      !studentsHandler.ready() ||
      !usersHandler.ready();

    let studentGroup = contextStudentGroup || {};
    let headerTitle = studentGroup?.name || "";
    let headerSubtitle = studentGroup?.grade ? `Grade: ${studentGroup?.grade}` : "";
    let additionalHeaderInfo = "";
    let students = [];
    let groupDemographics = [];
    let teacher = {};
    let allStudentGroupsForOrg = [];

    if (!loading) {
      allStudentGroupsForOrg = StudentGroups.find({ schoolYear }, { fields: { sectionId: 1 } }).fetch();
      if (studentGroupId) {
        studentGroup = StudentGroups.findOne({ _id: studentGroupId });
      } else if (studentGroupsInSite?.length) {
        [studentGroup] = studentGroupsInSite;
      }
      if (!isEmpty(studentGroup)) {
        if (studentGroup.ownerIds.length) {
          teacher = Users.findOne({ _id: studentGroup.ownerIds[0] });
        }
        headerTitle = studentGroup?.name;
        headerSubtitle = `Grade: ${studentGroup?.grade}`;

        if (teacher?.profile) {
          additionalHeaderInfo = `Teacher: ${teacher.profile.name.last}, ${teacher.profile.name.first}`;
        }

        const query = {
          studentGroupId: studentGroup._id,
          isActive: true
        };

        const studentsEnrollments = StudentGroupEnrollments.find(query).fetch();
        const studentIds = studentsEnrollments.map(enrollment => enrollment.studentId);
        students = Students.find(
          { _id: { $in: studentIds } },
          { sort: { "identity.name.lastName": 1, "identity.name.firstName": 1 } }
        ).fetch();
      }

      groupDemographics = [{ label: "Students", value: students.length }];
    }

    return {
      loading,
      siteId,
      studentGroup,
      students,
      headerTitle,
      headerSubtitle,
      additionalHeaderInfo,
      groupDemographics,
      grades,
      siteName: site?.name || "",
      schoolYear,
      allStudentGroupsForOrg,
      rostering
    };
  }, [orgid, siteId, studentGroupId, manageView, schoolYear]);

  return <ManageGroup {...trackerData} manageView={manageView} orgid={orgid} siteId={siteId} />;
};

ManageGroupWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  manageView: PropTypes.string
};

export default ManageGroupWithTracker;
