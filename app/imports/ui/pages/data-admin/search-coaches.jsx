import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { <PERSON>ton } from "react-bootstrap";
import Alert from "react-s-alert";
import { <PERSON> } from "react-router-dom";
import qs from "query-string";
import Loading from "../../components/loading";

class SearchCoaches extends Component {
  constructor(props) {
    super(props);
    this.state = {
      lastName: "",
      firstName: "",
      email: "",
      coaches: [],
      searching: false
    };
  }

  findCoaches = () => {
    const { orgid } = this.props;
    const { firstName, lastName, email } = this.state;
    Meteor.call("users:getCoaches", orgid, firstName, lastName, email, (err, res) => {
      if (err) {
        this.setState({ searching: false });
        Alert.error(err.message, { timeout: 5000 });
      } else {
        this.setState({
          coaches: res,
          searching: false
        });
      }
    });
  };

  clearInputs = () => {
    this.setState({
      lastName: "",
      firstName: "",
      email: "",
      coaches: []
    });
  };

  setSearchValue = event => {
    this.setState({ [event.target.name]: event.target.value });
  };

  sentenceCase = string => string.charAt(0).toUpperCase() + string.slice(1);

  getInputField = fieldName => (
    <input
      className="form-control"
      ref={fieldName}
      type="text"
      required=""
      name={fieldName}
      placeholder=""
      data-testid={`coaches${this.sentenceCase(fieldName)}`}
      value={this.state[fieldName]}
      onChange={this.setSearchValue}
    />
  );

  render() {
    return (
      <div className="workspace-container">
        <div className="conOverviewHeader">
          <h1>Coaches Search</h1>
        </div>
        <div className="card-box-wrapper animated fadeIn scroll-y mt-2">
          <div className="p-2">
            <div className="row flex-container">
              <div className="col-3">
                <label>Last Name</label>
                {this.getInputField("lastName")}
              </div>
              <div className="col-3">
                <label>First Name</label>
                {this.getInputField("firstName")}
              </div>
              <div className="col-3">
                <label>Email</label>
                {this.getInputField("email")}
              </div>
              <div className="col-3 pull-bottom">
                {this.state.searching ? (
                  <Loading inline={true} />
                ) : (
                  <Button
                    onClick={this.findCoaches}
                    className="btn btn-outline-blue btn-wide btn-xs me-2"
                    data-testid="coachesSearchButton"
                  >
                    Search
                  </Button>
                )}
                <Button
                  onClick={this.clearInputs}
                  className="btn btn-outline-blue btn-wide btn-xs me-2"
                  data-testid="coachesClearButton"
                >
                  Reset
                </Button>
              </div>
            </div>
            <div className="main-content mt-2">
              <table className="table table-condensed">
                <thead>
                  <tr>
                    <th>Last Name</th>
                    <th>First Name</th>
                    <th>Email</th>
                    <th>School</th>
                  </tr>
                </thead>
                <tbody data-testid="coaches-search-rows">
                  {this.state.coaches.map(coach => (
                    <tr key={coach._id} data-testid={`coaches-search-row-${coach._id}`}>
                      <td>{coach.profile.name.last}</td>
                      <td>{coach.profile.name.first}</td>
                      <td>{coach.emails[0].address}</td>
                      <td>
                        <ul className="list-unstyled">
                          {coach.filterSites.map((site, index) => {
                            const query = {
                              id: coach._id,
                              role: "arbitraryIdadmin"
                            };
                            const queryString = qs.stringify(query);
                            const linkToSite = `/data-admin/manage-accounts/${this.props.orgid}/site/${site.siteId}/?${queryString}`;
                            return (
                              <li key={index}>
                                <Link to={linkToSite} key={index}>
                                  {site.name}
                                </Link>
                              </li>
                            );
                          })}
                        </ul>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SearchCoaches.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string
};

export default SearchCoaches;
