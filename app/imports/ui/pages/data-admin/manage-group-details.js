import React from "react";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import Select from "react-select";
import { ListGroup, ListGroupItem } from "react-bootstrap";
import Alert from "react-s-alert";

import { isEqual } from "lodash";
import AddTeacherForm from "./add-teacher-form";
import { Grades } from "/imports/api/grades/grades";
import Loading from "../../components/loading";
import { getClassNameFromStudentGroupName, getTeacherLabel, getUsersThatCanBeGroupOwners } from "./utilities";
import { getReactSelectCustomHeightStyle } from "../../utilities";
import { trimValuesInObject } from "/imports/api/utilities/utilities";
import ConfirmModal from "./confirm-modal";
import UserProfile from "../../components/user/user-profile";

const newTeacher = { lastName: "", firstName: "", localId: "", email: "" };

const getDefaultState = (studentGroup, allStudentGroupsForOrg) => {
  const { name, ownerIds = [], secondaryTeachers, grade, sectionId } = studentGroup || {};
  return {
    // className is an editable property that is displayed in Roster File. It's just a part of a full group name
    className: getClassNameFromStudentGroupName(name),
    teacher: ownerIds[0],
    isAddTeacherOpen: false,
    newTeacher,
    secondaryTeachers: secondaryTeachers || [],
    isSecondaryTeacherSelectorOpen: false,
    selectedSecondaryTeacher: "",
    grade,
    hasGradeChanged: false,
    isSending: false,
    isSaving: false,
    isTeacherFormValid: false,
    sectionId,
    isSectionIdFieldValid: true,
    allStudentGroupsForOrg: allStudentGroupsForOrg.map(sg => sg.sectionId).filter(sId => sId !== sectionId),
    isProfileModalOpen: false,
    selectedUserId: null
  };
};
class ManageGroupDetails extends React.Component {
  state = getDefaultState(this.props.studentGroup, this.props.allStudentGroupsForOrg);

  componentDidUpdate(prevProps) {
    if (!isEqual(prevProps.studentGroup, this.props.studentGroup)) {
      this.setState(getDefaultState(this.props.studentGroup, this.props.allStudentGroupsForOrg));
    }
  }

  onChangeTeacher = event => {
    const currentSecondaryTeachers = [...this.state.secondaryTeachers];
    const index = currentSecondaryTeachers.indexOf(event.value);
    if (index >= 0) {
      currentSecondaryTeachers.splice(index, 1);
    }
    this.setState({
      teacher: event.value,
      secondaryTeachers: currentSecondaryTeachers
    });
  };

  openNewTeacher = () => {
    this.setState({
      isAddTeacherOpen: true
    });
  };

  isAddTeacherFormValid = () => Object.values(this.state.newTeacher).every(value => value.trim().length);

  addTeacher = () => {
    if (!this.isAddTeacherFormValid()) {
      Alert.error("Please fix teacher form before proceeding", { timeout: 3000 });
    } else {
      this.setState({
        isSending: true
      });
      const teacher = trimValuesInObject(this.state.newTeacher);
      Meteor.call(
        "addTeacher",
        {
          teacher,
          orgid: this.props.studentGroup.orgid,
          siteId: this.props.studentGroup.siteId,
          schoolYear: this.props.studentGroup.schoolYear
        },
        err => {
          let stateToSet = {
            isSending: false
          };
          if (!err) {
            stateToSet = {
              ...stateToSet,
              newTeacher,
              isAddTeacherOpen: false
            };
          } else {
            Alert.error(err.message, { timeout: 5000 });
          }
          this.setState(stateToSet);
        }
      );
    }
  };

  updateNewTeacherData = (updatedData, isTeacherFormValid = false) => {
    this.setState(state => ({
      ...state,
      newTeacher: { ...state.newTeacher, ...updatedData },
      isTeacherFormValid
    }));
  };

  saveTeacherAssignment = () => {
    if (!this.state.className.length) {
      return Alert.error("Class Name property cannot be empty", { timeout: 5000 });
    }
    if (!this.state.isSectionIdFieldValid) {
      return Alert.error("Class section ID must be unique!", { timeout: 5000 });
    }
    this.setState({ isSaving: true });
    const warningText = "Warning! By changing the grade, you will end all of the current interventions. Continue?";
    // eslint-disable-next-line no-restricted-globals
    if (this.state.hasGradeChanged && !confirm(warningText)) {
      return null;
    }
    const fullGroupName = `${this.state.className} (${this.state.sectionId})`;
    return Meteor.call(
      "saveGroupData",
      {
        studentGroupId: this.props.studentGroup._id,
        newTeacherId: this.state.teacher,
        orgid: this.props.studentGroup.orgid,
        siteId: this.props.studentGroup.siteId,
        studentGroupName: fullGroupName,
        secondaryTeachers: this.state.secondaryTeachers,
        grade: this.state.grade,
        hasGradeChanged: this.state.hasGradeChanged,
        hasPrimaryTeacherChanged: true,
        sectionId: this.state.sectionId
      },
      (err, res) => {
        this.setState({ isSaving: false });
        if (!err) {
          const groupId = res || this.props.studentGroup._id;
          this.props.history.push(
            `/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}/${groupId}`
          );
          Alert.success("Class saved successfully", {
            timeout: 3000
          });
        } else {
          Alert.error(err.message, { timeout: 5000 });
        }
      }
    );
  };

  changeClassName = event => {
    this.setState({
      className: event.target.value
    });
  };

  changeSectionId = event => {
    this.validateSectionId(event.target.value);
    this.setState({
      sectionId: event.target.value
    });
  };

  validateSectionId = async value => {
    this.setState({
      isSectionIdFieldValid: !this.state.allStudentGroupsForOrg.includes(value)
    });
  };

  cancelAddTeacher = () => {
    this.setState({
      isAddTeacherOpen: false
    });
  };

  removeSecondaryTeacher = secondaryTeacherId => {
    const currentSecondaryTeachers = [...this.state.secondaryTeachers];
    const index = currentSecondaryTeachers.indexOf(secondaryTeacherId);
    currentSecondaryTeachers.splice(index, 1);
    this.setState({
      secondaryTeachers: currentSecondaryTeachers
    });
  };

  openSecondaryTeacherSelector = () => {
    this.setState({
      isSecondaryTeacherSelectorOpen: true
    });
  };

  onChangeSecondaryTeacher = event => {
    const secondaryTeachers = [...this.state.secondaryTeachers, event.value];
    this.setState({
      isSecondaryTeacherSelectorOpen: false,
      secondaryTeachers,
      selectedSecondaryTeacher: ""
    });
  };

  onChangeGrade = event => {
    const grade = event.target.value;
    const hasGradeChanged = this.props.studentGroup.grade !== grade;
    this.setState({
      grade,
      hasGradeChanged
    });
  };

  getAddNewTeacherButton = isExternalRostering => {
    return (
      !isExternalRostering && (
        <button className="btn btn-outline-blue btn-wide btn-xs me-2" onClick={this.openNewTeacher}>
          Add new teacher
        </button>
      )
    );
  };

  openProfileModal = userId => {
    this.setState({ isProfileModalOpen: true, selectedUserId: userId });
  };

  closeProfileModal = () => {
    this.setState({ isProfileModalOpen: false, selectedUserId: null });
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    const { siteId, teachers, grades, isExternalRostering = false } = this.props;
    const availableSecondaryTeachers = teachers.filter(
      teacher =>
        teacher._id !== this.state.teacher &&
        !this.state.secondaryTeachers.includes(teacher._id) &&
        !teacher.profile.siteAccess.some(sa => sa.siteId === siteId && sa.role === "arbitraryIdadmin")
    );

    return (
      <div>
        <div className="p-3">
          <div className="row">
            <div className="form-group col-lg-5">
              <label>Class Name</label>
              <input
                type="text"
                className="form-control"
                value={this.state.className}
                onChange={this.changeClassName}
                disabled={isExternalRostering}
              />
            </div>
            <div className="form-group col-lg-6 offset-lg-1">
              <label>Full Class Name</label>
              <input
                type="text"
                className="form-control"
                value={`${this.state.className} (${this.state.sectionId})`}
                disabled={true}
              />
            </div>
          </div>
          <div className="row">
            <div className="form-group col-lg-5">
              <label>Teacher</label>
              <div className="d-flex flex-row gap-1">
                <Select
                  className="form-control input-sm flex-grow-1"
                  classNamePrefix="react-select"
                  onChange={this.onChangeTeacher}
                  disabled={isExternalRostering}
                  styles={getReactSelectCustomHeightStyle(21)}
                  value={
                    !this.state.teacher
                      ? { value: null, label: "No teacher assigned" }
                      : {
                          value: this.state.teacher,
                          label: getTeacherLabel(this.state.teacher, teachers)
                        }
                  }
                  options={teachers.map(teacher => ({
                    value: teacher._id,
                    label: getTeacherLabel(teacher._id, teachers)
                  }))}
                />
                <button
                  type="button"
                  className="btn btn-outline-primary"
                  onClick={() => this.openProfileModal(this.state.teacher)}
                >
                  <i className="fa fa-user" />
                </button>
              </div>
            </div>
            <div className="form-group col-lg-6 offset-lg-1">
              <label>Class section ID</label>
              <input
                type="text"
                className="form-control"
                value={this.state.sectionId}
                onChange={this.changeSectionId}
                disabled={isExternalRostering}
              />
              <span
                className={`help-block help-block-override text-danger animated bounceIn${this.state
                  .isSectionIdFieldValid && " d-none"}`}
              >
                This class section ID already exists in this organization
              </span>
            </div>
          </div>
          <div className="form-group">
            <label>Grade</label>
            <select
              className="form-control input-sm"
              value={this.state.grade}
              onChange={this.onChangeGrade}
              data-testid="manage-group-details-grade"
              disabled={isExternalRostering}
            >
              {grades.map(grade => (
                <option value={grade._id} key={grade._id}>
                  {grade.display}
                </option>
              ))}
            </select>
          </div>
          <div className="form-group">
            <label>Secondary Teachers</label>
            <ListGroup data-testid="secondary-teacher-name">
              {this.state.secondaryTeachers.map(secondaryTeacherId => {
                const currentTeacher = teachers.find(teacher => teacher._id === secondaryTeacherId);
                return currentTeacher ? (
                  <ListGroupItem key={secondaryTeacherId}>
                    <div className="row">
                      <div className="d-flex flex-row align-items-center gap-3">
                        <div className="flex-grow-1">
                          {currentTeacher.profile.name.first} {currentTeacher.profile.name.last}
                        </div>
                        <div className="">
                          <i
                            data-testid="remove-secondary-teacher"
                            className="fa fa-times"
                            style={{
                              cursor: "pointer"
                            }}
                            onClick={() => this.removeSecondaryTeacher(secondaryTeacherId)}
                          />
                        </div>
                        <button
                          type="button"
                          className="btn btn-outline-primary"
                          onClick={() => this.openProfileModal(secondaryTeacherId)}
                        >
                          <i className="fa fa-user" />
                        </button>
                      </div>
                    </div>
                  </ListGroupItem>
                ) : null;
              })}
              <ListGroupItem>
                {this.state.isSecondaryTeacherSelectorOpen ? (
                  <div data-testid="select-secondary-teacher">
                    <Select
                      className="form-control input-sm"
                      classNamePrefix="react-select"
                      value={{
                        value: this.state.selectedSecondaryTeacher,
                        label: getTeacherLabel(this.state.selectedSecondaryTeacher, teachers)
                      }}
                      onChange={this.onChangeSecondaryTeacher}
                      options={availableSecondaryTeachers.map(teacher => ({
                        value: teacher._id,
                        label: getTeacherLabel(teacher._id, teachers)
                      }))}
                      styles={getReactSelectCustomHeightStyle(21)}
                    />
                  </div>
                ) : (
                  <Link to="#" onClick={this.openSecondaryTeacherSelector}>
                    <i className="fa fa-plus-square" /> Add new secondary teacher
                  </Link>
                )}
              </ListGroupItem>
            </ListGroup>
          </div>

          {this.state.isAddTeacherOpen ? (
            <div className="addTeacherBackground">
              <AddTeacherForm
                teacher={this.state.newTeacher}
                onChange={this.updateNewTeacherData}
                orgid={this.props.orgid}
              />
              {this.state.isSending ? (
                <React.Fragment>
                  <Loading inline={true} />
                </React.Fragment>
              ) : (
                <React.Fragment>
                  <button className="btn btn-outline-blue btn-wide btn-xs me-2" onClick={this.addTeacher}>
                    Add teacher
                  </button>
                  <button className="btn btn-outline-blue btn-wide btn-xs me-2" onClick={this.cancelAddTeacher}>
                    Cancel
                  </button>
                </React.Fragment>
              )}
            </div>
          ) : (
            this.getAddNewTeacherButton(isExternalRostering)
          )}
          {this.state.isSaving ? (
            <button className="btn btn-outline-blue btn-wide btn-xs me-2 disabled">
              <Loading inline />
            </button>
          ) : (
            <button className="btn btn-outline-blue btn-wide btn-xs me-2" onClick={this.saveTeacherAssignment}>
              Save
            </button>
          )}
        </div>
        {this.state.isProfileModalOpen ? (
          <ConfirmModal
            showModal={this.state.isProfileModalOpen}
            confirmText=""
            cancelText="Close"
            customProps={{ useCustomHeader: true }}
            headerText=""
            onCloseModal={this.closeProfileModal}
            bodyQuestion=""
            bodyText={<UserProfile userId={this.state.selectedUserId} />}
            size="lg"
          />
        ) : null}
      </div>
    );
  }
}

export default withTracker(props => {
  const { siteId, orgid } = props;
  const usersHandler = Meteor.subscribe("Users", { siteId, orgid });
  const gradeHandler = Meteor.subscribe("Grades");
  const loading = !usersHandler.ready() && !gradeHandler.ready();
  let teachers = [];
  let grades = [];
  if (!loading) {
    teachers = getUsersThatCanBeGroupOwners(orgid);
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
  }
  return {
    teachers,
    grades,
    loading,
    orgid,
    siteId
  };
})(withRouter(ManageGroupDetails));

ManageGroupDetails.propTypes = {
  studentGroup: PropTypes.object,
  teachers: PropTypes.array,
  grades: PropTypes.array,
  loading: PropTypes.bool,
  isExternalRostering: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  allSectionIds: PropTypes.array,
  allStudentGroupsForOrg: PropTypes.array
};
