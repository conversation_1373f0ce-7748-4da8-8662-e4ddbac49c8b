import React, { Component } from "react";
import PropTypes from "prop-types";
import { Button, Col, Form, Row } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { Formik } from "formik";

import { createCustomBenchmarkPeriodsSchema } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { BenchmarkPeriodsTable } from "./benchmark-periods-table";
import { FieldError } from "./field-error";
import { isDateValid } from "./utilities";

export class CustomBenchmarkPeriodsForm extends Component {
  addCustomBenchmarkPeriods = (data, { resetForm }) => {
    Meteor.call("BenchmarkPeriods:addCustom", data, err => {
      if (err) {
        Alert.error(err.reason || err.message || "There was a problem adding custom Benchmark Periods");
      } else {
        Alert.success(`Successfully added custom Benchmark Periods`);
        resetForm();
      }
    });
  };

  render() {
    const { isSuperAdminOrUniversalDataAdmin, benchmarkPeriods } = this.props;

    const restrictedNames = Object.keys(benchmarkPeriods[0]?.startDate || {});

    const customBenchmarkPeriodsSchema = createCustomBenchmarkPeriodsSchema(restrictedNames);
    return (
      <Formik
        validationSchema={customBenchmarkPeriodsSchema}
        onSubmit={this.addCustomBenchmarkPeriods}
        initialValues={{
          name: "",
          fallStartMonth: 8,
          fallStartDay: 1,
          winterStartMonth: 1,
          winterStartDay: 1,
          springStartMonth: 4,
          springStartDay: 1
        }}
      >
        {formProps => {
          const { values, handleChange, errors } = formProps;

          const isFallStartDateValid = isDateValid(values.fallStartMonth, values.fallStartDay);
          const isWinterStartDateValid = isDateValid(values.winterStartMonth, values.winterStartDay);
          const isSpringStartDateValid = isDateValid(values.springStartMonth, values.springStartDay);
          const areStartDatesValid = isFallStartDateValid && isWinterStartDateValid && isSpringStartDateValid;

          return (
            <React.Fragment>
              {isSuperAdminOrUniversalDataAdmin && (
                <Row>
                  <Form.Group as={Col} md="12" controlId="name" className="position-relative">
                    <Form.Label>Name</Form.Label>
                    <Form.Control
                      type="text"
                      required
                      placeholder="Unique Benchmark Periods Group Name (eg. Summer 7/1)"
                      name="name"
                      value={values.name}
                      onChange={handleChange}
                      isInvalid={!!errors.name}
                    />
                    <FieldError>{errors.name}</FieldError>
                  </Form.Group>
                </Row>
              )}
              <Row className="mt-3">
                <Col>
                  <BenchmarkPeriodsTable
                    formProps={formProps}
                    benchmarkPeriods={benchmarkPeriods}
                    benchmarkPeriodsGroupId={values.benchmarkPeriodsGroupId}
                  />
                </Col>
              </Row>

              <Row className="mt-3">
                <Button
                  variant="primary"
                  onClick={formProps.handleSubmit}
                  disabled={!formProps.isValid || !areStartDatesValid}
                >
                  Add
                </Button>
              </Row>
            </React.Fragment>
          );
        }}
      </Formik>
    );
  }
}

CustomBenchmarkPeriodsForm.propTypes = {
  isSuperAdminOrUniversalDataAdmin: PropTypes.bool,
  benchmarkPeriods: PropTypes.array
};
