import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { Alert } from "react-bootstrap";
import { initial } from "lodash";

import ReactPaginate from "react-paginate";
import { withTracker } from "meteor/react-meteor-data";
import moment from "moment";
import Loading from "../../components/loading";
import PageHeader from "../../components/page-header";
import { areSubscriptionsLoading } from "../../utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { RosterImports } from "/imports/api/rosterImports/rosterImports";
import { Users } from "/imports/api/users/users";
import ConfirmModal from "./confirm-modal";
import FileUploadErrors from "./upload/file-upload-errors";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

function RosterImportHistory(props) {
  const baseQuantityPerPage = 20;
  if (props.loading) {
    return <Loading />;
  }

  const [loading, setLoading] = useState(false);
  const [errorMessages, setErrorMessages] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [data, setData] = useState([]);
  const [numberOfPages, setNumberOfPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(0);

  const getPageData = (startAt, length) => {
    setLoading(true);
    Meteor.call(
      "RosterImports:getRosterHistory",
      props.orgid,
      startAt,
      length,
      (err, { rosters, documentsAvailable }) => {
        if (!err) {
          setData(rosters);
          setNumberOfPages(Math.ceil(documentsAvailable / baseQuantityPerPage));
          setLoading(false);
        } else {
          Alert.error("There was a problem while fetching roster import history", { timeout: 5000 });
        }
      }
    );
  };

  const getNumberOfPages = () => {
    Meteor.call("RosterImports:getNumberOfRosterImports", props.orgid, (err, total) => {
      if (!err) {
        setNumberOfPages(Math.ceil(total / baseQuantityPerPage));
      } else {
        Alert.error("There was a problem while fetching roster import history", { timeout: 5000 });
      }
    });
  };

  useEffect(() => {
    setLoading(true);
    Meteor.call(
      "RosterImports:getRosterHistory",
      props.orgid,
      1,
      baseQuantityPerPage - 1,
      (err, { rosters, documentsAvailable }) => {
        if (!err) {
          if (props.latestRosterItem) {
            setData([props.latestRosterItem, ...rosters]);
          } else {
            setData(rosters);
          }
          setNumberOfPages(Math.ceil(documentsAvailable / baseQuantityPerPage));
          setLoading(false);
        } else {
          Alert.error("There was a problem while fetching roster import history", { timeout: 5000 });
        }
      }
    );
  }, []);

  useEffect(() => {
    if (currentPage === 0 && props.latestRosterItem) {
      const existingObjectIndex = data.findIndex(el => el._id === props.latestRosterItem._id);

      if (existingObjectIndex !== -1) {
        const currData = [...data];
        currData.splice(existingObjectIndex, 1, props.latestRosterItem);
        setData(currData);
      } else if (data.length >= baseQuantityPerPage) {
        setData([props.latestRosterItem, ...initial(data)]);
        getNumberOfPages();
      } else {
        setData([props.latestRosterItem, ...data]);
      }
    } else {
      getPageData(baseQuantityPerPage * currentPage, baseQuantityPerPage);
    }
  }, [props.latestRosterItem]);

  const handlePageClick = pageObject => {
    getPageData(baseQuantityPerPage * pageObject.selected, baseQuantityPerPage);
    setCurrentPage(pageObject.selected);
  };

  const onErrorClick = (hasNoErrors, errors) => {
    if (hasNoErrors) {
      return;
    }
    setIsModalOpen(true);
    setErrorMessages(errors);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const statusIconClassNames = {
    completed: "fa fa-check",
    "upload failed": "fa fa-times",
    default: "fa fa-hourglass-half"
  };

  const statusHeaderClassNames = {
    completed: "alert-success",
    "upload failed": "alert-danger",
    default: "alert-warning"
  };

  return (
    <div className="conFullScreen">
      {props.isModal ? null : <PageHeader title="Import history" description={props.orgName} />}

      <div className={`${props.isModal ? "" : "container"} mt-2 animated fadeIn`}>
        {numberOfPages > 1 && (
          <ReactPaginate
            previousLabel={"Prev"}
            nextLabel={"Next"}
            breakLabel={"..."}
            pageCount={numberOfPages}
            marginPagesDisplayed={1}
            pageRangeDisplayed={5}
            onPageChange={handlePageClick}
            containerClassName={"pagination m-t-15 custom-pagination pagination-sm"}
            pageClassName="page-item"
            previousClassName="page-item"
            nextClassName="page-item"
            breakClassName="page-item"
            pageLinkClassName="page-link"
            previousLinkClassName="page-link"
            nextLinkClassName="page-link"
            breakLinkClassName="page-link"
            activeClassName={"active"}
          />
        )}
        <div className="p-3">
          {loading ? (
            <Loading />
          ) : (
            <div className={`${props.isModal ? "m-t-50" : ""}`}>
              <div className="row table-responsive">
                <table className="table table-condensed table-bordered table-hover table-row-centered">
                  <thead>
                    <tr>
                      <td>Status</td>
                      <td>Import Started</td>
                      <td>Import Finished</td>
                      <td># of students added / updated / removed</td>
                      <td># of teachers added / updated / removed</td>
                      <td># of student groups added / updated / removed</td>
                      <td># of sites added / updated</td>
                      <td>Total # of records received</td>
                      <td>No Errors</td>
                      <td>Import Source</td>
                      <td>Run By</td>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((datum, index) => {
                      const statusHeaderClassName =
                        statusHeaderClassNames[datum.status] || statusHeaderClassNames.default;
                      const { added: studentsAdded, updated: studentsUpdated, removed: studentsRemoved } =
                        datum?.students || {};
                      const { added: teachersAdded, updated: teachersUpdated, removed: teachersRemoved } =
                        datum?.teachers || {};
                      const {
                        added: studentGroupsAdded,
                        updated: studentGroupsUpdated,
                        removed: studentGroupsRemoved
                      } = datum?.studentGroups || {};
                      const { added: sitesAdded, updated: sitesUpdated } = datum?.sites || {};
                      const statusIconClassName = statusIconClassNames[datum.status] || statusIconClassNames.default;
                      const studentStatistics = `${studentsAdded} / ${studentsUpdated} / ${studentsRemoved}`;
                      const teacherStatistics = `${teachersAdded} / ${teachersUpdated} / ${teachersRemoved}`;
                      const studentGroupStatistics = `${studentGroupsAdded} / ${studentGroupsUpdated} / ${studentGroupsRemoved}`;
                      const sitesStatistics = `${sitesAdded} / ${sitesUpdated}`;
                      const hasNoErrors = !datum.error || !Object.keys(datum.error).length;
                      const noErrorsHeaderClassName = hasNoErrors ? "alert-success" : "alert-danger";
                      const noErrorsIconClassName = hasNoErrors ? "fa fa-check" : "fa fa-times";
                      return (
                        <tr key={index} onClick={() => onErrorClick(hasNoErrors, datum)}>
                          <th className={statusHeaderClassName}>
                            <i className={statusIconClassName} aria-hidden="true" />
                          </th>
                          <th>{moment(datum?.started?.date).format("L HH:mm:ss")}</th>
                          <th>{datum.finished ? moment(datum?.finished?.date).format("L HH:mm:ss") : "N/A"}</th>
                          <th>{datum.status === "completed" ? studentStatistics : "N/A"}</th>
                          <th>{datum.status === "completed" ? teacherStatistics : "N/A"}</th>
                          <th>{datum.status === "completed" ? studentGroupStatistics : "N/A"}</th>
                          <th>{datum.status === "completed" ? sitesStatistics : "N/A"}</th>
                          <th>{datum.itemCount}</th>
                          <th className={noErrorsHeaderClassName}>
                            <i className={noErrorsIconClassName} aria-hidden="true" />
                          </th>
                          <th>{datum.source || "Unknown"}</th>
                          <th>{datum.runBy}</th>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
                <ConfirmModal
                  showModal={isModalOpen}
                  onCloseModal={closeModal}
                  confirmAction={closeModal}
                  headerText="Roster Import errors:"
                  bodyQuestion=""
                  bodyText={
                    <div style={{ whiteSpace: "break-spaces" }}>
                      <FileUploadErrors
                        errorData={errorMessages}
                        hasUploadFailed={true}
                        fileName={"Import_History"}
                        orgid={props.orgid}
                      />
                    </div>
                  }
                  confirmText=""
                  cancelText="Close"
                  size="lg"
                  customProps={{ shouldUseCustomShadow: props.isModal }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
RosterImportHistory.propTypes = {
  orgid: PropTypes.string,
  orgName: PropTypes.string,
  loading: PropTypes.bool,
  isModal: PropTypes.bool,
  latestRosterItem: PropTypes.object
};

export default withTracker(props => {
  const curUser = getMeteorUserSync();
  const orgid = props.orgid || curUser?.profile?.orgid;

  const organizationsHandler = Meteor.subscribe("Organizations", orgid);
  const rosterImportsHandler = Meteor.subscribe("RosterImports:lastImport", orgid);

  let orgName = "";
  let latestRosterItem = {};

  let loading = true;
  const initialLoading = areSubscriptionsLoading(organizationsHandler, rosterImportsHandler);
  if (!initialLoading) {
    const org = Organizations.findOne(orgid);
    if (org) {
      orgName = org.name || "";
      latestRosterItem = RosterImports.findOne({});
      if (latestRosterItem) {
        latestRosterItem.runBy = latestRosterItem?.started?.by || "Unknown";
        if (latestRosterItem?.started?.by) {
          const isSyncSchedule = latestRosterItem?.started?.by.startsWith("CRON");
          if (isSyncSchedule) {
            loading = false;
            latestRosterItem.runBy = "Sync Schedule";
          } else {
            const usersHandler = Meteor.subscribe("UserNames", [latestRosterItem.started.by]);
            loading = areSubscriptionsLoading(usersHandler);
            if (!loading) {
              const user = Users.findOne({ _id: latestRosterItem.started.by });
              if (user?.profile?.name) {
                latestRosterItem.runBy = `${user.profile.name.first} ${user.profile.name.last}`;
              }
            }
          }
        } else {
          loading = false;
        }
      } else {
        loading = false;
      }
    } else {
      loading = false;
    }
  }

  return { loading, orgName, orgid, latestRosterItem };
})(RosterImportHistory);
