import React, { useState } from "react";
import PropTypes from "prop-types";

import CreatableSelect from "react-select/creatable";
import Alert from "react-s-alert";

import {
  capitalizeFirstLetter,
  DEFAULT_USER_IDENTIFIERS,
  SELECTABLE_USER_IDENTIFIERS
} from "/imports/api/utilities/utilities";

export function UserRoleIdentifiers({ orgid, customIdentifiersByRole = {} }) {
  return (
    <table className="table table-condensed">
      <thead>
        <tr>
          <th className="col-6">User Role Identifier</th>
          <th className="col-6">Role Identifier Labels</th>
        </tr>
      </thead>
      <tbody>
        {DEFAULT_USER_IDENTIFIERS.map(({ role, identifiers }) => (
          <tr key={role}>
            <td className="col-6">{capitalizeFirstLetter(role)}</td>
            <td className="col-6">
              <UserRoleIdentifierSelector
                orgid={orgid}
                role={role}
                defaultIdentifiers={identifiers}
                customIdentifiersByRole={customIdentifiersByRole}
              />
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

UserRoleIdentifiers.propTypes = {
  orgid: PropTypes.string,
  customIdentifiersByRole: PropTypes.object
};

function mapIdentifierOptions(identifiers = [], isFixed = false) {
  return identifiers.map(identifier => ({
    value: identifier,
    label: identifier.toUpperCase(),
    isFixed
  }));
}

function UserRoleIdentifierSelector({ orgid, role, defaultIdentifiers = [], customIdentifiersByRole = {} }) {
  const defaultIdentifierOptions = mapIdentifierOptions(defaultIdentifiers, true);
  const customIdentifierOptions = mapIdentifierOptions(customIdentifiersByRole[role]);
  const identifierOptions = mapIdentifierOptions(SELECTABLE_USER_IDENTIFIERS);

  const [selectedIdentifiers, setSelectedIdentifiers] = useState([
    ...defaultIdentifierOptions,
    ...customIdentifierOptions
  ]);

  const styles = {
    multiValue: (base, state) => {
      return state.data.isFixed ? { ...base, backgroundColor: "gray" } : base;
    },
    multiValueLabel: (base, state) => {
      return state.data.isFixed ? { ...base, fontWeight: "bold", color: "white", paddingRight: 6 } : base;
    },
    multiValueRemove: (base, state) => {
      return state.data.isFixed ? { ...base, display: "none" } : base;
    }
  };

  const handleIdentifierChange = (newValue, actionMeta) => {
    const usedIdentifiers = [
      ...DEFAULT_USER_IDENTIFIERS.map(({ identifiers }) => identifiers).flat(1),
      ...Object.entries(customIdentifiersByRole)
        .map(([, identifiers]) => identifiers)
        .flat(1)
    ];
    switch (actionMeta.action) {
      case "create-option":
        // eslint-disable-next-line no-param-reassign
        if (usedIdentifiers.includes(actionMeta.option.value.trim().toUpperCase())) {
          return;
        }
        break;
      case "remove-value":
      case "pop-value":
        if (actionMeta.removedValue.isFixed) {
          return;
        }
        break;
      case "clear":
        // eslint-disable-next-line no-param-reassign
        newValue = selectedIdentifiers.filter(v => v.isFixed);
        break;
      default:
    }

    const chosenIdentifiers = newValue.map(t => ({
      ...t,
      label: t.value.toUpperCase(),
      value: t.value.toLowerCase()
    }));

    setSelectedIdentifiers(chosenIdentifiers);
    const identifiers = chosenIdentifiers.filter(v => !v.isFixed).map(v => v.value);
    Meteor.call("Organizations:saveUserRoleIdentifiers", { orgid, role, identifiers }, err => {
      if (err) {
        Alert.error("There was a problem while saving user role identifiers", {
          timeout: 5000
        });
      }
    });
  };

  const value = selectedIdentifiers;

  return (
    <CreatableSelect
      value={value}
      isMulti
      styles={styles}
      options={identifierOptions}
      isClearable={value.some(v => !v.isFixed)}
      name={`${role}_identifiers`}
      noOptionsMessage={() => "Type in Role Identifier Label and add with Enter or Tab"}
      onChange={handleIdentifierChange}
    />
  );
}

UserRoleIdentifierSelector.propTypes = {
  orgid: PropTypes.string,
  role: PropTypes.string,
  defaultIdentifiers: PropTypes.array,
  customIdentifiersByRole: PropTypes.object
};
