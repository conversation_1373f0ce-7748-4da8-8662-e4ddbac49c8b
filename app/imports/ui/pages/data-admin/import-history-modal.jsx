import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import RosterImportHistory from "./roster-import-history";

export default class ImportHistoryModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  render() {
    const { showModal, org } = this.props;
    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName="modal-95"
        backdrop={true}
        data-testid="importHistoryModal"
      >
        <ModalHeader className="align-content-center justify-content-center">
          <div className="text-center">
            <h3>Import History</h3>
            <p>{org?.name}</p>
          </div>
        </ModalHeader>
        <ModalBody>
          <RosterImportHistory orgid={org?._id} isModal={true} />
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

ImportHistoryModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  cancelText: PropTypes.string,
  org: PropTypes.object
};
