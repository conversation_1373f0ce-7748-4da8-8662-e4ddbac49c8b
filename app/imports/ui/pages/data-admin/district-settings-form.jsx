import React, { Component } from "react";
import PropTypes from "prop-types";
import { Accordion, Col, Form, Row } from "react-bootstrap";
import CreatableSelect from "react-select/creatable";
import { startCase } from "lodash";

import { CustomBenchmarkPeriodsForm } from "./custom-benchmark-periods-form";
import { BenchmarkPeriodsTable } from "./benchmark-periods-table";
import { FieldError } from "./field-error";
import { SchoolBreaksForm } from "./school-breaks-form";
import { isEmailValid } from "../../../api/utilities/utilities";

const studentInformationSystemOptions = [
  {
    value: "__hint__",
    label: "— or type your system name —",
    isDisabled: true
  },
  { value: "Aeries", label: "Aeries" },
  { value: "eSchoolData (Guru)", label: "eSchoolData (Guru)" },
  { value: "Infinite Campus", label: "Infinite Campus" },
  { value: "K-12 SIS by Focus School Software", label: "K-12 SIS by Focus School Software" },
  { value: "PowerSchool eSchoolPlus", label: "PowerSchool eSchoolPlus" },
  { value: "PowerSchool SIS", label: "PowerSchool SIS" },
  { value: "Sapphire", label: "Sapphire" },
  { value: "Skyward", label: "Skyward" },
  { value: "Synergy", label: "Synergy" }
];

export class DistrictSettingsForm extends Component {
  scrollTargetRef = React.createRef();

  scrollToFormBottom = () => {
    setTimeout(() => {
      this.scrollTargetRef.current.scrollIntoView({ block: "end", behavior: "smooth" });
    }, 500);
  };

  onProtectedFieldChange = e => {
    const { handleChange, isSuperAdminOrUniversalDataAdmin } = this.props;
    if (isSuperAdminOrUniversalDataAdmin) {
      handleChange(e);
    }
  };

  render() {
    const {
      handleChange,
      setValidateSchoolBreaksFunction,
      setAreSchoolBreakDatesValid,
      setFieldValue,
      setFieldTouched,
      setFieldError,
      values,
      errors,
      org,
      isSuperAdminOrUniversalDataAdmin,
      benchmarkPeriods
    } = this.props;

    const availableBenchmarkPeriodGroupIds = Object.keys(benchmarkPeriods[0]?.startDate || {});

    return (
      <Form noValidate>
        <Row>
          <Form.Group as={Col} md="4" controlId="settingsName" className="position-relative">
            <Form.Label>District Name</Form.Label>
            <Form.Control
              type="text"
              required
              placeholder="Name"
              name="name"
              value={values.name}
              onChange={this.onProtectedFieldChange}
              disabled={!isSuperAdminOrUniversalDataAdmin}
              isInvalid={!!errors.name}
            />
            <FieldError>{errors.name}</FieldError>
          </Form.Group>
          <Form.Group as={Col} md="4" controlId="settingsCity" className="position-relative">
            <Form.Label>City</Form.Label>
            <Form.Control
              type="text"
              required
              placeholder="City"
              name="city"
              value={values.city}
              onChange={this.onProtectedFieldChange}
              disabled={!isSuperAdminOrUniversalDataAdmin}
              isInvalid={!!errors.city}
            />
            <FieldError>{errors.city}</FieldError>
          </Form.Group>
          <Form.Group as={Col} md="4" controlId="settingsState" className="position-relative">
            <Form.Label>State</Form.Label>
            <Form.Control
              type="text"
              required
              placeholder="State"
              name="state"
              value={values.state}
              onChange={this.onProtectedFieldChange}
              disabled={!isSuperAdminOrUniversalDataAdmin}
              isInvalid={!!errors.state}
            />
            <FieldError>{errors.state}</FieldError>
          </Form.Group>
        </Row>
        {isSuperAdminOrUniversalDataAdmin && (
          <Row className="mt-3">
            <Form.Group as={Col} md="12" controlId="settingsIsTestOrg">
              <Form.Check
                required
                name="isTestOrg"
                label={
                  <span>
                    Is Test Organization{" "}
                    <small className="font-normal">
                      (allows saving, restoring, clearing test data, setting a custom date)
                    </small>
                  </span>
                }
                checked={values.isTestOrg}
                onChange={handleChange}
                isInvalid={!!errors.isTestOrg}
                feedback={errors.isTestOrg}
                feedbackType="invalid"
                id="settingsIsTestOrg"
              />
            </Form.Group>
          </Row>
        )}
        <Row className="mt-3">
          <Form.Group as={Col} md={12} controlId="settingsAllowResearch">
            <Form.Check
              type="checkbox"
              name="allowsDeIdentifiedDataResearch"
              label=" Allow research of de-identified data"
              data-testid="setIsLearnMoreActive"
              checked={values.allowsDeIdentifiedDataResearch}
              onChange={this.onProtectedFieldChange}
              disabled={!isSuperAdminOrUniversalDataAdmin}
            />
          </Form.Group>
        </Row>
        <fieldset className="border rounded-3 p-3 p-t-0 mt-3">
          <legend className="float-none w-auto px-3">Primary Contact:</legend>
          <Row>
            <Form.Group as={Col} md="6" controlId="settingsFirstName" className="position-relative">
              <Form.Label>First Name</Form.Label>
              <Form.Control
                type="text"
                required
                placeholder="First Name"
                name="firstName"
                value={values.firstName}
                onChange={handleChange}
                isInvalid={!!errors.firstName}
              />
              <FieldError>{errors.firstName}</FieldError>
            </Form.Group>
            <Form.Group as={Col} md="6" controlId="settingsLastName" className="position-relative">
              <Form.Label>Last Name</Form.Label>
              <Form.Control
                type="text"
                required
                placeholder="Last Name"
                name="lastName"
                value={values.lastName}
                onChange={handleChange}
                isInvalid={!!errors.lastName}
              />
              <FieldError>{errors.lastName}</FieldError>
            </Form.Group>
          </Row>
          <Row className="mt-3">
            <Form.Group as={Col} md="6" controlId="email" className="position-relative">
              <Form.Label>
                Email <small>(optional)</small>
              </Form.Label>
              <Form.Control
                type="email"
                placeholder="Email"
                name="email"
                value={values.email}
                onChange={handleChange}
                isInvalid={!!errors.email}
              />
              <FieldError>{errors.email}</FieldError>
            </Form.Group>
            <Form.Group as={Col} md="6" controlId="settingsPhone" className="position-relative">
              <Form.Label>
                Phone <small>(optional)</small>
              </Form.Label>
              <Form.Control
                type="tel"
                placeholder="Phone"
                name="phone"
                value={values.phone}
                onChange={handleChange}
                isInvalid={!!errors.phone}
              />
              <FieldError>{errors.phone}</FieldError>
            </Form.Group>
          </Row>
        </fieldset>

        <fieldset className="border rounded-3 p-3 p-t-0 mt-3">
          <legend className="float-none w-auto px-3">Details:</legend>
          <Row>
            <Form.Group as={Col} md={6} controlId="settingsStudentInformationSystem">
              <Form.Label>Student Information System</Form.Label>
              <CreatableSelect
                isClearable={true}
                isMulti={false}
                options={studentInformationSystemOptions}
                styles={{
                  control: base => ({
                    ...base,
                    boxShadow: "none",
                    border: errors.studentInformationSystem ? "1px solid red !important" : "1px solid lightgray"
                  })
                }}
                value={
                  values.studentInformationSystem
                    ? {
                        label:
                          studentInformationSystemOptions.find(opt => opt.value === values.studentInformationSystem)
                            ?.label || values.studentInformationSystem,
                        value: values.studentInformationSystem
                      }
                    : null
                }
                onChange={option => {
                  setFieldValue("studentInformationSystem", option ? option.value : "");
                  setFieldTouched("studentInformationSystem", true);
                }}
                onBlur={() => setFieldTouched("studentInformationSystem", true)}
                placeholder="Select or type to create…"
              />
              {errors.studentInformationSystem && (
                <span className="error font-14">{errors.studentInformationSystem}</span>
              )}
            </Form.Group>

            <Form.Group as={Col} md={3} controlId="settingsContractBeginDate" className="position-relative">
              <Form.Label>Contract Begin Date</Form.Label>
              <Form.Control
                type="date"
                name="contractBeginDate"
                value={values.contractBeginDate}
                onChange={this.onProtectedFieldChange}
                disabled={!isSuperAdminOrUniversalDataAdmin}
                isInvalid={!!errors.contractBeginDate}
              />
            </Form.Group>

            <Form.Group as={Col} md={3} controlId="settingsContractEndDate" className="position-relative">
              <Form.Label>Contract End Date</Form.Label>
              <Form.Control
                type="date"
                name="contractEndDate"
                value={values.contractEndDate}
                onChange={this.onProtectedFieldChange}
                disabled={!isSuperAdminOrUniversalDataAdmin}
                isInvalid={!!errors.contractEndDate}
              />
              <FieldError>{errors.contractEndDate}</FieldError>
            </Form.Group>
          </Row>

          <Row className="mt-3">
            <Col md={12}>
              <Form.Group as={Row} controlId="settingsPostContractDeleteDays">
                <Form.Label md={6} column sm="auto" className="mb-0">
                  Number of days post contract end date to delete data
                </Form.Label>
                <Col md={6}>
                  <Form.Control
                    type="number"
                    name="postContractDeleteDays"
                    min={0}
                    max={365}
                    value={values.postContractDeleteDays}
                    className="w-100"
                    onChange={this.onProtectedFieldChange}
                    disabled={!isSuperAdminOrUniversalDataAdmin}
                  />
                </Col>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mt-3">
            <Form.Group as={Col} md={12} controlId="settingsEmergencyContacts">
              <Form.Label>
                Emergency Contact Information <small className="font-normal">(Mailing list in case of emergency)</small>
              </Form.Label>
              <CreatableSelect
                isMulti
                isClearable
                menuIsOpen={false}
                styles={{
                  control: base => ({
                    ...base,
                    boxShadow: "none",
                    border: errors.emergencyContactInput ? "1px solid red !important" : "1px solid lightgray"
                  })
                }}
                components={{ DropdownIndicator: null }}
                placeholder="Type email and press enter..."
                inputValue={values.emergencyContactInput || ""}
                value={(values.emergencyContacts || []).map(v => ({ label: v, value: v }))}
                onInputChange={val => setFieldValue("emergencyContactInput", val)}
                onChange={opts =>
                  setFieldValue(
                    "emergencyContacts",
                    (opts || []).map(o => o.value)
                  )
                }
                onKeyDown={e => {
                  if (!values.emergencyContactInput) {
                    return;
                  }
                  if (e.key === "Enter" || e.key === "Tab") {
                    e.preventDefault();
                    const input = values.emergencyContactInput.trim();
                    if (!input || !isEmailValid(input)) {
                      setFieldError("emergencyContactInput", "Please enter a valid email address");
                      return;
                    }
                    const current = values.emergencyContacts || [];
                    if (!current.includes(input)) {
                      setFieldValue("emergencyContacts", [...current, input]);
                    }
                    setFieldValue("emergencyContactInput", "");
                    setFieldTouched("emergencyContacts", true);
                  }
                }}
                isInvalid={!!errors.emergencyContactInput}
              />
              {errors.emergencyContactInput && <span className="error font-14">{errors.emergencyContactInput}</span>}
            </Form.Group>
          </Row>
        </fieldset>

        <fieldset className="border rounded-3 p-3 p-t-0 mt-3">
          <legend className="float-none w-auto px-3">Benchmark Periods:</legend>
          {!isSuperAdminOrUniversalDataAdmin && (
            <p>If you need to modify your benchmark periods please submit a ticket via the support portal</p>
          )}
          <Row className="mt-3">
            <Form.Group as={Col} controlId="settingsBenchmarkPeriodsGroupId" className="position-relative">
              <Form.Label>Selected Benchmark Periods</Form.Label>
              {isSuperAdminOrUniversalDataAdmin ? (
                <Form.Select
                  name="benchmarkPeriodsGroupId"
                  disabled={!isSuperAdminOrUniversalDataAdmin}
                  value={values.benchmarkPeriodsGroupId}
                  onChange={handleChange}
                  isInvalid={!!errors.benchmarkPeriodsGroupId}
                >
                  {availableBenchmarkPeriodGroupIds.map(benchmarkPeriodsGroupId => (
                    <option key={benchmarkPeriodsGroupId} value={benchmarkPeriodsGroupId}>
                      {startCase(benchmarkPeriodsGroupId)}
                    </option>
                  ))}
                </Form.Select>
              ) : (
                <div className="small">{startCase(values.benchmarkPeriodsGroupId)}</div>
              )}
              <FieldError>{errors.benchmarkPeriodsGroupId}</FieldError>
            </Form.Group>
          </Row>
          <Row className="mt-3">
            <Col>
              <BenchmarkPeriodsTable
                benchmarkPeriods={benchmarkPeriods}
                benchmarkPeriodsGroupId={values.benchmarkPeriodsGroupId}
              />
            </Col>
          </Row>
        </fieldset>
        {isSuperAdminOrUniversalDataAdmin && (
          <Accordion className="mt-3 mb-3">
            <Accordion.Item eventKey="0">
              <Accordion.Header className="m-0" onClick={this.scrollToFormBottom}>
                Add Custom Benchmark Periods
              </Accordion.Header>
              <Accordion.Body>
                <CustomBenchmarkPeriodsForm
                  isSuperAdminOrUniversalDataAdmin={isSuperAdminOrUniversalDataAdmin}
                  benchmarkPeriods={benchmarkPeriods}
                />
              </Accordion.Body>
            </Accordion.Item>
          </Accordion>
        )}
        <div ref={this.scrollTargetRef} />

        <fieldset className="border rounded-3 p-3 p-t-0 mt-3">
          <legend className="float-none w-auto px-3">School Breaks:</legend>
          <p>
            The default winter school break starts on the first Monday after December 19 and runs for 2 weeks.
            <br />
            The default spring school break starts on the first Monday in April and runs for 1 week.
            <br />
            School breaks need to be a minimum of 7 days. Weeks should begin on a Monday and run through the following
            Sunday.
          </p>
          <Row className="mt-3">
            <Col>
              <SchoolBreaksForm
                org={org}
                setValidateSchoolBreaksFunction={setValidateSchoolBreaksFunction}
                setAreSchoolBreakDatesValid={setAreSchoolBreakDatesValid}
              />
            </Col>
          </Row>
        </fieldset>
      </Form>
    );
  }
}

DistrictSettingsForm.propTypes = {
  handleChange: PropTypes.func,
  setFieldValue: PropTypes.func,
  setFieldTouched: PropTypes.func,
  setFieldError: PropTypes.func,
  setValidateSchoolBreaksFunction: PropTypes.func,
  setAreSchoolBreakDatesValid: PropTypes.func,
  values: PropTypes.object,
  errors: PropTypes.object,
  org: PropTypes.object,
  isSuperAdminOrUniversalDataAdmin: PropTypes.bool,
  benchmarkPeriods: PropTypes.array
};
