import React from "react";
import PropTypes from "prop-types";

export default function ManageStudentsButtons(props) {
  const isManageStudentPage = props.isManageStudentsPage;

  function getEditButton() {
    if (!props.isUploadStudentsOpen) {
      return !props.isUploadStudentsOpen && !props.isEditingStudents ? (
        <button
          className="btn btn-outline-blue btn-wide btn-xs me-2"
          onClick={props.setEditingStudents}
          data-testid="editStudentsButton"
        >
          Edit
        </button>
      ) : (
        <button
          className="btn btn-outline-blue btn-wide btn-xs me-2"
          onClick={props.cancelEditingStudents}
          data-testid="cancelEditStudentsButton"
        >
          End Editing
        </button>
      );
    }
    return null;
  }

  // function getUploadButton() {
  //   if (!isManageStudentPage) {
  //     return null;
  //   }
  //   return props.isUploadStudentsOpen ? (
  //     <button className="btn btn-outline-blue btn-wide btn-xs me-2" onClick={props.hideUploadStudents}>
  //       <i className="fa fa-caret-left" /> Back
  //     </button>
  //   ) : (
  //     <button
  //       className="btn btn-outline-blue btn-wide btn-xs me-2"
  //       onClick={props.showUploadStudents}
  //       data-testid="uploadStudentButton"
  //     >
  //       Upload
  //     </button>
  //   );
  // }

  return (
    <div className="p-3">
      {props.isUploadStudentsOpen ? null : (
        <button
          className="btn btn-outline-blue btn-wide btn-xs me-2"
          disabled={!props.selectedStudents.length}
          onClick={props.moveStudents}
          data-testid="moveStudentButton"
        >
          Move
        </button>
      )}
      {isManageStudentPage && !props.isUploadStudentsOpen && (
        <React.Fragment>
          <button
            className="btn btn-outline-blue btn-wide btn-xs me-2"
            disabled={!props.selectedStudents.length}
            onClick={props.openArchiveStudentsModal}
          >
            Archive
          </button>
          <button
            className="btn btn-outline-blue btn-wide btn-xs me-2"
            onClick={props.showAddStudentForm}
            data-testid="addNewStudentButton"
          >
            Add
          </button>
        </React.Fragment>
      )}
      {/* {props.rostering === "rosterImport" ? getUploadButton() : null} */}
      {isManageStudentPage ? getEditButton() : null}
    </div>
  );
}

ManageStudentsButtons.propTypes = {
  isManageStudentsPage: PropTypes.bool,
  isUploadStudentsOpen: PropTypes.bool,
  selectedStudents: PropTypes.array,
  showUploadStudents: PropTypes.func,
  hideUploadStudents: PropTypes.func,
  showAddStudentForm: PropTypes.func,
  openArchiveStudentsModal: PropTypes.func,
  moveStudents: PropTypes.func,
  isEditingStudents: PropTypes.bool,
  setEditingStudents: PropTypes.func,
  cancelEditingStudents: PropTypes.func,
  rostering: PropTypes.string
};
