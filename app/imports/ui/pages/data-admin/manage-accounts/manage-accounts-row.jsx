import React from "react";
import { Meteor } from "meteor/meteor";
import capitalize from "lodash/capitalize";
import cloneDeep from "lodash/cloneDeep";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import moment from "moment";

import { Loading } from "../../../components/loading";
import { getMeteorUserId, isIdValid, userIdInvalidErrorMessage } from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../../routing/AppDataContext";
import { isExternalRostering } from "../../../utilities";
import ConfirmModal from "../confirm-modal";
import UserProfile from "../../../components/user/user-profile";

export default class ManageAccountsRow extends React.Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);
    this.state = {
      teacherId: props.user.profile.localId || "",
      lastName: capitalize(props.user.profile.name.last) || "",
      firstName: capitalize(props.user.profile.name.first) || "",
      email: props.user.emails[0].address || "",
      isProfileModalOpen: false,
      selectedUserId: null
    };
  }

  setSelectedUser = () => this.props.setSelectedUser(this.props.user._id);

  sendEmail = () => this.props.sendEmail(this.props.user._id);

  handleChange = event => this.setState({ [event.target.name]: event.target.value });

  // eslint-disable-next-line consistent-return
  handleSave = () => {
    const updatedUserProfile = cloneDeep(this.props.user.profile);
    const { firstName, lastName, email, teacherId } = this.state;
    updatedUserProfile.localId = teacherId.trim();
    updatedUserProfile.name.first = firstName.trim();
    updatedUserProfile.name.last = lastName.trim();
    const userData = {
      userId: this.props.user._id,
      profile: updatedUserProfile,
      email: ""
    };
    if (updatedUserProfile.localId.length && !isIdValid(updatedUserProfile.localId, true)) {
      return Alert.error(userIdInvalidErrorMessage, {
        timeout: 5000
      });
    }
    if (email.trim().toLowerCase() !== this.props.user.emails[0].address.trim().toLowerCase()) {
      userData.email = email;
    }
    Meteor.call("users:updateUserStandard", userData, err => {
      if (err) {
        Alert.error(
          `${err.reason ||
            "Could not update teacher data"} - verify the provided data and try using different email address`,
          { timeout: 10000 }
        );
      } else {
        Alert.success("The user data was successfully updated");
      }
    });
  };

  getInputField = fieldName => {
    if (this.shouldRenderElementForExternalRostering()) {
      return <span data-testid={`${fieldName}_${this.props.user._id}`}>{this.state[fieldName]}</span>;
    }
    return (
      <input
        type="text"
        className="form-control"
        onChange={this.handleChange}
        name={fieldName}
        id={`${fieldName}_${this.props.user._id}`}
        value={this.state[fieldName]}
        data-testid={`${fieldName}_${this.props.user._id}`}
      />
    );
  };

  shouldRenderElementForExternalRostering = () => {
    return (
      isExternalRostering(this.context.rostering) &&
      (this.props.user.isMultiRoleTeacher || this.props.selectedRole === "arbitraryIdteacher")
    );
  };

  getLastLogIn = user => {
    return user?.loginData?.lastLogin ? moment(user.loginData.lastLogin).format("L") : "N/A";
  };

  openProfileModal = userId => {
    this.setState({ isProfileModalOpen: true, selectedUserId: userId });
  };

  closeProfileModal = () => {
    this.setState({ isProfileModalOpen: false, selectedUserId: null });
  };

  activeUserRow = user => {
    const inviteEmailSent = this.props.inviteSent ? (
      <i className="text-success fa fa-check" />
    ) : (
      <i className="fa fa-minus" />
    );
    return (
      <tr key={user._id} data-testid={`${user._id}_Active`}>
        <td>
          {getMeteorUserId() === user._id ? null : (
            <span className="fa-stack" onClick={this.setSelectedUser}>
              <i className="fa fa-square-o fa-stack-2x" data-testid={`unchecked_${this.props.user._id}`} />
              {!this.props.selectedUsers.includes(this.props.user._id) ? null : (
                <i className="text-success fa fa-check fa-stack-1x" data-testid={`checked_${this.props.user._id}`} />
              )}
            </span>
          )}
        </td>
        <td className="text-center input-short overflow-break">{this.getInputField("teacherId")}</td>
        <td className="text-center input-short overflow-break">{this.getInputField("lastName")}</td>
        <td className="text-center input-short overflow-break">{this.getInputField("firstName")}</td>
        <td className="text-center input-short overflow-break">{this.getInputField("email")}</td>
        <td className="text-center input-short overflow-break">
          {this.shouldRenderElementForExternalRostering() ? null : (
            <button className="btn btn-primary" onClick={this.handleSave}>
              Save
            </button>
          )}
        </td>
        <td>
          <button type="button" className="btn btn-outline-primary" onClick={() => this.openProfileModal(user._id)}>
            <i className="fa fa-user" />
          </button>
        </td>
        <td className="text-center">
          {this.props.isSending && this.props.isInSendingList ? <Loading inline /> : inviteEmailSent}
        </td>
        <td className="text-center">
          {!user.profile.onboarded ? <i className="fa fa-minus" /> : <i className="text-success fa fa-check" />}
        </td>
        <td>
          {!user.profile.onboarded && !this.props.isSSOOnlyOrg && (
            <button className="btn btn-primary" onClick={this.sendEmail}>
              Send Invite
            </button>
          )}
        </td>
        <td className="text-center">{this.getLastLogIn(user)}</td>
      </tr>
    );
  };

  archivedUserRow = user => {
    return (
      <tr key={user._id} data-testid={`${user._id}_Archived`} className="archivedTeacherRow">
        <td className="text-center">{this.getInputField("teacherId")}</td>
        <td className="text-center">{this.getInputField("lastName")}</td>
        <td className="text-center">{this.getInputField("firstName")}</td>
        <td className="text-center">{this.getInputField("email")}</td>
        {this.shouldRenderElementForExternalRostering() ? null : (
          <td className="text-center">
            <button className="btn btn-primary" onClick={this.handleSave}>
              Save
            </button>
          </td>
        )}
        <td>
          <button type="button" className="btn btn-outline-primary" onClick={() => this.openProfileModal(user._id)}>
            <i className="fa fa-user" />
          </button>
        </td>
        <td className="text-center">{user.schoolYear}</td>
        <td className="text-center">{this.getLastLogIn(user)}</td>
      </tr>
    );
  };

  render() {
    const { isArchived, user } = this.props;
    const { isProfileModalOpen, selectedUserId } = this.state;
    return (
      <React.Fragment>
        {isArchived ? this.archivedUserRow(user) : this.activeUserRow(user)}
        {isProfileModalOpen ? (
          <ConfirmModal
            showModal={isProfileModalOpen}
            confirmText=""
            cancelText="Close"
            customProps={{ useCustomHeader: true }}
            headerText=""
            onCloseModal={this.closeProfileModal}
            bodyQuestion=""
            bodyText={<UserProfile userId={selectedUserId} />}
            size="lg"
          />
        ) : null}
      </React.Fragment>
    );
  }
}

ManageAccountsRow.propTypes = {
  dbUser: PropTypes.object,
  inviteSent: PropTypes.bool,
  isArchived: PropTypes.bool.isRequired,
  isInSendingList: PropTypes.bool.isRequired,
  isSending: PropTypes.bool.isRequired,
  selectedRole: PropTypes.string,
  selectedUsers: PropTypes.array.isRequired,
  sendEmail: PropTypes.func.isRequired,
  setSelectedUser: PropTypes.func.isRequired,
  user: PropTypes.object.isRequired,
  isSSOOnlyOrg: PropTypes.bool
};
