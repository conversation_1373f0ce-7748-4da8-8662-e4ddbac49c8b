import React from "react";
import PropTypes from "prop-types";
import IndividualInterventionProgress from "../../components/student-detail/individual-intervention-progress";
import Loading from "../../components/loading";

const ManageScoresIndividual = ({ student, studentGroup }) => {
  if (!student || !studentGroup) {
    return <Loading />;
  }

  return (
    <div className="studentDetailContent">
      <IndividualInterventionProgress
        student={student}
        studentGroup={studentGroup}
        printAllIndividualInterventionGoalSkills={false}
        printAllIndividualInterventionSkills={false}
        printAllIndividualInterventionGraphs={false}
      />
    </div>
  );
};

ManageScoresIndividual.propTypes = {
  student: PropTypes.object.isRequired,
  studentGroup: PropTypes.object.isRequired
};

export default ManageScoresIndividual;
