import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import Alert from "react-s-alert";
import PageHeader from "../../components/page-header";
import ManageMessageRow from "./manage-message-row";
import { getUserRoles } from "../data-admin/utilities";

export default class ManageMessage extends Component {
  state = {
    globalMessages: [],
    rosteringMessage: null,
    isZendeskWidgetEnabled: false
  };

  componentDidMount() {
    this.getAllMessages();
    this.getZendeskWidgetFlag();
  }

  updateZendeskWidgetSettings = () => {
    Meteor.call("Settings:updateZendeskWidget", !this.state.isZendeskWidgetEnabled, err => {
      if (!err) {
        Alert.success("Successfully updated widget settings.");
        return this.setState(state => ({
          ...state,
          isZendeskWidgetEnabled: !this.state.isZendeskWidgetEnabled
        }));
      }
      return Alert.error("There was a problem updating widget settings.");
    });
  };

  getZendeskWidgetFlag = () => {
    const userRole = getUserRoles();
    const isAllowedToFetchZendeskFlag = userRole.includes("superAdmin");
    if (!isAllowedToFetchZendeskFlag) {
      return;
    }
    Meteor.call("Settings:getZendeskWidgetFlag", (err, resp) => {
      if (!err) {
        return this.setState({ isZendeskWidgetEnabled: resp });
      }
      return Alert.error("There was an issue getting widget settings.");
    });
  };

  updateMessage = messageParams => {
    Meteor.call("News:updateMessage", messageParams, err => {
      if (err) {
        Alert.error("Error updating the message", err.reason || err.message);
      } else {
        Alert.success("Message successfully updated");
        this.getAllMessages();
      }
    });
  };

  addNewMessage = (messageParams, onSuccess) => {
    Meteor.call("News:addNewMessage", messageParams, err => {
      if (err) {
        Alert.error("Error updating the message", err.reason || err.message);
      } else {
        Alert.success("Message successfully added");
        onSuccess();
        this.getAllMessages();
      }
    });
  };

  getAllMessages = () => {
    Meteor.call("News:getAllMessages", (err, res) => {
      if (err) {
        Alert.error("There was a problem getting messages.", err.reason || err.message);
      } else {
        this.setState({
          globalMessages: res.globalMessages || [],
          rosteringMessage: res.rosteringMessage || null
        });
      }
    });
  };

  changeActiveMessage = (id, type) => {
    Meteor.call("News:changeActiveMessage", id, type, (err, resp) => {
      if (err) {
        Alert.error("Error changing active message", err.reason || err.message);
      } else {
        Alert.closeAll();
        if (resp.deactivated) {
          Alert.success("Successfully deactivated message");
        } else if (resp.globalChanged) {
          Alert.success("Successfully changed active message");
        } else if (resp.rosteringChecked) {
          Alert.success("Successfully activated message");
        }
        this.getAllMessages();
      }
    });
  };

  removeMessage = id => {
    Meteor.call("News:removeMessage", id, err => {
      if (err) {
        Alert.error("Error removing message", err.reason || err.message);
      } else {
        Alert.success("Successfully removed message");
        this.getAllMessages();
      }
    });
  };

  displayGlobalMessagesTable() {
    if (!this.state.globalMessages.length) {
      return null;
    }
    return (
      <React.Fragment>
        <tr className="table-row-centered message-header">
          <td />
          <th>Global Messages</th>
          <td colSpan="8" />
        </tr>
        {this.state.globalMessages.map(message => (
          <ManageMessageRow
            key={message._id}
            isNewMessage={false}
            messageProps={message}
            submit={this.updateMessage}
            changeActiveMessage={this.changeActiveMessage}
            removeMessage={this.removeMessage}
          />
        ))}
      </React.Fragment>
    );
  }

  displayRosteringMessage() {
    if (!this.state.rosteringMessage) {
      return null;
    }
    return (
      <React.Fragment>
        <tr className="table-row-centered message-header">
          <td />
          <th>Rostering Messages</th>
          <td colSpan="8" />
        </tr>
        <ManageMessageRow
          key={this.state.rosteringMessage._id}
          isNewMessage={false}
          messageProps={this.state.rosteringMessage}
          submit={this.updateMessage}
          changeActiveMessage={this.changeActiveMessage}
        />
      </React.Fragment>
    );
  }

  displayZendeskWidgetSettings() {
    return (
      <div className="pull-right pe-5">
        <label>Support Link/Widget Available Within the App</label>
        <input
          type="checkbox"
          className="ms-2"
          onChange={this.updateZendeskWidgetSettings}
          checked={this.state.isZendeskWidgetEnabled}
        />
      </div>
    );
  }

  render() {
    return (
      <div className="conFullScreen">
        <PageHeader title="Manage Message" />
        {this.displayZendeskWidgetSettings()}
        <div className="container">
          <table className="table table-condensed table-layout-fixed">
            <thead>
              <tr className="table-row-centered">
                <th rowSpan={2}>Active</th>
                <th className="col-md-3" rowSpan={2}>
                  Message
                </th>
                <th className="col-md-3" rowSpan={2}>
                  Learn More Url
                </th>
                <th colSpan={4}>Color</th>
                <th style={{ borderBottom: "none" }}>
                  <div className="d-flex flex-column align-items-center justify-content-center h-100">
                    Learn More Button
                  </div>
                </th>
                <th style={{ borderBottom: "none" }}>
                  <div className="d-flex flex-column align-items-center justify-content-center h-100">
                    <span>Support Button</span>
                  </div>
                </th>
                <th rowSpan={2} />
              </tr>
              <tr className="table-row-centered">
                <th>Background</th>
                <th>Text</th>
                <th>Button</th>
                <th>Icon</th>
                <th style={{ borderTop: "none" }}>
                  <div className="w-100 d-flex justify-content-center">
                    <span className="d-flex align-items-center justify-content-center icon-outline">
                      <i className="fa fa-info-circle" />
                    </span>
                  </div>
                </th>
                <th style={{ borderTop: "none" }}>
                  <div className="w-100 d-flex justify-content-center">
                    <span className="d-flex align-items-center justify-content-center icon-outline">
                      <i className="fa fa-support" />
                    </span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <ManageMessageRow submit={this.addNewMessage} isNewMessage={true} />
              {this.displayGlobalMessagesTable()}
              {this.displayRosteringMessage()}
            </tbody>
          </table>
        </div>
      </div>
    );
  }
}
