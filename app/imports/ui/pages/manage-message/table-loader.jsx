import React from "react";
import PropTypes from "prop-types";
import Loading from "../../components/loading";

export default function TableLoader(props) {
  return (
    <tr>
      <td colSpan={props.numberOfColumns} className="text-center">
        <Loading inline={true} />
      </td>
    </tr>
  );
}

TableLoader.defaultProps = {
  numberOfColumns: 10
};

TableLoader.propTypes = {
  numberOfColumns: PropTypes.number
};
