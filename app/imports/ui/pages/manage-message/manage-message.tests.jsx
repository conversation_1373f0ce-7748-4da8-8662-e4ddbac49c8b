import React from "react";
import { cleanup, fireEvent, render, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import td from "testdouble";
import { Meteor } from "meteor/meteor";
import { act } from "react-dom/test-utils";
import { omit } from "lodash";
import ManageMessage from "./manage-message";
import { defaultColors } from "./manage-message-row";
import { colors } from "../../../api/constants";

describe("ManageMessage", () => {
  let meteorCallSpy;
  beforeEach(() => {
    meteorCallSpy = td.replace(Meteor, "call");
  });
  afterEach(() => {
    cleanup();
    td.reset();
  });
  it("should make possible adding a new message using a placeholder row", async () => {
    td.when(meteorCallSpy("News:getAllMessages", td.matchers.isA(Function))).thenDo((methodName, callback) => {
      callback(null, {
        globalMessages: [],
        rosteringMessage: null
      });
    });
    td.when(meteorCallSpy("Settings:getZendeskWidgetFlag", td.matchers.isA(Function))).thenDo(
      (methodName, callback) => {
        callback(null, false);
      }
    );

    const { getByPlaceholderText, getByTestId } = render(<ManageMessage />);

    const messageContent = "New Message";
    const learnMoreUrl = "http://www.new-message.com";

    await act(async () => {
      fireEvent.change(getByPlaceholderText("Message text"), { target: { value: messageContent } });
      fireEvent.change(getByPlaceholderText("Learn More Url"), { target: { value: learnMoreUrl } });
      fireEvent.click(getByTestId("setIsLearnMoreActive"));
    });

    await act(async () => {
      fireEvent.click(getByTestId("add-new-message"));
    });

    td.verify(
      meteorCallSpy(
        "News:addNewMessage",
        {
          messageColor: colors.orange,
          messageTextColor: colors.white,
          buttonIconColor: colors.white,
          buttonColor: colors.brightBlue,
          messageContent: "New Message",
          learnMoreUrl: "http://www.new-message.com",
          isLearnMoreActive: true,
          isSupportLinkActive: false
        },
        td.matchers.isA(Function)
      )
    );
  });
  it("should display error when trying to submit a message without a content", async () => {
    const { getByPlaceholderText, getByTestId } = render(<ManageMessage />);

    await act(async () => {
      fireEvent.click(getByTestId("add-new-message"));
    });

    expect(getByPlaceholderText("Message text")).toHaveClass("withError");
  });
  it("should display error when trying to submit a message with invalid url", async () => {
    const { getByPlaceholderText, getByTestId } = render(<ManageMessage />);
    const messageContent = "New Message";
    const learnMoreUrl = "www.Test URL.com";

    await act(async () => {
      fireEvent.change(getByPlaceholderText("Message text"), {
        target: { value: messageContent }
      });
      fireEvent.change(getByPlaceholderText("Learn More Url"), {
        target: { value: learnMoreUrl }
      });
    });

    await act(async () => {
      fireEvent.click(getByTestId("add-new-message"));
    });

    expect(getByPlaceholderText("Learn More Url")).toHaveClass("withError");
  });

  it("should make possible editing the historical messages", async () => {
    const exampleMessage = [
      {
        _id: "exampleMessageId",
        learnMoreActive: false,
        learnMoreUrl: "",
        messageActive: true,
        messageContent: "Test",
        messageColor: defaultColors.messageColor,
        messageTextColor: defaultColors.messageTextColor,
        buttonColor: defaultColors.buttonColor,
        buttonIconColor: defaultColors.buttonIconColor,
        isLearnMoreActive: false,
        isSupportLinkActive: false
      }
    ];
    const updatedContentValue = "Updated";

    td.when(meteorCallSpy("News:getAllMessages", td.matchers.isA(Function))).thenDo((methodName, callback) => {
      callback(null, {
        globalMessages: exampleMessage,
        rosteringMessage: null
      });
    });

    // Mock the Zendesk widget call that's also made in componentDidMount
    td.when(meteorCallSpy("Settings:getZendeskWidgetFlag", td.matchers.isA(Function))).thenDo(
      (methodName, callback) => {
        callback(null, false);
      }
    );

    const { getByDisplayValue, getByTestId } = render(<ManageMessage />);

    // Wait for the component to load the messages
    const input = await waitFor(() => getByDisplayValue("Test"));
    await act(async () => {
      fireEvent.change(input, {
        target: { value: updatedContentValue }
      });
      fireEvent.blur(input);
    });

    await act(async () => {
      fireEvent.click(getByTestId("save-message"));
    });

    td.verify(
      meteorCallSpy(
        "News:updateMessage",
        { ...omit(exampleMessage[0], "messageActive"), messageContent: updatedContentValue },
        td.matchers.isA(Function)
      )
    );
  });
});
