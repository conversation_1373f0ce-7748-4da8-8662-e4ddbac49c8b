import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON>, withRouter } from "react-router-dom";
import Alert from "react-s-alert";
import _pull from "lodash/pull";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";

import ManageUsersRow from "./manage-users-row";
import ConfirmModal from "../data-admin/confirm-modal";
import PageHeader from "../../components/page-header.jsx";
import { Loading } from "../../components/loading.jsx";
import { getOrganizationNames } from "../../utilities";

class ManageUsers extends Component {
  state = {
    selectedUsers: [],
    isDeleteUsersModalOpen: false
  };

  selectRole = role => e => {
    e.preventDefault();
    this.setState({ selectedUsers: [] });
    this.props.history.push(`/manage-users/${role}`);
  };

  deleteSelectedUsers = () => {
    const { selectedUsers } = this.state;
    this.setState({ isDeleteUsersModalOpen: false });
    Meteor.call("users:removeUsers", selectedUsers, err => {
      if (err) {
        Alert.error("There was a problem while removing selected users", {
          timeout: 5000
        });
      } else {
        this.setState({ selectedUsers: [] });
        Alert.success("Selected users removed successfully", {
          timeout: 5000
        });
      }
    });
  };

  isUserSelected = userId => this.state.selectedUsers.includes(userId);

  toggleSelectedUsers = userId => e => {
    const { selectedUsers } = this.state;
    if (!e.currentTarget.checked) {
      _pull(selectedUsers, userId);
    } else if (!selectedUsers.includes(userId)) {
      selectedUsers.push(userId);
    }

    this.setState({ selectedUsers });
  };

  showDeleteUsersModal = () => {
    this.setState({ isDeleteUsersModalOpen: true });
  };

  closeDeleteUsersModal = () => {
    this.setState({ isDeleteUsersModalOpen: false });
  };

  getClassTextForRole = role => (role === this.props.role ? "active" : "");

  renderRoleButton = (buttonText, role) => (
    <li className="nav-item">
      <a
        className={`nav-link cursor-pointer ${this.getClassTextForRole(role)}`}
        data-testid={`manage_${role}`}
        onClick={this.selectRole(role)}
      >
        <span className={role === this.props.role ? "" : "btn-outline-blue p-2 rounded-3"}>{buttonText}</span>
      </a>
    </li>
  );

  renderUserRoleSelector() {
    return (
      <React.Fragment>
        <div className="col-12">
          <div className="mb-1">Select user role:</div>
        </div>
        <div className="col-12">
          <ul id="user-role-selector" className="nav nav-pills nav-fill">
            {this.renderRoleButton("Support User", "support")}
            {this.renderRoleButton("Super Admin", "superAdmin")}
            {this.renderRoleButton("Universal Coach", "universalCoach")}
            {this.renderRoleButton("Universal Data Admin", "universalDataAdmin")}
            {this.renderRoleButton("Downloader", "downloader")}
          </ul>
        </div>
      </React.Fragment>
    );
  }

  renderUsers() {
    const { users, role, organizations } = this.props;
    return (
      <React.Fragment>
        <button
          type="button"
          disabled={!this.state.selectedUsers.length}
          className="btn btn-danger"
          onClick={this.showDeleteUsersModal}
        >
          Delete Selected Users
        </button>
        <table className="table">
          <thead>
            <tr>
              <th>&nbsp;</th>
              <th>Last Name</th>
              <th>First Name</th>
              <th>Email</th>
              {role === "support" && <th>Organization Access</th>}
              <th>Profile</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => (
              <ManageUsersRow
                key={user._id}
                user={user}
                role={role}
                isSelected={this.isUserSelected(user._id)}
                selectUser={this.toggleSelectedUsers(user._id)}
                organizations={organizations}
              />
            ))}
          </tbody>
        </table>
      </React.Fragment>
    );
  }

  renderAddNewUserButtons = () => {
    return (
      <React.Fragment>
        <Link to="/support-account-setup" className="btn btn-success" data-testid="addSupportUserButton">
          Add Support User
        </Link>
        &nbsp;
        <Link to="/super-admin-account-setup" className="btn btn-success" data-testid="addSuperAdminAccountButton">
          Add Super Admin
        </Link>
        &nbsp;
        <Link to="/universal-coach-setup" className="btn btn-success" data-testid="addUniversalCoachButton">
          Add Universal Coach
        </Link>
        &nbsp;
        <Link to="/universal-data-admin-setup" className="btn btn-success" data-testid="addUniversalDataAdminButton">
          Add Universal Data Admin
        </Link>
        &nbsp;
        <Link to="/downloader-setup" className="btn btn-success" data-testid="addDownloaderButton">
          Add Downloader
        </Link>
      </React.Fragment>
    );
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    const { users, role } = this.props;
    // const isUniversalDataAdmin = Meteor.user().profile.siteAccess.find(sa => sa.role === ROLE_IDS.universalDataAdmin);

    return (
      <div className="conFullScreen">
        <PageHeader title={"Manage Users"} />
        <div className="container">
          <div>{this.renderAddNewUserButtons()}</div>
          <hr />
          <div className="row">
            {this.renderUserRoleSelector()}
            {role && (
              <div className="col-12 m-t-5">
                {users.length ? (
                  this.renderUsers()
                ) : (
                  <div className="alert alert-info text-center m-t-5">No users with this role</div>
                )}
              </div>
            )}
          </div>
        </div>
        <ConfirmModal
          showModal={this.state.isDeleteUsersModalOpen}
          onCloseModal={this.closeDeleteUsersModal}
          confirmAction={this.deleteSelectedUsers}
          headerText="Are you sure you want to delete selected users?"
          bodyQuestion="Selected users will be removed from the SpringMath database."
          confirmText="Yes, delete selected users"
        />
      </div>
    );
  }
}

ManageUsers.propTypes = {
  loading: PropTypes.bool,
  users: PropTypes.array,
  organizations: PropTypes.array,
  role: PropTypes.string,
  history: PropTypes.object
};

export default withTracker(({ role }) => {
  const usersSub = Meteor.subscribe("Users:InAllOrganizations", role);
  const organizationsSub = Meteor.subscribe("Organizations");
  const loading = !usersSub.ready() || !organizationsSub.ready();
  let users = [];
  let organizations = [];
  if (!loading) {
    users = Meteor.users.find({ "profile.siteAccess.role": `arbitraryId${role}` }).fetch();
    if (role === "support") {
      organizations = getOrganizationNames();
    }
  }

  return { loading, users, role, organizations };
})(withRouter(ManageUsers));

export { ManageUsers as PureManageUsers }; // for tests
