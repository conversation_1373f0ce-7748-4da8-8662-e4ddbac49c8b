import React, { useState } from "react";
import PropTypes from "prop-types";

import OrganizationAccessSelect from "./organization-access-select";
import { getMeteorUserId } from "/imports/api/utilities/utilities";
import ConfirmModal from "../data-admin/confirm-modal";
import UserProfile from "../../components/user/user-profile";

export default function ManageUsersRow(props) {
  const { user, organizations, role } = props;
  const isCurrentUser = user._id === getMeteorUserId();
  const isSupportUser = role === "support";
  const { first: firstName, last: lastName } = user.profile.name;
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  return (
    <tr data-testid="user-row">
      <td>
        {!isCurrentUser && (
          <input
            data-testid="user-row-checkbox"
            className="me-2"
            type="checkbox"
            checked={props.isSelected}
            onChange={props.selectUser}
          />
        )}
      </td>
      <td data-testid={`user_last_name_${lastName}`}>{lastName}</td>
      <td>{firstName}</td>
      <td>{user.emails[0].address}</td>
      {isSupportUser && (
        <td>
          <OrganizationAccessSelect user={user} organizations={organizations} />
        </td>
      )}
      <td>
        <button type="button" className="btn btn-outline-primary" onClick={() => setIsProfileModalOpen(true)}>
          <i className="fa fa-user" />
        </button>
      </td>
      {isProfileModalOpen ? (
        <ConfirmModal
          showModal={isProfileModalOpen}
          confirmText=""
          cancelText="Close"
          customProps={{ useCustomHeader: true }}
          headerText=""
          onCloseModal={() => setIsProfileModalOpen(false)}
          bodyQuestion=""
          bodyText={<UserProfile userId={user._id} />}
          size="lg"
        />
      ) : null}
    </tr>
  );
}

ManageUsersRow.propTypes = {
  user: PropTypes.object,
  isSelected: PropTypes.bool,
  selectUser: PropTypes.func,
  role: PropTypes.string,
  organizations: PropTypes.array
};
