import React, { useContext } from "react";
import PropTypes from "prop-types";
import ClasswideInterventionTable from "../admin-view/classwide-intervention-table";
import { ClassContext } from "../classContext";

/**
 * @return {null}
 */
export function ClasswideInterventionSection(props) {
  const { studentGroup, groupStats } = useContext(ClassContext);
  return (
    <ClasswideInterventionTable
      allClasswideStats={[groupStats]}
      studentGroups={[studentGroup]}
      assessmentResults={props.assessmentResults}
      type="growth"
    />
  );
}

ClasswideInterventionSection.propTypes = {
  assessmentResults: PropTypes.array
};
