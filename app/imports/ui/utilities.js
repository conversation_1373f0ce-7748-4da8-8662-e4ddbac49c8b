import React from "react";
import { Meteor } from "meteor/meteor";
import { difference, get, groupBy, random, isEqual } from "lodash";
import { Organizations } from "../api/organizations/organizations";
import { capitalizeFirstLetter } from "../api/utilities/utilities";
import { ROLE_IDS } from "../../tests/cypress/support/common/constants";
import { prepareSitesWithPotentialNormalization } from "../api/rosterImports/helpers";
import layoutMethods from "./layouts/methods";

function createBlobFromPrintMaterials(printMaterials) {
  const byteCharacters = atob(printMaterials);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: "application/pdf" });
}

export function download({ filename, hrefData }) {
  const element = document.createElement("a");
  element.setAttribute("href", hrefData);
  element.setAttribute("download", filename);
  element.style.display = "none";
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
}

export const downloadPdf = (printMaterials, filename) => {
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    const blob = createBlobFromPrintMaterials(printMaterials);
    window.navigator.msSaveOrOpenBlob(blob, filename);
  } else {
    const hrefData = Meteor.settings.public.USE_TEST_PRINT
      ? printMaterials
      : URL.createObjectURL(createBlobFromPrintMaterials(printMaterials));
    download({ filename, hrefData });
  }
};

export function getPlotLine(target, type) {
  return {
    value: target,
    color: type === "Mastery" ? "#21BA6E" : "#F4D03F",
    dashStyle: "Dash",
    width: 2,
    label: {
      text: `${type} Target (${target})`, // Dynamic Based on Possible Target Score for Skill
      style: {
        color: "#555"
      }
    }
  };
}

export function getXAxisDateTimeLabelFormats() {
  return {
    ...getTimeLabelsFromMilisecondsToHour(),
    day: "%b-%d",
    week: "%b-%d",
    month: "%b %Y",
    year: "%Y"
  };
}

export function getTooltipTimeLabelFormats() {
  return {
    ...getTimeLabelsFromMilisecondsToHour(),
    day: "%b-%d, %l%P",
    week: "%b-%d, %l%P",
    month: "%B %Y",
    year: "%Y"
  };
}

function getTimeLabelsFromMilisecondsToHour() {
  return {
    millisecond: "%b-%d, %l%P",
    second: "%b-%d, %l%P",
    minute: "%b-%d, %l%P",
    hour: "%b-%d, %l%P"
  };
}

const hourInMilliseconds = 3600 * 1000;
const weekInMilliseconds = 7 * 24 * hourInMilliseconds;
const monthInMilliseconds = 4 * 7 * 24 * hourInMilliseconds;
const dayInMilliseconds = 24 * hourInMilliseconds;

export { hourInMilliseconds, dayInMilliseconds, weekInMilliseconds, monthInMilliseconds };

export const optionalRosterFields = ["StudentGrade", "StudentBirthDate"];

export const requiredRosterFields = [
  "DistrictID",
  "DistrictName",
  "SchoolID",
  "SchoolName",
  "TeacherID",
  "TeacherLastName",
  "TeacherFirstName",
  "TeacherEmail",
  "ClassName",
  "ClassSectionID",
  "StudentLocalID",
  "StudentStateID",
  "StudentLastName",
  "StudentFirstName",
  "SpringMathGrade"
];

export const optionalStudentUploadFields = ["StudentBirthDate"];

export const requiredStudentUploadFields = ["StudentLocalID", "StudentStateID", "StudentLastName", "StudentFirstName"];

export const requiredAssessmentScoreUploadFields = [
  "StudentLocalID",
  "StudentStateID",
  "StudentLastName",
  "StudentFirstName",
  "AssessmentYear",
  "StateAssessmentName",
  "StateAssessmentScaleScore",
  "StateAssessmentProficient",
  "StateAssessmentPercentileScore",
  "DistrictAssessmentName",
  "DistrictAssessmentFallScaleScore",
  "DistrictAssessmentFallProficient",
  "DistrictAssessmentSpringScaleScore",
  "DistrictAssessmentSpringProficient"
];

export const isHighSchoolGrade = grade => ["HS"].includes(grade);

export function areSubscriptionsLoading(...handles) {
  return handles.some(handle => !handle.ready());
}

export function getOrganizationNames() {
  return Organizations.find({ isSelfEnrollee: { $ne: true } }, { fields: { name: 1 }, sort: { name: 1 } }).fetch();
}

export const getTimeRange = dates => {
  const dateStart = Math.min(...dates);
  const dateEnd = Math.max(...dates);
  const isDateRangeShorterThanFourWeeks = dateEnd - dateStart <= weekInMilliseconds * 4;
  const datesRange = isDateRangeShorterThanFourWeeks ? weekInMilliseconds * 4 : dateEnd - dateStart;
  const axisPaddingFactor = 0.05;
  const axisPadding = datesRange * axisPaddingFactor;
  const start = dateStart - axisPadding;
  const end = isDateRangeShorterThanFourWeeks ? dateEnd + weekInMilliseconds * 4 + axisPadding : dateEnd + axisPadding;
  return { start, end };
};

export const openPrintWindow = printURL => {
  const width = 790;
  const height = 800;

  Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT"], (err, { METEOR_ENVIRONMENT }) => {
    if (METEOR_ENVIRONMENT === "TEST") {
      const printWindow = window.open(printURL, "_blank");
      // workaround for cypress that does not allow new window
      if (printWindow && printWindow.addEventListener) {
        // THIS BLOCKS RESIZING WHICH MAY HAVE CAUSED ISSUES WITH HIGHCHARTS BEING REDRAWN AND IMPROPERLY DISPLAYED
        printWindow.addEventListener("resize", () => {
          printWindow.resizeTo(width, height);
        });
      }
    } else {
      window.parent.postMessage(`printScheduled`, "*");
      const printIframe = document.createElement("iframe");
      printIframe.setAttribute("id", "print-iframe");
      printIframe.setAttribute("width", width);
      printIframe.setAttribute("height", height);
      printIframe.setAttribute("src", printURL);
      printIframe.setAttribute("class", "invisible");

      // printIframe.setAttribute("class", "workspace-container"); // debug, makes iframe visible
      // printIframe.setAttribute("style", "margin-top: 100px"); // debug, moves in view
      document.body.appendChild(printIframe);
    }
  });
};

export const generateHSLColor = (colorNum, colors) => {
  const div = colors < 1 ? 1 : colors;
  return `hsl(${(colorNum * (360 / div)) % 360},100%,50%)`;
};

export function formatPercentage(percentage) {
  return percentage === "N/A" ? percentage : `${percentage}%`;
}

export const useContextOrProps = ({ componentInstance, property, verificationGetPath = "_id" }) => {
  const prop = componentInstance.context[property];
  if (typeof prop === "object" && get(prop, verificationGetPath)) {
    return prop;
  }
  if (Array.isArray(prop) && prop.length) {
    return prop;
  }
  return componentInstance.props[property];
};

export function getLegendText(period) {
  return period.match(/classwide/i) ? "Final Classwide Intervention" : `${capitalizeFirstLetter(period)} Screening`;
}

export function getSelectCustomStyles() {
  return {
    container: base => ({ ...base, fontSize: "0.9rem" }),
    valueContainer: base => ({ ...base, padding: "3px 8px" }),
    clearIndicator: base => ({ ...base, padding: 4 }),
    dropdownIndicator: base => ({ ...base, padding: 4 }),
    control: base => ({ ...base, minHeight: 35 })
  };
}

export function isExternalRostering(rostering) {
  return ["rosterEdFi", "rosterOR"].includes(rostering);
}

export async function fetchRosteringType(orgid) {
  const org = await Organizations.findOneAsync({ _id: orgid }, { fields: { rostering: 1 } });
  return get(org, "rostering");
}

export function getRosteringTypeLabel(rosteringType) {
  const labelMap = {
    rosterImport: "Roster Import",
    rosterUpload: "File Upload",
    rosterEdFi: "Ed-Fi",
    rosterOR: "OneRoster"
  };
  return labelMap[rosteringType] || "Unknown Rostering";
}

export function getReactSelectCustomHeightStyle(height) {
  return {
    control: provided => ({
      ...provided,
      border: 0,
      boxShadow: "none",
      minHeight: `${height}px`,
      height: `${height}px`,
      backgroundColor: "inherit"
    }),

    valueContainer: provided => ({
      ...provided,
      height: `${height}px`,
      padding: 0,
      margin: 0
    }),

    input: provided => ({
      ...provided,
      margin: 0
    }),
    indicatorSeparator: () => ({
      display: "none"
    }),
    indicatorsContainer: provided => ({
      ...provided,
      height: `${height}px`
    })
  };
}

export function isOnPrintPage() {
  return window.location.pathname.includes("/print/");
}

export function filterOutHistoryItemsThatDidNotLeadToIntervention(historyItems) {
  const newHistory = [...historyItems.filter(h => h.interventions.length)];

  // NOTE(fmazur) - Separate goal skill trees
  const historyItemsByBenchmarkAssessmentId = groupBy(
    historyItems.sort((a, b) => a.whenEnded.on - b.whenEnded.on).filter(h => !h.interventions.length),
    "benchmarkAssessmentId"
  );
  Object.values(historyItemsByBenchmarkAssessmentId).forEach(drilldownSet => {
    newHistory.push(drilldownSet[drilldownSet.length - 1]);
  });

  return newHistory;
}

function fillScores(target) {
  const scoreInputs = document.querySelectorAll("[data-testid='scoreInput']");
  const event = new Event("change", { bubbles: true });
  scoreInputs.forEach(scoreInput => {
    const maxScore = parseInt(scoreInput.getAttribute("data-assessment-score-limit"));
    const targetAt = parseInt(scoreInput.getAttribute("data-assessment-score-at"));
    const targetAbove = parseInt(scoreInput.getAttribute("data-assessment-score-limit")) / 5;
    let score = random(0, targetAt - 1);
    if (target === "at") {
      score = random(targetAt, targetAbove - 1);
    } else if (target === "above") {
      score = random(targetAbove, maxScore);
    } else if (target === "clear") {
      score = "";
    }
    Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set.call(scoreInput, score);
    scoreInput.dispatchEvent(event);
  });
}

function insertBelowScores() {
  fillScores("below");
}

function insertAtScores() {
  fillScores("at");
}

function insertAboveScores() {
  fillScores("above");
}

function clearScores() {
  fillScores("clear");
}

export function renderQuickActionButtons(justifyLocation) {
  return (
    <div className={`d-flex gap-1 justify-content-${justifyLocation} btn-group-xs`}>
      <button className="btn btn-inverse m-t-5 font-13" onClick={clearScores}>
        <i className="fa fa-trash" /> Clear
      </button>
      <button className="btn btn-danger m-t-5 font-13" onClick={insertBelowScores}>
        <i className="fa fa-times" /> Below
      </button>
      <button className="btn btn-warning m-t-5 font-13" onClick={insertAtScores}>
        <i className="fa fa-minus" /> At
      </button>
      <button className="btn btn-success m-t-5 font-13" onClick={insertAboveScores}>
        <i className="fa fa-check" /> Above
      </button>
    </div>
  );
}

export function isSuperAdminOrUniversalDataAdminOrDataAdmin(userRole = "") {
  const roleIds = [ROLE_IDS.dataAdmin, ROLE_IDS.universalDataAdmin, ROLE_IDS.superAdmin];
  const userRoleId = userRole.includes("arbitraryId") ? userRole : `arbitraryId${userRole}`;
  return roleIds.includes(userRoleId);
}

export function getSchoolNumberComparison(importData, existingSites) {
  const importDataSchoolNumbers = Array.from(new Set(importData.map(d => d.SchoolID)));
  const existingSchoolNumbers = existingSites.map(s => s.stateInformation.schoolNumber);

  const importDataSchoolNumbersByExistingSchoolNumbers = prepareSitesWithPotentialNormalization(
    existingSites,
    importDataSchoolNumbers,
    false
  );

  const matchedExistingSchoolNumbers = existingSchoolNumbers.map(
    s => importDataSchoolNumbersByExistingSchoolNumbers[s] || s
  );

  const schoolNumbersToBeAdded = difference(importDataSchoolNumbers, matchedExistingSchoolNumbers);

  return { matchedExistingSchoolNumbers, schoolNumbersToBeAdded, importDataSchoolNumbersByExistingSchoolNumbers };
}

export function arePropsEqual(oldProps, newProps) {
  return isEqual(oldProps, newProps);
}

export function renderFooter({ hasSideNav = false } = {}) {
  const calendarYear = new Date().getFullYear();
  return (
    <footer className={`d-flex flex-row footer-${hasSideNav ? "with" : "without"}-side-nav`}>
      <div className="footer-block flex-grow-1 text-nowrap">
        © {calendarYear} Education Research & Consulting. All rights reserved. VanDerHeyden, A. (2025) “System and Data
        Structure for Guided Learning,” U.S. Patent No. 12,223,855.
      </div>
      <div className="text-end">{layoutMethods.version()}</div>
    </footer>
  );
}

export function getZendeskUrl(callback) {
  Meteor.call("getMeteorPrivateSettings", ["ZENDESK_SPRINGMATH_SUBDOMAIN"], (err, settings) => {
    if (err) {
      callback({ error: "There was a problem getting Zendesk url" });
    } else {
      Meteor.call("zendesk:getCurrentUserJWTToken", (error, jwt) => {
        if (error) {
          callback({ error: "There was a problem getting Zendesk url" });
        } else {
          const returnTo = encodeURIComponent(`https://${settings.ZENDESK_SPRINGMATH_SUBDOMAIN}.zendesk.com`);
          const action = `https://${settings.ZENDESK_SPRINGMATH_SUBDOMAIN}.zendesk.com/access/jwt?jwt=${jwt}&return_to=${returnTo}`;
          callback({ action, jwt });
        }
      });
    }
  });
}
