import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useCallback } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Dropdown, DropdownButton } from "react-bootstrap";
import Select from "react-select";

import { check } from "meteor/check";
import { Link } from "react-router-dom";
import { startCase } from "lodash";
import OrgStatColumns from "./org-stat-columns";
import ConfirmModal from "../../pages/data-admin/confirm-modal";
import Loading from "../loading";
import { isSML } from "/imports/api/utilities/utilities";
import { isExternalRostering } from "../../utilities";
import { getUserRoles } from "/imports/ui/pages/data-admin/utilities";

export default function ClientRow(props) {
  const [isDeactivateClientModalOpen, setIsDeactivateClientModalOpen] = useState(false);
  const [allowClasswideWithoutScreening, setAllowClasswideWithoutScreening] = useState(false);
  const [allowMultipleGradeLevels, setAllowMultipleGradeLevels] = useState(false);
  const [rostering, setRostering] = useState(props.org?.rostering || "");

  const setters = {
    allowClasswideWithoutScreening: setAllowClasswideWithoutScreening,
    allowMultipleGradeLevels: setAllowMultipleGradeLevels,
    rostering: setRostering
  };

  const {
    org: { _id: orgId, name, isTestOrg, isActive, details, isMFARequired, ssoIssuerOrgId, useSSOOnly },
    dataAdminUsers,
    studentCount,
    isHidden,
    isUniversalDataAdmin,
    isSuperAdmin,
    toggleActiveClientState,
    isSupportClientView,
    schoolYear,
    shouldShowStats,
    onStatsLoaded,
    shouldDisplayManageTestData
  } = props;

  const getOrganizationFieldValues = useCallback(() => {
    // Check if user has required permissions
    const userRoles = getUserRoles();
    const hasPermission = isSuperAdmin || isUniversalDataAdmin || userRoles.includes("dataAdmin");

    if (!hasPermission) {
      // User doesn't have permission - exit silently
      return;
    }

    Meteor.call(
      "Organizations:getOrganizationFieldValues",
      orgId,
      ["allowClasswideWithoutScreening", "allowMultipleGradeLevels", "rostering"],
      (err, resp) => {
        if (!err && resp) {
          setAllowClasswideWithoutScreening(resp.allowClasswideWithoutScreening || false);
          setAllowMultipleGradeLevels(resp.allowMultipleGradeLevels || false);
          setRostering(resp.rostering || "");
        }
      }
    );
  }, [isSuperAdmin, isUniversalDataAdmin, orgId]);

  useEffect(() => {
    getOrganizationFieldValues();
  }, []);

  const updateOrganizationFieldValue = keyToSet => event => {
    const eventValue = event.value || event.target.checked;
    Meteor.call("Organizations:updateOrganizationFieldValue", orgId, keyToSet, eventValue, err => {
      if (!err) {
        Alert.success(`Successfully updated ${startCase(keyToSet)}`);
      }
    });
    if (keyToSet === "rostering" && isExternalRostering(rostering) && rostering !== eventValue) {
      Meteor.call("Cron:removeCronJob", orgId, (err, resp) => {
        if (!err) {
          Alert.success(resp);
        }
      });
    }
    setters[keyToSet](eventValue);
  };

  const dbRestore = (orgid, msg, type) => {
    check(orgid, String);
    check(msg, String);
    check(type, String);
    // eslint-disable-next-line no-restricted-globals
    const r = confirm(msg);
    if (r !== true) {
      return false;
    }
    // eslint-disable-next-line no-restricted-globals
    const r2 = confirm("Are you sure? THIS CANNOT BE UNDONE!");
    if (r2 !== true) {
      return false;
    }
    return Meteor.call(type, orgid, err => {
      if (err) {
        Alert.error(err.reason || err.message);
        return;
      }
      if (type === "removeOrg") {
        props.onClientRemoved?.(orgid);
      }
      Alert.success("Operation completed successfully");
    });
  };

  const openDeactivateClientModal = () => {
    setIsDeactivateClientModalOpen(true);
  };

  const closeDeactivateClientModal = () => {
    setIsDeactivateClientModalOpen(false);
  };

  const getHiddenDisplayStyle = () => (isHidden ? { display: "none" } : {});

  const renderRosteringMenu = () => {
    const rosterLabelByKey = {
      rosterImport: "Roster Import",
      rosterUpload: "File Upload",
      rosterEdFi: "Ed-Fi",
      rosterOR: "OneRoster"
    };
    // TODO(fmazur) - rewrite to not call db
    if (isSML(orgId)) {
      delete rosterLabelByKey.rosterEdFi;
      delete rosterLabelByKey.rosterOR;
    }
    const options = Object.entries(rosterLabelByKey).map(([value, label]) => ({ value, label }));

    return rostering ? (
      <div data-testid={`${orgId}_rostering`}>
        <Select
          onChange={updateOrganizationFieldValue("rostering")}
          classNamePrefix="react-select"
          value={{ value: rostering, label: rosterLabelByKey[rostering] }}
          options={options}
        />
      </div>
    ) : (
      <Loading inline={true} />
    );
  };

  const selectRestoreOption = type => {
    const message = {
      saveDemoDB:
        "Are you sure you want to save a new restore point for this demo organization? This will delete your previous restore point.",
      refreshDemoDB: "Are you sure you want to restore your data from your saved restore point?",
      clearDemoDB:
        "Are you sure you want to clear your data? This will remove all test scores. It will not affect your current restore point.",
      removeOrg: "Are you sure you want to remove this organization? This will remove all data."
    };
    return dbRestore(orgId, message[type], type);
  };

  const renderManageTestDataButtons = () => {
    return (
      !isUniversalDataAdmin &&
      isSuperAdmin && (
        <DropdownButton title="Manage" id="selectClass" onSelect={selectRestoreOption}>
          {isTestOrg && (
            <>
              <Dropdown.Item eventKey="saveDemoDB" data-testid="save-org">
                <i className="fa fa-save m-r-5" /> Save Restore Point
              </Dropdown.Item>
              <Dropdown.Item eventKey="refreshDemoDB" data-testid="restore-org">
                <i className="fa fa-refresh m-r-5" /> Restore Saved Data
              </Dropdown.Item>
            </>
          )}
          <Dropdown.Item eventKey="clearDemoDB" data-testid="clear-org">
            <i className="fa fa-trash m-r-5" /> Clear Active Data
          </Dropdown.Item>
          <Dropdown.Item eventKey="removeOrg" data-testid="remove-org">
            <i className="fa fa-trash m-r-5" /> Remove organization
          </Dropdown.Item>
        </DropdownButton>
      )
    );
  };

  const renderAccountStatus = () => {
    let buttonElement = null;
    if (isSuperAdmin) {
      buttonElement = isActive ? (
        <button
          data-testid="deactivateOrganizationButton"
          className="btn btn-sm btn-danger"
          onClick={openDeactivateClientModal}
        >
          Deactivate
        </button>
      ) : (
        <button
          data-testid="reactivateOrganizationButton"
          className="btn btn-sm btn-success"
          onClick={() => toggleActiveClientState(orgId, "reactivate")}
        >
          Reactivate
        </button>
      );
    }

    return (
      <td className="col-md-1 text-center text-nowrap align-content-center">
        <div className="d-flex flex-column">
          <small className={isActive ? "text-success" : "text-danger"}>{isActive ? "Active" : "Inactive"} </small>
          {buttonElement}
        </div>
        {isDeactivateClientModalOpen ? (
          <ConfirmModal
            showModal={isDeactivateClientModalOpen}
            onCloseModal={closeDeactivateClientModal}
            confirmAction={() => toggleActiveClientState(orgId, "deactivate")}
            headerText="Are you sure you want to deactivate this client?"
            bodyQuestion="By making this account inactive the users will no longer be able to login to SpringMath."
            confirmText="Yes, deactivate client"
          />
        ) : null}
      </td>
    );
  };

  let prettyCityState = "";
  if (details) {
    const state = details.state || "";
    const city = details.city || "";
    prettyCityState = city.length > 0 && state.length > 0 ? `${city}, ${state}` : "";
  }

  let prettyNamesJoined = "Not Found";
  const prettyDANames = [];
  if (dataAdminUsers && dataAdminUsers.length) {
    dataAdminUsers.forEach(dataAdminUser => {
      if (dataAdminUser && dataAdminUser.profile && dataAdminUser.profile.name) {
        const firstName = dataAdminUser.profile.name.first;
        const lastName = dataAdminUser.profile.name.last;
        prettyDANames.push(prettyName(firstName, lastName));
      }
    });
    prettyNamesJoined = prettyDANames.join(", ");
  }

  const dataAdminUrl = `/data-admin-account-setup/${orgId}`;
  let localStudentCount;
  switch (studentCount) {
    case 0:
      localStudentCount = "No students";
      break;
    case 1:
      localStudentCount = "1 student";
      break;
    default:
      localStudentCount = `${studentCount} students`;
  }
  const sitesDashboardRoute = isSuperAdmin || isUniversalDataAdmin ? "/data-admin/dashboard" : "/support/dashboard";

  return (
    <tr key={`${orgId}_row`} style={getHiddenDisplayStyle()} data-testid={`${orgId}_row`}>
      <td className="col-md-4 align-content-center">
        {isMFARequired ? <div className="pull-right org-icon org-icon--mfa m-l-5">MFA</div> : null}
        {ssoIssuerOrgId ? (
          <div className={`pull-right org-icon org-icon--sso${useSSOOnly ? "-only" : ""} m-l-5`}>
            SSO{useSSOOnly ? <div className="small">ONLY</div> : null}
          </div>
        ) : null}
        <div data-testid={name}>
          <Link to={`${sitesDashboardRoute}/${orgId}`} data-testid="orgLink">
            {name}
          </Link>
        </div>
        <span>{prettyCityState}</span>
      </td>
      <td className="col-md-2 text-center align-content-center">
        {prettyNamesJoined}{" "}
        {isSuperAdmin || isUniversalDataAdmin ? (
          <Link to={dataAdminUrl}>
            <i className="fa fa-plus" />
          </Link>
        ) : null}
      </td>
      {renderAccountStatus()}
      <td className="col-md-1 text-nowrap align-content-center">
        <span>{localStudentCount}</span>
      </td>
      {isSupportClientView && (
        <OrgStatColumns
          org={props.org}
          studentCount={studentCount}
          shouldShowStats={shouldShowStats}
          onStatsLoaded={onStatsLoaded}
          schoolYear={schoolYear}
        />
      )}
      {!isSupportClientView && <th className="col-md-2 align-content-center">{renderRosteringMenu()}</th>}
      {!isSupportClientView ? (
        <td className="col-md-1 text-center align-content-center">
          <input
            type="checkbox"
            data-testid={`${orgId}_allowClasswideWithoutScreening`}
            onChange={updateOrganizationFieldValue("allowClasswideWithoutScreening")}
            checked={allowClasswideWithoutScreening}
          />
        </td>
      ) : null}
      {!isSupportClientView ? (
        <td className="col-md-1 text-center align-content-center">
          <input
            type="checkbox"
            data-testid={`${orgId}_allowMultipleGradeLevels`}
            onChange={updateOrganizationFieldValue("allowMultipleGradeLevels")}
            checked={allowMultipleGradeLevels}
          />
        </td>
      ) : null}
      {isSuperAdmin && shouldDisplayManageTestData && (
        <td className="col-md-1 align-content-center">{renderManageTestDataButtons()}</td>
      )}
    </tr>
  );
}

function prettyName(first, last) {
  return first.length > 0 && last.length > 0 ? `${first} ${last}` : first + last;
}

ClientRow.propTypes = {
  org: PropTypes.object,
  dataAdminUsers: PropTypes.array,
  isActive: PropTypes.bool,
  studentCount: PropTypes.number,
  isSuperAdmin: PropTypes.bool,
  isSupportClientView: PropTypes.bool,
  isUniversalDataAdmin: PropTypes.bool,
  isHidden: PropTypes.bool,
  shouldDisplayManageTestData: PropTypes.bool,
  shouldShowStats: PropTypes.bool,
  onStatsLoaded: PropTypes.func,
  toggleActiveClientState: PropTypes.func,
  schoolYear: PropTypes.number,
  onClientRemoved: PropTypes.func
};
