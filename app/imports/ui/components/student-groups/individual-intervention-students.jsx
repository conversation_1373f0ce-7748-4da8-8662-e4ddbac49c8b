import React, { useContext, useState, useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { Meteor } from "meteor/meteor";
import * as helpers from "./helperFunction";

import StudentName from "./StudentName.jsx";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";

const IndividualInterventionStudents = props => {
  const { studentGroup } = useContext(StudentGroupContext);
  const [individualStats, setIndividualStats] = useState(null);
  const [loading, setLoading] = useState(false);

  const lastScoreUpdatedAtByStudentId = props.individualAssessmentResults.reduce((acc, result) => {
    if (!acc[result.studentId] || (result.lastScoreUpdatedAt && result.lastScoreUpdatedAt > acc[result.studentId])) {
      acc[result.studentId] = result.lastScoreUpdatedAt;
    }
    return acc;
  }, {});

  const studentIds = useMemo(
    () =>
      props.individualInterventionStudents
        ?.map(s => s._id)
        .sort()
        .join(",") || "",
    [props.individualInterventionStudents]
  );

  useEffect(() => {
    if (!studentGroup || !studentGroup._id || !props.individualInterventionStudents?.length) {
      setIndividualStats(null);
      return;
    }

    setLoading(true);

    Meteor.call("CalculateIndividualStats", studentGroup, (err, result) => {
      setLoading(false);
      if (err) {
        setIndividualStats(null);
      } else {
        setIndividualStats(result);
      }
    });
  }, [studentGroup?._id, studentIds]);

  if (loading) {
    return (
      <div className="text-center">
        <i className="fa fa-spinner fa-spin" /> Loading stats...
      </div>
    );
  }

  return (
    <div>
      {helpers.sortStudentsByName(props.individualInterventionStudents).map((s, i) => {
        const individualResults = individualStats?.individualResults || [];
        const individualStatsForStudent = individualResults.find(stats => stats.studentId === s._id);
        const lastScoreUpdatedAtForStudent = lastScoreUpdatedAtByStudentId[s._id];
        const lastScoreUpdatedAt =
          (lastScoreUpdatedAtForStudent && moment(lastScoreUpdatedAtForStudent).format("L")) || "N/A";
        return (
          <div key={i} className="row">
            <StudentName student={s} studentGroup={studentGroup} />
            <div
              className="col-md-2 row-individual-intervention-currentAssessment"
              data-testid={`currentIntAssessmentRow_${i}`}
            >
              {s.currentSkill.assessmentName}
            </div>

            {individualStatsForStudent && typeof individualStatsForStudent.interventionConsistency === "number" ? (
              <div
                className={`col-md-2 text-center${
                  individualStatsForStudent.interventionConsistency < 80 ? " text-danger" : ""
                }`}
              >
                {`${individualStatsForStudent.interventionConsistency}%`}
                <small>{`${individualStatsForStudent.numberOfWeeksWithScoresEntered} of ${individualStatsForStudent.numberOfWeeksActive} weeks with scores`}</small>
              </div>
            ) : (
              <div className="col-md-2 text-center">N/A</div>
            )}
            <div className="col-md-2 text-center">
              {individualStatsForStudent ? individualStatsForStudent.averageWeeksPerSkill || "N/A" : "N/A"}
            </div>
            <div className="col-md-2 text-center">{individualStatsForStudent ? lastScoreUpdatedAt : "N/A"}</div>
          </div>
        );
      })}
    </div>
  );
};

IndividualInterventionStudents.propTypes = {
  individualInterventionStudents: PropTypes.array,
  individualAssessmentResults: PropTypes.array
};

export default IndividualInterventionStudents;
