import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import * as messageNoticeUtils from "/imports/api/messageNotices/methods.js";

function MessageNotice({ noticeLocation, expandedStateCB, expandedNoticeState }) {
  const [messageNotice, setMessageNotice] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadMessageNotice = async () => {
      try {
        setLoading(true);
        const notice = await messageNoticeUtils.getMessageNoticeByLocation(noticeLocation);

        // Apply the props modifications if notice exists
        if (notice && notice.restOfComponent && notice.reactTypeOfKeyValue) {
          notice.restOfComponent.props.expandedStateCB = expandedStateCB;
          notice.restOfComponent.props.expandedNoticeState = !!expandedNoticeState; // in case of undefined
        }

        setMessageNotice(notice);
      } catch (error) {
        setMessageNotice(null);
      } finally {
        setLoading(false);
      }
    };

    loadMessageNotice();
  }, [noticeLocation, expandedStateCB, expandedNoticeState]);

  if (loading) {
    return null;
  }

  if (messageNotice && messageNotice.restOfComponent && messageNotice.reactTypeOfKeyValue) {
    // mini mongo doesn't like storing react components inside of itself, so this is a workaround.
    return {
      $$typeof: messageNotice.reactTypeOfKeyValue,
      ...messageNotice.restOfComponent
    };
  }

  return null;
}

MessageNotice.propTypes = {
  noticeLocation: PropTypes.string.isRequired,
  expandedStateCB: PropTypes.func,
  expandedNoticeState: PropTypes.bool
};

export default MessageNotice;
