import React, { Component } from "react";
import PropTypes from "prop-types";

// Used on Student and Classwide Detail Pages
function renderLabel(skill) {
  const isCurrentSkill = skill.complete && skill.active === true;
  const isPreviousSkill = skill.complete && skill.active === undefined;
  const isClickableSkill = skill.complete && !skill.active;

  if (isCurrentSkill) {
    return (
      <React.Fragment>
        <i className={"fa fa-circle"} />
        <label className="current-skill">{skill.name}</label>
      </React.Fragment>
    );
  }
  if (isPreviousSkill) {
    return (
      <React.Fragment>
        <i className="fa fa-circle-o" />
        <label className="previous-skill">{skill.name}</label>
      </React.Fragment>
    );
  }
  if (isClickableSkill) {
    return (
      <React.Fragment>
        <i className={"fa fa-check"} />
        <label className="clickable-skill">{skill.name}</label>
      </React.Fragment>
    );
  }
  return (
    <React.Fragment>
      <i className="fa fa-circle-o" />
      <label>{skill.name}</label>
    </React.Fragment>
  );
}
export default class ClasswideSkillList extends Component {
  renderSkills() {
    const { skillList } = this.props;
    return skillList.map((skill, i) => {
      const selectedSkillClassName = this.props.selectedSkillIndex === i ? "selected" : "";
      const activeSkillClassName = skill.active === undefined ? "" : "active";
      return (
        <div
          key={skill.key}
          className={`skill-item ${activeSkillClassName} ${selectedSkillClassName}`}
          data-testid={skill.complete && skill.active === true ? "currentSkill" : null}
          onClick={() => this.props.setSelectedAssessment(i)}
        >
          {renderLabel(skill)}
        </div>
      );
    });
  }

  render() {
    return (
      <div className="d-none d-lg-block col-md-3 print-display page-break-before">
        <div className="skill-progress page-break-avoid" data-testid="skill-tree-progress">
          <h4>Skill Tree Progress</h4>
          {this.props.skillList && this.props.skillList.length > 0 ? (
            <div className="skill-map-scroll">
              <div className="intervention-dots">{this.renderSkills()}</div>
            </div>
          ) : null}
        </div>
      </div>
    );
  }
}

ClasswideSkillList.propTypes = {
  skillList: PropTypes.array,
  setSelectedAssessment: PropTypes.func,
  selectedSkillIndex: PropTypes.number
};
