import { assert } from "chai";
import React from "react";
import { render, mount } from "enzyme";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { compileStudentScores, PureStudentLog } from "./student-log";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import {
  allClasswideScores,
  enrollments,
  groupChangeEnrollments,
  groupLeaveEnrollment,
  individualInterventionScores,
  leftGroup,
  newGroup,
  otherEnrollmentsResults,
  otherStudentGroups,
  previousGroup,
  student,
  verifyRenderedText
} from "./testHelpers";
import { StudentContext } from "/imports/contexts/StudentContext";

jest.mock("../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2018),
  getFormattedSchoolYear: jest.fn(() => "2017-18")
}));

describe("imports/ui/components/student-detail/student-log.jsx tests", () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });
  describe("StudentLog methods tests", () => {
    let studentLog;

    beforeEach(async () => {
      await StudentGroups.insertAsync(previousGroup);
      await StudentGroups.insertAsync(newGroup);
      await StudentGroups.insertAsync(leftGroup);
      studentLog = mount(
        <StudentContext.Provider value={{ student }}>
          <PureStudentLog
            student={student}
            studentEnrollments={enrollments}
            allClasswideScores={allClasswideScores}
            individualInterventionScores={individualInterventionScores}
            otherResults={otherEnrollmentsResults}
            otherStudentGroups={otherStudentGroups}
          />
        </StudentContext.Provider>
      );
    });
    afterEach(async () => {
      await StudentGroups.removeAsync({});
    });
    it("should render enrollments", () => {
      const renderedEnrollments = studentLog.instance().renderEnrollments();

      const expectedTimeline = [
        ["Jan 13", "TestUserFirstName moved from group previousGroupName to newGroupName"],
        ["Jan 30", "TestUserFirstName moved from group newGroupName to leftGroupName"],
        ["Jan 30", "TestUserFirstName was removed from leftGroupName"]
      ];
      verifyRenderedText(expectedTimeline, renderedEnrollments);
    });
    it("should display a note that the individual intervention was closed when a new enrollment was created in another grade", () => {
      const groupChangeEnrollmentsWithInterventionClosed = [
        groupChangeEnrollments[0],
        {
          ...groupChangeEnrollments[1],
          wasIndividualInterventionClosed: true,
          grade: "05"
        }
      ];
      const updatedEnrollments = [...groupChangeEnrollmentsWithInterventionClosed, ...groupLeaveEnrollment];
      studentLog = mount(
        <StudentContext.Provider value={{ student }}>
          <PureStudentLog
            student={student}
            studentEnrollments={updatedEnrollments}
            allClasswideScores={allClasswideScores}
            individualInterventionScores={individualInterventionScores}
            otherResults={otherEnrollmentsResults}
            otherStudentGroups={otherStudentGroups}
          />
        </StudentContext.Provider>
      );

      const renderedEnrollments = studentLog.instance().renderEnrollments();

      const interventionClosedText = render(renderedEnrollments[0])
        .find("#intervention-closed-text")
        .text();
      assert.equal(
        interventionClosedText,
        "Individual intervention was closed",
        "should display a note that an intervention was closed"
      );
    });
    it("should render student scores sorted by the newest score", () => {
      const result = studentLog.instance().renderScores();

      const expectedTimeline = [
        ["Jan 19", "Fall 2017-18", "ScreeningTestUserFirstName's score on testAssessmentName was 10."],
        ["Jan 18", "InterventionTestUserFirstName's score on testAssessmentName was 10."],
        [
          "Jan 17",
          "Winter 2017-18",
          "ScreeningTestUserFirstName's class was assessed on testAssessmentName, but TestUserFirstName was absent or unavailable."
        ],
        [
          "Jan 15",
          "InterventionTestUserFirstName's class was assessed on testAssessmentName, but TestUserFirstName was absent or unavailable."
        ],
        ["Jan 12", "InterventionTestUserFirstName's score on classwideAssessment 1 was 0."],
        [
          "Jan 11",
          "Winter 2017-18",
          "ScreeningTestUserFirstName's score on benchmarkAssessment 1 was 0.TestUserFirstName's score on benchmarkAssessment 2 was 0."
        ]
      ];
      verifyRenderedText(expectedTimeline, result);
    });
    it("should render sorted student scores and group migration", () => {
      const result = studentLog.instance().renderHistory();

      const expectedTimeline = [
        ["Jan 30", "TestUserFirstName was removed from leftGroupName"],
        ["Jan 30", "TestUserFirstName moved from group newGroupName to leftGroupName"],
        ["Jan 19", "Fall 2017-18 Screening", "TestUserFirstName's score on testAssessmentName was 10"],
        ["Jan 18", "Individual Intervention", "TestUserFirstName's score on testAssessmentName was 10."],
        [
          "Jan 17",
          "Winter 2017-18",
          "ScreeningTestUserFirstName's class was assessed on testAssessmentName, but TestUserFirstName was absent or unavailable"
        ],
        [
          "Jan 15",
          "Individual Intervention",
          "TestUserFirstName's class was assessed on testAssessmentName, but TestUserFirstName was absent or unavailable."
        ],
        ["Jan 13", "TestUserFirstName moved from group previousGroupName to newGroupName"],
        ["Jan 12", "Classwide Intervention", "TestUserFirstName's score on classwideAssessment 1 was 0."],
        [
          "Jan 11",
          "Winter 2017-18",
          "ScreeningTestUserFirstName's score on benchmarkAssessment 1 was 0.TestUserFirstName's score on benchmarkAssessment 2 was 0."
        ]
      ];
      verifyRenderedText(expectedTimeline, result);
    });
  });
  describe("compileStudentScores test", () => {
    const compiledScores = compileStudentScores(allClasswideScores, individualInterventionScores);

    assert.equal(compiledScores[0].whenEnded.on, 1516358047305, "should return sorted scores");
    assert.equal(compiledScores[1].whenEnded.on, 1516273047305, "should return sorted scores");
    assert.equal(compiledScores[2].whenEnded.on, 1516183847305, "should return sorted scores");
    assert.equal(compiledScores[3].whenEnded.on, 1516011847305, "should return sorted scores");
  });
  describe("reformatAssessmentResults", () => {
    const assessmentResults = [
      {
        lastModified: {
          by: "",
          date: "Wed Jan 31 2018 14:14:29 GMT+0100 (CET)",
          on: 1517404469132
        },
        measures: [],
        type: "benchmark"
      },
      {
        lastModified: {
          by: "",
          date: "Wed Jan 29 2018 14:14:29 GMT+0100 (CET)",
          on: 1517404469234
        },
        measures: [],
        type: "classwide"
      }
    ];

    const expectedResult = [
      {
        whenEnded: {
          by: "",
          date: "Wed Jan 31 2018 14:14:29 GMT+0100 (CET)",
          on: 1517404469132
        },
        assessmentResultMeasures: [],
        type: "benchmark"
      },
      {
        whenEnded: {
          by: "",
          date: "Wed Jan 29 2018 14:14:29 GMT+0100 (CET)",
          on: 1517404469234
        },
        assessmentResultMeasures: [],
        type: "classwide"
      }
    ];

    const result = reformatAssessmentResults(assessmentResults);

    expect(result).toEqual(expectedResult);
  });
});
