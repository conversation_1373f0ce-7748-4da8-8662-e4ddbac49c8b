import { assert } from "chai";
import moment from "moment";
import React from "react";
import { shallow } from "enzyme";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups.js";
import GroupChangeEntry from "./GroupChangeEntry";

const groupChangeEnrollments = [
  {
    _id: "firstGroupEnrollmentId",
    created: {
      date: new Date("2018-01-19T10:44:07.305Z"),
      on: 1516358647305
    },
    studentGroupId: "previousGroupId"
  },
  {
    _id: "secondGroupEnrollmentId",
    created: {
      date: new Date("2018-02-19T10:44:17.305Z"),
      on: 1519033457000
    },
    studentGroupId: "newGroupId"
  }
];

const previousGroup = {
  _id: "previousGroupId",
  name: "previousGroupName",
  created: { on: 1516358647305, date: new Date("2018-01-19T10:44:07.305Z") }
};

const newGroup = {
  _id: "newGroupId",
  name: "newGroupN<PERSON>",
  created: { on: 1516358657305, date: new Date("2018-01-19T10:44:17.305Z") }
};

describe("imports/ui/components/student-detail/student-log.jsx tests", () => {
  describe("StudentLog methods tests", () => {
    let groupChangeEntry;

    beforeEach(async () => {
      await StudentGroups.insertAsync(previousGroup);
      await StudentGroups.insertAsync(newGroup);
      groupChangeEntry = shallow(
        <GroupChangeEntry
          firstName={"TestUserFirstName"}
          previousGroupName={previousGroup.name}
          newGroupName={newGroup.name}
          enrollmentChangeDate={moment(groupChangeEnrollments[1].created.date).format("MMM DD")}
        />
      );
    });
    afterEach(async () => {
      await StudentGroups.removeAsync({});
    });

    it("should return group change entry from provided data", () => {
      assert.equal(
        groupChangeEntry.text(),
        "Feb 19TestUserFirstName moved from group previousGroupName to newGroupName",
        "should return group change entry"
      );
    });
  });
});
