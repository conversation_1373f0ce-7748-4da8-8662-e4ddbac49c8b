import React, { useState, useEffect, useContext } from "react";
import { Loading } from "../loading.jsx";
import { Rules } from "/imports/api/rules/rules";
import ClasswidePMChart from "./student-detail-PM-Chart.jsx";
import {
  calculateClasswideROI,
  getPageBreakClassForEverySecondElementByIndex,
  MAX_SKILLS_FROM_NEXT_GRADE
} from "/imports/api/utilities/utilities";
import getSkillHistory from "./getSkillHistory";
import GraphTitle from "./GraphTitle";
import { StudentContext } from "../../../contexts/StudentContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";
import { isOnPrintPage } from "../../utilities";

export default function ClasswideInterventionGraphHistory() {
  const { student } = useContext(StudentContext);
  const { studentGroup, studentsInStudentGroup } = useContext(StudentGroupContext);
  const { assessments } = useContext(StaticDataContext);

  const isPrinting = isOnPrintPage();

  const [classwideROI, setClasswideROI] = useState("N/A");
  const [studentROI, setStudentROI] = useState("N/A");
  const [skillList, setSkillList] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadSkillData = () => {
    if (!studentGroup || !studentsInStudentGroup || !assessments) {
      return;
    }

    try {
      const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;
      const gradeLevelRules = Rules.findOne({ grade: studentGroup.grade });
      const additionalGradeLevelRules = additionalGradeForRules
        ? Rules.findOne({ grade: additionalGradeForRules })
        : undefined;

      const studentGroupHistory = JSON.parse(JSON.stringify(studentGroup.history));
      const additionalStudentGroupHistory = JSON.parse(JSON.stringify(studentGroup.additionalHistory || []));

      const defaultSkillList = getSkillHistory(
        studentGroupHistory,
        gradeLevelRules,
        studentGroup,
        studentsInStudentGroup,
        undefined,
        false,
        assessments
      );
      const isAdditionalSkillList = true;

      const additionalSkillListFull = getSkillHistory(
        additionalStudentGroupHistory,
        additionalGradeLevelRules,
        studentGroup,
        studentsInStudentGroup,
        undefined,
        isAdditionalSkillList,
        assessments
      );

      const additionalSkillList = (additionalSkillListFull || []).slice(0, MAX_SKILLS_FROM_NEXT_GRADE);
      const newSkillList = [...(defaultSkillList || []), ...(additionalSkillList || [])];

      setSkillList(newSkillList);

      const newClasswideROI = calculateClasswideROI(studentGroup);
      const newStudentROI = student ? calculateClasswideROI(studentGroup, student) : null;
      setClasswideROI(newClasswideROI);
      setStudentROI(newStudentROI);

      setLoading(false);
    } catch (error) {
      console.error("Error loading skill data:", error);
      setLoading(false);
    }
  };

  const wasSkillPracticed = lastPracticedSkillIndex => {
    return (
      skillList[lastPracticedSkillIndex].allStudentsScores.length ||
      skillList[lastPracticedSkillIndex].studentScores.length
    );
  };

  const getSkillsWithScores = () => {
    const skillsWithScores = [];
    let currentSkillIndex = 0;
    skillList.forEach(skill => {
      if (wasSkillPracticed(currentSkillIndex)) {
        skillsWithScores.push(skill);
      }
      currentSkillIndex += 1;
    });
    return skillsWithScores;
  };

  useEffect(() => {
    loadSkillData();
  }, [studentGroup, studentsInStudentGroup, assessments]);

  useEffect(() => {
    if (studentGroup && !loading) {
      setClasswideROI(calculateClasswideROI(studentGroup));
      setStudentROI(student ? calculateClasswideROI(studentGroup, student) : null);
    }
  }, [student, studentGroup, loading]);

  if (loading) {
    return <Loading />;
  }

  const chartOptions = {
    chartType: "line",
    title: "",
    height: 400,
    xAxisTitle: "",
    yAxisTitle: "Score",
    marginTop: 50,
    marginRight: 175
  };

  if (skillList && skillList.length > 0) {
    let name = "";
    let studentId = "";
    if (student) {
      name = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
      studentId = student._id;
    }
    return (
      <div data-testid="classwide-intervention-progress-section">
        <h3>Classwide Intervention Progress</h3>
        {getSkillsWithScores().map((skill, index) => (
          <section key={`${skill.id}_${index}`} className={getPageBreakClassForEverySecondElementByIndex(index, true)}>
            <div className="col-md-9 print-clear">
              <GraphTitle skill={skill} classwideROI={classwideROI} studentROI={studentROI} />
              <ClasswidePMChart
                key={`${skill.id}_${studentId}`}
                scores={skill}
                chartId={`classwide_PM_CHART_${skill.id}_${studentId}`}
                type="chart"
                options={chartOptions}
                pmName="Progress Monitoring Scores"
                studentName={name}
                shouldShowStudentsScores={isPrinting}
                showSelectedScores={undefined}
                selectedScoreDate={undefined}
                scoresClickable={true}
                studentGroup={studentGroup}
              />
            </div>
          </section>
        ))}
      </div>
    );
  }
  return <Loading />;
}
