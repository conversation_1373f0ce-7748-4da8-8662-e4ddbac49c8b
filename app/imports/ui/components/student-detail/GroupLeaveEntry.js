import React from "react";
import PropTypes from "prop-types";
import moment from "moment";

const GroupLeaveEntry = props => (
  <li className="activity-item" key={props.groupLeaveEnrollment.lastModified.on}>
    <span className="activity-date">{moment(props.groupLeaveEnrollment.lastModified.date).format("MMM DD")}</span>
    <h5>
      <span>
        {props.firstName} was removed from {props.groupName}
      </span>
    </h5>
  </li>
);

GroupLeaveEntry.propTypes = {
  groupLeaveEnrollment: PropTypes.object,
  firstName: PropTypes.string,
  groupName: PropTypes.string
};

export default GroupLeaveEntry;
