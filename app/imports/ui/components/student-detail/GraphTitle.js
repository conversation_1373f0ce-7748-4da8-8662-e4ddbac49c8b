import React from "react";
import PropTypes from "prop-types";

const GraphTitle = props => {
  if (props.skill) {
    return (
      <div>
        <h4 data-testid={props.skill.name}>
          <div className="float-end text-right">
            {props.classwideROI && <div className="roi">{`Classwide Rate of Improvement: ${props.classwideROI}`}</div>}
            {props.studentROI && (
              <div className="student-roi">{`Student Rate of Improvement: ${props.studentROI}`}</div>
            )}
          </div>
          {props.skill.active && (
            <span>
              <i className="fa fa-dot-circle-o " aria-hidden="true" />{" "}
              <span className="current-skill">Current Skill: </span>
            </span>
          )}
          {props.skill.name}
        </h4>
      </div>
    );
  }

  return <h4>Unknown Skill Name</h4>;
};

GraphTitle.propTypes = {
  skill: PropTypes.object,
  classwideROI: PropTypes.string,
  studentROI: PropTypes.string
};

export default GraphTitle;
