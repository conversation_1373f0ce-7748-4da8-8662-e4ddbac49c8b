import React from "react";
import PropTypes from "prop-types";

const IndividualGraphTitle = props => {
  let roiElement = "";
  if (props.skill && props.skill.roi) {
    roiElement = <span className="roiGoal">{`Rate of Improvement: ${props.skill.roi}`}</span>;
  }
  if (props.skill?.active) {
    return (
      <h4 data-testid={props.skill.assessmentName}>
        <i className={props.skill.active ? "fa fa-dot-circle-o" : null} />{" "}
        <span className="current-skill">
          Current {props.goalSkill ? "Goal " : "Intervention "}
          Skill:
        </span>
        {` ${props.skill.assessmentName}`}
        {roiElement}
      </h4>
    );
  }
  return (
    <h4 data-testid={props.skill?.assessmentName}>
      <span className="current-skill">
        {props.goalSkill ? "Goal " : "Intervention "}
        Skill:
      </span>
      {props.skill?.assessmentName}
      {roiElement}
    </h4>
  );
};

IndividualGraphTitle.propTypes = {
  goalSkill: PropTypes.bool,
  skill: PropTypes.object
};

export default IndividualGraphTitle;
