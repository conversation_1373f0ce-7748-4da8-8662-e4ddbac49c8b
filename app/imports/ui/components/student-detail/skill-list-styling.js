export function getIconClassName(skillInfo) {
  if (skillInfo.complete) {
    return skillInfo.drillDownOnlyScore ? "fa fa-circle-o active" : "fa fa-check";
  }
  if (skillInfo.active) {
    return "fa fa-circle";
  }
  return "fa fa-circle-o";
}

export function getLabelClasses(skillInfo, isSelectedGoal) {
  const classList = [];
  if ((skillInfo.active || skillInfo.complete) && (skillInfo.studentScores || skillInfo.interventions)) {
    classList.push("active");
  }
  if (isSelectedGoal) {
    classList.push("selected");
  }
  return classList.join(" ");
}
