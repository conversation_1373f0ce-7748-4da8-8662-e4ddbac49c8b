import React from "react";
import PropTypes from "prop-types";

const GroupChangeEntry = props => (
  <li className="activity-item">
    <span className="activity-date">{props.enrollmentChangeDate}</span>
    <h5>
      <span>
        {props.firstName} moved from group {props.previousGroupName} to {props.newGroupName}
      </span>
    </h5>
    {props.wasInterventionClosed && <p id="intervention-closed-text">Individual intervention was closed</p>}
  </li>
);

GroupChangeEntry.propTypes = {
  firstName: PropTypes.string,
  enrollmentChangeDate: PropTypes.string,
  previousGroupName: PropTypes.string,
  newGroupName: PropTypes.string,
  wasInterventionClosed: PropTypes.bool
};

export default GroupChangeEntry;
