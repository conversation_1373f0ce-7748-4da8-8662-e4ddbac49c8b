import React from "react";
import PropTypes from "prop-types";

const MissingScoreText = props => {
  let text;
  if (props.wasStudentEnrolled) {
    text = (
      <span>
        {props.studentName} was <strong>absent or unavailable</strong> during screening.
      </span>
    );
  } else {
    text = props.groupName ? (
      <span>
        {props.studentName} was enrolled in class {props.groupName} when they took the screening assessment.
        <strong> See the timeline below</strong> for their scores.
      </span>
    ) : (
      <span>
        {props.studentName} did <strong>not participate</strong> in this screening assessment.
      </span>
    );
  }
  return <div className="font-light mt-1">{text}</div>;
};

MissingScoreText.propTypes = {
  groupName: PropTypes.string,
  studentName: PropTypes.string,
  wasStudentEnrolled: PropTypes.bool
};

export default MissingScoreText;
