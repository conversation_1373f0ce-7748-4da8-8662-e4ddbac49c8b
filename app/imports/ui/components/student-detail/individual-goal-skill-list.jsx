import React, { Component } from "react";
import PropTypes from "prop-types";
import { translateBenchmarkPeriod } from "/imports/api/utilities/utilities";
import { getIconClassName, getLabelClasses } from "./skill-list-styling";

export default class IndividualGoalSkillList extends Component {
  renderGoalSkillList(benchmarkPeriodId) {
    const filteredGoalList = this.props.goalSkillList.filter(gs => gs.benchmarkPeriodId === benchmarkPeriodId);
    return (
      <div key={`goal_${benchmarkPeriodId}`} className="page-break-avoid goal-skills">
        <h5>{translateBenchmarkPeriod(benchmarkPeriodId).title} Goals</h5>
        {filteredGoalList.map((goalSkillInfo, i) => {
          let onclick = () => {};
          const goalSkillIcon = getIconClassName(goalSkillInfo);
          const isSelectedGoal = this.props.selectedGoal?.goalAssessmentId === goalSkillInfo.goalAssessmentId;
          if (
            (goalSkillInfo.studentScores && goalSkillInfo.studentScores.length) ||
            (goalSkillInfo.interventions && goalSkillInfo.interventions.some(ints => !!ints.interventionScores))
          ) {
            onclick = () => {
              this.props.setSelectedAssessment(goalSkillInfo.goalAssessmentId, null, true);
            };
          }
          let classNames = "skill-item active individual-skill-item-goal clearfix";
          if (i === filteredGoalList.length - 1) {
            classNames += " individual-skill-item-last-goal";
          }
          return (
            <div key={`${benchmarkPeriodId}_${i}`} className={classNames} onClick={onclick}>
              <i className={goalSkillIcon} />
              <label className={getLabelClasses(goalSkillInfo, isSelectedGoal)}>{goalSkillInfo.assessmentName}</label>
            </div>
          );
        })}
      </div>
    );
  }

  renderSkills() {
    return (
      <div className="intervention-dots">
        <div className="skill-list-title">Goal Skills</div>
        <div className="goal-skill-list">
          {this.props.activeBMPeriodIds.map(activeBMPeriodId => this.renderGoalSkillList(activeBMPeriodId))}
        </div>
      </div>
    );
  }

  render() {
    return <div className="skill-progress">{this.renderSkills()}</div>;
  }
}

IndividualGoalSkillList.propTypes = {
  activeBMPeriodIds: PropTypes.array,
  goalSkillList: PropTypes.array,
  selectedGoal: PropTypes.object,
  setSelectedAssessment: PropTypes.func
};
