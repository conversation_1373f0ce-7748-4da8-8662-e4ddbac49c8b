import { assert } from "chai";
import React from "react";
import { shallow } from "enzyme";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups.js";
import GroupLeaveEntry from "./GroupLeaveEntry";

const groupLeaveEnrollment = {
  _id: "thirdGroupEnrollmentId",
  created: {
    date: new Date("2018-01-19T10:44:27.305Z"),
    on: 1516358667305
  },
  lastModified: {
    date: new Date("2018-01-19T10:44:27.306Z"),
    on: 1516358667306
  },
  studentGroupId: "leftGroupId"
};

const leftGroup = {
  _id: "leftGroupId",
  name: "leftGroupName",
  created: { on: 1516358667305, date: new Date("2018-01-19T10:44:27.305Z") }
};

describe("imports/ui/components/student-detail/student-log.jsx tests", () => {
  describe("StudentLog methods tests", () => {
    let groupChangeEntry;

    beforeEach(async () => {
      await StudentGroups.insertAsync(leftGroup);
      groupChangeEntry = shallow(
        <GroupLeaveEntry
          firstName={"TestUserFirstName"}
          groupLeaveEnrollment={groupLeaveEnrollment}
          groupName={leftGroup.name}
        />
      );
    });
    afterEach(async () => {
      await StudentGroups.removeAsync({});
    });

    it("should return group leave entry from provided data", () => {
      assert.equal(
        groupChangeEntry.text(),
        "Jan 19TestUserFirstName was removed from leftGroupName",
        "should return group leave entry"
      );
    });
  });
});
