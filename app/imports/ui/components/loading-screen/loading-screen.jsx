import React from "react";
import PropTypes from "prop-types";

import { withTracker } from "meteor/react-meteor-data";
import { LoadingCounter } from "/imports/api/loadingCounter/loadingCounter.js";
import { incWaitingOn, decWaitingOn } from "/imports/api/loadingCounter/methods.js";

const loadingScreen = props => {
  if (props.waitingOn < 1) {
    return <div id="loaderContainerHolder" />;
  }
  this.incWaitingOn = incWaitingOn;
  this.decWaitingOn = decWaitingOn;
  return (
    <div id="loaderContainer">
      <div id="loader">
        <p id="loadingText" data-testid="loading-text">
          {props.messages.length > 0 ? props.messages[props.messages.length - 1] : "Loading"}
        </p>
      </div>
    </div>
  );
};

loadingScreen.propTypes = {
  waitingOn: PropTypes.number.isRequired,
  messages: PropTypes.arrayOf(PropTypes.string)
};

export default withTracker(() => {
  let waitingOn = 0;
  let messages = [];

  const lc = LoadingCounter.findOne();
  if (lc && lc.waitingOn) {
    waitingOn = lc.waitingOn;
    messages = lc.messages;
  }
  return { waitingOn, messages };
})(loadingScreen);
