import React, { useContext } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import YouTube from "react-youtube";
import { StaticDataContext } from "../../contexts/StaticDataContext";

// https://developers.google.com/youtube/player_parameters
const opts = {
  height: "656",
  width: "1076",
  playerVars: {
    autoplay: 0,
    iv_load_policy: 3,
    modestbranding: 1,
    rel: 0,
    origin: `${window.location.protocol}//${window.location.host}`,
    host: "https://www.youtube.com"
  }
};

const InstructionalVideoModal = props => {
  const { onCloseModal, closeModal, videoTimestamp, videoId, showModal, testIdPrefix, headerText } = props;
  const {
    env: { METEOR_ENVIRONMENT }
  } = useContext(StaticDataContext);

  const close = () => {
    onCloseModal();
    closeModal();
  };

  const playVideo = ({ target }) => {
    if (Number(videoTimestamp)) {
      target.seekTo(videoTimestamp);
    }
    target.playVideo();
  };

  const getPlayer = () => {
    if (Meteor.isDevelopment && METEOR_ENVIRONMENT === "TEST") {
      return null;
    }

    return <YouTube videoId={videoId} opts={opts} onReady={playVideo} />;
  };

  return (
    <Modal
      show={showModal}
      onHide={close}
      dialogClassName="instructional-video-modal"
      backdrop="static"
      data-testid={`${testIdPrefix}-instructional-video-modal`}
    >
      <ModalHeader>
        <h2 className="text-center text-info">{headerText}</h2>
      </ModalHeader>

      <ModalBody>
        <div data-testid="instructional-video">{getPlayer()}</div>
      </ModalBody>

      <ModalFooter className="d-flex justify-content-center">
        <Button variant="default" data-testid={`close-and-assign-${testIdPrefix}-button`} onClick={close}>
          Close
        </Button>
      </ModalFooter>
    </Modal>
  );
};

InstructionalVideoModal.propTypes = {
  showModal: PropTypes.bool,
  closeModal: PropTypes.func,
  videoId: PropTypes.string,
  headerText: PropTypes.string,
  testIdPrefix: PropTypes.string,
  onCloseModal: PropTypes.func,
  videoTimestamp: PropTypes.number
};

export default InstructionalVideoModal;
