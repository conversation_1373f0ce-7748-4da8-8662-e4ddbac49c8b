import React from "react";
import PropTypes from "prop-types";

export const Loading = ({ cssClasses = "", message = "", inline = false }) => (
  <div className={`spinner-${inline ? "inline" : "full"} ${cssClasses}`} data-testid="loading-icon">
    <div>
      <div className="rect1" />
      <div className="rect2" />
      <div className="rect3" />
      <div className="rect4" />
      <div className="rect5" />
    </div>
    {inline ? null : <br />}
    <span>{message}</span>
  </div>
);

Loading.propTypes = {
  cssClasses: PropTypes.string,
  inline: PropTypes.bool,
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.node])
};

export default Loading;
