import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import React, { useContext, useEffect, useState } from "react";
import { ListGroup } from "react-bootstrap";
import { <PERSON>, withRouter } from "react-router-dom";

import { Grades } from "/imports/api/grades/grades";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import * as utils from "/imports/api/utilities/utilities";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { SiteContext } from "/imports/contexts/SiteContext";
import { UserContext } from "/imports/contexts/UserContext";

import { getUserRoles } from "../pages/data-admin/utilities";
import { areSubscriptionsLoading } from "../utilities";
import { Loading } from "./loading.jsx";

const AdminSideNav = ({ loading, siteName, siteId, orgid, grades, organizationName, match }) => {
  const [shouldDisplayZendeskWidget, setShouldDisplayZendeskWidget] = useState(false);
  const [isFetchingZendeskData, setIsFetchingZendeskData] = useState(true);

  // Handle Zendesk widget flag fetching
  useEffect(() => {
    const userRole = getUserRoles();
    const isAllowedToFetchZendeskFlag = userRole.includes("teacher") || userRole.includes("admin");
    if (!isAllowedToFetchZendeskFlag || !siteId) {
      return;
    }
    Meteor.call("Settings:getZendeskWidgetFlag", siteId, (err, resp) => {
      if (!err) {
        setShouldDisplayZendeskWidget(resp);
      }
    });
  }, [siteId]);

  // Handle fetching data completion
  useEffect(() => {
    if (siteName && organizationName) {
      setIsFetchingZendeskData(false);
    }
  }, [siteName, organizationName]);

  const renderZendeskWidget = () => {
    if (!isFetchingZendeskData && shouldDisplayZendeskWidget) {
      return utils.renderZendeskWidget(organizationName, siteName);
    }
    return null;
  };

  const isActive = gradeId => (match.params.gradeId === gradeId ? " active" : "");

  if (loading) {
    // TODO(fmazur) - remove message
    return <Loading message="side nav admin" />;
  }

  return (
    <aside className="side-nav">
      <div className="site-selector" data-testid="siteSelectorId">
        {siteName}
      </div>
      <ListGroup className="student-grade-list student-group-list">
        <Link key="all" to={`/school-overview/${orgid}/all/${siteId}`} className={`${isActive("all")} list-group-item`}>
          School Overview
        </Link>
        {grades.map(({ _id, display }) => (
          <Link
            key={_id}
            to={`/school-overview/${orgid}/${_id}/${siteId}`}
            className={`${isActive(_id)} list-group-item`}
          >
            {getCurrentEnrolledGrade(display)}
          </Link>
        ))}
      </ListGroup>
      {renderZendeskWidget()}
    </aside>
  );
};

AdminSideNav.propTypes = {
  grades: PropTypes.array,
  loading: PropTypes.bool,
  organizationName: PropTypes.string,
  siteName: PropTypes.node,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  match: PropTypes.object
};

const AdminSideNavWithTracker = ({ orgid, siteId, history, ...props }) => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { org } = useContext(OrganizationContext);
  const { site, isLoading: isLoadingSiteContext } = useContext(SiteContext);
  const { user } = useContext(UserContext);

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, schoolYear);
    // FIXME(fmazur) - may break other components, potentially restore for now
    const userDataSub = Meteor.subscribe("userData");

    const loading = areSubscriptionsLoading(gradesSub, userDataSub);

    let grades = [];

    if (!loading) {
      if (!site && !isLoadingSiteContext) {
        history.push(user ? "/unauthorized" : "/login");
      }
      grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    }

    return {
      loading,
      grades,
      organizationName: org?.name,
      siteName: site?.name,
      orgid,
      siteId
    };
  }, [schoolYear, siteId, orgid, history]);

  return <AdminSideNav {...props} {...trackerData} />;
};

AdminSideNavWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  schoolYear: PropTypes.number
};

export default withRouter(AdminSideNavWithTracker);
