import React, { useEffect, useState, useCallback, useContext } from "react";
import PropTypes from "prop-types";

import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import queryString from "query-string";
import { cloneDeep, groupBy, keyBy, map, uniq } from "lodash";

import IndividualIntervention from "../../pages/dashboard/individual-intervention.jsx";
import FollowUpAssessmentContainer from "../../pages/dashboard/follow-up-assessment.jsx";
import IndividualComplete from "./individual-complete.jsx";
import ActiveSchoolYearMessage from "../ActiveSchoolYearMessage";
import InstructionalVideoModal from "../instructional-video-modal";
import { GroupedAssessmentResults } from "./grouped-assessment-results";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { OrganizationContext } from "../../../contexts/OrganizationContext";

function DashboardIndividual(props) {
  const { studentGroup, studentsInStudentGroup: students } = useContext(StudentGroupContext);
  const { siteId, schoolYear, orgid } = studentGroup || {};
  const { customDate, latestAvailableSchoolYear } = useContext(SchoolYearContext);
  const { org } = useContext(OrganizationContext);
  const { isTestOrg } = org || {};

  const inActiveSchoolYear = customDate?.length ? true : !schoolYear || schoolYear === latestAvailableSchoolYear;

  const [isFetchingSkillGroups, setIsFetchingSkillGroups] = useState(false);
  const [skillGroups, setSkillGroups] = useState([]);
  const [shouldOpenManualIIVideoPopup, setShouldOpenManualIIVideoPopup] = useState(false);
  const [videoId, setVideoId] = useState("");
  const [videoTimestamp, setVideoTimestamp] = useState("");

  const [groupedAssessments, setGroupedAssessments] = useState(props.groupedAssessments || []);
  const [measureNumberByAssessmentId, setMeasureNumberByAssessmentId] = useState(
    props.measureNumberByAssessmentId || {}
  );

  const setVideoData = useCallback(() => {
    const youTubeUrl = "https://www.youtube.com/watch?v=V5PPIQeXCD0&feature=youtu.be";
    const queryParameters = youTubeUrl.substring(youTubeUrl.indexOf("?"));
    const { v, t = "" } = queryString.parse(queryParameters);
    setVideoId(v);
    setVideoTimestamp(parseInt(t.replace(/[^0-9.]+/g, "")) || 0);
  }, []);

  useEffect(() => {
    setIsFetchingSkillGroups(true);
    Meteor.call("getSkillGroupAssignments", { siteId, schoolYear, orgid }, (err, resp) => {
      if (!err) {
        setSkillGroups(resp);
      } else {
        Alert.error("Error getting skill groups");
      }
      setIsFetchingSkillGroups(false);
    });
    setVideoData();
  }, [studentGroup, setVideoData]);

  useEffect(() => {
    if (props.groupedAssessments) {
      return;
    }

    const assessmentResultsInStudentGroup = props.assessmentResults.filter(assessmentResult =>
      students.find(student => student._id === assessmentResult.studentId)
    );
    // NOTE(fmazur) - assessmentResult assessmentIds contain ids for interventionSkill and goalSkill
    const assessmentIdsInGroup = uniq(assessmentResultsInStudentGroup.map(a => a.assessmentIds).flat(2));
    Meteor.call("getGroupedAssessments", { assessmentIds: assessmentIdsInGroup, siteId }, (err, resp) => {
      if (!err) {
        const { groupedAssessments: ga, measureNumberByAssessmentId: a } = resp || {};
        setGroupedAssessments(ga || []);
        setMeasureNumberByAssessmentId(a || {});
      }
    });
  }, [studentGroup, props.assessmentResults, props.groupedAssessments]);

  const renderInterventionComplete = useCallback(
    (stu, context, shouldDismissEndIntervention = false) => (
      <IndividualComplete
        key={`Individual_${context}_${stu._id}`}
        student={stu}
        studentGroup={{
          _id: studentGroup?._id,
          orgid: studentGroup?.orgid,
          siteId: studentGroup?.siteId
        }}
        shouldDismissEndIntervention={shouldDismissEndIntervention}
      />
    ),
    [studentGroup]
  );

  const renderIndividualIntervention = useCallback(
    ar => {
      const student = cloneDeep(students.find(s => s._id === ar.studentId));
      return (
        <IndividualIntervention
          key={`${student._id}_individual_int`}
          assessmentName={ar.individualSkills.assessmentName}
          inActiveSchoolYear={inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          studentInfo={student}
          studentGroupId={studentGroup._id}
          schoolYear={studentGroup.schoolYear}
          benchmarkAssessmentName={ar.individualSkills.benchmarkAssessmentName}
          assessmentResult={ar}
          skillGroups={skillGroups}
          isFetchingSkillGroups={isFetchingSkillGroups}
          isTestOrg={isTestOrg}
        />
      );
    },
    [students, inActiveSchoolYear, props.isReadOnly, studentGroup, skillGroups, isFetchingSkillGroups]
  );

  const renderFollowUpAssessment = useCallback(
    ar => {
      const student = cloneDeep(students.find(s => s._id === ar.studentId));
      return (
        <FollowUpAssessmentContainer
          key={`${student._id}_followup`}
          assessmentName={ar.individualSkills.assessmentName}
          inActiveSchoolYear={inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          studentInfo={student}
          studentGroupId={studentGroup._id}
          schoolYear={studentGroup.schoolYear}
          preAssessmentResultId={student.currentSkill ? student.currentSkill.assessmentResultId : null}
          assessmentResult={ar}
          skillGroups={skillGroups}
          isFetchingSkillGroups={isFetchingSkillGroups}
        />
      );
    },
    [students, inActiveSchoolYear, props.isReadOnly, studentGroup, skillGroups, isFetchingSkillGroups]
  );

  const isAssessmentResultBelongsToStudentInGroup = useCallback(
    assessmentResult => students.find(student => student._id === assessmentResult.studentId),
    [students]
  );

  const isStudentFinishedWithIntervention = useCallback(
    stu =>
      stu.currentSkill &&
      !stu.currentSkill.assessmentId &&
      stu.currentSkill.message &&
      !stu.currentSkill.message.dismissed,
    []
  );

  const closeManualIIVideoPopup = useCallback(() => {
    setShouldOpenManualIIVideoPopup(false);
  }, []);

  const renderGroupedInterventions = useCallback(
    ({ assessmentResultsPerType = [], type }) => {
      const studentsById = keyBy(students, "_id");
      if (!assessmentResultsPerType.length) {
        return null;
      }

      const groupedAssessmentResults = assessmentResultsPerType.reduce((groupedResults, assessmentResult) => {
        const { assessmentId, benchmarkAssessmentId, assessmentName } = assessmentResult.individualSkills;
        const groupedAssessmentsDoc = groupedAssessments.find(ga => ga.assessmentIds.includes(assessmentId)) || {};
        if (!groupedResults[benchmarkAssessmentId]) {
          // eslint-disable-next-line no-param-reassign
          groupedResults[benchmarkAssessmentId] = {};
        }
        const skillName = type === "drillDownAssessment" ? assessmentName : groupedAssessmentsDoc.skillName;
        if (!groupedResults[benchmarkAssessmentId][skillName]) {
          // eslint-disable-next-line no-param-reassign
          groupedResults[benchmarkAssessmentId][skillName] = [];
        }

        const studentDoc = cloneDeep(studentsById[assessmentResult.studentId]);

        const studentMostRecentOutcome =
          studentDoc?.history?.[0]?.assessmentResultMeasures.find(arm => arm.assessmentId === assessmentId)
            ?.studentResults[0].individualRuleOutcome || "";
        const studentInstructionalLevel = studentMostRecentOutcome === "at" ? "Fluency" : "Acquisition";
        if (!groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel]) {
          // eslint-disable-next-line no-param-reassign
          groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel] = [];
        }
        groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel].push(assessmentResult);
        return groupedResults;
      }, {});

      return (
        <div>
          <h3>{type === "individualIntervention" ? "Individual Interventions" : "Drill-Down Assessments"}</h3>
          {map(
            Object.entries(groupedAssessmentResults),
            ([benchmarkAssessmentId, assessmentResultsPerBenchmarkAssessmentId]) => {
              return map(
                Object.entries(assessmentResultsPerBenchmarkAssessmentId),
                ([groupedAssessmentName, assessmentResultsByInstructionalLevel]) => {
                  return map(
                    Object.entries(assessmentResultsByInstructionalLevel),
                    ([instructionalLevel, assessmentResults]) => {
                      const sortedAssessmentResults = [...assessmentResults].sort((a, b) => {
                        const studentA = studentsById[a.studentId];
                        const studentB = studentsById[b.studentId];
                        return studentA.identity.name.lastName.localeCompare(studentB.identity.name.lastName);
                      });
                      return (
                        <GroupedAssessmentResults
                          key={`${groupedAssessmentName}_${instructionalLevel}`}
                          instructionalLevel={instructionalLevel}
                          assessmentResults={sortedAssessmentResults}
                          studentsById={studentsById}
                          type={type}
                          benchmarkAssessmentId={benchmarkAssessmentId}
                          groupedAssessments={groupedAssessments}
                          measureNumberByAssessmentId={measureNumberByAssessmentId}
                          renderIndividualIntervention={renderIndividualIntervention}
                          renderFollowUpAssessment={renderFollowUpAssessment}
                          studentGroupName={studentGroup.name}
                        />
                      );
                    }
                  );
                }
              );
            }
          )}
        </div>
      );
    },
    [props, groupedAssessments, measureNumberByAssessmentId, renderIndividualIntervention, renderFollowUpAssessment]
  );

  const { assessmentResults } = props;
  const assessmentResultsInStudentGroup = assessmentResults.filter(assessmentResult =>
    isAssessmentResultBelongsToStudentInGroup(assessmentResult)
  );
  const {
    drillDownAssessment: drillDownAssessmentResults,
    individualIntervention: individualInterventionAssessmentResults
  } = groupBy(assessmentResultsInStudentGroup, assessmentResult => {
    return assessmentResult.individualSkills.interventions.length ? "individualIntervention" : "drillDownAssessment";
  });
  const studentIdsThatAdvancedToNextSkillTree =
    students
      .filter(s => s.currentSkill?.message?.messageCode === "55" && !s.currentSkill?.userSelectedContinue)
      .map(s => s._id) || [];

  return (
    <div className="dashboard-individual-content">
      <ActiveSchoolYearMessage inActiveSchoolYear={inActiveSchoolYear} />
      {/* <div className="text-center"> */}
      {/*  <Button */}
      {/*    variant="default" */}
      {/*    className="m-t-5" */}
      {/*    onClick={openManualIIVideoPopup} */}
      {/*    data-testid="manual-intervention-video-button" */}
      {/*  > */}
      {/*    Video - How to manually schedule and implement individual interventions */}
      {/*  </Button> */}
      {/* </div> */}
      {students
        .filter(
          stu => isStudentFinishedWithIntervention(stu) && !studentIdsThatAdvancedToNextSkillTree.includes(stu._id)
        )
        .map(stu => renderInterventionComplete(stu, "Completed"))}
      {students
        .filter(s => studentIdsThatAdvancedToNextSkillTree.includes(s._id))
        .map(s => renderInterventionComplete(s, "Continue_Stop", true))}
      {renderGroupedInterventions({
        assessmentResultsPerType: individualInterventionAssessmentResults?.filter(
          a => !studentIdsThatAdvancedToNextSkillTree.includes(a.studentId)
        ),
        type: "individualIntervention"
      })}
      {renderGroupedInterventions({
        assessmentResultsPerType: drillDownAssessmentResults?.filter(
          a => !studentIdsThatAdvancedToNextSkillTree.includes(a.studentId)
        ),
        type: "drillDownAssessment"
      })}
      {shouldOpenManualIIVideoPopup ? (
        <InstructionalVideoModal
          showModal={shouldOpenManualIIVideoPopup}
          closeModal={closeManualIIVideoPopup}
          onCloseModal={() => {}}
          videoId={videoId}
          videoTimestamp={videoTimestamp}
          headerText="If this is your first time using manual individual intervention please watch this video."
          testIdPrefix="manual-individual-intervention"
        />
      ) : null}
    </div>
  );
}

DashboardIndividual.propTypes = {
  assessmentResults: PropTypes.array,
  measureNumberByAssessmentId: PropTypes.object, // Optional - for tests
  groupedAssessments: PropTypes.array, // Optional - for tests
  isReadOnly: PropTypes.bool
};

export default DashboardIndividual;
