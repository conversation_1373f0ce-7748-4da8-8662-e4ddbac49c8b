import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import queryString from "query-string";

import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { useLocation } from "react-router-dom";
import { DropdownButton } from "react-bootstrap";
import ConfettiExplosion from "react-confetti-explosion";
import { findLastIndex } from "lodash";

import { Rules } from "/imports/api/rules/rules";
import InterventionProgress from "./intervention-progress/intervention-progress.jsx";
import ClassWideStudentTable from "./class-wide-student-table/class-wide-student-table.jsx";
import InterventionContent from "./intervention-content/intervention-content.jsx";
import PageHeader from "../page-header";
import getSkillsHistory from "../student-detail/getSkillHistory";
import Loading from "../loading";
import { AppDataContext } from "../../routing/AppDataContext";
import { Assessments } from "/imports/api/assessments/assessments";
import ClasswideInterventionPrintOptions from "./intervention-content/classwide-intervention-print-options";
import {
  getPageBreakClassForEverySecondElementByIndex,
  MAX_SKILLS_FROM_NEXT_GRADE,
  shouldUseDevMode
} from "/imports/api/utilities/utilities";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

function DashboardClasswide(props) {
  const context = useContext(AppDataContext);
  const { env } = useContext(StaticDataContext);

  const [selectedSkillIndex, setSelectedSkillIndex] = useState(props.selectedSkillIndex || null);
  const [selectedSkillAssessmentId, setSelectedSkillAssessmentId] = useState(props.selectedSkillAssessmentId || null);

  useEffect(() => {
    if (selectedSkillAssessmentId && !context.classwideIntervention?.selectedSkillAssessmentId) {
      // TODO(fmazur) - replace
      context.setClasswideInterventionDetails({ selectedSkillAssessmentId });
    }
  }, []);

  const getPrintMaterialProps = upcomingAssessment => ({
    assessmentMeasure: upcomingAssessment.monitorAssessmentMeasure,
    assessmentId: upcomingAssessment.assessmentId,
    benchmarkPeriodId: props.assessmentResult?.benchmarkPeriodId,
    protocolMeasure: upcomingAssessment.monitorAssessmentMeasure,
    grade: props.studentGroup.grade,
    groupName: props.studentGroup.name,
    selectedSkillAssessmentId
  });

  const setSelectedAssessment = (newSelectedSkillIndex, newSelectedSkillAssessmentId) => {
    setSelectedSkillIndex(newSelectedSkillIndex);
    setSelectedSkillAssessmentId(newSelectedSkillAssessmentId);
    // TODO(fmazur) - replace
    context.setClasswideInterventionDetails({
      selectedSkillIndex: newSelectedSkillIndex,
      selectedSkillAssessmentId: newSelectedSkillAssessmentId
    });
  };

  const setIdOfStudentGroupWithConfetti = idOfStudentGroupWithConfetti => {
    context.updateAppDataContext({ idOfStudentGroupWithConfetti });
  };

  const getSkillIndex = () => {
    if (!props.skillList || !Array.isArray(props.skillList)) {
      return 0;
    }
    const parsedSelectedSkillIndex = parseInt(selectedSkillIndex);
    const selectedIndex =
      Number.isInteger(parsedSelectedSkillIndex) && parsedSelectedSkillIndex >= 0
        ? parsedSelectedSkillIndex
        : props.skillList.findIndex(skill => skill.active);
    return selectedIndex >= 0 ? selectedIndex : findLastIndex(props.skillList, "complete");
  };

  if (props.loading || !props.skillList || !props.defaultSkillList) {
    return <Loading />;
  }

  const selectedIndex = getSkillIndex();
  const defaultSkillIds = props.defaultSkillList.map(skill => skill.id);
  const isAdditionalSkill = !defaultSkillIds.includes(selectedSkillAssessmentId);

  if (props.isPrinting) {
    let skillsWithScores;
    if (props.printAllClasswideInterventionSkillsGraphs) {
      skillsWithScores = (props.skillList || []).filter(skill => skill.allStudentsScores.length);
      skillsWithScores = skillsWithScores.map((skill, index) => ({ ...skill, index }));
    } else {
      const skillAtIndex = props.skillList?.[selectedIndex];
      skillsWithScores = skillAtIndex ? [{ ...skillAtIndex, index: selectedIndex }] : [];
    }
    const title = `Classwide Intervention for ${props.studentGroup.name}`;
    return (
      <div>
        <PageHeader title={title} />
        {skillsWithScores.length ? (
          skillsWithScores.map((skill, skillIndex) => (
            <div
              key={`classwideInterventionItem_${skill.id}`}
              className={`m-b-40${
                props.printAllClasswideInterventionSkillsGraphs
                  ? getPageBreakClassForEverySecondElementByIndex(skillIndex, true)
                  : ""
              }${skillIndex !== 0 && skillIndex % 2 === 0 ? " m-t-20" : ""}`}
            >
              <InterventionContent
                benchmarkPeriodId={props.assessmentResult?.benchmarkPeriodId}
                inActiveSchoolYear={props.inActiveSchoolYear}
                studentGroup={props.studentGroup}
                selectedSkillAssessmentId={skill.id}
                isPrinting={props.isPrinting}
                isAdditionalSkill={isAdditionalSkill}
              />
              {props.assessmentResult && (
                <ClassWideStudentTable
                  assessmentResult={props.assessmentResult}
                  inActiveSchoolYear={props.inActiveSchoolYear}
                  isReadOnly={props.isReadOnly}
                  schoolYear={props.studentGroup.schoolYear}
                  studentGroup={props.studentGroup}
                  students={props.students}
                  selectedSkillIndex={skill.index}
                  isPrinting={props.isPrinting}
                />
              )}
            </div>
          ))
        ) : (
          <PageHeader
            title="There is no historic classwide intervention data to print"
            description="Enter some classwide intervention scores first"
          />
        )}
      </div>
    );
  }
  const shouldDisplayNextAssessmentMaterialsPrintButton =
    props.nextAssessmentsPrintProps.length &&
    props.skillList &&
    selectedIndex < props.skillList.length - 1 &&
    props.assessmentResult.assessmentIds[0] === props.skillList[selectedIndex]?.id;
  const currentSkillList = (props.skillList || []).map((skill, index) => {
    // eslint-disable-next-line no-param-reassign
    skill.selected = index === selectedIndex;
    return skill;
  });
  const shouldRenderReducedConfetti = shouldUseDevMode(env.CI, ["LOCAL"]) || env.CI;

  return (
    <div className="dashboard-classwide-content clearfix">
      {context.idOfStudentGroupWithConfetti === props.studentGroup._id && (
        <ConfettiExplosion
          className="confetti-container"
          duration={shouldRenderReducedConfetti ? 500 : 5000}
          force={0.8}
          particleCount={shouldRenderReducedConfetti ? 5 : 250}
          width={1600}
          onComplete={() => setIdOfStudentGroupWithConfetti(null)}
        />
      )}
      <div className="col-sm-9 float-start">
        <InterventionContent
          benchmarkPeriodId={props.assessmentResult?.benchmarkPeriodId}
          inActiveSchoolYear={props.inActiveSchoolYear}
          studentGroup={props.studentGroup}
          selectedSkillAssessmentId={selectedSkillAssessmentId}
          isAdditionalSkill={isAdditionalSkill}
        />
        {props.assessmentResult && (
          <ClassWideStudentTable
            assessmentResult={props.assessmentResult}
            inActiveSchoolYear={props.inActiveSchoolYear}
            isReadOnly={props.isReadOnly}
            schoolYear={props.studentGroup.schoolYear}
            studentGroup={props.studentGroup}
            students={props.students}
            selectedSkillIndex={selectedIndex}
            isPrinting={props.isPrinting}
            setIdOfStudentGroupWithConfetti={setIdOfStudentGroupWithConfetti}
          />
        )}
      </div>
      <InterventionProgress
        skillList={currentSkillList}
        defaultSkillListLength={props.defaultSkillList.length}
        setSelectedAssessment={setSelectedAssessment}
        grade={props.studentGroup.grade}
      />
      <div className="col-md-3 next-skill-button-container float-end">
        {shouldDisplayNextAssessmentMaterialsPrintButton ? (
          <DropdownButton
            variant="primary"
            title="View intervention materials for future skills"
            id="dropdownUpcomingMaterials"
          >
            <div data-testid="upcomingAssessmentPrintingContainer">
              {props.nextAssessmentsPrintProps.map((assessmentPrintProps, index) => {
                return (
                  <div key={assessmentPrintProps.assessmentId} className={`d-flex ${index > 0 ? "m-t-10" : ""}`}>
                    <ClasswideInterventionPrintOptions
                      printMaterialProps={{
                        ...getPrintMaterialProps(assessmentPrintProps),
                        assessmentId: assessmentPrintProps.assessmentId,
                        assessmentMeasure: assessmentPrintProps.monitorAssessmentMeasure,
                        protocolMeasure: assessmentPrintProps.monitorAssessmentMeasure
                      }}
                      selectedSkillAssessmentId={assessmentPrintProps.id}
                      dropdownTitle={assessmentPrintProps.name}
                      dropdownOptions={{ pullright: "true" }}
                      variant={`default col-12 invert white-space-normal text-center`}
                      isVertical={true}
                      interventionType={"CW"}
                    />
                  </div>
                );
              })}
            </div>
          </DropdownButton>
        ) : null}
      </div>
    </div>
  );
}

function DashboardClasswideWithData({ studentGroup, students, assessmentResult, ...routerProps }) {
  const location = useLocation();
  const { assessments } = useContext(StaticDataContext);
  const [skillData, setSkillData] = useState({
    skillList: [],
    defaultSkillList: [],
    loading: true
  });

  const subscriptionData = useTracker(() => {
    const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroup._id, additionalGradeForRules);
    const assessmentSub = Meteor.subscribe("AssessmentsForGrade", studentGroup.grade);
    const loading = !rulesSub.ready() || !assessmentSub.ready();

    return {
      loading,
      gradeLevelRules: loading ? null : Rules.findOne({ grade: studentGroup.grade }),
      additionalGradeLevelRules:
        loading || !additionalGradeForRules ? null : Rules.findOne({ grade: additionalGradeForRules }),
      assessments: loading ? [] : Assessments.find({}, { fields: { monitorAssessmentMeasure: 1, hasVideo: 1 } }).fetch()
    };
  }, [studentGroup._id, studentGroup.grade]);

  useEffect(() => {
    if (subscriptionData.loading || !subscriptionData.gradeLevelRules) {
      return;
    }

    try {
      const studentGroupHistory = JSON.parse(JSON.stringify(studentGroup.history));
      const additionalStudentGroupHistory = JSON.parse(JSON.stringify(studentGroup.additionalHistory || []));

      const defaultSkillList = getSkillsHistory(
        studentGroupHistory,
        subscriptionData.gradeLevelRules,
        studentGroup,
        students,
        undefined,
        false,
        assessments
      );

      const isAdditionalSkillList = true;
      const additionalSkillListFull = getSkillsHistory(
        additionalStudentGroupHistory,
        subscriptionData.additionalGradeLevelRules,
        studentGroup,
        students,
        undefined,
        isAdditionalSkillList,
        assessments
      );
      const additionalSkillList = (additionalSkillListFull || []).slice(0, MAX_SKILLS_FROM_NEXT_GRADE);

      const assessmentMeasureById = subscriptionData.assessments.reduce((a, c) => {
        // eslint-disable-next-line no-param-reassign
        a[c._id] = { measureNumber: c.monitorAssessmentMeasure, hasVideo: c.hasVideo };
        return a;
      }, {});

      const skillList = [...(defaultSkillList || []), ...(additionalSkillList || [])].map(s => {
        return {
          ...s,
          measureNumber: assessmentMeasureById[s.id]?.measureNumber,
          hasVideo: assessmentMeasureById[s.id]?.hasVideo
        };
      });

      setSkillData({
        skillList,
        defaultSkillList: defaultSkillList || [],
        loading: false
      });
    } catch (error) {
      console.error("Error loading skill data:", error);
      setSkillData(prev => ({ ...prev, loading: false }));
    }
  }, [
    subscriptionData.loading,
    subscriptionData.gradeLevelRules,
    subscriptionData.additionalGradeLevelRules,
    studentGroup,
    students,
    subscriptionData.assessments
  ]);

  const trackerData = useTracker(() => {
    const queryParams = queryString.parse(location.search);
    const {
      printAllClasswideInterventionSkillsGraphs,
      selectedSkillIndex: selectedSkillIndexString,
      selectedSkillAssessmentId = assessmentResult?.assessmentIds[0]
    } = queryParams;
    const selectedSkillIndex = parseInt(selectedSkillIndexString) >= 0 ? parseInt(selectedSkillIndexString) : undefined;

    const currentSelectedSkillIndex = skillData.skillList.findIndex(skill => skill.active);

    const nextAssessmentsPrintProps = [];
    const nextSkillIndex = currentSelectedSkillIndex + 1;
    const upcomingSkillListElements = skillData.skillList.slice(nextSkillIndex, nextSkillIndex + 4);

    upcomingSkillListElements.forEach((_, index) => {
      const upcomingSkillIndex = nextSkillIndex + index;
      const nextAssessment = Assessments.findOne({ _id: skillData.skillList[upcomingSkillIndex]?.id });
      if (nextAssessment) {
        nextAssessmentsPrintProps.push({
          assessmentId: skillData.skillList[upcomingSkillIndex]?.id,
          name: nextAssessment.name,
          monitorAssessmentMeasure: nextAssessment.monitorAssessmentMeasure
        });
      }
    });

    return {
      loading: subscriptionData.loading || skillData.loading,
      skillList: skillData.skillList,
      defaultSkillList: skillData.defaultSkillList,
      printAllClasswideInterventionSkillsGraphs: printAllClasswideInterventionSkillsGraphs === "true",
      selectedSkillIndex,
      selectedSkillAssessmentId,
      nextAssessmentsPrintProps
    };
  }, [location.search, skillData, subscriptionData.loading]);

  return (
    <DashboardClasswide
      {...trackerData}
      {...routerProps}
      studentGroup={studentGroup}
      students={students}
      assessmentResult={assessmentResult}
    />
  );
}

DashboardClasswideWithData.propTypes = {
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  assessmentResult: PropTypes.object
};

export default DashboardClasswideWithData;

DashboardClasswide.propTypes = {
  assessmentResult: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  isPrinting: PropTypes.bool,
  loading: PropTypes.bool,
  skillList: PropTypes.array,
  defaultSkillList: PropTypes.array,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  selectedSkillIndex: PropTypes.number,
  selectedSkillAssessmentId: PropTypes.string,
  printAllClasswideInterventionSkillsGraphs: PropTypes.bool,
  nextAssessmentsPrintProps: PropTypes.array
};
