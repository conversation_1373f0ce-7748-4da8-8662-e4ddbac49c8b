import { Meteor } from "meteor/meteor";
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import JSZip from "jszip";
import { saveAs } from "file-saver";

import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

function requestMaterialsForStudent({
  protocolType,
  assessmentIds,
  assessmentMeasureIds,
  protocolMeasureIds,
  studentGrade,
  studentName,
  studentGroupId,
  payloadType,
  benchmarkPeriodId,
  groupName,
  materialsPart
}) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType,
        assessmentIds,
        assessmentMeasureIds,
        protocolMeasureIds,
        studentGrade,
        studentName,
        studentGroupId,
        payloadType,
        benchmarkPeriodId,
        groupName,
        materialsPart
      },
      (error, resp) => {
        if (error) {
          return reject(error);
        }
        return resolve(resp);
      }
    );
  });
}

const PrintMultipleMaterials = props => {
  const [printMaterialsStatus, setPrintMaterialsStatus] = useState("NOT_ATTEMPTED");
  const [printMaterialsQueue, setPrintMaterialsQueue] = useState([]);

  const requestPrintMultipleMaterials = () => {
    const {
      benchmarkPeriodId,
      materialsType,
      materialsPart = "",
      interventionType = "",
      materialRequestItems,
      groupName = "",
      mayTakeLong,
      studentGroupId
    } = props;

    setPrintMaterialsStatus("FETCHING");
    if (mayTakeLong) {
      Alert.info("Generating all materials may take up to a minute. Do not exit this web page.", {
        timeout: 120000
      });
    }

    const materialRequestItemPromises = materialRequestItems.map(
      ({ assessmentIds, measureNumbers, studentName, grade }) =>
        requestMaterialsForStudent({
          protocolType: interventionType,
          assessmentIds,
          assessmentMeasureIds: measureNumbers,
          protocolMeasureIds: [],
          studentGrade: grade,
          studentName,
          studentGroupId,
          payloadType: materialsType,
          benchmarkPeriodId,
          groupName,
          materialsPart
        })
    );

    Promise.all(materialRequestItemPromises)
      .then(resp => {
        Alert.closeAll();
        setPrintMaterialsStatus("SUCCESS");
        setPrintMaterialsQueue(resp);
      })
      .catch(error => {
        Alert.closeAll();
        Alert.error(`${error.error}: ${error.reason}`, { timeout: 5000 });
        setPrintMaterialsStatus("ERROR");
      });
  };

  const resetToDefaultState = () => {
    setPrintMaterialsStatus("NOT_ATTEMPTED");
  };

  const resetErrorState = () => {
    if (printMaterialsStatus === "ERROR") {
      resetToDefaultState();
    }
  };

  const handleDownloadPdf = () => {
    const fileName = `springmath-assessments_${props.materialRequestItems[0].studentName}.pdf`;
    downloadPdf(printMaterialsQueue[0], fileName);
    resetToDefaultState();
  };

  const handleDownloadZip = () => {
    const fileName = `springmath-assessments-${props.assessmentName}.zip`;
    const zip = new JSZip();
    printMaterialsQueue.forEach((pdfContent, index) => {
      const pdfName = `springmath-assessments_${props.materialRequestItems[index].studentName}.pdf`;
      zip.file(pdfName, pdfContent, { base64: true });
    });
    zip
      .generateAsync({
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: {
          level: 6
        }
      })
      .then(content => {
        saveAs(content, fileName);
      });
    resetToDefaultState();
  };

  useEffect(() => {
    if (printMaterialsStatus === "SUCCESS" && printMaterialsQueue.length > 0) {
      if (printMaterialsQueue.length > 1) {
        handleDownloadZip();
      } else {
        handleDownloadPdf();
      }
    }
  }, [printMaterialsStatus, printMaterialsQueue]);

  const renderPrintAssessmentsButton = () => {
    const itemClass = "btn btn-primary btn-xs invert white-space-normal";
    if (printMaterialsStatus === "NOT_ATTEMPTED") {
      return (
        <span
          className={itemClass}
          style={{ width: "150px", fontSize: "12px", lineHeight: "1.4" }}
          onClick={requestPrintMultipleMaterials}
        >
          {props.initialText}
        </span>
      );
    }
    if (printMaterialsStatus === "SUCCESS") {
      return null;
    }
    if (printMaterialsStatus === "ERROR") {
      return (
        <span className="btn btn-danger" onClick={resetErrorState}>
          <i className="fa fa-file-text-o fa-left" />
          Printing error, click to reset
        </span>
      );
    }
    return <p>There was a problem loading assessments</p>;
  };

  if (props.loading) return <Loading />;

  return (
    <div className="m-l-5 w9 text-center print-materials-button" data-testid="printMultipleAssessmentsButton">
      {printMaterialsStatus === "FETCHING" ? (
        <Loading message={props.loadingText} inline />
      ) : (
        renderPrintAssessmentsButton()
      )}
    </div>
  );
};

PrintMultipleMaterials.defaultProps = {
  mayTakeLong: false
};

PrintMultipleMaterials.propTypes = {
  assessmentName: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  initialText: PropTypes.string,
  interventionType: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  materialRequestItems: PropTypes.array,
  materialsType: PropTypes.string,
  materialsPart: PropTypes.string,
  printOptionKey: PropTypes.string,
  groupName: PropTypes.string,
  mayTakeLong: PropTypes.bool,
  studentGroupId: PropTypes.string
};

export default PrintMultipleMaterials;
