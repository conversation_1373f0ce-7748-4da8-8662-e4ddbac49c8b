import React, { Component } from "react";
import PropTypes from "prop-types";
import { Button } from "react-bootstrap";
import { Meteor } from "meteor/meteor";

import { translateBenchmarkPeriod } from "/imports/api/utilities/utilities.js";
import InterventionMessage from "./intervention-message.jsx";
import * as helpers from "../student-groups/helperFunction";

class IndividualComplete extends Component {
  state = {
    pendingClick: false
  };

  continueIntervention = () => {
    this.setState({ pendingClick: true });
    Meteor.call(
      "Students:setStudentContinueSkill",
      {
        studentId: this.props.student._id,
        orgid: this.props.studentGroup.orgid,
        siteId: this.props.studentGroup.siteId
      },
      err => {
        if (err) {
          helpers.displayError(`There was an error: ${err.message}`);
        }
        this.setState({ pendingClick: false });
      }
    );
  };

  stopIntervention = () => {
    this.setState({ pendingClick: true });
    Meteor.call(
      "endCurrentIndividualIntervention",
      {
        studentId: this.props.student._id,
        studentGroupId: this.props.studentGroup._id
      },
      err => {
        if (err) {
          helpers.displayError(`There was an error: ${err.message}`);
        }
        this.setState({ pendingClick: false });
      }
    );
  };

  render() {
    const {
      _id,
      currentSkill,
      identity: { name }
    } = this.props.student;
    const { benchmarkPeriodId, message } = currentSkill;
    const completedSeason = translateBenchmarkPeriod(benchmarkPeriodId).title;
    return (
      <div className="skill-container individual clearfix" data-student-id={_id}>
        <div className="skill-details individual-complete">
          {!message.dismissed ? (
            <InterventionMessage
              entityId={_id}
              entityType="Student"
              name={name}
              season={completedSeason}
              message={message}
              shouldDismissEndIntervention={this.props.shouldDismissEndIntervention}
              studentGroupId={this.props.studentGroup._id}
            />
          ) : null}
          {message.messageCode === "55" ? (
            <div className="d-flex align-items-start">
              <small className="me-auto text-dark">
                We recommend your team meet to determine if this intervention has been successful. <br />
                You may continue the intervention or stop the intervention if your teams believe that a successful RTI
                has been shown.
              </small>
              <Button
                className="btn btn-success me-2"
                disabled={this.state.pendingClick}
                onClick={this.continueIntervention}
                data-testid={`continue_individual_button_${_id}`}
              >
                Continue
              </Button>
              <Button className="btn btn-danger" disabled={this.state.pendingClick} onClick={this.stopIntervention}>
                Stop
              </Button>
            </div>
          ) : (
            <small className="text-dark">
              We recommend that you visit the students page to schedule a new student for individual interventions.
            </small>
          )}
        </div>
      </div>
    );
  }
}
export default IndividualComplete;

IndividualComplete.propTypes = {
  student: PropTypes.object,
  studentGroup: PropTypes.object,
  shouldDismissEndIntervention: PropTypes.bool
};
