import { assert } from "chai";
import React from "react";
import { mount } from "enzyme";
import sinon from "sinon";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import DashboardIndividual from "./dashboard-individual.jsx";
import IndividualComplete from "./individual-complete.jsx";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";

describe("InterventionContent UI", () => {
  describe("IndividualComplete Component", () => {
    let meteorCallSpy;

    beforeEach(() => {
      meteorCallSpy = sinon.stub(Meteor, "call");
      meteorCallSpy.withArgs("getSkillGroupAssignments", sinon.match.any, sinon.match.func).callsArgWith(2, null, []);
      meteorCallSpy
        .withArgs("getGroupedAssessments", sinon.match.any, sinon.match.func)
        .callsArgWith(2, null, { groupedAssessments: [], measureNumberByAssessmentId: {} });
    });

    afterEach(() => {
      meteorCallSpy.restore();
    });

    const studentWithMessageToDisplay = [
      {
        _id: "studentId",
        identity: {
          name: {
            firstName: "Test",
            lastName: "Tester"
          }
        },
        currentSkill: {
          message: {
            code: "99",
            dismissed: false
          }
        }
      }
    ];
    const studentWithMessageToDisplayAndAssessmentId = [
      {
        _id: "studentId",
        identity: {
          name: {
            firstName: "Test",
            lastName: "Tester"
          }
        },
        currentSkill: {
          assessmentId: "assessmentId",
          message: {
            code: "99",
            dismissed: false
          }
        }
      }
    ];
    const studentGroup = { siteId: "testSiteId", schoolYear: 2024, orgid: "testOrgId" };

    const defaultProps = {
      assessmentResults: [],
      groupedAssessments: [],
      measureNumberByAssessmentId: {}
    };

    const TestWrapper = ({ children, students = [] }) => (
      <StudentGroupContext.Provider
        value={{
          studentGroupId: "test_student_group_id",
          studentGroup,
          studentsInStudentGroup: students,
          showInterventionModal: false,
          studentsWithIndividualRuleNotProcessed: [],
          alreadyCompletedTreeData: null,
          processIndividualRule: () => {},
          setAlreadyCompletedTreeData: () => {},
          endInterventionHandling: () => {}
        }}
      >
        <SchoolYearContext.Provider
          value={{
            schoolYear: 2024,
            latestAvailableSchoolYear: 2024,
            customDate: null
          }}
        >
          {children}
        </SchoolYearContext.Provider>
      </StudentGroupContext.Provider>
    );

    TestWrapper.propTypes = {
      children: PropTypes.node.isRequired,
      students: PropTypes.array
    };
    it("is rendered when the Student has a currentSkill with a message that has not been dismissed", () => {
      const dashboardIndividualComponent = mount(
        <TestWrapper students={studentWithMessageToDisplay}>
          <DashboardIndividual {...defaultProps} />
        </TestWrapper>
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find("IndividualComplete");
      assert.isOk(IndividualCompleteComponent.length);
    });
    it("is not rendered when the Student has a currentSkill with a message that has been dismissed", () => {
      studentWithMessageToDisplay[0].currentSkill.message.dismissed = true;
      const dashboardIndividualComponent = mount(
        <TestWrapper students={studentWithMessageToDisplay}>
          <DashboardIndividual {...defaultProps} />
        </TestWrapper>
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find(IndividualComplete);
      assert.isNotOk(IndividualCompleteComponent.length);
    });
    it("is not rendered when the Student has a currentSkill with an assessmentId", () => {
      const dashboardIndividualComponent = mount(
        <TestWrapper students={studentWithMessageToDisplayAndAssessmentId}>
          <DashboardIndividual {...defaultProps} />
        </TestWrapper>
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find(IndividualComplete);
      assert.isNotOk(IndividualCompleteComponent.length);
    });

    it("calls getSkillGroupAssignments and getGroupedAssessments when groupedAssessments prop is not provided", () => {
      const propsWithoutGroupedAssessments = {
        assessmentResults: []
      };

      mount(
        <TestWrapper students={[]}>
          <DashboardIndividual {...propsWithoutGroupedAssessments} />
        </TestWrapper>
      );

      assert.isTrue(meteorCallSpy.calledWith("getSkillGroupAssignments"));
      assert.isTrue(meteorCallSpy.calledWith("getGroupedAssessments"));
    });
  });
});
