import React from "react";
import "@testing-library/jest-dom";
import { cleanup, fireEvent, waitFor, within } from "@testing-library/react";
import { Meteor } from "meteor/meteor";
import td from "testdouble";
import { colors } from "/imports/api/constants";
import { getCurrentSchoolYear } from "/imports/api/utilities/utilities";
import StudentGroupLayout from "/imports/ui/layouts/student-group-layout";
import { PureAdminOverview as AdminOverview } from "/imports/ui/pages/admin-view/admin-view";
import { PureDataAdminDashboard as DataAdminDashboard } from "/imports/ui/pages/data-admin/data-admin-dashboard";
import { renderWithRouter } from "/tests/helpers/testUtils";
import { Sites } from "/imports/api/sites/sites";
import { Grades } from "/imports/api/grades/grades";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";

// Mock the utilities module using jest.mock
jest.mock("../../pages/data-admin/utilities", () => ({
  ...jest.requireActual("../../pages/data-admin/utilities"),
  getUserRoles: jest.fn()
}));

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserSync: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

const { getUserRoles } = require("../../pages/data-admin/utilities");

// Mock Meteor methods and subscriptions
td.replace(Meteor, "call");
Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
Meteor.subscribe = jest.fn(() => ({ ready: () => true }));

describe("News Banner", () => {
  const siteId = "test_elementary_site_id";
  const orgid = "test_organization_id";
  const testNewsMessages = [
    {
      _id: "message1",
      isSupportLinkActive: false,
      isLearnMoreActive: false,
      learnMoreUrl: "http://www.google.com",
      messageActive: true,
      messageContent: "Test message",
      messageColor: colors.orange,
      messageTextColor: colors.white,
      buttonColor: colors.brightBlue,
      buttonIconColor: colors.white
    },
    {
      _id: "message2",
      isSupportLinkActive: true,
      isLearnMoreActive: true,
      learnMoreUrl: "http://www.archlinux.org",
      messageActive: true,
      messageContent: "Other message",
      messageColor: colors.orange,
      messageTextColor: colors.white,
      buttonColor: colors.brightBlue,
      buttonIconColor: colors.white
    },
    {
      _id: "message3",
      isSupportLinkActive: true,
      isLearnMoreActive: true,
      learnMoreUrl: "http://www.archlinux.org",
      messageActive: true,
      messageContent: "Other very long message that doesnt fit",
      messageColor: colors.orange,
      messageTextColor: colors.white,
      buttonColor: colors.brightBlue,
      buttonIconColor: colors.white,
      type: "rostering"
    }
  ];
  const newsBannerTestIds = testNewsMessages.map(t => `news-banner-${t._id}`);
  const newsBannerLearnMoreButtonTestIds = testNewsMessages.map(t => `news-banner-${t._id}-learn-more-btn`);
  const newsBannerSupportButtonTestIds = testNewsMessages.map(t => `news-banner-${t._id}-support-btn`);

  describe("StudentGroupLayout", () => {
    beforeEach(() => {
      td.replace(Meteor, "call");
      td.replace(Meteor, "user", () => ({ _id: "testUserId" }));
      td.when(Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT", "CI"])).thenCallback(null, {});
      td.replace(getCurrentSchoolYear, "getCurrentSchoolYear", () => 2024);
    });

    afterEach(() => {
      cleanup();
      td.reset();
    });

    const studentGroupLayout = {
      studentGroup: {
        orgid,
        siteId
      }
    };

    it("should not show news banner if there isn't any active message", async () => {
      td.when(Meteor.call("News:getActiveMessage", { siteId, orgid })).thenCallback(null, null);
      const { queryByTestId } = renderWithRouter(<StudentGroupLayout {...studentGroupLayout} />);

      newsBannerTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
      newsBannerLearnMoreButtonTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
      newsBannerSupportButtonTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
    });
    it("should show only active news banner", async () => {
      td.when(Meteor.call("News:getActiveMessage", { siteId, orgid })).thenCallback(null, testNewsMessages[0]);
      const { getByTestId, queryByTestId } = renderWithRouter(<StudentGroupLayout {...studentGroupLayout} />);

      await waitFor(() => {
        expect(getByTestId(newsBannerTestIds[0])).toBeVisible();
      });
      expect(queryByTestId(newsBannerLearnMoreButtonTestIds[0])).toBeNull();
      expect(queryByTestId(newsBannerSupportButtonTestIds[0])).toBeNull();

      expect(queryByTestId(newsBannerTestIds[1])).toBeNull();
    });
  });
  describe("DataAdminDashboard", () => {
    beforeEach(async () => {
      // Mock Meteor methods - the component uses Meteor.call, not callAsync
      td.replace(Meteor, "call");
      td.when(Meteor.call("Students:getNumberOfActiveStudents", orgid, td.callback)).thenCallback(null, []);
      td.when(Meteor.call("StudentGroups:getActiveStudentGroupsCountInSites", orgid, td.callback)).thenCallback(null, [
        "test"
      ]);
      td.when(Meteor.call("Sites:getSchoolItemData", orgid, td.callback)).thenCallback(null, []);
      td.when(Meteor.call("AssessmentScoresUpload:getSchoolYears", orgid, td.callback)).thenCallback(null, []);

      // Mock getUserRoles using jest
      getUserRoles.mockReturnValue(["dataAdmin"]);
    });

    afterEach(() => {
      cleanup();
      td.reset();
      jest.clearAllMocks();
      delete HTMLElement.prototype?.scrollWidth;
      delete HTMLElement.prototype?.clientWidth;
    });

    const dataAdminDashboardParams = {
      orgid,
      loading: false,
      lastRosterImport: {},
      org: {},
      dataAdmins: []
    };

    it("should not show news banner if there isn't any global active message", async () => {
      td.when(Meteor.call("News:getActiveMessage", { orgid, multi: true }, td.callback)).thenCallback(null, []);
      const { queryByTestId } = renderWithRouter(<DataAdminDashboard {...dataAdminDashboardParams} />);

      expect(queryByTestId("global-news-banner")).toBeNull();
    });
    it("should show only active global news banners", async () => {
      td.when(Meteor.call("News:getActiveMessage", { orgid, multi: true }, td.callback)).thenCallback(null, [
        testNewsMessages[1],
        testNewsMessages[2]
      ]);
      const { getByTestId: getByTestIdParent } = renderWithRouter(<DataAdminDashboard {...dataAdminDashboardParams} />);

      await waitFor(() => {
        expect(getByTestIdParent("global-news-banner")).toBeInTheDocument();
      });

      const { getByTestId: getByTestId1, queryByTestId } = within(getByTestIdParent("global-news-banner"));
      const { getByTestId: getByTestId2 } = within(getByTestIdParent("compact-news-banner"));

      // NOTE(fmazur) - global
      expect(getByTestId1(newsBannerTestIds[1])).toBeVisible();
      expect(getByTestId1(newsBannerLearnMoreButtonTestIds[1])).toBeVisible();
      expect(getByTestId1(newsBannerSupportButtonTestIds[1])).toBeVisible();
      expect(queryByTestId(newsBannerTestIds[0])).toBeNull();

      // NOTE(fmazur) - rostering
      expect(getByTestId2(newsBannerTestIds[2])).toBeVisible();
      expect(getByTestId2(newsBannerLearnMoreButtonTestIds[2])).toBeVisible();
      expect(getByTestId2(newsBannerSupportButtonTestIds[2])).toBeVisible();
    });
    it("should be able to see and expand local banner", async () => {
      // NOTE(fmazur) - jsdom doesn't calculate layout dimensions
      Object.defineProperty(HTMLElement.prototype, "scrollWidth", {
        configurable: true,
        value: 500
      });
      Object.defineProperty(HTMLElement.prototype, "clientWidth", {
        configurable: true,
        value: 100
      });

      td.when(Meteor.call("News:getActiveMessage", { orgid, multi: true }, td.callback)).thenCallback(null, [
        testNewsMessages[2]
      ]);
      const { getByTestId: getByTestIdParent } = renderWithRouter(<DataAdminDashboard {...dataAdminDashboardParams} />);

      await waitFor(() => {
        expect(getByTestIdParent("compact-news-banner")).toBeInTheDocument();
      });

      const { getByTestId } = within(getByTestIdParent("compact-news-banner"));
      expect(getByTestId(newsBannerTestIds[2])).toBeVisible();
      expect(getByTestId(newsBannerTestIds[2])).not.toHaveClass("expanded");
      fireEvent.click(getByTestId(newsBannerTestIds[2]));
      expect(getByTestId(newsBannerTestIds[2])).toHaveClass("expanded");
    });
  });
  describe("AdminOverview", () => {
    beforeEach(async () => {
      td.replace(Meteor, "call");

      // Set up test data in actual collections
      await Sites.insertAsync({
        _id: siteId,
        name: "Test Site",
        orgid,
        isHighSchool: false
      });

      await Grades.insertAsync([
        { _id: "01", display: "1st", sortorder: 1 },
        { _id: "02", display: "2nd", sortorder: 2 },
        { _id: "03", display: "3rd", sortorder: 3 }
      ]);
    });

    afterEach(async () => {
      cleanup();
      td.reset();

      await Sites.removeAsync({});
      await Grades.removeAsync({});
      await AssessmentResults.removeAsync({});
    });

    const adminOverviewParams = {
      gradeId: "all",
      orgid,
      siteId,
      siteName: "",
      loading: false,
      grades: [],
      studentGroups: [],
      bmPeriods: [],
      currentBMPeriod: {},
      // TODO(fmazur) - use false after fixing scroll indicator
      isPrinting: true
    };

    const mockSiteContextValue = {
      siteId,
      siteName: "Test Site",
      site: {
        _id: siteId,
        name: "Test Site",
        orgid,
        grades: ["01", "02", "03"]
      },
      studentGroupsInSite: []
    };

    it("should not show news banner if there isn't any active message", async () => {
      td.when(Meteor.call("News:getActiveMessage", { siteId, orgid }, td.callback)).thenCallback(null, null);
      const { queryByTestId } = renderWithRouter(<AdminOverview {...adminOverviewParams} />, {
        siteContextValue: mockSiteContextValue
      });

      newsBannerTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
      newsBannerLearnMoreButtonTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
      newsBannerSupportButtonTestIds.forEach(id => {
        expect(queryByTestId(id)).toBeNull();
      });
    });
    it("should show only active news banners", async () => {
      td.when(Meteor.call("News:getActiveMessage", { siteId, orgid }, td.callback)).thenCallback(
        null,
        testNewsMessages[0]
      );
      const { getByTestId, queryByTestId } = renderWithRouter(<AdminOverview {...adminOverviewParams} />, {
        siteContextValue: mockSiteContextValue
      });

      await waitFor(() => {
        expect(getByTestId(newsBannerTestIds[0])).toBeVisible();
      });
      expect(queryByTestId(newsBannerLearnMoreButtonTestIds[0])).toBeNull();
      expect(queryByTestId(newsBannerSupportButtonTestIds[0])).toBeNull();

      expect(queryByTestId(newsBannerTestIds[1])).toBeNull();
    });
  });
});
