import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import { sinon } from "sinon";
import React from "react";
import { shallow } from "enzyme";
import ClassWideStudentTable from "./class-wide-student-table.jsx";
import ProgressMonitoringChart from "../../pmChart.jsx";

function stubReactComponentPrototype(component, retVal = null) {
  Object.keys(component.prototype).forEach(k => {
    if (typeof component.prototype[k] === "function") {
      sinon.stub(component.prototype, k).returns(retVal);
    }
  });
}

if (Meteor.isClient) {
  describe("ClassWideStudentTable UI", () => {
    describe("Render", () => {
      let classWideStudentTableComponent;
      beforeEach(() => {
        const studentGroup = {
          currentClasswideSkill: {
            targets: [1, 2, 3]
          }
        };
        const assessmentResult = {
          scores: []
        };
        stubReactComponentPrototype(ProgressMonitoringChart);
        const fixture = '<div id="classWidePmChart1"></div>';
        document.body.insertAdjacentHTML("afterbegin", fixture);
        classWideStudentTableComponent = shallow(
          <ClassWideStudentTable students={[]} studentGroup={studentGroup} assessmentResult={assessmentResult} />
        );
      });
      it("render", () => {
        // Verify that the method does what we expected
        assert.isDefined(classWideStudentTableComponent, "classWideStudentTableComponent did not render");
      });
    });
  });
}
