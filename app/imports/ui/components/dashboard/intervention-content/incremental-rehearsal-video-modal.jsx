import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { Link } from "react-router-dom";

export default class IncrementalRehearsalVideoModal extends Component {
  state = {
    videoUrl: null,
    slidesUrl: null,
    isLoading: true,
    error: null
  };

  componentDidMount() {
    this.loadSignedUrls();
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.day !== this.props.day ||
      prevProps.incrementalRehearsal?.name !== this.props.incrementalRehearsal?.name
    ) {
      this.loadSignedUrls();
    }
  }

  loadSignedUrls = async () => {
    const { day, incrementalRehearsal } = this.props;
    const { maxVideos = 20, maxSlides = 20 } = incrementalRehearsal;
    const useSignedUrls = Meteor.settings.public.S3_USE_SIGNED_URLS;

    this.setState({ isLoading: true, error: null });

    try {
      let videoUrl = null;
      let slidesUrl = null;

      if (useSignedUrls) {
        // Use signed URLs for secure access
        if (day <= maxVideos) {
          const videoFilename = `${incrementalRehearsal.name}_Video-${day}.mp4`;
          videoUrl = await Meteor.callAsync(
            "Utilities:getIncrementalRehearsalSignedUrl",
            incrementalRehearsal.name,
            videoFilename
          );
        }
        if (day <= maxSlides) {
          const slidesFilename = `${incrementalRehearsal.name}_Day-${day}.pdf`;
          slidesUrl = await Meteor.callAsync(
            "Utilities:getIncrementalRehearsalSignedUrl",
            incrementalRehearsal.name,
            slidesFilename
          );
        }
      } else {
        // Fallback to direct URLs (original behavior)
        const incrementalRehearsalBaseUrl = Meteor.settings.public.INCREMENTAL_REHEARSAL_BASE_URL || "";
        if (incrementalRehearsalBaseUrl) {
          const incrementalRehearsalSkillBaseUrl = `${incrementalRehearsalBaseUrl}/${incrementalRehearsal.name}`;
          if (day <= maxSlides) {
            slidesUrl = `${incrementalRehearsalSkillBaseUrl}/${incrementalRehearsal.name}_Day-${day}.pdf`;
          }
          if (day <= maxVideos) {
            videoUrl = `${incrementalRehearsalSkillBaseUrl}/${incrementalRehearsal.name}_Video-${day}.mp4`;
          }
        }
      }

      this.setState({ videoUrl, slidesUrl, isLoading: false });
    } catch (error) {
      console.error("Failed to load Incremental Rehearsal content:", error);
      this.setState({ error: error.message, isLoading: false });
    }
  };

  close = () => {
    this.props.closeModal();
  };

  render() {
    const { day, incrementalRehearsal, type } = this.props;
    const { videoUrl, slidesUrl, isLoading, error } = this.state;

    return (
      <Modal show={this.props.showModal} onHide={this.close} dialogClassName="modal-lg modal-xl" backdrop="static">
        <ModalHeader>
          <h3 className="w9">
            Incremental Rehearsal: {incrementalRehearsal.name} - Day {day}
            {type === "preview" && " (Preview)"}
          </h3>
        </ModalHeader>

        <ModalBody>
          <div className="modal-container">
            {/* eslint-disable-next-line no-nested-ternary */}
            {isLoading ? (
              <div className="text-center p-5">
                <i className="fa fa-spinner fa-spin fa-3x" />
                <p className="mt-3">Loading content...</p>
              </div>
            ) : error ? (
              <div className="alert alert-danger">
                <i className="fa fa-exclamation-triangle" /> Failed to load content: {error}
              </div>
            ) : (
              <div className="row">
                {videoUrl ? (
                  <video
                    className="video-js vjs-default-skin vjs-big-play-centered"
                    controls
                    preload="auto"
                    width="100%"
                    height="auto"
                  >
                    <source src={videoUrl} type="video/mp4" />
                    <p className="vjs-no-js">
                      To view this video please enable JavaScript, and consider upgrading to a web browser that{" "}
                      <Link to="http://videojs.com/html5-video-support/" target="_newTab">
                        supports HTML5 video
                      </Link>
                    </p>
                  </video>
                ) : null}
              </div>
            )}
          </div>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          {slidesUrl && !isLoading ? (
            <a className="btn btn-success" href={slidesUrl} target="_blank" rel="noreferrer">
              View Slides
            </a>
          ) : null}
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

IncrementalRehearsalVideoModal.propTypes = {
  showModal: PropTypes.bool,
  closeModal: PropTypes.func,
  incrementalRehearsal: PropTypes.object,
  day: PropTypes.number,
  type: PropTypes.string
};
