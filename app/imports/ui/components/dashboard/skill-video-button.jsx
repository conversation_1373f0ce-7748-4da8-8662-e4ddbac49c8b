import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, ModalHeader } from "react-bootstrap";
import { Link } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import ConfirmModal from "../../pages/data-admin/confirm-modal";

export default class SkillVideoButton extends Component {
  state = {
    isVideoModalOpen: false,
    videoUrl: null,
    name: null,
    isLoading: false
  };

  renderSkillVideoIcon = () => {
    const { hasVideo, additionalClassNames } = this.props;
    if (!hasVideo) {
      return null;
    }

    let className = "skill-button";
    if (additionalClassNames.length) {
      className += " ";
      className += additionalClassNames;
    }
    return (
      <Button
        size="xs"
        variant="outline-blue"
        className={className}
        onClick={this.handleVideoButtonClick}
        disabled={this.state.isLoading}
      >
        <i className={`fa ${this.state.isLoading ? "fa-spinner fa-spin" : "fa-video-camera"} cursor-pointer`} />
      </Button>
    );
  };

  handleVideoButtonClick = async () => {
    const { measureNumber, skillName } = this.props;
    const useSignedUrls = Meteor.settings.public.S3_USE_SIGNED_URLS;

    if (useSignedUrls) {
      // Use signed URL approach
      this.setState({ isLoading: true });

      try {
        const signedUrl = await Meteor.callAsync("Utilities:getVideoSignedUrl", measureNumber);
        this.setState({
          isVideoModalOpen: true,
          videoUrl: signedUrl,
          skillName,
          isLoading: false
        });
      } catch (error) {
        console.error("Failed to get video URL:", error);
        this.setState({ isLoading: false });
        // You might want to show an error message to the user here
      }
    } else {
      // Fallback to direct URL construction (original behavior)
      const folderPath = Meteor.settings.public.S3_FOLDER_PATH || "protocol-images/Skills";
      const s3BaseUrl = Meteor.settings.public.S3_BASE_URL || "https://springmath-web-data.s3.us-east-va.io.cloud.ovh.us";
      const url = `${s3BaseUrl}/${folderPath}/${measureNumber}.mp4`;

      this.setState({
        isVideoModalOpen: true,
        videoUrl: url,
        skillName
      });
    }
  };

  closeVideoModal = () => {
    this.setState({ isVideoModalOpen: false, videoUrl: null, skillName: null });
  };

  renderSkillVideoModal = () => {
    const { skillName, isVideoModalOpen, videoUrl } = this.state;
    return (
      <ConfirmModal
        onCloseModal={this.closeVideoModal}
        confirmAction={() => {}}
        confirmText=""
        bodyQuestion=""
        cancelText="Close"
        showModal={isVideoModalOpen}
        size="lg"
        headerText={
          <ModalHeader className="justify-content-center">
            <div className="w9">{skillName}</div>
          </ModalHeader>
        }
        customProps={{
          shouldCenterHeader: true,
          canCloseUsingBackdrop: true,
          useCustomHeader: true
        }}
        bodyText={
          <div className="text-center">
            <video className="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto" width="100%">
              <source src={videoUrl} type="video/mp4" />
              <p className="vjs-no-js">
                To view this video please enable JavaScript, and consider upgrading to a web browser that{" "}
                <Link to="http://videojs.com/html5-video-support/" target="_newTab">
                  supports HTML5 video
                </Link>
              </p>
            </video>
          </div>
        }
      />
    );
  };

  render() {
    return (
      <React.Fragment>
        {this.renderSkillVideoIcon()}
        {this.state.isVideoModalOpen ? this.renderSkillVideoModal() : null}
      </React.Fragment>
    );
  }
}

SkillVideoButton.propTypes = {
  measureNumber: PropTypes.string,
  skillName: PropTypes.string,
  hasVideo: PropTypes.bool,
  additionalClassNames: PropTypes.string
};

SkillVideoButton.defaultProps = {
  additionalClassNames: ""
};
