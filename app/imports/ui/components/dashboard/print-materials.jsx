import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

const PrintMaterials = props => {
  const [printMaterialsStatus, setPrintMaterialsStatus] = useState("NOT_ATTEMPTED");
  const [printMaterials, setPrintMaterials] = useState("");
  const [printMaterialsQueue, setPrintMaterialsQueue] = useState([]);

  const inDropdown = useRef(!!props.getCurrentlyPrinting);
  const prevSelectedSkillAssessmentId = useRef(props.selectedSkillAssessmentId);

  const resetToDefaultState = () => {
    setPrintMaterialsStatus("NOT_ATTEMPTED");
    setPrintMaterials("");
    setPrintMaterialsQueue([]);
    if (inDropdown.current) {
      props.setCurrentlyPrinting(false);
    }
  };

  useEffect(() => {
    if (props.selectedSkillAssessmentId !== prevSelectedSkillAssessmentId.current) {
      resetToDefaultState();
      prevSelectedSkillAssessmentId.current = props.selectedSkillAssessmentId;
    }
  }, [props.selectedSkillAssessmentId]);

  const requestPrintMaterials = () => {
    const assessmentIds = [props.assessmentId];
    const {
      benchmarkPeriodId,
      materialsType, // 'intervention-packet' or 'assessment'
      materialsPart = "", // empty for all parts, "instructions" or "practice"
      interventionType = "", // 'CW', 'CCC', 'GP', '' for assessment,
      assessmentMeasure, // measure number
      protocolMeasure,
      studentName = "",
      groupName = "",
      mayTakeLong
    } = props;

    setPrintMaterialsStatus("FETCHING");
    if (mayTakeLong) {
      Alert.info("Generating all materials for this intervention may take up to a minute. Do not exit this web page.", {
        timeout: 120000
      });
    }
    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: interventionType,
        assessmentIds,
        assessmentMeasureIds: [assessmentMeasure],
        protocolMeasureIds: [protocolMeasure],
        studentGrade: props.grade,
        studentName,
        studentGroupId: props.match?.params?.studentGroupId,
        payloadType: materialsType,
        benchmarkPeriodId,
        groupName,
        materialsPart
      },
      (error, resp) => {
        Alert.closeAll();
        if (error) {
          Alert.error(`${error.error}: ${error.reason}`, { timeout: 5000 });
          setPrintMaterialsStatus("ERROR");
        } else {
          setPrintMaterialsStatus(currentStatus => {
            if (currentStatus === "FETCHING") {
              const parts = Array.from(resp.matchAll(/"part\d+"([^"]+)/gi));
              if (parts.length) {
                setPrintMaterialsQueue(parts.map(part => part[1]));
                return "SUCCESS";
              }
              setPrintMaterials(resp);
              return "SUCCESS";
            }
            return currentStatus;
          });
        }
      }
    );
  };

  const selectPrintMaterialsInDropdown = () => {
    if (inDropdown.current) {
      props.setCurrentlyPrinting(props.printOptionKey);
    }
  };

  const resetErrorState = () => {
    if (printMaterialsStatus === "ERROR") {
      resetToDefaultState();
    }
  };

  useEffect(() => {
    if (printMaterialsStatus === "SUCCESS") {
      if (printMaterialsQueue.length) {
        printMaterialsQueue.forEach((pdfContent, index) => {
          const titles = ["student-materials", "answer-key"];
          const fileName = `springmath-assessment${props.assessmentId}_${titles[index] || `part${index + 1}`}.pdf`;
          downloadPdf(pdfContent, fileName);
        });
      } else if (printMaterials) {
        const fileName = `springmath-assessment${props.assessmentId}.pdf`;
        downloadPdf(printMaterials, fileName);
      }

      if (inDropdown.current) {
        props.setCurrentlyPrinting(false);
      }
      resetToDefaultState();
    }
  }, [printMaterialsStatus, printMaterials, printMaterialsQueue, props.assessmentId, props.setCurrentlyPrinting]);

  const renderPrintAssessmentsButton = () => {
    const itemClass = inDropdown.current
      ? "menuitem print-materials-link"
      : `btn btn-primary invert white-space-normal`;
    if (printMaterialsStatus === "NOT_ATTEMPTED") {
      return (
        <span
          className={itemClass}
          onClick={inDropdown.current ? selectPrintMaterialsInDropdown : requestPrintMaterials}
        >
          <i className="fa fa-file-text-o fa-left" />
          {`${inDropdown.current ? " " : ""}${props.initialText}`}
        </span>
      );
    }

    if (printMaterialsStatus === "ERROR") {
      return (
        <span className="btn btn-danger" onClick={resetErrorState}>
          <i className="fa fa-file-text-o fa-left" />
          Printing error, click to reset
        </span>
      );
    }
    return <p>There was a problem loading assessments</p>;
  };

  useEffect(() => {
    if (
      inDropdown.current &&
      printMaterialsStatus === "NOT_ATTEMPTED" &&
      props.getCurrentlyPrinting() === props.printOptionKey
    ) {
      requestPrintMaterials();
    }
  }, []);

  if (props.loading) {
    return <Loading />;
  }

  return (
    <div
      className={`w9 text-center ${
        inDropdown.current && !props.getCurrentlyPrinting() ? "print-materials-item" : "print-materials-button"
      } ${inDropdown.current && props.getCurrentlyPrinting() && props.isVertical ? "col-12" : ""}`}
      data-testid="printAssessmentsButton"
    >
      {printMaterialsStatus === "FETCHING" ? (
        <Loading message={props.loadingText} inline />
      ) : (
        renderPrintAssessmentsButton()
      )}
    </div>
  );
};

PrintMaterials.defaultProps = {
  mayTakeLong: false
};

PrintMaterials.propTypes = {
  assessmentId: PropTypes.string,
  assessmentMeasure: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  grade: PropTypes.string,
  initialText: PropTypes.string,
  interventionType: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  materialsType: PropTypes.string,
  materialsPart: PropTypes.string,
  printOptionKey: PropTypes.string,
  protocolMeasure: PropTypes.string,
  studentName: PropTypes.string,
  groupName: PropTypes.string,
  setCurrentlyPrinting: PropTypes.func,
  getCurrentlyPrinting: PropTypes.func,
  mayTakeLong: PropTypes.bool,
  selectedSkillAssessmentId: PropTypes.string,
  isVertical: PropTypes.bool,
  match: PropTypes.object
};

export default withRouter(PrintMaterials);
