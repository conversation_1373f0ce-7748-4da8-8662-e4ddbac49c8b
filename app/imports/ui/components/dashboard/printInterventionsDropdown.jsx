import { Meteor } from "meteor/meteor";
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { DropdownButton, Dropdown } from "react-bootstrap";
import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

function convertIdToName(id) {
  switch (id) {
    case "CCC":
      return "Cover, Copy, Compare";
    case "GP":
      return "Guided Practice";
    case "TT":
      return "Timed Trial";
    case "RC":
      return "Response Cards";
    case "B":
      return "Bingo";
    case "FIB":
      return "Fill-In Bingo";
    case "SRG":
      return "Scatter and Rearrange Game";
    case "IR":
      return "Incremental Rehearsal";
    case "BAG":
      return "Before and After Game";
    default:
      return null;
  }
}

const PrintInterventionsDropdown = props => {
  const [fetchingPrintMaterials, setFetchingPrintMaterials] = useState(false);
  const [printMaterialsStatus, setPrintMaterialsStatus] = useState("NOT_ATTEMPTED");
  const [printMaterials, setPrintMaterials] = useState("");

  useEffect(() => {
    if (printMaterialsStatus === "SUCCESS" && printMaterials) {
      const fileName = `springmath-assessment${props.assessmentId}.pdf`;
      downloadPdf(printMaterials, fileName);
      setFetchingPrintMaterials(false);
      setPrintMaterialsStatus("NOT_ATTEMPTED");
      setPrintMaterials("");
    }
  }, [printMaterialsStatus, printMaterials, props.assessmentId]);

  const handleDropDownActivitySelect = (eventKey, event) => {
    event.preventDefault();
    const aid = props.assessmentMeasure; // NOTE(fmazur) - monitorAssessmentMeasure from benchmarkAssessmentId
    const pid = props.protocolMeasure; // NOTE(fmazur) - monitorAssessmentMeasure from assessmentId
    const assessmentIds = [props.assessmentId, props.benchmarkAssessmentId];
    const { benchmarkPeriodId, groupName, studentGroupId, grade: studentGrade, studentName = "" } = props;

    setFetchingPrintMaterials(true);

    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: eventKey,
        assessmentIds,
        assessmentMeasureIds: [aid],
        protocolMeasureIds: [pid],
        studentGrade,
        studentName,
        studentGroupId,
        payloadType: "intervention-packet",
        benchmarkPeriodId,
        groupName
      },
      (err, resp) => {
        if (err) {
          Alert.error(`${err.error}: ${err.reason}`, { timeout: 3000 });
          setFetchingPrintMaterials(false);
          setPrintMaterialsStatus("ERROR");
        } else {
          setFetchingPrintMaterials(false);
          setPrintMaterialsStatus("SUCCESS");
          setPrintMaterials(resp);
        }
      }
    );
  };

  const renderDropDown = () => {
    if (props.protocolMeasure && props.protocolMeasure.length > 0) {
      return (
        <DropdownButton
          variant="primary"
          className="invert"
          onSelect={handleDropDownActivitySelect}
          title={props.initialText}
          id="ddbActivity"
        >
          {props.interventionsAvailable.map(intervention => (
            <Dropdown.Item key={intervention} eventKey={intervention}>
              {convertIdToName(intervention)}
            </Dropdown.Item>
          ))}
        </DropdownButton>
      );
    }
    return <Loading inline={true} />;
  };

  return (
    <div>
      {fetchingPrintMaterials ? (
        <Loading message="Custom building intervention activity packets. Just a moment please." inline />
      ) : (
        renderDropDown()
      )}
    </div>
  );
};

PrintInterventionsDropdown.propTypes = {
  assessmentId: PropTypes.string,
  assessmentMeasure: PropTypes.string,
  benchmarkAssessmentId: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  grade: PropTypes.string,
  interventionsAvailable: PropTypes.array,
  protocolMeasure: PropTypes.string,
  studentGroupId: PropTypes.string,
  studentName: PropTypes.string,
  groupName: PropTypes.string,
  initialText: PropTypes.string
};

export default PrintInterventionsDropdown;
