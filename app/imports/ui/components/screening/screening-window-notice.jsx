import React, { useState } from "react";
import PropTypes from "prop-types";

export default function ScreeningWindowNotice({
  expandedNoticeState,
  showThreeDayWarning,
  daysBeforeScreeningWindow,
  screeningSeason,
  expandedStateCB
}) {
  const [showScreeningProgress, setShowScreeningProgress] = useState(expandedNoticeState);

  const handleShowScreeningProgress = () => {
    const newState = !showScreeningProgress;
    if (expandedStateCB) {
      expandedStateCB(newState);
    }
    setShowScreeningProgress(newState);
  };

  return (
    <div
      className={`conScreeningNotice ${showThreeDayWarning ? "screeningWindowStarted" : ""}
          schoolwideLevelProgress ${showScreeningProgress ? "opened" : "closed"}`}
    >
      <div className="conScreeningNotice-Heading clearfix">
        <button
          className={`btnNoticeAction btn
                ${showThreeDayWarning ? "btnStartScreening" : "btnViewProgress btn-success"} btn-xs`}
          onClick={handleShowScreeningProgress}
        >
          {showScreeningProgress ? "Hide" : "Learn More"}
        </button>

        <div className="iconCallout">
          <i className="fa fa-bullhorn" />
        </div>

        <h2>
          {screeningSeason} screening starts in {daysBeforeScreeningWindow} days!
        </h2>
      </div>
    </div>
  );
}

ScreeningWindowNotice.propTypes = {
  expandedNoticeState: PropTypes.bool,
  showThreeDayWarning: PropTypes.bool,
  daysBeforeScreeningWindow: PropTypes.number,
  screeningSeason: PropTypes.string,
  expandedStateCB: PropTypes.func
};
