import React, { useContext } from "react";
import { Meteor } from "meteor/meteor";
import { useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";

const ScreeningWindowStarted = ({ screeningContinues, screeningSeason }) => {
  const history = useHistory();
  const { studentGroup } = useContext(StudentGroupContext);

  const startNewScreening = () => {
    const { siteId, grade, orgid } = studentGroup || {};
    const studentGroupId = studentGroup?._id;
    const opts = {
      studentGroupId,
      siteId,
      grade,
      orgid
    };
    Meteor.call("setUpNewScreeningPeriod", opts, (error, results) => {
      if (error) {
        console.log("error: ", error);
      }
      if (results) {
        history.push(`/${orgid}/site/${siteId}/student-groups/${studentGroupId}/screening/form`);
      }
    });
  };

  return (
    <div className="conScreeningNotice screeningWindowStarted">
      <div className="conScreeningNotice-Heading clearfix">
        {screeningContinues ? (
          <button
            className="btnNoticeAction btnStartScreening btn btn-xs"
            onClick={() => {
              history.push(
                `/${studentGroup?.orgid}/site/${studentGroup?.siteId}/student-groups/${studentGroup?._id}/screening/form`
              );
            }}
          >
            Continue Screening
          </button>
        ) : (
          <button className="btnNoticeAction btnStartScreening btn btn-xs" onClick={startNewScreening}>
            Start Screening
          </button>
        )}
        <div className="iconCallout">
          <i className="fa fa-bullhorn" />
        </div>

        {screeningContinues ? (
          <h2>It looks like your class has not completed {screeningSeason} screening.</h2>
        ) : (
          <h2>{`It's time to start ${screeningSeason} screening!`}</h2>
        )}
      </div>
    </div>
  );
};

ScreeningWindowStarted.propTypes = {
  screeningContinues: PropTypes.bool,
  studentGroup: PropTypes.object,
  screeningSeason: PropTypes.string
};

export default ScreeningWindowStarted;
