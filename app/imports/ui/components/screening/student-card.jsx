import React, { Component } from "react";
import PropTypes from "react-dom/prop-types";

export default class StudentCard extends Component {
  constructor(props) {
    super(props);
    this.addStudent = this.addStudent.bind(this);
  }
  addStudent(e) {
    this.props.addStudent(e, this.props.studentInfo.studentId);
  }
  checkboxName() {
    return `${this.props.studentInfo.lastName}_${this.props.studentInfo.firstName}_${this.props.studentInfo.studentId}`;
  }
  renderCheckboxDecision() {
    if (this.props.scheduled) {
      return (
        <div>
          <i className="fa fa-check fa-2x text-success" />
          <h6>Scheduled for interventions</h6>
        </div>
      );
    } else if (this.props.enabled) {
      return (
        <input
          ref={this.props.studentInfo.studentId}
          type="checkbox"
          name={this.checkboxName()}
          onClick={this.addStudent}
        />
      );
    }
    return null;
  }
  render() {
    return (
      <div className="student-intervention-card active">
        <h4 className="student-name">
          {this.props.studentInfo.firstName} {this.props.studentInfo.lastName}
        </h4>
        {this.renderCheckboxDecision()}
      </div>
    );
  }
}

StudentCard.propTypes = {
  addStudent: PropTypes.func,
  enabled: PropTypes.bool,
  scheduled: PropTypes.bool,
  studentInfo: PropTypes.shape({
    firstName: PropTypes.string,
    lastName: PropTypes.string,
    studentId: PropTypes.string
  })
};
