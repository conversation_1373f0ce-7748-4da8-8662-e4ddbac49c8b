import React from "react";
import PropTypes from "prop-types";

const ActiveSchoolYearMessage = props =>
  props.inActiveSchoolYear ? null : (
    <div className="text-center prior-year-text">
      <em>This class/group is not in the active school year. The form is disabled and kept for reference only.</em>
    </div>
  );

ActiveSchoolYearMessage.propTypes = {
  inActiveSchoolYear: PropTypes.bool
};

export default ActiveSchoolYearMessage;
