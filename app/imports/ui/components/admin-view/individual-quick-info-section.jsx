import React, { Component } from "react";
import PropTypes from "prop-types";

import QuickInfoContainer from "./quick-info-container.jsx";

export default class IndividualQuickInfoSection extends Component {
  constructor(props) {
    super(props);

    this.state = {
      expanded: false
    };
  }

  toggle = () => {
    this.setState({ expanded: !this.state.expanded });
  };

  getQuickInfoData = (positiveInfoData, negativeInfoData) => {
    const studentNamesWithNegativeInfoData = negativeInfoData.map(n => n.name);
    const filteredPositiveInfoData = positiveInfoData.filter(p => !studentNamesWithNegativeInfoData.includes(p.name));
    return [...negativeInfoData, ...filteredPositiveInfoData];
  };

  render() {
    if (!this.props.data || !this.props.data.length) return null;

    const positiveInfoData = [];
    const negativeInfoData = [];

    const individualInfoMessages = {
      goodProgress: averageWeeksPerSkill =>
        `Progress is great. This student is progressing at an average of ${averageWeeksPerSkill} skills per week. We'd recommend asking the teacher what is working and if they have any tips for others`,
      goodInterventionConsistency:
        "This student has excellent intervention consistency! This means that their progress monitoring scores are entered consistently, great job.",
      stuckOnSkill:
        "This student has been on one skill for 4 weeks or more. It might be worth checking in with their teacher.",
      lowInterventionConsistency:
        "This student has low intervention consistency. This means that progress monitoring scores aren't being entered each week. We would recommend checking in with their teacher.",
      scoresNotImproving:
        "The scores for most of the students in this class are not improving. It might be a good idea to check in with them."
    };

    this.props.data.forEach(group => {
      group.results.forEach(stats => {
        const messageInitialState = {
          name: `${stats.studentLastName}, ${stats.studentFirstName}`,
          href: `/${group.orgid}/site/${this.props.siteId}/student-groups/${group.groupId}/students/${stats.studentId}`
        };
        if (stats.averageWeeksPerSkill && stats.averageWeeksPerSkill > 0 && stats.averageWeeksPerSkill < 2) {
          positiveInfoData.push({
            ...messageInitialState,
            message: individualInfoMessages.goodProgress(stats.averageWeeksPerSkill)
          });
        }
        if (stats.averageWeeksPerSkill && stats.averageWeeksPerSkill >= 4) {
          negativeInfoData.push({
            ...messageInitialState,
            message: individualInfoMessages.stuckOnSkill,
            warning: true
          });
        }
        if (stats.interventionConsistency && stats.interventionConsistency > 95) {
          positiveInfoData.push({
            ...messageInitialState,
            message: individualInfoMessages.goodInterventionConsistency
          });
        }
        if (
          stats.numberOfWeeksActive &&
          stats.numberOfWeeksActive <= 2 &&
          stats.interventionConsistency &&
          stats.interventionConsistency < 100
        ) {
          negativeInfoData.push({
            ...messageInitialState,
            message: individualInfoMessages.lowInterventionConsistency,
            warning: true
          });
        } else if (stats.interventionConsistency && stats.interventionConsistency < 80) {
          negativeInfoData.push({
            ...messageInitialState,
            message: individualInfoMessages.lowInterventionConsistency,
            warning: true
          });
        }
      });
    });

    if (!positiveInfoData.length && !negativeInfoData.length) {
      return null;
    }
    const quickInfoData = this.getQuickInfoData(positiveInfoData, negativeInfoData);
    if (!quickInfoData.length) {
      return null;
    }

    return (
      <section className="quickInfoSection quickInfoIndividualSection" data-testid="quickInfoIndividualSection">
        <h2>Summary Notes for Your Students</h2>
        <QuickInfoContainer quickInfoData={quickInfoData} containerExpanded={this.state.expanded} />
        {quickInfoData.length > 4 && (
          <button className="btn btn-link btn-center" onClick={this.toggle}>
            {this.state.expanded ? "Show Less" : "Show More"}
          </button>
        )}
      </section>
    );
  }
}

IndividualQuickInfoSection.propTypes = {
  data: PropTypes.array,
  siteId: PropTypes.string
};
