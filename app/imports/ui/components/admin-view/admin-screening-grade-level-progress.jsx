import React, { Component } from "react";
import { Link } from "react-router-dom";
import PropTypes from "prop-types";
import { intersection } from "lodash";

import { isHighSchoolGrade } from "../../utilities";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";

export default class GradeLevelScreeningInProgress extends Component {
  state = {
    showScreeningProgress: true
  };

  showScreeningProgress = () => {
    this.setState({ showScreeningProgress: !this.state.showScreeningProgress });
  };

  hasIndividualInterventionRecommendations = (individualInterventionQueue = [], studentIds = []) => {
    return individualInterventionQueue.length > 0 && intersection(individualInterventionQueue, studentIds).length > 0;
  };

  renderStudentGroups() {
    if (!this.props.progressData) {
      return <div>Loading...</div>;
    }
    const studentIdsByStudentGroupId = (this.props.studentGroupEnrollments || []).reduce((acc, current) => {
      if (!acc[current.studentGroupId]) {
        acc[current.studentGroupId] = [];
      }
      acc[current.studentGroupId].push(current.studentId);
      return acc;
    }, {});
    return (
      <div className="studentGroupProgress">
        <ul className="fa-ul">
          {this.props.progressData.map((groupData, i) => {
            let iconStyle = "check-circle inactive";
            if (this.props.inActiveSchoolYear) {
              iconStyle = groupData.screeningComplete ? "check-circle completed" : "times-circle";
            }
            const hasIndividualInterventionRecommendations = this.hasIndividualInterventionRecommendations(
              groupData.individualInterventionQueue,
              studentIdsByStudentGroupId[groupData.groupId]
            );
            return (
              <li key={`${groupData.lastName}_${i}`} className="w7 container-item container-item-full-border">
                <i className={`fa fa-li fa-${iconStyle}`} />
                <Link
                  to={`/${this.props.orgid}/site/${groupData.siteId}/student-groups/${groupData.groupId}/${
                    isHighSchoolGrade(this.props.grade) ? "" : "screening"
                  }`}
                >
                  {groupData.firstName.slice(0, 1)} {groupData.lastName} ({groupData.groupName})
                </Link>
                {hasIndividualInterventionRecommendations ? (
                  <div className="important-dog-ear">
                    <i className="fa fa-star important-dog-ear-icon" />
                  </div>
                ) : null}
              </li>
            );
          })}
        </ul>
      </div>
    );
  }

  render() {
    const gradeLevelText = this.props.inActiveSchoolYear
      ? `${this.props.currentBMPeriod.name} Screening Status for ${getCurrentEnrolledGrade(this.props.grade)}`
      : "Student Groups:";
    return (
      <div
        className={`conScreeningNotice gradeLevelProgress ${this.state.showScreeningProgress ? "opened" : "closed"}`}
        data-testid="screeningNotice"
      >
        <div className="conScreeningNotice-Heading clearfix">
          <button
            className="btnNoticeAction btnViewProgress btn btn-success btn-xs"
            onClick={this.showScreeningProgress}
          >
            {this.state.showScreeningProgress ? "Hide" : "View"} {this.props.inActiveSchoolYear ? "Progress" : "Groups"}
          </button>

          <div className="iconCallout">
            <i className="fa fa-bullhorn" />
          </div>
          <h2>{gradeLevelText}</h2>
        </div>
        {this.renderStudentGroups()}
      </div>
    );
  }
}

GradeLevelScreeningInProgress.propTypes = {
  currentBMPeriod: PropTypes.shape({
    name: PropTypes.string
  }),
  grade: PropTypes.string,
  siteName: PropTypes.string,
  orgid: PropTypes.string,
  progressData: PropTypes.arrayOf(
    PropTypes.shape({
      firstName: PropTypes.string,
      lastName: PropTypes.string,
      screeningComplete: PropTypes.bool,
      numberScreened: PropTypes.number
    })
  ),
  inActiveSchoolYear: PropTypes.bool,
  studentGroups: PropTypes.array,
  studentGroupEnrollments: PropTypes.array
};
