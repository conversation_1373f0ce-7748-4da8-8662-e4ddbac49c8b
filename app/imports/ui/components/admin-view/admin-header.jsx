import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";

import { openPrintWindow } from "/imports/ui/utilities";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { SCHOOL_OVERVIEW_TITLE } from "/imports/api/constants";

export default function Header(props) {
  const { schoolYear } = useContext(SchoolYearContext);
  const [isPrinting, setIsPrinting] = useState(false);

  useEffect(() => {
    window.onmessage = event => {
      if (event.data === "printScheduled") {
        setIsPrinting(true);
      } else if (event.data === "printWindowClosed") {
        setIsPrinting(false);
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
      }
    };
  }, []);

  const printPage = () => {
    const printURL = `/${props.orgid}/print/SchoolOverview?orgid=${props.orgid}&gradeId=all&siteId=${props.siteId}&screeningHidden=${props.screeningHidden}&schoolYear=${schoolYear}`;
    openPrintWindow(printURL);
  };

  if (!props.keyLabel || !props.headerTitle) {
    return null;
  }

  const shouldShowDemographics =
    props.headerTitle === SCHOOL_OVERVIEW_TITLE
      ? props.headerStats && props.headerStats.some(statObject => statObject.value)
      : true;

  return (
    <div className="conOverviewHeader clearfix">
      <h1 data-testid="groupHeader">
        {props.headerTitle} {props.headerSubtitle && <small>{props.headerSubtitle}</small>}{" "}
        {props.additionalHeaderInfo && <small>{props.additionalHeaderInfo}</small>}
      </h1>
      <div className="overview-stats">
        {shouldShowDemographics &&
          props.headerStats &&
          props.headerStats.length > 0 &&
          props.headerStats.map((item, i) => (
            <span className="stat" key={`${props.keyLabel}_${i}`} data-testid="demographics-header">
              {item.value}
              <small> {item.label}</small>
              {!(props.keyLabel === "schoolOverview") || props.isPrinting ? null : (
                <button className="btn btn-success pull-right print-page" disabled={isPrinting} onClick={printPage}>
                  <i className="fa fa-print" /> {isPrinting ? "Preparing printout..." : "Print This Page"}
                </button>
              )}
            </span>
          ))}
      </div>
    </div>
  );
}

Header.propTypes = {
  keyLabel: PropTypes.string,
  headerTitle: PropTypes.string,
  headerSubtitle: PropTypes.string,
  headerStats: PropTypes.array,
  additionalHeaderInfo: PropTypes.string,
  isPrinting: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  screeningHidden: PropTypes.bool
};
