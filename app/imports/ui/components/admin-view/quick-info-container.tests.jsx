import { assert } from "chai";
import React from "react";
import { shallow } from "enzyme";

import QuickInfoContainer from "./quick-info-container.jsx";

describe("imports/ui/components/admin-view/quick-info-container.jsx tests", () => {
  const buildTestQuickInfoData = num =>
    [...Array(num).keys()].map(i => ({
      name: `testStudentGroup${i}`,
      href: `/testStudentGroup${i}`,
      message: "test message"
    }));
  it("should have a list of the size of the quickInfoData array", () => {
    const testQuickInfoData = buildTestQuickInfoData(9);
    const testQuickInfoContainer = shallow(<QuickInfoContainer quickInfoData={testQuickInfoData} />);
    const quickInfoLIs = testQuickInfoContainer.find(".quick-info-container li");
    assert.equal(quickInfoLIs.length, 9);
  });
  it("should have a collapsed class if the containerExpanded prop is falsy", () => {
    const testQuickInfoData = buildTestQuickInfoData(0);
    const testQuickInfoContainer = shallow(<QuickInfoContainer quickInfoData={testQuickInfoData} />);
    const collapsedList = testQuickInfoContainer.find(".collapsed");
    assert.equal(collapsedList.length, 1);
  });
  it("should not have a collapsed class if the containerExpanded prop is true", () => {
    const testQuickInfoData = buildTestQuickInfoData(0);
    const testQuickInfoContainer = shallow(<QuickInfoContainer quickInfoData={testQuickInfoData} containerExpanded />);
    const collapsedList = testQuickInfoContainer.find(".collapsed");
    assert.equal(collapsedList.length, 0);
  });
});
