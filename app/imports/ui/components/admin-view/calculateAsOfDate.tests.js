import React from "react";
import { assert } from "chai";
import { mount } from "enzyme";
import sinon from "sinon";
import { Meteor } from "meteor/meteor";

import CalculateAsOfDate, { getStartDateOfInterventions } from "./calculateAsOfDate.jsx";

describe("CalculateAsOfDate", () => {
  describe("getStartDateOfInterventions", () => {
    describe("when passed a null history and null currentSkill", () => {
      it("should return null", () => {
        const startDate = getStartDateOfInterventions({
          history: null,
          currentSkill: null,
          type: "individual",
          benchmarkPeriodId: "BM_PERIOD_ID"
        });
        assert.equal(startDate, null);
      });
    });
    describe("when passed a currentSkill and a null history", () => {
      it("should return the when started date of the currentSkill", () => {
        const date = new Date().toISOString();
        const startDate = getStartDateOfInterventions({
          history: null,
          currentSkill: { whenStarted: { date } }
        });
        assert.equal(date, startDate);
      });
    });
    describe("when passed a currentSkill and a history", () => {
      it("should return the oldest intervention that matches the benchmarkPeriod", () => {
        const currentSkill = {
          benchmarkPeriodId: "testBMPeriodId",
          whenStarted: {
            date: "DATE3"
          }
        };
        const history = [
          {
            type: "individual",
            benchmarkPeriodId: "testBMPeriodId",
            whenStarted: {
              date: "DATE2"
            }
          },
          {
            type: "individual",
            benchmarkPeriodId: "testBMPeriodId",
            whenStarted: {
              date: "DATE1"
            }
          }
        ];
        const startDate = getStartDateOfInterventions({
          history,
          currentSkill,
          type: "individual",
          benchmarkPeriodId: "testBMPeriodId"
        });
        assert.equal("DATE1", startDate);
      });
    });
  });
  describe("CalculateAsOfDate.handleDateChange", () => {
    let meteorSpy;
    beforeEach(() => {
      meteorSpy = sinon.spy(Meteor, "call");
    });
    afterEach(() => {
      meteorSpy.restore();
    });
    describe("when called with arguments for an individual student", () => {
      let wrapper;
      beforeEach(() => {
        const testStudentGroup = {
          _id: "studentGroupId",
          siteId: "testSiteId",
          schoolYear: 2023
        };
        const testStudent = {
          _id: "studentId",
          currentSkill: {
            whenStarted: { date: new Date() }
          }
        };
        wrapper = mount(
          <CalculateAsOfDate
            benchmarkPeriodId="testBMperiodId"
            group={testStudentGroup}
            student={testStudent}
            type="individual"
            updateStats={() => {}}
          />
        );
        // Wait for the component to finish loading (async isSupportUser call)
        setTimeout(() => {
          const datePicker = wrapper.find("DatePicker");
          if (datePicker.length > 0) {
            datePicker.prop("onChange")(new Date());
          }
        }, 100);
      });
      afterEach(() => {
        if (wrapper) {
          wrapper.unmount();
        }
      });
      it("should call a the meteor method once", done => {
        setTimeout(() => {
          assert.equal(meteorSpy.calledOnce, true);
          done();
        }, 200);
      });
      it("should call Students:updateCalculateAsOfDate", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[0], "Students:updateCalculateAsOfDate");
          }
          done();
        }, 200);
      });
      it("should call Students:updateCalculateAsOfDate with an object as its second argument", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(typeof meteorSpy.getCall(0).args[1], "object");
          }
          done();
        }, 200);
      });
      it("the object parameter should contain a siteId key with a value of testSiteId", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[1].siteId, "testSiteId");
          }
          done();
        }, 200);
      });
      it("the object parameter should contain an entityId key with a value of studentId", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[1].entityId, "studentId");
          }
          done();
        }, 200);
      });
    });

    describe("when called with arguments for a studentGroup", () => {
      let wrapper;
      beforeEach(() => {
        const testStudentGroup = {
          _id: "studentGroupId",
          siteId: "testSiteId",
          schoolYear: 2023,
          currentClasswideSkill: {
            whenStarted: { date: new Date() }
          }
        };
        wrapper = mount(
          <CalculateAsOfDate
            benchmarkPeriodId="testBMperiodId"
            group={testStudentGroup}
            type="classwide"
            updateStats={() => {}}
          />
        );
        // Wait for the component to finish loading (async isSupportUser call)
        setTimeout(() => {
          const datePicker = wrapper.find("DatePicker");
          if (datePicker.length > 0) {
            datePicker.prop("onChange")(new Date());
          }
        }, 100);
      });
      afterEach(() => {
        if (wrapper) {
          wrapper.unmount();
        }
      });
      it("should call a the meteor method once", done => {
        setTimeout(() => {
          assert.equal(meteorSpy.calledOnce, true);
          done();
        }, 200);
      });
      it("should call StudentGroups:updateCalculateAsOfDate", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[0], "StudentGroups:updateCalculateAsOfDate");
          }
          done();
        }, 200);
      });
      it("should call StudentGroups:updateCalculateAsOfDate with an object as its second argument", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(typeof meteorSpy.getCall(0).args[1], "object");
          }
          done();
        }, 200);
      });
      it("the object parameter should contain a siteId key with a value of testSiteId", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[1].siteId, "testSiteId");
          }
          done();
        }, 200);
      });
      it("the object parameter should contain an entityId key with a value of studentGroupId", done => {
        setTimeout(() => {
          if (meteorSpy.called) {
            assert.equal(meteorSpy.getCall(0).args[1].entityId, "studentGroupId");
          }
          done();
        }, 200);
      });
    });
  });
});
