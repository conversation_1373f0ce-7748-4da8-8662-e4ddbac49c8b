import React, { useState, useCallback } from "react";
import PropTypes from "prop-types";
import { ProgressBar } from "react-bootstrap";

import AdminProgressCircleChart from "/imports/ui/components/admin-view/admin-progress-circle-chart";

export default function AdminSchoolwideProgress({ progressData, currentBMPeriod, isPrinting, changePrintingStatus }) {
  const [showScreeningProgress, setShowScreeningProgress] = useState(true);

  const handleShowScreeningProgress = useCallback(() => {
    const newShowScreeningProgress = !showScreeningProgress;
    setShowScreeningProgress(newShowScreeningProgress);
    changePrintingStatus(!newShowScreeningProgress);
  }, [showScreeningProgress, changePrintingStatus]);

  return (
    <div
      className={`conScreeningNotice schoolwideLevelProgress ${showScreeningProgress ? "opened" : "closed"} ${
        isPrinting ? " page-break-after" : ""
      }`}
    >
      <div className="conScreeningNotice-Heading clearfix">
        {isPrinting ? null : (
          <button
            className="btnNoticeAction btnViewProgress btn btn-success btn-xs"
            data-testid="screeningProgressHideButton"
            onClick={handleShowScreeningProgress}
          >
            {showScreeningProgress ? "Hide" : "View"} Progress
          </button>
        )}
        <div className="iconCallout">
          <i className="fa fa-bullhorn" />
        </div>
        <h2>{currentBMPeriod.name} screening is underway!</h2>
      </div>
      <div className="conScreeningNotice-Progress">
        <h4>Screening Progress</h4>
        <div className="small fw-light mb-2">% of classes in each grade that have completed screening</div>
      </div>

      <div className="conScreeningNotice-Progress progressBar clearfix">
        <span className="classesScreened">
          <strong>{progressData.numTotalScreened}</strong> of
          <strong> {progressData.numTotalClasses}</strong> Classes Screened
        </span>
        <ProgressBar now={progressData.sitewideComplete} label={`${progressData.sitewideComplete}% Completed`} />
      </div>
      <div className={`conScreeningNotice-Progress circleCharts clearfix mb-3 ${isPrinting ? "isPrinting" : ""}`}>
        {progressData.gradesScreening.map(g => (
          <AdminProgressCircleChart
            key={`screeningProgress_${g.grade}`}
            chartId={`screeningProgressChart_${g.grade}`}
            grade={g.grade}
            percentComplete={g.percentComplete}
            shown={showScreeningProgress}
            isPrinting={isPrinting}
          />
        ))}
      </div>

      {currentBMPeriod.name === "Winter" && (
        <React.Fragment>
          <hr />
          <div className="conScreeningNotice-Progress">
            <h4>Classwide Progress</h4>
            <div className="small fw-light mb-2">
              % of classes in each grade that have reached their mid-year goal for classwide interventions
            </div>
          </div>
          <div className="conScreeningNotice-Progress progressBar clearfix">
            <span className="classesScreened">
              <strong>{progressData.numTotalReached}</strong> of
              <strong> {progressData.numTotalClassesWithClasswide}</strong> Classes Reached Goal
            </span>
            <ProgressBar now={progressData.sitewideReached} label={`${progressData.sitewideReached}% Reached`} />
          </div>
          <div className={`conScreeningNotice-Progress circleCharts clearfix mb-3 ${isPrinting ? "isPrinting" : ""}`}>
            {progressData.gradesClasswideProgress.map(g => (
              <AdminProgressCircleChart
                key={`classwideProgress_${g.grade}`}
                chartId={`classwideProgressChart_${g.grade}`}
                grade={g.grade}
                percentComplete={g.percentComplete}
                percentText="Reached"
                shown={showScreeningProgress}
                isPrinting={isPrinting}
              />
            ))}
          </div>
        </React.Fragment>
      )}
    </div>
  );
}

AdminSchoolwideProgress.propTypes = {
  siteName: PropTypes.node,
  currentBMPeriod: PropTypes.object,
  isPrinting: PropTypes.bool,
  progressData: PropTypes.shape({
    sitewideComplete: PropTypes.node,
    sitewideReached: PropTypes.node,
    numTotalClasses: PropTypes.node,
    numTotalClassesWithClasswide: PropTypes.node,
    numTotalScreened: PropTypes.node,
    numTotalReached: PropTypes.node,
    gradesScreening: PropTypes.arrayOf(Object),
    gradesClasswideProgress: PropTypes.arrayOf(Object)
  }),
  changePrintingStatus: PropTypes.func
};
