import { assert } from "chai";
import React from "react";
import TestUtils from "react-dom/test-utils";
import ScreeningRowCell from "./score-entry-row-cell.jsx";

describe("ScreeningRowCell UI", () => {
  describe("Render", () => {
    let screeningRowCellComponent;
    const assessmentScore = {
      _id: "AHW4gkG9CheDLnr8W",
      assessmentId: "oCNYMYX3WE8xhRhZR",
      orgid: "test_organization_id",
      siteId: "test_elementary_site_id",
      status: "STARTED",
      studentId: "Spencer_Elliott_DEMO_STUDENT"
    };
    const inputValidationIndicator = {
      labels: [],
      classNames: []
    };

    beforeEach(() => {
      screeningRowCellComponent = TestUtils.renderIntoDocument(
        <ScreeningRowCell
          cellId="cellId1"
          assessmentScore={assessmentScore}
          index={0}
          rowScoreCount={1}
          inputValidationIndicator={inputValidationIndicator}
          assessmentScoreLimit={{ limit: 20 }}
        />
      );
    });

    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(screeningRowCellComponent, "screeningRowCellComponent did not render");
    });
  });
});
