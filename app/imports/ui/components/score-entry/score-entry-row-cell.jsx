import React, { useState, useEffect, useRef, useContext, useCallback } from "react";
import PropTypes from "prop-types";
import { debounce } from "lodash";
import { ninjalog } from "/imports/api/utilities/utilities";
import { ScoreEntryContext } from "./score-entry-context";
import { SiteContext } from "../../../contexts/SiteContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

const ScoreEntryRowCell = ({
  assessmentScore,
  assessmentScoreLimit,
  assessmentResultId,
  cellId,
  index,
  inActiveSchoolYear,
  isReadOnly,
  onFocus,
  rowScoreCount,
  seleniumSelectorText,
  isAbsent
}) => {
  const scoreEntryContext = useContext(ScoreEntryContext);
  const { siteId } = useContext(SiteContext);
  const {
    env: { CI }
  } = useContext(StaticDataContext);
  const [value, setValue] = useState(assessmentScore.value ?? "");
  const scoreHelperTimeoutHandleRef = useRef(null);
  const inputRefs = useRef({});
  const isUserInputRef = useRef(false);

  useEffect(() => {
    if (isAbsent) {
      setValue("N/A");
    } else if (!isAbsent && value === "N/A") {
      setValue("");
    }
  }, [isAbsent]);

  useEffect(() => {
    setValue(assessmentScore.value ?? "");
  }, [assessmentScore.value]);

  const resetErrors = elemTarget => {
    /* eslint-disable no-param-reassign */
    clearTimeout(scoreHelperTimeoutHandleRef.current);
    elemTarget.nextElementSibling.textContent = "";
    elemTarget.nextElementSibling.className = "help-block d-none";
    scoreEntryContext.removeUnusualHighScoreField({ cellId, assessmentResultId });
  };

  const validateField = (elemTarget, newValue) => {
    if (isAbsent) {
      return false;
    }
    /* eslint-disable no-param-reassign */
    if (newValue.match(/[^0-9]/g) || (newValue[0] === "0" && newValue.length > 1)) {
      // Set helper label / class if invalid input
      if (newValue[0] === "0" && newValue.length > 1) {
        elemTarget.nextElementSibling.textContent = "No Leading Zeroes";
      } else if (newValue[0] === "-") {
        elemTarget.nextElementSibling.textContent = "Must Be Positive";
      } else {
        elemTarget.nextElementSibling.textContent = "Integers Only";
      }
      // alert
      elemTarget.nextElementSibling.className = "help-block text-danger animated bounceIn";
      // focus
      elemTarget.focus();
      return false;
    }

    const limit = assessmentScoreLimit;
    if (limit && +newValue > +limit.limit) {
      elemTarget.nextElementSibling.textContent = "Unusual High Score";
      elemTarget.nextElementSibling.className = "help-block text-warning animated bounceIn";
      scoreEntryContext.setUnusualHighScoreField({
        cellId,
        ref: inputRefs.current[assessmentScore._id],
        assessmentResultId
      });
      return false;
    }
    return true;
  };

  const updateCellData = (elemTarget, newValue) => {
    const scoresPkg = [];

    scoresPkg.push({
      assessmentResultId,
      scoreId: elemTarget.getAttribute("data-assessment-score-id"),
      number_correct: newValue,
      status: newValue !== "" ? "COMPLETE" : "STARTED"
    });

    Meteor.call("AssessmentResults:updateScores", scoresPkg, siteId || assessmentScore.siteId, err => {
      if (err) {
        ninjalog.error({
          msg: "Error in AssessmentResults:updateScores",
          val: err,
          context: "score-entry"
        });
      } else if (["", "Saved"].includes(elemTarget.nextElementSibling.textContent) || newValue === elemTarget.value) {
        elemTarget.nextElementSibling.textContent = "Saved";
        elemTarget.nextElementSibling.className = "help-block text-success animated bounceIn";
        scoreHelperTimeoutHandleRef.current = setTimeout(
          () => {
            elemTarget.nextElementSibling.className = "help-block d-none";
          },
          CI ? 1000 : 1500
        );
      }
    });
  };

  const validateAndUpdate = useCallback((elemTarget, newValue) => {
    if (validateField(elemTarget, newValue)) {
      updateCellData(elemTarget, newValue);
    }
  }, []);

  const debouncedValidateAndUpdate = useCallback(
    debounce(validateAndUpdate, 300, {
      leading: false,
      trailing: true
    }),
    [validateAndUpdate]
  );

  useEffect(() => {
    if (isUserInputRef.current) {
      const elem = document.getElementById(cellId);
      if (elem) {
        resetErrors(elem);
        debouncedValidateAndUpdate(elem, value);
      }
      isUserInputRef.current = false;
    }
  }, [value]);

  useEffect(() => {
    return () => {
      debouncedValidateAndUpdate.flush();
    };
  }, []);

  const handleNavigationKeys = e => {
    const inputVal = document.activeElement.value;
    const currentPosition = document.activeElement.selectionStart;
    const currentIndex = document.activeElement.id.slice(12);
    let newIndex = currentIndex;
    switch (e.which) {
      case 9: // Tab
        break;
      case 37: // Left Arrow
        if (currentPosition === 0) {
          newIndex = parseInt(currentIndex) - 1;
        }
        break;
      case 38: // Up Arrow
        newIndex = parseInt(currentIndex) - rowScoreCount;
        break;
      case 39: // Right Arrow
        if ((inputVal && currentPosition === inputVal.length) || !inputVal) {
          newIndex = parseInt(currentIndex) + 1;
        }
        break;
      case 40: // Down Arrow
      case 13: // Enter
        e.preventDefault();
        newIndex = parseInt(currentIndex) + rowScoreCount;
        break;
      default:
      // Do Nothing
    }
    const newElement = document.getElementById(`score_input_${newIndex}`);
    if (newElement) {
      newElement.focus();
    }
  };

  const onChange = e => {
    const elemTarget = e.target;
    const newValue = elemTarget.value || "";
    isUserInputRef.current = true;
    setValue(newValue);
  };

  return (
    <li key={`${assessmentScore._id}:${index}`}>
      <input
        id={cellId}
        ref={r => {
          inputRefs.current[assessmentScore._id] = r;
        }}
        className="form-control"
        placeholder={"Enter Score"}
        name={`score ${index + 1}`}
        data-student-id={assessmentScore.studentId}
        data-purpose="enterScore"
        data-assessment-score-id={assessmentScore._id}
        data-testid={seleniumSelectorText || "scoreInput"}
        data-assessment-score-limit={assessmentScoreLimit ? assessmentScoreLimit.limit : 0}
        data-assessment-score-at={assessmentScoreLimit ? assessmentScoreLimit.at : 0}
        value={value}
        disabled={assessmentScore.value === "N/A" || !inActiveSchoolYear || isReadOnly}
        onFocus={onFocus}
        onKeyDown={handleNavigationKeys}
        onChange={onChange}
      />
      <span
        ref={r => {
          inputRefs.current[`error${assessmentScore._id}`] = r;
        }}
        className="help-block d-none"
      />
    </li>
  );
};

ScoreEntryRowCell.propTypes = {
  assessmentScore: PropTypes.object.isRequired,
  assessmentScoreLimit: PropTypes.shape({
    limit: PropTypes.number,
    at: PropTypes.number
  }),
  assessmentResultId: PropTypes.string,
  cellId: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  onFocus: PropTypes.func,
  rowScoreCount: PropTypes.number.isRequired,
  seleniumSelectorText: PropTypes.string,
  isAbsent: PropTypes.bool
};

export default ScoreEntryRowCell;
