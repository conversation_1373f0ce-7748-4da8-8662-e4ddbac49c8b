import React from "react";
import PropTypes from "prop-types";

const ScoreEntryContext = React.createContext();
const { Provider } = ScoreEntryContext;

class ScoreEntryProvider extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      unusualHighScoreFields: {},
      env: {}
    };
  }

  setUnusualHighScoreField = ({ cellId, ref, assessmentResultId }) => {
    this.setState(state => ({
      ...state,
      unusualHighScoreFields: {
        ...state.unusualHighScoreFields,
        [assessmentResultId]: {
          [cellId]: ref
        }
      }
    }));
  };

  componentDidMount() {
    this.getEnv();
  }

  removeUnusualHighScoreField = ({ cellId, assessmentResultId }) => {
    this.setState(state => {
      const { unusualHighScoreFields } = state;
      delete (unusualHighScoreFields[assessmentResultId] || {})[cellId];
      return {
        ...state,
        unusualHighScoreFields
      };
    });
  };

  clearUnusualHighScoreFields = () => {
    this.setState({ unusualHighScoreFields: {} });
  };

  getUnusualHighScoreFields = assessmentResultId => this.state.unusualHighScoreFields[assessmentResultId] || {};

  getEnv = () => {
    Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT", "CI"], (err, env) => {
      this.setState({ env });
    });
  };

  render() {
    return (
      <Provider
        value={{
          ...this.state,
          setUnusualHighScoreField: this.setUnusualHighScoreField,
          removeUnusualHighScoreField: this.removeUnusualHighScoreField,
          clearUnusualHighScoreFields: this.clearUnusualHighScoreFields,
          getUnusualHighScoreFields: this.getUnusualHighScoreFields
        }}
      >
        {this.props.children}
      </Provider>
    );
  }
}

ScoreEntryProvider.propTypes = {
  children: PropTypes.any
};

export { ScoreEntryProvider, ScoreEntryContext };
