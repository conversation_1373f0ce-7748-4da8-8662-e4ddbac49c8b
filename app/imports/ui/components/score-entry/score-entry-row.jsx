import React, { useState, useRef, useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import ScoreEntryRowCell from "./score-entry-row-cell";
import { ninjalog } from "/imports/api/utilities/utilities";
import { SiteContext } from "../../../contexts/SiteContext";

const valueNA = "N/A";

export const ScoreEntryRow = ({
  assessmentResultId,
  assessmentScoreLimits,
  assessmentScores,
  currentStudentScreeningStatus,
  inActiveSchoolYear,
  interventionName,
  progressMonitoringInfo,
  student,
  studentIndex,
  isReadOnly,
  isSML,
  sortBy
}) => {
  const { siteId } = useContext(SiteContext);
  const [isAbsent, setIsAbsent] = useState(false);
  const studentRefs = useRef({});

  const getBulletClassNames = () => {
    const statuses = currentStudentScreeningStatus;
    let bulletIcon = "fa-circle-o";
    let bulletColor = "default";
    if (statuses.someScoresEntered && !statuses.allScoresCancelled) {
      bulletIcon = statuses.allScoresEntered ? "fa-check-circle" : bulletIcon;
      bulletColor = "success";
    } else if (statuses.allScoresCancelled) {
      bulletIcon = "fa-times-circle";
      bulletColor = "danger";
    }
    return `fa ${bulletIcon} text-${bulletColor}`;
  };

  const highlightRow = (isVisible, e) => {
    if (
      e.target.getAttribute("data-purpose") !== "allScoresUnavailable" &&
      e.target.getAttribute("data-purpose") !== "allScoresAvailable"
    ) {
      const studentItems = Array.from(document.getElementsByClassName("student-item"));
      studentItems.forEach(si => {
        const studentItem = si;
        studentItem.className = "student-item";
      });
      const studentId = e.target.getAttribute("data-student-id");
      if (studentRefs.current[studentId]) {
        studentRefs.current[studentId].setAttribute("class", `student-item ${isVisible ? "active" : ""}`);
      }
    }
  };

  const updateScoreData = e => {
    e.preventDefault();
    const purpose = e.currentTarget.getAttribute("data-purpose");
    let scoresPkg = [];

    highlightRow(false, e);
    if (purpose === "allScoresUnavailable") {
      setIsAbsent(true);
      scoresPkg = assessmentScores.map(score => ({
        assessmentResultId,
        scoreId: score._id,
        number_correct: valueNA,
        status: "CANCELLED"
      }));
    } else if (purpose === "allScoresAvailable") {
      setIsAbsent(false);
      scoresPkg = assessmentScores.map(score => ({
        assessmentResultId,
        scoreId: score._id,
        number_correct: "",
        status: "STARTED"
      }));
    }
    Meteor.call("AssessmentResults:updateScores", scoresPkg, siteId, err => {
      if (err) {
        ninjalog.error({
          msg: "Error in AssessmentResults:updateScores",
          val: err,
          context: "score-entry"
        });
      }
    });
  };

  const renderButton = (hide, textTop, textBottom, purpose) => {
    return (
      <li>
        <a
          id="btnUnavailable"
          className={`btn btn-link not-screened ${hide ? "hide" : ""}`}
          data-purpose={purpose}
          onClick={inActiveSchoolYear ? updateScoreData : null}
          disabled={!inActiveSchoolYear}
        >
          {textTop}
          <br />
          {textBottom}
        </a>
      </li>
    );
  };

  // Provide assessment labels only when administering individual level progressmonitoring
  let inLinePMAssessmentLabel = null;

  if (progressMonitoringInfo && progressMonitoringInfo.type === "INDIVIDUAL_PROGRESS_MONITORING") {
    inLinePMAssessmentLabel = <td className="student-name">{interventionName} </td>;
  }
  const bulletClassNames = getBulletClassNames();

  const statuses = currentStudentScreeningStatus;
  const studentFirstName = student.identity.name.firstName;
  const studentLastName = student.identity.name.lastName;
  return (
    <tr
      ref={r => {
        studentRefs.current[student._id] = r;
      }}
      data-testid="student-test-name"
      className="student-item"
    >
      <td className="student-name">
        <i className={bulletClassNames} />
        &nbsp;
        {sortBy === "lastFirst" ? `${studentLastName}, ${studentFirstName}` : `${studentFirstName} ${studentLastName}`}
      </td>
      {inLinePMAssessmentLabel}
      <td className="screening-status">
        <form className="" id="screening-score-js" autoComplete="off">
          <ul className="screening-cols">
            {assessmentScores.length < 1
              ? null
              : assessmentScores.map((assessmentScore, index) => (
                  <li key={`${assessmentScore.studentId}_${index}`}>
                    <ul className="lstScoreInputs">
                      <ScoreEntryRowCell
                        cellId={`score_input_${studentIndex * assessmentScores.length + index}`}
                        assessmentScore={assessmentScore}
                        isAbsent={isAbsent}
                        assessmentResultId={assessmentResultId}
                        assessmentScoreLimit={assessmentScoreLimits.find(
                          l => l.assessmentId === assessmentScore.assessmentId
                        )}
                        index={index}
                        inActiveSchoolYear={inActiveSchoolYear}
                        isReadOnly={isReadOnly}
                        onFocus={e => highlightRow(true, e)}
                        rowScoreCount={assessmentScores.length}
                      />
                    </ul>
                  </li>
                ))}
            {statuses.allScoresCancelled
              ? renderButton(false, "Enter", "Scores", "allScoresAvailable")
              : renderButton(isSML || statuses.allScoresEntered, "Mark Student", "Absent", "allScoresUnavailable")}
          </ul>
        </form>
      </td>
    </tr>
  );
};

ScoreEntryRow.propTypes = {
  assessmentResultId: PropTypes.string,
  assessmentScoreLimits: PropTypes.array,
  assessmentScores: PropTypes.array.isRequired,
  currentStudentScreeningStatus: PropTypes.object.isRequired,
  inActiveSchoolYear: PropTypes.bool,
  interventionName: PropTypes.string,
  progressMonitoringInfo: PropTypes.object,
  student: PropTypes.object.isRequired,
  studentIndex: PropTypes.number,
  isReadOnly: PropTypes.bool,
  isSML: PropTypes.bool,
  sortBy: PropTypes.string
};

/** ************************************************
 * Screening Score Entry Data Container
 ************************************************* */
export default ScoreEntryRow;

/** ************************************************
 * Progress Monitoring Score Entry Data Container
 ************************************************* */
export const PMScoreEntry = ({ student, assessmentScores, assessmentResultId, ...otherProps }) => {
  // Subscriptions were performed on the parent (dashboard) component

  const screeningStatus = {
    allScoresCancelled: false,
    allScoresEntered: false,
    studentId: student._id
  };

  // Weekly progress monitoring will only be
  // performed once per week so there should only be one score.
  if (assessmentScores && assessmentScores[0] && assessmentScores[0].status === "CANCELLED") {
    screeningStatus.allScoresCancelled = true;
  } else if (assessmentScores && assessmentScores[0] && assessmentScores[0].status === "COMPLETE") {
    screeningStatus.someScoresEntered = true;
    screeningStatus.allScoresEntered = true;
  }

  return (
    <ScoreEntryRow
      student={student}
      assessmentScores={assessmentScores.filter(a => a)}
      currentStudentScreeningStatus={screeningStatus}
      assessmentResultId={assessmentResultId}
      {...otherProps}
    />
  );
};

PMScoreEntry.propTypes = {
  student: PropTypes.object.isRequired,
  assessmentScores: PropTypes.array.isRequired,
  assessmentResultId: PropTypes.string.isRequired
};
