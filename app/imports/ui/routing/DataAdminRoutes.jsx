import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import CustomRoute from "./CustomRoute";
import {
  checkIsDataAdminOrUniversalDataAdminOrSuperAdmin,
  checkIsOrgDataAdminOrUniversalDataAdminOrSuperAdmin,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import Upload from "../pages/data-admin/upload/upload";
import ManageTeachersView from "../pages/data-admin/manage-accounts/manage-accounts-view";
import AddTeacherToClass from "../pages/data-admin/add-teacher-to-class";
import DataAdminDashboard from "../pages/data-admin/data-admin-dashboard";
import SupportAccountSetup from "../pages/account-setup/support-account-setup";
import CoachAccountSetup from "../pages/account-setup/coach-account-setup";
import DataAdminSideNavLayout from "../layouts/side-nav-layout-data-admin";
import ManageGroup from "../pages/data-admin/manage-group";
import ManageSchool from "../pages/data-admin/manage-school";
import UnarchiveStudents from "../pages/data-admin/unarchive-students";
import SearchStudents from "../pages/data-admin/search-students";
import SearchTeachers from "../pages/data-admin/search-teachers";
import SearchCoaches from "../pages/data-admin/search-coaches";
import { AppDataContext } from "./AppDataContext";
import AssessmentScoreUpload from "../pages/data-admin/assessment-score-upload";
import DataScripts from "../pages/assessments-dashboard/data-scripts";
import Rostering from "../pages/data-admin/rostering";
import RosterImportHistory from "../pages/data-admin/roster-import-history";
import AuditLog from "../pages/audit-log/audit-log";
import { ManageOrgUsers } from "../pages/manage-org-users";
import { navbarItemsByRoleId } from "./navbarItems";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

export default class DataAdminRoutes extends React.Component {
  navbarItems = navbarItemsByRoleId.arbitraryIddataAdmin;

  tasksForOrgDataAdmins = [checkLoggedIn, setApplicationVersion, checkIsOrgDataAdminOrUniversalDataAdminOrSuperAdmin];

  routerGroupName = "dataAdmins";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/data-admin/upload/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<Upload orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/upload-assessment-scores/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<AssessmentScoreUpload orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-accounts/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<ManageTeachersView orgid={orgid} params={{}} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-accounts/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid, ...rest } = match.params;
            return <Layout content={<ManageTeachersView orgid={orgid} params={rest} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-accounts/:orgid/site/:siteId/add-teacher"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid, siteId } = match.params;
            return <Layout content={<AddTeacherToClass orgid={orgid} siteId={siteId} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/dashboard/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return (
              <SchoolYearContext.Consumer>
                {({ schoolYear }) => <Layout content={<DataAdminDashboard orgid={orgid} schoolYear={schoolYear} />} />}
              </SchoolYearContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/data-admin/rostering/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<Rostering orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/support-account-setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<SupportAccountSetup />} />;
          }}
        />
        <CustomRoute
          path="/coach-account-setup/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<CoachAccountSetup orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/coach-account-setup/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid, siteId } = match.params;
            return <Layout content={<CoachAccountSetup siteId={siteId} orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-group/:manageView/:orgid/site/:siteId/:studentGroupId?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          routeName="data-admin-manage-group"
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid, siteId, studentGroupId, manageView } = match.params;
            return (
              <DataAdminSideNavLayout
                content={layoutProps => (
                  <ManageGroup
                    manageView={manageView}
                    orgid={orgid}
                    siteId={siteId}
                    studentGroupId={studentGroupId || layoutProps?.studentGroupId}
                  />
                )}
                orgid={orgid}
                siteId={siteId}
                studentGroupId={studentGroupId}
              />
            );
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/unarchive/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          routeName="data-admin-unarchive"
          render={({ match }) => {
            const { orgid, siteId } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear }) => (
                  <DataAdminSideNavLayout
                    content={() => <UnarchiveStudents orgid={orgid} siteId={siteId} schoolYear={schoolYear} />}
                    orgid={orgid}
                    siteId={siteId}
                    schoolYear={schoolYear}
                  />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/add-class-and-teacher/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          routeName="data-admin-manage-school"
          render={({ match }) => {
            const { orgid, siteId } = match.params;
            return (
              <DataAdminSideNavLayout
                content={() => <ManageSchool orgid={orgid} siteId={siteId} />}
                orgid={orgid}
                siteId={siteId}
              />
            );
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/search-students/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<SearchStudents orgid={orgid} shouldSearchInOrg={true} />} orgid={orgid} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/search-students/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid, siteId } = match.params;
            return (
              <DataAdminSideNavLayout
                content={() => <SearchStudents orgid={orgid} shouldSearchInOrg={false} siteId={siteId} />}
                orgid={orgid}
                siteId={siteId}
              />
            );
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/search-teachers/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<SearchTeachers orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-school/search-coaches/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<SearchCoaches orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-scripts"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={() => {
            return <Layout content={<DataScripts />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/roster-import-history/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.tasksForOrgDataAdmins}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<RosterImportHistory orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/audit-log/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<AuditLog orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/data-admin/manage-users/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<ManageOrgUsers orgid={orgid} params={{}} />} />;
          }}
        />
      </Switch>
    );
  }
}

DataAdminRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsDataAdminOrUniversalDataAdminOrSuperAdmin]
};

DataAdminRoutes.propTypes = {
  tasks: PropTypes.array
};
