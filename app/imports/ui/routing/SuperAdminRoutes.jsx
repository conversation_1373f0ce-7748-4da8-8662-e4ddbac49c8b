import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import ClientSetup from "../pages/account-setup/client-setup";
import CustomRoute from "./CustomRoute";
import {
  checkIsDownloaderOrSuperAdmin,
  checkIsSuperAdmin,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import ManageRules from "../pages/manage-rules/manage-rules";
import ClassRules from "../pages/class-rules/class-rules";
import ManageScreening from "../pages/manage-screening/manage-screening";
import ClientManagement from "../pages/account-setup/client-management";
import UniversalCoachSetup from "../pages/account-setup/universal-coach-setup";
import UniversalDataAdminSetup from "../pages/account-setup/universal-data-admin-setup";
import DownloaderSetup from "../pages/account-setup/downloader-setup";
import ManageUsers from "../pages/manage-users/manage-users";
import ManageMessage from "../pages/manage-message/manage-message";
import AssessmentsDashboard from "../pages/assessments-dashboard/assessments-dashboard";
import { AppDataContext } from "./AppDataContext";
import { DownloadUsers } from "../pages/download-users";
import { navbarItemsByRoleId } from "./navbarItems";
import SuperAdminSetup from "../pages/account-setup/super-admin-setup";

const navbarItems = navbarItemsByRoleId.arbitraryIdsuperAdmin;

const tasks = [checkLoggedIn, setApplicationVersion, checkIsDownloaderOrSuperAdmin];

export default class SuperAdminRoutes extends React.Component {
  routerGroupName = "superAdmins";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/class-rules"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<ClassRules />} />;
          }}
        />
        <CustomRoute
          path="/manage-screening"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<ManageScreening />} />;
          }}
        />
        <CustomRoute
          path="/clients"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => <Layout content={<ClientManagement userRole={"superadmin"} />} />}
        />
        <CustomRoute
          path="/clients/setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => <Layout content={<ClientSetup />} />}
        />
        <CustomRoute
          path="/universal-coach-setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<UniversalCoachSetup />} />;
          }}
        />
        <CustomRoute
          path="/universal-data-admin-setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<UniversalDataAdminSetup />} />;
          }}
        />
        <CustomRoute
          path="/super-admin-account-setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<SuperAdminSetup />} />;
          }}
        />
        <CustomRoute
          path="/downloader-setup"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<DownloaderSetup />} />;
          }}
        />
        <CustomRoute
          path="/clients-sml"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => (
            <AppDataContext.Consumer>
              {({ schoolYear }) => (
                <Layout content={<ClientManagement userRole={"superadmin"} schoolYear={schoolYear} sml />} />
              )}
            </AppDataContext.Consumer>
          )}
        />
        <CustomRoute
          path="/progress-monitoring"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <Layout content={<ManageRules {...match.params} />} />;
          }}
        />
        <CustomRoute
          path="/manage-users/:role?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <Layout content={<ManageUsers {...match.params} />} />;
          }}
        />
        <CustomRoute
          path="/manage-message"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <Layout content={<ManageMessage {...match.params} />} />;
          }}
        />
        <CustomRoute
          path="/assessments-dashboard/:pageName?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { pageName } = match.params;
            return <Layout content={<AssessmentsDashboard pageName={pageName} />} />;
          }}
        />
        <CustomRoute
          path="/download-users"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={tasks}
          render={() => {
            return <Layout content={<DownloadUsers />} />;
          }}
        />
      </Switch>
    );
  }
}

SuperAdminRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsSuperAdmin]
};

SuperAdminRoutes.propTypes = {
  tasks: PropTypes.array
};
