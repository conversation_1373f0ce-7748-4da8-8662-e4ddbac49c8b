import React, { useEffect, useContext, useState } from "react";
import { useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import SideNavLayout from "./side-nav-layout";
import Navigation from "./navigation/navigation";
import layoutMethods from "./methods";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { SiteContext } from "../../contexts/SiteContext";
import { UserContext } from "../../contexts/UserContext";

const SideNavLayoutWrapper = props => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { orgId: orgid } = useContext(OrganizationContext);
  const { siteId } = useContext(SiteContext);
  const { userId, userSiteAccess, user } = useContext(UserContext);
  const history = useHistory();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userId && loading) {
      // let userRole;
      // const roles = [ROLE_IDS.admin, ROLE_IDS.support, ROLE_IDS.universalCoach];
      // userSiteAccess?.forEach(sa => {
      //   if (sa.schoolYear === schoolYear || (roles.includes(sa.role) && sa.isActive)) {
      //     userRole = sa.role;
      //   }
      // });

      // TODO(fmazur) - handle elsewhere?
      // TODO(fmazur) - do we still need this?
      // if (roles.includes(userRole)) {
      //   const targetRoute = orgid && siteId ? `/school-overview/${orgid}/all/${siteId}` : "/";
      //   history.push(targetRoute);
      // } else {
      //   history.push(`/${userId ? "unauthorized" : "login"}`);
      // }

      setLoading(false);
    }
  }, [userId, schoolYear]);

  if (loading) {
    return (
      <div className="wrapper">
        <Navigation navName={props.navName} orgid={orgid} />
        <footer>{layoutMethods.version()}</footer>
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
  return <SideNavLayout {...props} navName={props.navName} orgid={orgid} />;
};

export default SideNavLayoutWrapper;

SideNavLayoutWrapper.propTypes = {
  loading: PropTypes.bool,
  navName: PropTypes.string
};
