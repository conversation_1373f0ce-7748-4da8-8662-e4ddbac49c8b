import React, { useContext, useState } from "react";
import PropTypes from "prop-types";

import { useTracker } from "meteor/react-meteor-data";
import <PERSON><PERSON> from "react-s-alert";
import { MessageNotices } from "/imports/api/messageNotices/messageNotices";

import sortBy from "lodash/sortBy";
import { cloneDeep } from "lodash";
import Navigation from "./navigation/navigation";
import SideNav from "../components/side-nav";
import DetailLayout from "./detail-layout";
import StudentGroupLayout from "./student-group-layout";
import DefaultLayout from "./default-layout";
import { isHighSchoolGrade, renderFooter } from "../utilities";
import { sortByGradeAndName } from "/imports/api/utilities/utilities";
import { sortStudentsByName } from "../components/student-groups/helperFunction";
import { StudentGroupContext } from "../../contexts/StudentGroupContext";
import { SiteContext } from "../../contexts/SiteContext";
import { OrganizationContext } from "../../contexts/OrganizationContext";
import { UserContext } from "../../contexts/UserContext";
import { SchoolYearContext } from "../../contexts/SchoolYearContext";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";
import { StudentContext } from "../../contexts/StudentContext";

function SideNavLayout(props) {
  const { studentGroup, studentGroupId, studentsInStudentGroup } = useContext(StudentGroupContext);
  const { studentId: contextStudentId } = useContext(StudentContext);
  const { studentGroupsInSite, siteId } = useContext(SiteContext);
  const { orgId: orgid } = useContext(OrganizationContext);
  const { user, userSiteAccess } = useContext(UserContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const [expandedNoticeState, setExpandedNoticeState] = useState(false);
  const studentId = props.studentId || (props.context === "student-detail" ? contextStudentId : undefined);

  const expandedStateCB = isExpanded => {
    setExpandedNoticeState(isExpanded);
  };

  const messageNotice = useTracker(() => {
    return MessageNotices.findOne({ noticeLocation: "side-nav-layout" });
  }, []);

  const trackerData = useTracker(() => {
    const { context } = props;

    let isAdmin = false;
    let isSupport = false;
    let isUniversalCoach = false;
    let students = [];

    // Admin - only show the student group that they clicked on in navigation
    if (userSiteAccess.length) {
      isAdmin = userSiteAccess.filter(sa => sa.siteId === siteId).some(sa => sa.role === ROLE_IDS.admin);
      isSupport = userSiteAccess.some(sa => sa.role === ROLE_IDS.support);
      isUniversalCoach = userSiteAccess.some(sa => sa.role === ROLE_IDS.universalCoach);

      if (context === "student-detail") {
        const rosterSortingItem = localStorage.getItem("rosterSorting");
        const sorting = rosterSortingItem || "lastFirst";
        if (!rosterSortingItem) {
          localStorage.setItem("rosterSorting", "lastFirst");
        }
        const isIndividualSection = window.location.href.includes("/individual");
        const studentDocs = cloneDeep(studentsInStudentGroup);
        if (isIndividualSection) {
          students = sortStudentsByName(studentDocs.filter(s => s.currentSkill?.assessmentId));
        } else {
          students = sortBy(studentDocs, student => {
            const { firstName, lastName } = student.identity.name;
            return sorting === "lastFirst" ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
          });
        }
      }
    }

    const sortedStudentGroups = cloneDeep(studentGroupsInSite).sort(sortByGradeAndName);

    return {
      studentGroupId,
      isAdmin,
      isSupport,
      isUniversalCoach,
      studentGroups: sortedStudentGroups,
      user,
      studentGroup,
      students,
      orgid
    };
  }, [
    user,
    orgid,
    siteId,
    studentGroup,
    schoolYear,
    props.context,
    studentGroupsInSite,
    studentsInStudentGroup,
    studentGroupId
  ]);

  return (
    <div className="wrapper nav-support">
      <Navigation navName={props.navName} />
      <main>
        <SideNav
          studentGroups={trackerData.studentGroups}
          isAdmin={trackerData.isAdmin}
          isSupport={trackerData.isSupport}
          isUniversalCoach={trackerData.isUniversalCoach}
          orgid={trackerData.orgid}
          siteId={siteId}
        />
        {props.context === "student-detail" && (
          <DetailLayout
            studentId={props.studentId}
            students={trackerData.students}
            studentGroup={studentGroup}
            siteId={siteId}
            studentGroupId={studentGroupId}
            content={props.content}
            context={props.context}
            expandedStateCB={expandedStateCB}
            messageNotice={messageNotice}
            expandedNoticeState={expandedNoticeState}
          />
        )}
        {!studentId && studentGroup && props.context !== "student-detail" && (
          <StudentGroupLayout
            groupName={studentGroup.name}
            siteId={siteId}
            studentGroupId={studentGroupId}
            studentGroup={studentGroup}
            content={props.content}
            expandedStateCB={expandedStateCB}
            expandedNoticeState={expandedNoticeState}
            messageNotice={messageNotice}
            isHighSchoolGroup={isHighSchoolGrade(studentGroup.grade)}
          />
        )}
        {!studentId && !studentGroup && props.context !== "student-detail" && <DefaultLayout content={props.content} />}
      </main>
      {renderFooter({ hasSideNav: true })}
      <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
    </div>
  );
}

SideNavLayout.propTypes = {
  content: PropTypes.object,
  loading: PropTypes.bool,
  messageNotice: PropTypes.object,
  navName: PropTypes.string,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  studentGroups: PropTypes.array,
  students: PropTypes.array,
  studentId: PropTypes.string,
  context: PropTypes.string,
  user: PropTypes.object,
  isAdmin: PropTypes.bool,
  isSupport: PropTypes.bool,
  isUniversalCoach: PropTypes.bool,
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

export default SideNavLayout;
