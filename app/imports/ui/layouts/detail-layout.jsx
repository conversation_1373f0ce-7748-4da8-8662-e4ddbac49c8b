import PropTypes from "prop-types";
import React, { useCallback, useContext, useEffect, useState } from "react";
import { Button, Dropdown, DropdownButton } from "react-bootstrap";
import { Link, useHistory } from "react-router-dom";

import MessageNotice from "../components/message-notices/message-notice.jsx";
import { isHighSchoolGrade, openPrintWindow } from "../utilities";
import DetailContentLayout from "./detail-content-layout";
import DetailHeaderText from "./detail-header-text";
import { PRINT_OPTIONS } from "/imports/api/constants";
import { StudentContext } from "../../contexts/StudentContext";
import { SchoolYearContext } from "../../contexts/SchoolYearContext";
import { OrganizationContext } from "../../contexts/OrganizationContext";

const DetailLayout = ({
  studentId: propsStudentId,
  studentGroup: propsStudentGroup,
  siteId,
  studentGroupId,
  content,
  context,
  expandedStateCB,
  expandedNoticeState,
  students
}) => {
  const [isPrinting, setIsPrinting] = useState(false);
  const [selectedPrintOption, setSelectedPrintOption] = useState(PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION);
  const { student, studentId: contextStudentId } = useContext(StudentContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const { orgId } = useContext(OrganizationContext);
  const history = useHistory();

  const studentId = propsStudentId || contextStudentId;
  const studentGroup = propsStudentGroup || {};

  useEffect(() => {
    const handlePrintMessage = event => {
      if (event.data === "printScheduled") {
        setIsPrinting(true);
      } else if (event.data === "printWindowClosed") {
        setIsPrinting(false);
        window.document.getElementById("print-iframe")?.remove();
        window.document.title = "SpringMath";
      }
    };

    window.onmessage = handlePrintMessage;

    return () => {
      window.onmessage = null;
    };
  }, []);

  const isPrintDataAvailable = useCallback(() => {
    const effectiveOrgId = studentGroup?.orgid || orgId;
    const effectiveSchoolYear = studentGroup?.schoolYear || schoolYear;
    return !!(effectiveOrgId && effectiveSchoolYear);
  }, [studentGroup?.orgid, orgId, studentGroup?.schoolYear, schoolYear]);

  const printPage = useCallback(() => {
    const effectiveOrgId = studentGroup?.orgid || orgId;
    const effectiveSchoolYear = studentGroup?.schoolYear || schoolYear;

    const printURL = `/${effectiveOrgId}/print/StudentDetail?siteId=${siteId}&studentGroupId=${studentGroupId}&studentId=${studentId}&schoolYear=${effectiveSchoolYear}&orgid=${effectiveOrgId}&printOption=${selectedPrintOption}`;
    openPrintWindow(printURL);
  }, [
    studentGroup?.orgid,
    orgId,
    studentGroup?.schoolYear,
    schoolYear,
    siteId,
    studentGroupId,
    studentId,
    selectedPrintOption
  ]);

  const shouldRenderIndividualProgress = () => {
    return student?.history?.length > 0;
  };

  const changePrintOption = printOptionName => {
    setSelectedPrintOption(printOptionName);
  };

  const getButtonClassname = shouldBeDisabled => {
    return `btn btn-success${shouldBeDisabled ? " cursor-not-allowed" : ""}`;
  };

  const redirectToStudent = direction => () => {
    const targetStudentId =
      students[students.findIndex(s => s._id === studentId) + (direction === "next" ? 1 : -1)]?._id;
    const individualSectionSuffix = window.location.href.includes("/individual") ? "/individual" : "";
    const effectiveOrgId = studentGroup?.orgid || orgId;
    history.push(
      `/${effectiveOrgId}/site/${siteId}/student-groups/${studentGroupId}/students/${targetStudentId}${individualSectionSuffix}`
    );
  };

  const renderPrintButtonLabel = () => {
    if (isPrinting) {
      return "Preparing printout...";
    }
    if (isPrintDataAvailable()) {
      return "Print";
    }
    return "Loading...";
  };

  const isHighSchoolGroup = isHighSchoolGrade(studentGroup?.grade);
  const isFirstStudentInQueue = students.findIndex(s => s._id === studentId) === 0;
  const isLastStudentInQueue = students.findIndex(s => s._id === studentId) === students.length - 1;

  return (
    <div className="workspace-container profile-view">
      <div className="d-flex justify-content-between mt-1">
        <Link
          className="btn btn-success"
          to={`/${studentGroup?.orgid || orgId}/site/${siteId}/student-groups/${studentGroupId}/students`}
        >
          <i className="fa fa-arrow-left" /> Back to All Students
        </Link>
        {students.length <= 1 ? null : (
          <div className="d-flex gap-1">
            <Button
              className={getButtonClassname(isFirstStudentInQueue)}
              disabled={isFirstStudentInQueue}
              onClick={redirectToStudent("previous")}
            >
              <i className="fa fa-arrow-left" /> Previous Student
            </Button>
            <Button
              className={getButtonClassname(isLastStudentInQueue)}
              disabled={isLastStudentInQueue}
              onClick={redirectToStudent("next")}
            >
              Next Student <i className="fa fa-arrow-right" />
            </Button>
          </div>
        )}
        {context === "student-detail" && (
          <div>
            <button
              className="btn btn-success pull-right print-page"
              disabled={isPrinting || !isPrintDataAvailable()}
              onClick={printPage}
            >
              <i className="fa fa-print" />
              &nbsp;
              {renderPrintButtonLabel()}
            </button>
            <div className="pull-right btn-group m-r-5">
              <DropdownButton variant="default" title="Print Options">
                {Object.entries(PRINT_OPTIONS).map(([printOptionId, printOptionName]) => (
                  <Dropdown.Header key={printOptionId}>
                    <label>
                      <input
                        type="radio"
                        checked={selectedPrintOption === printOptionName}
                        onChange={() => changePrintOption(printOptionName)}
                        className="me-2"
                      />
                      <big>{printOptionName}</big>
                    </label>
                  </Dropdown.Header>
                ))}
              </DropdownButton>
            </div>
          </div>
        )}
      </div>

      <DetailHeaderText context={context} displayStats={shouldRenderIndividualProgress()} />

      {isHighSchoolGroup ? null : (
        <MessageNotice
          noticeLocation="side-nav-layout"
          expandedStateCB={expandedStateCB}
          expandedNoticeState={expandedNoticeState}
        />
      )}
      <DetailContentLayout content={content} />
    </div>
  );
};

DetailLayout.propTypes = {
  studentId: PropTypes.string,
  studentGroup: PropTypes.object,
  siteId: PropTypes.string,
  studentGroupId: PropTypes.string,
  content: PropTypes.object,
  context: PropTypes.string,
  expandedStateCB: PropTypes.func,
  messageNotice: PropTypes.object,
  expandedNoticeState: PropTypes.bool,
  students: PropTypes.array
};

export default DetailLayout;
