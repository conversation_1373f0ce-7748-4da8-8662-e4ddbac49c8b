import React, { Component } from "react";
import PropTypes from "prop-types";

import Alert from "react-s-alert";
import Navigation from "./navigation/navigation.jsx";
import AdminSideNav from "../components/side-nav-admin.jsx";
import LoadingScreen from "../components/loading-screen/loading-screen.jsx";
import { renderFooter } from "../utilities";

export default class AdminSideNavLayout extends Component {
  render() {
    return (
      <div className="wrapper">
        <Navigation navName={this.props.navName} />
        <main>
          <AdminSideNav
            gradeId={this.props.gradeId}
            orgid={this.props.orgid}
            siteId={this.props.siteId}
            schoolYear={this.props.schoolYear}
          />
          {this.props.content}
        </main>
        {renderFooter({ hasSideNav: true })}
        <LoadingScreen />
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
}

AdminSideNavLayout.propTypes = {
  user: PropTypes.object,
  gradeId: PropTypes.string,
  navName: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  schoolYear: PropTypes.string,
  content: PropTypes.object
};
