import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";

import Navigation from "./navigation/navigation";
import GradesSideNav from "../components/grades-side-nav";
import { renderFooter } from "../utilities";

class GradesSideNavLayout extends Component {
  render() {
    const guideTitle = "Generate Screening Assessments";

    return (
      <div className="wrapper nav-support">
        <Navigation navName={this.props.navName} showReturnToSM={true} />
        <main>
          <GradesSideNav currentPath={this.props.location.pathname} />
          <div className="workspace-container">
            <h2 className="w9">{guideTitle}</h2>
            <div className="card-box-wrapper animated fadeIn">{this.props.content}</div>
          </div>
        </main>
        {renderFooter({ hasSideNav: true })}
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
}

GradesSideNavLayout.propTypes = {
  content: PropTypes.object,
  location: PropTypes.object,
  navName: PropTypes.string,
  user: PropTypes.object
};

export default withRouter(GradesSideNavLayout);
