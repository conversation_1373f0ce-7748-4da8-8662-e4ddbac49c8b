import React, { use<PERSON>allback, useContext, useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { Container, Dropdown, Nav, Navbar, NavDropdown } from "react-bootstrap";
import { LinkContainer } from "react-router-bootstrap";
import { debounce, each, get, isEqual, range, uniq, uniqBy } from "lodash";
import Alert from "react-s-alert";
import * as utils from "/imports/api/utilities/utilities";
import { getZendeskUrl, isExternalRostering, isSuperAdminOrUniversalDataAdminOrDataAdmin } from "/imports/ui/utilities";
import { useHistory, useLocation, useRouteMatch } from "react-router-dom";
import { navbarItemsByRoleId } from "../../routing/navbarItems";
import layoutMethods from "../methods.js";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { OrganizationContext } from "../../../contexts/OrganizationContext";
import { UserContext } from "../../../contexts/UserContext";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";
import { SiteContext } from "../../../contexts/SiteContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { AppDataContext } from "../../routing/AppDataContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

function Navigation(props) {
  const { schoolYear, setSchoolYear, latestAvailableSchoolYear } = useContext(SchoolYearContext);
  const { orgId, org, sitesInOrg } = useContext(OrganizationContext);
  const { rostering, isTestOrg, isMFARequired = false } = org || {};
  const { userRoles, userSiteAccess, user, currentSiteAccess: contextSiteAccess, userId } = useContext(UserContext);
  const { roleDefinitions } = useContext(StaticDataContext);
  const { siteId } = useContext(SiteContext);
  const { studentGroup } = useContext(StudentGroupContext);
  const { navbarItems: contextNavbarItems } = useContext(AppDataContext);
  const navbarItems = props.navbarItems || contextNavbarItems;
  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();

  const [state, setState] = useState({
    displayDropdown: null,
    dashboardButtonsWidth: 0,
    isDataFetching: false,
    hasGradesInSelectedYear: false,
    shouldShowProgramEvaluation: false,
    shouldDisplayProgramEvaluationButton: false,
    shouldDisplayDistrictReportingButton: false,
    schoolYearsWithProgramEvaluationData: [],
    schoolYearsWithDistrictReportingData: [],
    customDate: ""
  });

  const [allSchoolYears, setAllSchoolYears] = useState([]);

  const getUserCapabilities = params => {
    let canSwitchSchoolYear = false;
    // let canUseCustomDate = false;
    // let canSwitchSites = false;
    // let canSwitchRoles = false;
    let currentSchoolYearSiteAccess;

    const allowedRoleIds = params.roleDefinitions
      .filter(r => ["admin", "support", "universalCoach"].includes(r.name))
      .map(role => role._id);

    if (
      allowedRoleIds.length &&
      params.userSiteAccess?.some(
        siteAccess =>
          allowedRoleIds.includes(siteAccess.role) &&
          ["allSites", "none", "", params.siteId].includes(siteAccess.siteId)
      )
    ) {
      canSwitchSchoolYear = true;
    }

    currentSchoolYearSiteAccess = params.userSiteAccess?.find(siteAccess =>
      ["allSites", "none", "", params.siteId].includes(siteAccess.siteId)
    );

    if (!currentSchoolYearSiteAccess) {
      currentSchoolYearSiteAccess = params.userSiteAccess?.find(s => s.schoolYear === params.schoolYear && s.isActive);
    }

    if (!currentSchoolYearSiteAccess) {
      currentSchoolYearSiteAccess = params.userSiteAccess?.[0] || null;
    }

    return {
      canSwitchSchoolYear,
      // canUseCustomDate, canSwitchSites, canSwitchRoles,
      currentSchoolYearSiteAccess
    };
  };

  const [userCapabilities, setUserCapabilities] = useState(
    getUserCapabilities({
      userSiteAccess,
      roleDefinitions,
      schoolYear,
      siteId
    })
  );

  const useComponentData = () => {
    const grade = studentGroup ? studentGroup.grade : "01";

    if (orgId) {
      // TODO(fmazur) - do we still need it?
      // TODO(fmazur) - probably used by routes without orgid
      localStorage.setItem("orgid", orgId);
    }

    let filteredSiteElements = [];

    const { currentSchoolYearSiteAccess } = userCapabilities;

    const siteAccessToUse = currentSchoolYearSiteAccess || userSiteAccess?.[0] || null;

    if (user) {
      let filteredSiteAccessArray = userSiteAccess;
      if (contextSiteAccess && ![ROLE_IDS.teacher, ROLE_IDS.admin].some(e => contextSiteAccess.role === e)) {
        filteredSiteAccessArray = userSiteAccess.filter(siteAccess => siteAccess.role === siteAccessToUse.role);
      }

      const allSitesIds = uniq(filteredSiteAccessArray.map(siteAccess => siteAccess.siteId) || []);
      const filteredSites =
        siteAccessToUse.siteId === "allSites" ? sitesInOrg : sitesInOrg.filter(s => allSitesIds.includes(s._id));

      if (allSitesIds.length) {
        filteredSiteElements = filteredSites.map(site => ({
          _id: site._id,
          name: site.name,
          orgid: site.orgid,
          grades: site.grades
        }));
      }

      const userSiteIds = uniq(
        userSiteAccess
          ?.filter(sa => (sa.role === ROLE_IDS.teacher && sa.schoolYear === schoolYear) || sa.role !== ROLE_IDS.teacher)
          ?.map(sa => sa.siteId) || []
      );

      if (userSiteIds.length && siteAccessToUse.siteId !== "allSites") {
        filteredSiteElements = filteredSiteElements.filter(s => userSiteIds.includes(s._id));
      }
    }
    return {
      currentUserRole: siteAccessToUse?.role,
      isLoggedIn: userId,
      filteredSiteElements,
      zendeskSupportEnabled: Meteor.settings.public.ZENDESK_SUPPORT_PORTAL_LINK_ENABLED ?? true,
      grade,
      currentSchoolYearSiteAccess: siteAccessToUse
    };
  };

  const [componentData, setComponentData] = useState(useComponentData());
  const formRef = useRef();
  const inputRef = useRef();

  const {
    currentUserRole,
    isLoggedIn,
    filteredSiteElements,
    zendeskSupportEnabled,
    grade,
    currentSchoolYearSiteAccess: currentSiteAccess
  } = componentData;

  const isSuperAdminOrDataAdminOrUniversalDataAdmin = isSuperAdminOrUniversalDataAdminOrDataAdmin(
    currentSiteAccess?.role
  );

  useEffect(() => {
    const localComponentData = useComponentData();
    if (!isEqual(componentData, localComponentData)) {
      setComponentData(localComponentData);
    }
  }, [
    user,
    userId,
    userSiteAccess,
    contextSiteAccess,
    roleDefinitions,
    orgId,
    sitesInOrg,
    siteId,
    schoolYear,
    studentGroup,
    userCapabilities.currentSchoolYearSiteAccess
  ]);

  const getAllSchoolYears = useCallback(() => {
    const localSchoolYears = (userCapabilities.canSwitchSchoolYear &&
      latestAvailableSchoolYear &&
      range(2017, latestAvailableSchoolYear + 1)) || [schoolYear];
    if (!localSchoolYears.includes(schoolYear)) {
      localSchoolYears.push(schoolYear);
    }
    setAllSchoolYears(localSchoolYears.filter(year => year && year > 0));
  }, [userCapabilities.canSwitchSchoolYear, latestAvailableSchoolYear, schoolYear]);

  useEffect(() => {
    const localUserCapabilities = getUserCapabilities({ userSiteAccess, siteId, schoolYear, roleDefinitions });
    setUserCapabilities(localUserCapabilities);
  }, [roleDefinitions?.length, userId, siteId, schoolYear]);

  const getFilteredNavbarItems = ({ siteId: userSiteId, roleId, navbarItemsToFilter }) => {
    const filteredNavbarItems = [];
    navbarItemsToFilter?.forEach(val => {
      if (typeof val.shouldDisplay === "function") {
        if (val.shouldDisplay({ rosteringType: rostering, siteId: userSiteId, roleId })) {
          filteredNavbarItems.push(val);
        }
      } else {
        filteredNavbarItems.push(val);
      }
    });
    return filteredNavbarItems;
  };

  const [navItems, setNavItems] = useState([]);

  const getNavItems = useCallback(() => {
    const userActiveRoleId = currentSiteAccess?.role || null;
    const userSiteId = currentSiteAccess?.siteId || null;

    const navItemsForUserRole = userActiveRoleId ? navbarItemsByRoleId[userActiveRoleId] || [] : [];

    const filteredRoleItems = getFilteredNavbarItems({
      siteId: userSiteId,
      roleId: userActiveRoleId,
      navbarItemsToFilter: navItemsForUserRole
    });

    const filteredRouteItems = getFilteredNavbarItems({
      siteId: userSiteId,
      roleId: userActiveRoleId,
      navbarItemsToFilter: navbarItems || []
    });

    const mainNavbarItems = {};
    filteredRoleItems.forEach(navItem => {
      mainNavbarItems[navItem.display] = navItem;
    });

    if (!filteredRouteItems?.length) {
      setNavItems(Object.values(mainNavbarItems));
      return;
    }

    const displayByType = {
      rosterEdFi: "Manage Ed-Fi",
      rosterOR: "Manage OneRoster",
      rosterImport: "Import Records",
      rosterUpload: "Import Records"
    };
    const routeNameByDisplay = {
      "Manage OneRoster": "/data-admin/rostering/{orgid}",
      "Manage Ed-Fi": "/data-admin/rostering/{orgid}",
      "Import Records": "/data-admin/upload/{orgid}"
    };

    const newItems = {};
    filteredRouteItems.forEach(navItem => {
      if (navItem.navName === "data-admin-import-records" && rostering) {
        navItem.display = displayByType[rostering];
        navItem.routeName = routeNameByDisplay[navItem.display];
      }

      if (mainNavbarItems[navItem.display]) {
        return;
      }
      newItems[navItem.display] = navItem;
    });

    if (!Object.values(newItems).length) {
      setNavItems(Object.values(mainNavbarItems));
      return;
    }

    setNavItems([
      ...Object.values(mainNavbarItems),
      {
        display: "Divider"
      },
      ...Object.values(newItems)
    ]);
  }, [currentSiteAccess]);

  useEffect(() => {
    getNavItems();
  }, [getNavItems]);

  const updateNavigationLayout = useCallback(() => {
    const offset = 125;
    const logoElement = document.querySelector(".navbar-brand.logo");
    const logoWidth = logoElement ? logoElement.getBoundingClientRect().width : 150;
    const navBarButtonsElement = document.getElementsByClassName("navbar-nav");
    const menuElement = document.getElementById("menu-nav-dropdown");
    const userElement = document.getElementById("basic-nav-dropdown");
    const userWidth = userElement ? userElement.offsetWidth : 100;
    const additionalWidth = offset + logoWidth + userWidth;

    const hasAnyNavbarElement = navBarButtonsElement[0] !== undefined;

    if (hasAnyNavbarElement && !menuElement) {
      const newWidth = navBarButtonsElement[0].offsetWidth;
      if (newWidth !== state.dashboardButtonsWidth || !state.dashboardButtonsWidth) {
        setState(prevState => ({ ...prevState, dashboardButtonsWidth: newWidth }));
        return;
      }
    }

    const navbarElement = document.querySelector(".navbar-nav");
    const maxOneLineNavHeight = 51;
    const isMultiLine = navbarElement && Math.ceil(navbarElement.getBoundingClientRect().height) > maxOneLineNavHeight;

    const shouldDisplay = isMultiLine || state.dashboardButtonsWidth + additionalWidth > window.innerWidth;

    if (shouldDisplay !== state.displayDropdown || state.displayDropdown === null) {
      setState(prevState => ({ ...prevState, displayDropdown: shouldDisplay }));
    }
  }, [state.dashboardButtonsWidth, state.displayDropdown, getNavItems]);

  useEffect(() => {
    updateNavigationLayout();
  }, [updateNavigationLayout, window.innerWidth]);

  useEffect(() => {
    getAllSchoolYears();
  }, [latestAvailableSchoolYear, userCapabilities.canSwitchSchoolYear]);

  // TODO(fmazur) - maybe block other exposed routes as well?
  // NOTE(fmazur) - maybe should update in custom route instead of some component?
  const updateLocalStorage = (key, value) => {
    if (key === "lastRouteWithGrades" && !studentGroup?.grade) {
      return;
    }
    if (!window.location.pathname.includes("no-enrollments") && value) {
      localStorage.setItem(key, value);
    }
  };

  const shouldDisplayStatistics = params => {
    if ((params.siteId || params.orgid) && schoolYear) {
      Meteor.call("AssessmentResults:shouldDisplayStatistics", params.siteId, params.orgid, schoolYear, (err, resp) => {
        if (!err) {
          if (params.siteId) {
            setState(prevState => ({
              ...prevState,
              shouldDisplayProgramEvaluationButton: resp?.hasDataForSelectedSchoolYear,
              schoolYearsWithProgramEvaluationData: resp?.schoolYearsWithData || []
            }));
          } else if (params.orgid) {
            setState(prevState => ({
              ...prevState,
              shouldDisplayDistrictReportingButton: resp?.hasDataForSelectedSchoolYear,
              schoolYearsWithDistrictReportingData: resp?.schoolYearsWithData || []
            }));
          }
        }
      });
    }
  };

  const getYearsWithGrades = () => {
    const orgid = localStorage.getItem("orgid") || orgId;
    const url = localStorage.getItem("lastRouteWithGrades") || "/";
    const siteIdParam = match?.params?.siteId;
    if (!allSchoolYears.length || !siteIdParam) {
      return;
    }
    // TODO(fmazur) - fix when using browser go back from student group to data admin dashboard
    Meteor.call("Grades:getGradesWithGroupsForYears", siteIdParam, allSchoolYears, (err, yearsWithGrades) => {
      if (err) {
        Alert.error("Error getting grades");
        return;
      }
      const localAllSchoolYears = allSchoolYears.filter(sy => yearsWithGrades[sy] && yearsWithGrades[sy].length);
      const hasAllowedRole = [ROLE_IDS.admin, ROLE_IDS.support, ROLE_IDS.universalCoach].includes(currentUserRole);
      if (
        schoolYear &&
        !localAllSchoolYears.includes(schoolYear) &&
        !window.location.pathname.includes("no-enrollments") &&
        hasAllowedRole
      ) {
        history.replace(`/${orgid}/site/${siteId}/no-enrollments`);
      } else if (
        schoolYear &&
        localAllSchoolYears.includes(schoolYear) &&
        window.location.pathname.includes("no-enrollments") &&
        hasAllowedRole
      ) {
        const lastSiteIdWithGrades = localStorage.getItem("lastSiteIdWithGrades");
        // eslint-disable-next-line no-unused-expressions
        lastSiteIdWithGrades ? history.push(url.replace(siteId, lastSiteIdWithGrades)) : history.push("/");
      } else if (
        localAllSchoolYears.length &&
        !localAllSchoolYears.includes(schoolYear) &&
        !window.location.pathname.includes("no-enrollments")
      ) {
        // TODO(fmazur) - rewrite
        // this.props.updateAppDataContext({
        //   schoolYear:
        //     allSchoolYears[allSchoolYears.length - 1] || utils.getCurrentSchoolYear(this.props.user, this.props.orgid)
        // });
        // TODO(fmazur) - reset context schoolYear maybe?
      }
    });
  };

  const isTeacherOrAdminOrDownloader = actingRole =>
    actingRole && ["teacher", "admin", "downloader"].includes(actingRole.name);

  const renderTopNav = () => {
    if (!navItems || navItems.length === 0) {
      return null;
    }

    const NavItemElement = state.displayDropdown ? NavDropdown.Item : Nav.Item;

    return navItems.map(n => {
      if (n.display === "Divider") {
        return (
          <NavItemElement as="li" key={n.display} bsPrefix=" ">
            <Dropdown.Divider />
          </NavItemElement>
        );
      }
      const map = {};
      const routeParams = {
        orgid: orgId,
        siteId,
        schoolYear,
        grade,
        ...match.params
      };

      each(Object.keys(routeParams), k => {
        const keyName = `{${k}}`;
        map[keyName] = routeParams[k];
      });
      const routeUsed = utils.mapReplaceAll(n.routeName, map);

      return (
        <NavItemElement as="li" key={routeUsed} bsPrefix=" ">
          <LinkContainer to={routeUsed}>
            <Nav.Link bsPrefix="dropdown-item" data-testid={`topNav_${n.navName}`}>
              {n.display}
            </Nav.Link>
          </LinkContainer>
        </NavItemElement>
      );
    });
  };

  const getNavDropdown = () => {
    if (!navItems?.length) {
      return null;
    }
    return (
      <Nav as="ul" navbar className="me-auto">
        {state.displayDropdown ? (
          <NavDropdown as="li" title="Menu" id="menu-nav-dropdown">
            <ul className="list-unstyled">{renderTopNav()}</ul>
          </NavDropdown>
        ) : (
          renderTopNav()
        )}
      </Nav>
    );
  };

  const hasRelevantRolesForAnySite = () => {
    const relevantRoles = userRoles.filter(role => role === ROLE_IDS.dataAdmin || role === ROLE_IDS.admin);

    return relevantRoles.length > 1;
  };

  const changePrimaryRoleForUser = roleObject => {
    const primaryRole = currentSiteAccess;
    if (primaryRole.role !== roleObject.role || primaryRole.siteId !== roleObject.siteId) {
      Meteor.call("users:changePrimaryRole", { roleObject, shouldClearSchoolYear: true }, err => {
        if (err) {
          Alert.error(err.message || "Error changing primary role for user", {
            timeout: 5000
          });
        } else {
          history.push("/");
        }
      });
    } else {
      Alert.error("Make sure to pick a role other than selected.", {
        timeout: 5000
      });
    }
  };

  const getRoleSwitches = () => {
    const primaryRoleObject = currentSiteAccess;
    const uniqSiteAccess = uniqBy(userSiteAccess, "role");
    return uniqSiteAccess.map(siteAccessObject => {
      let menuItemLabel = "";
      const menuItemValue = siteAccessObject.role;
      switch (siteAccessObject.role) {
        case ROLE_IDS.dataAdmin:
          menuItemLabel = "Data Admin";
          break;
        case ROLE_IDS.admin:
          menuItemLabel = `Coach`;
          break;
        default:
          break;
      }

      const uniqRoleKey = `availableRoles_${menuItemValue}`;
      return (
        <Dropdown.Item
          key={uniqRoleKey}
          onClick={() => changePrimaryRoleForUser(siteAccessObject)}
          data-testid={uniqRoleKey}
        >
          <i className="fa fa-user m-r-5" />
          {menuItemLabel}
          {primaryRoleObject.role === menuItemValue && primaryRoleObject.siteId === siteAccessObject.siteId ? (
            <i className="fa fa-check m-l-15" />
          ) : null}
        </Dropdown.Item>
      );
    });
  };

  const changeActiveSite = ({ actingRole, site: localSite }) => () => {
    let shouldUseFallbackRole = false;
    let finalActingRole = actingRole;
    if (actingRole?.name === "teacher" || actingRole?.name === "admin") {
      const siteAccessesForSite = user.profile.siteAccess.filter(
        siteAccessObject => siteAccessObject.siteId === localSite._id
      );
      shouldUseFallbackRole = !siteAccessesForSite.some(elem => elem.role === actingRole._id);
      if (shouldUseFallbackRole) {
        finalActingRole = roleDefinitions.find(role => role._id === siteAccessesForSite[0].role);
      }
    }
    let route = `/school-overview/${localSite.orgid}/all/${localSite._id}`;
    if (finalActingRole?.name === "teacher") {
      // TODO(fmazur) - maybe no longer needed with functional component
      // NOTE(fmazur) - landing page triggers refetch group associated with user
      route = "/";
    }
    setSchoolYear(schoolYear);
    Meteor.call("users:changeDefaultSite", localSite._id);
    history.push(route);
  };

  const updateCustomDate = e => {
    const customDate = e.target.value;
    setState(prevState => ({ ...prevState, customDate }));
  };

  const setCustomDateForUser = (localCustomDate = "") => {
    Meteor.call("users:setCustomDate", localCustomDate, err => {
      if (err) {
        Alert.error(err.message || "Error while setting custom date");
      } else {
        Alert.success(`Custom date ${localCustomDate ? "set" : "cleared"} successfully`);
        // TODO(fmazur) - remove when components use useContext(UserContext)
        window.location.reload();
      }
    });
  };

  const clearCustomDate = () => {
    setCustomDateForUser("");
    setState(prevState => ({ ...prevState, customDate: "" }));
  };

  const setCustomDate = () => {
    const { customDate } = state;
    setCustomDateForUser(customDate);
  };

  useEffect(() => {
    updateLocalStorage("lastSiteIdWithGrades", siteId);
    // TODO(fmazur) - maybe use useLocation ?
    updateLocalStorage("lastRouteWithGrades", window.location.pathname);
    // TODO(fmazur) - maybe use || userSiteId?
    if (siteId && orgId) {
      getYearsWithGrades();
      shouldDisplayStatistics({ siteId });
    }

    if (
      !isExternalRostering(rostering) &&
      window.location.pathname.includes("data-admin/rostering") &&
      !isSuperAdminOrUniversalDataAdminOrDataAdmin(currentSiteAccess.role)
    ) {
      history.push("/");
    }

    const debouncedUpdateFunction = debounce(updateNavigationLayout, 250);
    window.addEventListener("resize", debouncedUpdateFunction);
    return () => {
      window.removeEventListener("resize", debouncedUpdateFunction);
    };
  }, []);

  useEffect(() => {
    if (isMFARequired && orgId && user?.profile?.orgid !== orgId) {
      const { services: { twoFactorAuthentication } = {} } = user;
      const isMFAEnabled = (twoFactorAuthentication?.secret && twoFactorAuthentication?.type === "otp") || false;
      if (!isMFAEnabled) {
        localStorage.setItem("shouldEnableMFA", "true");
        history.push("/profile");
      }
    }
  }, [orgId, isMFARequired, user]);

  useEffect(() => {
    if (schoolYear) {
      if (siteId) {
        shouldDisplayStatistics({ siteId });
      }
      if (orgId) {
        shouldDisplayStatistics({ orgid: orgId });
      }
    }
  }, [orgId, siteId, schoolYear]);

  useEffect(() => {
    const customDate = get(user, "profile.customDate", "");
    setState(prev => {
      if (prev.customDate === customDate) {
        return prev;
      }
      return { ...prev, customDate };
    });
  }, [user]);

  useEffect(() => {
    const shouldCall = siteId && (schoolYear || allSchoolYears);

    if (shouldCall) {
      getYearsWithGrades();
    }
  }, [schoolYear, allSchoolYears, siteId]);

  useEffect(() => {
    if (
      !isExternalRostering(rostering) &&
      window.location.pathname.includes("data-admin/rostering") &&
      !isSuperAdminOrUniversalDataAdminOrDataAdmin(currentSiteAccess.role)
    ) {
      // TODO(fmazur) - are we sure this should be ran on every role change?
      history.push("/");
    }
  }, [roleDefinitions]);

  useEffect(() => {
    updateLocalStorage("lastSiteIdWithGrades", siteId);
    updateLocalStorage("lastRouteWithGrades", window.location.pathname);

    const customDate = get(user, "profile.customDate", "");
    setState(prev => {
      if (prev.customDate === customDate) {
        return prev;
      }
      return { ...prev, customDate };
    });
  }, [user, match.params.siteId]);

  const renderCustomDateSection = () => {
    return (
      <React.Fragment>
        <Dropdown.Divider />
        <Dropdown.Header>Custom date</Dropdown.Header>
        <Dropdown.Header>
          <input
            className="form-control"
            type="date"
            onChange={updateCustomDate}
            value={state.customDate}
            name="customUserDate"
            data-testid="customUserDate"
          />
          <div className="m-t-5">
            <button className="btn btn-primary btn-xs col-6" onClick={setCustomDate} data-testid="setCustomUserDate">
              Set
            </button>{" "}
            <button
              className="btn btn-default btn-xs col-6"
              onClick={clearCustomDate}
              data-testid="clearCustomUserDate"
            >
              Clear
            </button>
          </div>
        </Dropdown.Header>
      </React.Fragment>
    );
  };

  const openZendesk = () => {
    getZendeskUrl(({ action, jwt, error } = {}) => {
      if (error) {
        Alert.error(error);
      } else {
        inputRef.current.value = jwt;
        formRef.current.action = action;
        formRef.current.submit();
      }
    });
  };

  const renderNavUserDropdown = ({ firstName, lastName, schoolYearContext, actingRole }) => {
    const roleName = actingRole?.name;
    const isUniversalCoach = roleName === "universalCoach";
    const isSupportUser = roleName === "support";
    const isCoachUser = roleName === "admin";
    const url = window.location.pathname;

    const isSuperAdminOrUniversalDataAdmin = ["superAdmin", "universalDataAdmin"].includes(roleName);
    const dataAdminSiteId = currentSiteAccess?.siteId;
    const hasAccessToDataScripts =
      isSuperAdminOrUniversalDataAdmin || (roleName === "dataAdmin" && ["none", "allSites"].includes(dataAdminSiteId));

    const canUserViewReportsPage = isUniversalCoach || isSupportUser || isCoachUser;
    const shouldRenderProgramEvaluationButton =
      !window.location.pathname.includes("/program-evaluation/") &&
      state.shouldDisplayProgramEvaluationButton &&
      canUserViewReportsPage;
    const shouldRenderDistrictReportingButton =
      !window.location.pathname.includes("/district-reporting/") &&
      (isUniversalCoach || isSupportUser || isCoachUser) &&
      state.shouldDisplayDistrictReportingButton;

    return (
      <NavDropdown
        as="li"
        title={`${firstName} ${lastName}`}
        align="end"
        id="basic-nav-dropdown"
        data-testid="userContextMenu"
      >
        <NavDropdown.Item id="logout" href="/logout" data-testid="logout">
          <i className="fa fa-sign-out m-r-5" />
          Logout
        </NavDropdown.Item>
        {user ? (
          <NavDropdown.Item id="profile" href="/profile" data-testid="profile">
            <i className="fa fa-user m-r-5" />
            Profile
          </NavDropdown.Item>
        ) : null}
        {zendeskSupportEnabled ? (
          <NavDropdown.Item id="zendesk" onClick={openZendesk} eventKey={3.4} data-testid="support">
            <i className="fa fa-ambulance m-r-5" />
            Support
          </NavDropdown.Item>
        ) : null}
        <NavDropdown.Item id="zendesk" href={`/sampleAssessments/${grade}`} data-testid="screeningAssessments">
          <i className="fa fa-print m-r-5" />
          Screening Assessments
        </NavDropdown.Item>
        {shouldRenderProgramEvaluationButton ? (
          <NavDropdown.Item
            id="programEvaluation"
            href={`/program-evaluation/${orgId}/site/${siteId}`}
            data-testid="programEvaluation"
          >
            <i className="fa fa-print m-r-5" />
            Program Evaluation
          </NavDropdown.Item>
        ) : null}
        {shouldRenderDistrictReportingButton ? (
          <NavDropdown.Item
            id="districtReporting"
            href={`/district-reporting/${orgId}`}
            data-testid="districtReporting"
          >
            <i className="fa fa-bar-chart m-r-5" />
            District Reporting
          </NavDropdown.Item>
        ) : null}
        {hasAccessToDataScripts ? (
          <NavDropdown.Item id="dataScripts" href="/data-scripts" data-testid="dataScripts">
            <i className="fa fa-file-code-o m-r-5" />
            Data Scripts
          </NavDropdown.Item>
        ) : null}
        {hasRelevantRolesForAnySite()
          ? [
              <Dropdown.Divider key="availableRolesDivider" />,
              <Dropdown.Header key="availableRolesHeader">Available Roles</Dropdown.Header>
            ].concat(getRoleSwitches())
          : null}
        {isTestOrg && renderCustomDateSection()}
        {allSchoolYears?.length > 0 && !isSuperAdminOrDataAdminOrUniversalDataAdmin
          ? [
              <Dropdown.Divider key="schoolYearDivider" />,
              <Dropdown.Header key="schoolYearHeader" data-testid="schoolYearHeader">
                School Year
              </Dropdown.Header>
            ].concat(
              allSchoolYears
                .filter(sy => {
                  const isProgramEvaluation = url.includes("/program-evaluation/");
                  const isDistrictReporting = url.includes("/district-reporting/");
                  if (!isProgramEvaluation || !isDistrictReporting) {
                    return true;
                  }
                  const { schoolYearsWithProgramEvaluationData, schoolYearsWithDistrictReportingData } = state;
                  return (
                    (isProgramEvaluation && schoolYearsWithProgramEvaluationData.includes(sy)) ||
                    (isDistrictReporting && schoolYearsWithDistrictReportingData.includes(sy))
                  );
                })
                .map(sy => (
                  <Dropdown.Item
                    key={`schoolYear${sy}`}
                    data-testid={`schoolYear${sy}`}
                    onClick={() => {
                      Meteor.call("users:setSelectedSchoolYear", sy, err => {
                        if (err) {
                          Alert.error(err.message || "Error while selecting a school year", {
                            timeout: 5000
                          });
                        } else if (location.pathname.includes("no-access")) {
                          history.push(`/`);
                        } else {
                          setSchoolYear(sy);
                        }
                      });
                    }}
                  >
                    {`${sy - 1}-${sy % 100}`}
                    {schoolYearContext === sy ? (
                      <i className="fa fa-check m-l-15" data-testid="currentlySelectedYear" />
                    ) : null}
                  </Dropdown.Item>
                ))
            )
          : null}
        {filteredSiteElements && filteredSiteElements.length > 0 && !isSuperAdminOrDataAdminOrUniversalDataAdmin
          ? [<Dropdown.Divider key="sitesDivider" />, <Dropdown.Header key="sitesHeader">Site</Dropdown.Header>].concat(
              filteredSiteElements.map(siteElem => (
                <Dropdown.Item
                  key={`site${siteElem._id}`}
                  onClick={changeActiveSite({ actingRole, site: siteElem })}
                  data-testid={`site_${siteElem._id}`}
                >
                  {siteElem.name}
                  {siteElem._id === match.params.siteId ? <i className="fa fa-check m-l-15" /> : null}
                </Dropdown.Item>
              ))
            )
          : null}
      </NavDropdown>
    );
  };

  const isUserConfiguringRequiredMFA =
    user?.services?.twoFactorAuthentication?.secret &&
    !user.services.twoFactorAuthentication.type &&
    localStorage.getItem("isMFARequired");

  if (!isLoggedIn || isUserConfiguringRequiredMFA) {
    return (
      <Navbar id="topnav" className="navbar-default" bg="primary" variant="dark" expand fixed="top">
        <Container>
          <Navbar.Brand className="logo" href="/">
            {layoutMethods.environmentType()}
          </Navbar.Brand>
        </Container>
      </Navbar>
    );
  }

  const actingRole = roleDefinitions.find(r => componentData.currentSchoolYearSiteAccess?.role === r._id) || {};
  const firstName = user?.profile?.name.first;
  const lastName = user?.profile?.name.last;
  const customDate = get(user, "profile.customDate", "");

  return (
    <div>
      <Navbar
        id="topnav"
        className={`navbar-default${isTestOrg && customDate?.length ? " bg-success" : ""}`}
        bg="primary"
        variant="dark"
        expand
        fixed="top"
      >
        <Container>
          <Navbar.Brand className="logo" href="/">
            {layoutMethods.environmentType()}
            <span className="navbar-school-year" data-testid="navbar-school-year">{`${schoolYear - 1}-${schoolYear %
              100}`}</span>
          </Navbar.Brand>
          {isTeacherOrAdminOrDownloader(actingRole) ? null : getNavDropdown()}
          <Nav as="ul" navbar>
            {renderNavUserDropdown({
              firstName,
              lastName,
              schoolYearContext: schoolYear,
              actingRole
            })}
            {props.showReturnToSM ? (
              <Nav.Item as="li" id="guide">
                <Nav.Link href="/">
                  <i className="fa fa-arrow-circle-left m-r-5" />
                  Return to SpringMath
                </Nav.Link>
              </Nav.Item>
            ) : null}
          </Nav>
        </Container>
      </Navbar>
      <form ref={formRef} action={""} method="post" target="_blank">
        <input ref={inputRef} type="hidden" name="jwt"></input>
      </form>
    </div>
  );
}

Navigation.propTypes = {
  currentUserRole: PropTypes.string,
  history: PropTypes.object,
  isLoggedIn: PropTypes.bool,
  rosteringType: PropTypes.string,
  location: PropTypes.object,
  match: PropTypes.object,
  navbarItems: PropTypes.array,
  navName: PropTypes.string,
  routerGroupName: PropTypes.string,
  schoolYear: PropTypes.number,
  setSchoolYear: PropTypes.func,
  shouldDisplayProgramEvaluationButton: PropTypes.bool,
  showReturnToSM: PropTypes.bool,
  siteId: PropTypes.string,
  filteredSiteElements: PropTypes.arrayOf(PropTypes.object),
  updateAppDataContext: PropTypes.func,
  zendeskSupportEnabled: PropTypes.bool
};

export default Navigation;
export { Navigation as PureNavigation };
