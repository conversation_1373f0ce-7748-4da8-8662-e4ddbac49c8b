import { assert } from "chai";
import React from "react";
import { Meteor } from "meteor/meteor";
import MockDate from "mockdate";
import td from "testdouble";
import { cleanup, waitFor } from "@testing-library/react"; // eslint-disable-line
import { PureNavigation } from "./navigation.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";
import { AppDataContext } from "../../routing/AppDataContext";
import "@testing-library/jest-dom";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({
    profile: {
      orgid: "test_organization_id",
      siteAccess: [{ role: "teacher", siteId: "test_site_id" }]
    }
  })),
  getMeteorUserSync: jest.fn(() => ({
    profile: {
      orgid: "test_organization_id",
      siteAccess: [{ role: "teacher", siteId: "test_site_id" }]
    }
  })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

const defaultContext = {};

const defaultUserContextValue = {
  user: {
    _id: "test_user_id",
    profile: {
      name: { first: "Test", last: "User" },
      orgid: "test_organization_id",
      siteAccess: [
        {
          role: "arbitraryIdteacher",
          siteId: "test_site_id",
          schoolYear: 2019,
          isActive: true
        }
      ]
    }
  },
  userId: "test_user_id",
  userOrgId: "test_organization_id",
  userRoles: ["arbitraryIdteacher"],
  currentSiteAccess: {
    role: "arbitraryIdteacher",
    siteId: "test_site_id",
    schoolYear: 2019,
    isActive: true
  },
  userSiteAccess: [
    {
      role: "arbitraryIdteacher",
      siteId: "test_site_id",
      schoolYear: 2019,
      isActive: true
    }
  ],
  firstName: "Test",
  lastName: "User",
  warn: false
};

// eslint-disable-next-line react/prop-types
function renderNavigationWithContext({ context = defaultContext, props = {} } = {}) {
  return (
    <AppDataContext.Provider value={{ ...defaultContext, ...context }}>
      <PureNavigation {...props} />
    </AppDataContext.Provider>
  );
}

describe("imports/ui/navigation/navigation.jsx tests", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
    // Mock Meteor methods and subscriptions
    Meteor.user = jest.fn(() => ({
      profile: {
        orgid: "test_organization_id",
        siteAccess: [{ role: "teacher", siteId: "test_site_id" }]
      }
    }));
    Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  afterEach(cleanup);
  describe("when the user is not logged in", () => {
    it("should not render a logout button", () => {
      const navigationComponent = renderWithRouter(renderNavigationWithContext(), {
        userContextValue: {
          ...defaultUserContextValue,
          userId: null,
          user: null
        }
      });
      assert.notMatch(navigationComponent.innerHTML, /logout/i);
    });
    it("should not render a support button", () => {
      const navigationComponent = renderWithRouter(renderNavigationWithContext(), {
        userContextValue: {
          ...defaultUserContextValue,
          userId: null,
          user: null
        }
      });
      assert.notMatch(navigationComponent.innerHTML, /support/i);
    });
  });
  const getLoggedInNavComponent = async ({
    supportEnabled = false,
    schoolYears = [],
    userRole = "arbitraryIdadmin",
    roleName = "admin",
    customProps
  } = {}) => {
    const props = {
      ...customProps,
      isLoggedIn: true,
      isLoading: false,
      user: {
        profile: {
          name: {
            first: "testFirstName",
            last: "testLastName"
          },
          siteAccess: [
            {
              role: userRole
            }
          ]
        }
      },
      roles: [
        {
          _id: userRole,
          name: roleName
        }
      ],
      highSchoolSiteIds: [],
      shouldShowProgramEvaluation: true,
      allSchoolYears: schoolYears
    };

    Meteor.settings.public.ZENDESK_SUPPORT_PORTAL_LINK_ENABLED = supportEnabled;

    const userContextValue = {
      ...defaultUserContextValue,
      userRoles: [userRole],
      currentSiteAccess: {
        role: userRole,
        siteId: "allSites",
        schoolYear: 2019,
        isActive: true
      },
      userSiteAccess: [
        {
          role: userRole,
          siteId: "allSites",
          schoolYear: 2019,
          isActive: true
        }
      ]
    };

    const staticDataContextValue = {
      roleDefinitions: [
        { _id: "arbitraryIdteacher", name: "teacher" },
        { _id: "arbitraryIdadmin", name: "admin" },
        { _id: "arbitraryIdsupport", name: "support" },
        { _id: "arbitraryIduniversalCoach", name: "universalCoach" },
        { _id: userRole, name: roleName }
      ]
    };

    const navComponent = renderWithRouter(renderNavigationWithContext({ props }), {
      userContextValue,
      staticDataContextValue,
      organizationContextValue: {
        orgId: "test_organization_id",
        isSelfEnrollee: false,
        sitesInOrg: [
          {
            _id: "test_site_id",
            name: "Test Site",
            orgid: "test_organization_id",
            grades: ["01", "02", "03"]
          }
        ]
      },
      siteContextValue: {
        siteId: "allSites",
        siteName: null,
        site: null
      },
      schoolYearContextValue: {
        schoolYear: schoolYears.length > 0 ? 2019 : null,
        latestAvailableSchoolYear: schoolYears.length > 0 ? Math.max(...schoolYears) : null
      },
      studentGroupContextValue: {
        studentGroup: {
          grade: "01"
        }
      }
    });
    await waitFor(() => {
      navComponent
        .getByTestId("userContextMenu")
        .querySelector("a")
        .click();
    });
    return navComponent;
  };

  describe("when the user is logged in and support is enabled", () => {
    it("should render a logout button", async () => {
      const { getByTestId } = await getLoggedInNavComponent({ supportEnabled: true });
      await waitFor(() => {
        expect(getByTestId("logout")).toBeDefined();
      });
    });
    it("should render a support button", async () => {
      const { getByTestId } = await getLoggedInNavComponent({ supportEnabled: true });
      await waitFor(() => {
        expect(getByTestId("support")).toBeDefined();
      });
    });
    it("should render a screening assessment button", async () => {
      const { getByTestId } = await getLoggedInNavComponent({ supportEnabled: true });
      await waitFor(() => {
        expect(getByTestId("screeningAssessments")).toBeDefined();
      });
    });
  });
  describe("when the user is logged in and support is not enabled", () => {
    it("should not render a support button", async () => {
      const { getByTestId } = await getLoggedInNavComponent();
      await waitFor(() => {
        expect(() => getByTestId("support")).toThrow();
      });
    });
    it("should render a logout button", async () => {
      const { getByTestId } = await getLoggedInNavComponent();
      await waitFor(() => {
        expect(getByTestId("logout")).toBeDefined();
      });
    });
    it("should render a screening assessment button", async () => {
      const { getByTestId } = await getLoggedInNavComponent();
      await waitFor(() => {
        expect(getByTestId("screeningAssessments")).toBeDefined();
      });
    });
  });
  describe("school year context switching", () => {
    it("should have a school year menu item element in the navigation component if the array is not empty", async () => {
      const schoolYears = [1900, 1950];
      const { container } = await getLoggedInNavComponent({ schoolYears });
      await waitFor(() => {
        expect(container.querySelector(".dropdown-menu").innerHTML).toContain("School Year");
      });
    });
    it("should be available for all schoolyears in the array", async () => {
      const schoolYears = [2017, 2018];
      const { container } = await getLoggedInNavComponent({ schoolYears });
      await waitFor(() => {
        const dropdownMenu = container.querySelector(".dropdown-menu");
        schoolYears.forEach(schoolYear => {
          assert.match(dropdownMenu.innerHTML, new RegExp(`${schoolYear - 1}-${schoolYear % 100}`.toString()), "i");
        });
      });
    });
    it("should not be available for school years not in the array", async () => {
      const schoolYears = [2017, 2019];
      const { container } = await getLoggedInNavComponent({ schoolYears });
      await waitFor(() => {
        const dropdownMenu = container.querySelector(".dropdown-menu");
        // Should show 2016-17, 2017-18, 2018-19 (range from 2017 to 2019)
        assert.match(dropdownMenu.innerHTML, /2016-17/i);
        assert.match(dropdownMenu.innerHTML, /2017-18/i);
        assert.match(dropdownMenu.innerHTML, /2018-19/i);
      });
    });
    it("should not be displayed at all when the schoolYears array is empty", async () => {
      const { container } = await getLoggedInNavComponent({ userRole: "arbitraryIdteacher", roleName: "teacher" });
      await waitFor(() => {
        const dropdownMenu = container.querySelector(".dropdown-menu");
        assert.notMatch(dropdownMenu.innerHTML, /school year/i);
      });
    });
  });
  describe("Logged in data admin", () => {
    beforeEach(() => {
      td.replace(Meteor, "call");
    });

    afterEach(() => {
      cleanup();
      td.reset();
    });

    it("should see correctly rendered navbar items", async () => {
      td.when(Meteor.call("Organizations:getOrganizationFieldValues", "orgid", ["rostering"])).thenCallback(null, {
        rostering: "rosterImport"
      });
      const { getByTestId } = await getLoggedInNavComponent({
        userRole: "arbitraryIddataAdmin",
        roleName: "dataAdmin"
      });
      await waitFor(() => {
        expect(getByTestId("topNav_data-admin-dashboard")).toBeVisible();
        expect(getByTestId("topNav_data-admin-manage-accounts")).toBeVisible();
      });
    });
  });
});
