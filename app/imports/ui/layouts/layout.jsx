import React, { Component } from "react";
import PropTypes from "prop-types";

import Alert from "react-s-alert";
import Navigation from "./navigation/navigation.jsx";
import LoadingScreen from "../components/loading-screen/loading-screen.jsx";
import { renderFooter } from "../utilities";

export default class Layout extends Component {
  render() {
    return (
      <div className="wrapper">
        <Navigation studentGroupId={this.props.studentGroupId} navName={this.props.navName} orgid={this.props.orgid} />
        <main>{this.props.content}</main>
        {renderFooter()}
        <LoadingScreen />
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
}

Layout.propTypes = {
  content: PropTypes.object,
  navName: PropTypes.string,
  orgid: PropTypes.string,
  studentGroupId: PropTypes.string,
  user: PropTypes.object
};
