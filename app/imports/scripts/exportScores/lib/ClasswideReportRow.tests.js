import ClasswideReportRow from "./ClasswideReportRow";

describe("classwide report row", () => {
  describe("addClasswideScore", () => {
    it("should add scores to output rows", () => {
      const classwideReportRow = new ClasswideReportRow({
        studentGroup: { schoolYear: 2017 },
        result: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });
      const classwideScoreParams1 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "1",
        historyItemIndex: 1
      };
      const classwideScoreParams2 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "3",
        historyItemIndex: 2
      };

      classwideReportRow.addClasswideScore(classwideScoreParams1);
      classwideReportRow.addClasswideScore(classwideScoreParams2);

      expect(classwideReportRow.outputFields).toHaveProperty("classwide intervention progress monitoring skill-1");
      expect(classwideReportRow.outputFields).toHaveProperty(
        "classwide intervention progress monitoring progress monitoring score-1"
      );
      expect(classwideReportRow.outputFields).toHaveProperty(
        "classwide intervention progress monitoring score entry date-1"
      );
      expect(classwideReportRow.outputFields).toHaveProperty("classwide intervention progress monitoring skill-2");
      expect(classwideReportRow.outputFields).toHaveProperty(
        "classwide intervention progress monitoring progress monitoring score-2"
      );
      expect(classwideReportRow.outputFields).toHaveProperty(
        "classwide intervention progress monitoring score entry date-2"
      );
    });
  });
  describe("calculateClasswideRowStatistics", () => {
    it("should calculate correct statistics for the student row for a group without current classwide skill", () => {
      const classwideReportRow = new ClasswideReportRow({
        studentGroup: { schoolYear: 2017 },
        result: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });
      const classwideScoreParams1 = {
        whenEndedDate: new Date(1524569554729),
        assessmentName: "testSkill1",
        score: "1",
        historyItemIndex: 1
      };
      const classwideScoreParams2 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "3",
        historyItemIndex: 2
      };
      const classwideScoreParams3 = {
        whenEndedDate: new Date(1527159236722),
        assessmentName: "testSkill2",
        score: "4",
        historyItemIndex: 3
      };

      classwideReportRow.addClasswideScore(classwideScoreParams1);
      classwideReportRow.addClasswideScore(classwideScoreParams2);
      classwideReportRow.addClasswideScore(classwideScoreParams3);

      const outputFields = classwideReportRow.getOutputFields();
      expect(outputFields["classwide intervention count of progress monitoring scores"]).toEqual(3);
      expect(outputFields["classwide intervention number of skills mastered"]).toEqual(2);
      expect(outputFields["classwide intervention average weeks per skill"]).toEqual("2.1");
      expect(outputFields["classwide intervention date started"]).toEqual("2018-04-24T11:32:34.729Z");
      expect(outputFields["classwide intervention last score entry date"]).toEqual("2018-05-24T10:53:56.722Z");
    });
    it("should calculate correct statistics for the student row for a group with current classwide skill", () => {
      const classwideReportRow = new ClasswideReportRow({
        studentGroup: { schoolYear: 2017 },
        result: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });
      const classwideScoreParams1 = {
        whenEndedDate: new Date(1524569554729),
        assessmentName: "testSkill1",
        score: "1",
        groupHasCurrentClasswideSkill: true,
        historyItemIndex: 1
      };
      const classwideScoreParams2 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "3",
        historyItemIndex: 3
      };
      const classwideScoreParams3 = {
        whenEndedDate: new Date(1527159236722),
        assessmentName: "testSkill2",
        isCurrentSkill: true,
        score: "1",
        historyItemIndex: 4
      };

      classwideReportRow.addClasswideScore(classwideScoreParams1);
      classwideReportRow.addClasswideScore(classwideScoreParams2);
      classwideReportRow.addClasswideScore(classwideScoreParams3);

      const outputFields = classwideReportRow.getOutputFields();
      expect(outputFields["classwide intervention count of progress monitoring scores"]).toEqual(3);
      expect(outputFields["classwide intervention number of skills mastered"]).toEqual(1);
      expect(outputFields["classwide intervention average weeks per skill"]).toEqual("4.3");
      expect(outputFields["classwide intervention date started"]).toEqual("2018-04-24T11:32:34.729Z");
      expect(outputFields["classwide intervention last score entry date"]).toEqual("2018-05-24T10:53:56.722Z");
    });
  });
});
