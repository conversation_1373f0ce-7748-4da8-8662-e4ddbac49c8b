import BenchmarkReportRow from "./BenchmarkReportRow";

describe("benchmark report row", () => {
  describe("getCurrentMeasureIndex", () => {
    it("should generate correct indexes for benchmarkPeriods", () => {
      const benchmarkReportRow = new BenchmarkReportRow({
        studentGroup: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });

      const firstSpringMeasureIndex = benchmarkReportRow.getCurrentMeasureIndex("spring");
      expect(firstSpringMeasureIndex).toEqual(1);
      const secondSpringMeasureIndex = benchmarkReportRow.getCurrentMeasureIndex("spring");
      expect(secondSpringMeasureIndex).toEqual(2);
      const firstFallMeasureIndex = benchmarkReportRow.getCurrentMeasureIndex("fall");
      expect(firstFallMeasureIndex).toEqual(1);
    });
  });
  describe("addBenchmarkScoreToBenchmarkPeriod", () => {
    it("should add scores to output rows", () => {
      const benchmarkReportRow = new BenchmarkReportRow({
        studentGroup: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } },
        benchmarkPeriods: [
          { _id: "s", name: "spring" },
          { _id: "f", name: "fall" }
        ]
      });
      const benchmarkScoreParams = {
        benchmarkPeriodId: "s",
        whenEndedDate: new Date(),
        assessmentName: "testAssessment",
        score: "1"
      };

      benchmarkReportRow.addBenchmarkScoreToBenchmarkPeriod(benchmarkScoreParams);
      benchmarkReportRow.addBenchmarkScoreToBenchmarkPeriod(benchmarkScoreParams);

      expect(benchmarkReportRow.outputFields).toHaveProperty("spring screening assessment score entry date");
      expect(benchmarkReportRow.outputFields).toHaveProperty("spring screening assessment #1 name");
      expect(benchmarkReportRow.outputFields).toHaveProperty("spring screening assessment #1 score");
      expect(benchmarkReportRow.outputFields).toHaveProperty("spring screening assessment #2 name");
      expect(benchmarkReportRow.outputFields).toHaveProperty("spring screening assessment #2 score");
    });
  });
});
