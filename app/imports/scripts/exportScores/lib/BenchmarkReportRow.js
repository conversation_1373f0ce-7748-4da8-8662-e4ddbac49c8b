import ReportRow from "./ReportRow";

export default class BenchmarkReportRow extends ReportRow {
  constructor({
    studentGroup,
    teacher,
    district,
    school,
    student,
    studentGroupEnrollment,
    benchmarkPeriods,
    shouldGenerateInterventionNotes,
    interventionNotesToSet
  }) {
    super({
      studentGroup,
      teacher,
      district,
      school,
      student,
      studentGroupEnrollment,
      shouldGenerateInterventionNotes,
      interventionNotesToSet
    });
    this.benchmarkPeriods = benchmarkPeriods;
    this.nextAssessmentResultMeasureIndexForPeriods = {};
    ["Spring", "Winter", "Fall"].forEach(benchmarkPeriodName => {
      for (let i = 1; i <= 4; i += 1) {
        this.addScoreFields({ benchmarkPeriodName, index: i, entryDate: "N/A", assessmentName: "N/A", score: "N/A" });
      }
    });
  }

  getCurrentMeasureIndex = benchmarkPeriodName => {
    let currentMeasureIndex = 1;
    if (Object.prototype.hasOwnProperty.call(this.nextAssessmentResultMeasureIndexForPeriods, benchmarkPeriodName)) {
      this.nextAssessmentResultMeasureIndexForPeriods[benchmarkPeriodName] += 1;
      currentMeasureIndex = this.nextAssessmentResultMeasureIndexForPeriods[benchmarkPeriodName];
    } else {
      this.nextAssessmentResultMeasureIndexForPeriods[benchmarkPeriodName] = currentMeasureIndex;
    }
    return currentMeasureIndex;
  };

  addBenchmarkScoreToBenchmarkPeriod = ({ benchmarkPeriodId, whenEndedDate, assessmentName, score }) => {
    const benchmarkPeriod =
      this.benchmarkPeriods && this.benchmarkPeriods.find(bmPeriod => bmPeriod._id === benchmarkPeriodId);
    if (benchmarkPeriod) {
      const entryDate = whenEndedDate ? whenEndedDate.toISOString() : whenEndedDate;
      const assResMeasureIndex = this.getCurrentMeasureIndex(benchmarkPeriod.name);

      this.addScoreFields({
        benchmarkPeriodName: benchmarkPeriod.name,
        index: assResMeasureIndex,
        entryDate,
        assessmentName,
        score
      });
    }
  };

  addScoreFields = ({ benchmarkPeriodName, index, entryDate, assessmentName, score }) => {
    this.outputFields[`${benchmarkPeriodName} screening assessment score entry date`] = entryDate
      .slice(0, 19)
      .replace("T", " ");
    this.outputFields[`${benchmarkPeriodName} screening assessment #${index} name`] = assessmentName;
    this.outputFields[`${benchmarkPeriodName} screening assessment #${index} score`] = score;
  };
}
