/* eslint class-methods-use-this: ["error", { "exceptMethods": ["init"] }] */
/* eslint import/no-extraneous-dependencies: ["error", {"peerDependencies": true}] */
import { MongoClient } from "mongodb";

export default class MongoRepository {
  constructor(mongoUrl) {
    this.mongoUrl = mongoUrl;
    this.dbConnection = null;
    this.db = null;
  }

  connect = async () => {
    const options = { useUnifiedTopology: true };
    if (this.mongoUrl && (this.mongoUrl.includes("ssl=true") || this.mongoUrl.includes("tls=true"))) {
      options.tls = true;
      options.tlsInsecure = true;
    }
    this.dbConnection = await MongoClient.connect(this.mongoUrl, options);
  };

  selectDb = () => {
    this.db = this.dbConnection.db(this.dbConnection.s.options.dbName);
  };

  closeConnection = async () => {
    if (this.dbConnection) {
      await this.dbConnection.close();
    }
  };
}
