import ReportRow from "./ReportRow";

describe("reportRow", () => {
  describe("generateUserRow", () => {
    it("should create base row structure", () => {
      const inputData = {
        studentGroup: { grade: "1", name: "a", sectionId: "b", schoolYear: "c" },
        teacher: { profile: { name: { first: "g", last: "h" } } },
        district: { name: "i" },
        school: { name: "j" },
        student: {
          identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
          demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
        },
        studentGroupEnrollment: { title1: "s", freeReducedLunch: "t" }
      };

      const reportRow = new ReportRow(inputData);
      expect(reportRow.outputFields["School Year"]).toEqual(inputData.studentGroup.schoolYear);
      expect(reportRow.outputFields.DistrictID).toEqual(inputData.districtId);
      expect(reportRow.outputFields.SchoolID).toEqual(inputData.schoolId);
      expect(reportRow.outputFields.TeacherID).toEqual(inputData.teacherId);
      expect(reportRow.outputFields["Class SectionID"]).toEqual(inputData.studentGroup.sectionId);
      expect(reportRow.outputFields["Class Name"]).toEqual(inputData.studentGroup.name);
      expect(reportRow.outputFields["Teacher First Name"]).toEqual(inputData.teacher.profile.name.first);
      expect(reportRow.outputFields["Teacher Last Name"]).toEqual(inputData.teacher.profile.name.last);
      expect(reportRow.outputFields["District Name"]).toEqual(inputData.district.name);
      expect(reportRow.outputFields["School Name"]).toEqual(inputData.school.name);
      expect(reportRow.outputFields["Student First Name"]).toEqual(inputData.student.identity.name.firstName);
      expect(reportRow.outputFields["Student Last Name"]).toEqual(inputData.student.identity.name.lastName);
      expect(reportRow.outputFields["Student LocalID"]).toEqual(inputData.student.identity.identification.localId);
      expect(reportRow.outputFields["Student StateID"]).toEqual(inputData.student.identity.identification.stateId);
      expect(reportRow.outputFields["Student Birth Date"]).toEqual(inputData.student.demographic.birthDate);
    });
  });
});
