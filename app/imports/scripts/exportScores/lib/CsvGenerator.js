export default class CsvGenerator {
  constructor({ ORGID, TYPE, SCHO<PERSON>YEAR, OPTIONS = {} }) {
    this.ORGID = ORGID;
    this.TYPE = TYPE;
    this.SCHOOLYEAR = SCHOOLYEAR;
    this.shouldGenerateInterventionNotes = OPTIONS.shouldGenerateInterventionNotes;
  }

  generateCsvContent = rows => {
    let cvsHeaderCols = [];
    Object.values(rows).forEach(year => {
      Object.values(year).forEach(student => {
        const outputFields = student.getOutputFields();
        const nameOfCols = Object.keys(outputFields);
        if (nameOfCols.length > cvsHeaderCols.length) {
          cvsHeaderCols = nameOfCols;
        }
      });
    });

    const csvParts = [];

    let outRowParts = [];
    cvsHeaderCols.forEach(column => {
      outRowParts.push(`${column.includes(",") ? `"${column}"` : column},`);
    });
    csvParts.push(`${outRowParts.join("")}\r\n`);

    Object.values(rows).forEach(year => {
      Object.values(year).forEach(student => {
        const outputFields = student.getOutputFields();
        outRowParts = [];
        cvsHeaderCols.forEach(column => {
          const value = outputFields[column] ? outputFields[column] : "N/A";
          outRowParts.push(`${typeof value === "string" && value.includes(",") ? `"${value}"` : value},`);
        });
        csvParts.push(`${outRowParts.join("")}\r\n`);
      });
    });
    return csvParts;
  };

  generateFilename = () => {
    const currentDate = new Date().toISOString().slice(0, 10);
    return `scoresHistory-${this.SCHOOLYEAR ? this.SCHOOLYEAR : "allYears"}-${this.ORGID ? this.ORGID : "allOrgs"}-${
      this.TYPE
    }-${currentDate}.csv`;
  };

  saveCsv = csvContent => {
    const fs = require("fs");
    const filename = this.generateFilename();
    fs.writeFileSync(filename, csvContent);
  };
}
