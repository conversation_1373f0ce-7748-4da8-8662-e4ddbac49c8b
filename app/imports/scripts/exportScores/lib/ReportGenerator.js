/* eslint-disable no-await-in-loop */
import { Meteor } from "meteor/meteor";
import { difference, flatten, get, uniq } from "lodash";
import moment from "moment";
import Database from "./database/database";
import IndividualReportRow from "./IndividualReportRow";
import ClasswideReportRow from "./ClasswideReportRow";
import BenchmarkReportRow from "./BenchmarkReportRow";
import CsvGenerator from "./CsvGenerator";
import { keyExists } from "./utils";
import { getIndividualInterventionQueueBasedOnFourWeekRule } from "/imports/api/assessmentResults/methods";
import { getAbsoluteWeekNumber } from "/imports/api/utilities/server/utilities";

export default class ReportGenerator {
  constructor({ TYPE, ORGID, SCHOOLYEAR, OPTIONS = {} }) {
    const environment = Meteor.settings.public.ENVIRONMENT;
    this.MONGO_URL =
      environment && environment !== "LOCAL"
        ? `${process.env.MONGO_URL}&readPreference=secondaryPreferred`
        : `${process.env.MONGO_URL}`;

    if (process.env.SCRIPT_MONGO_URL) {
      // Ability to use different database for scripts
      this.MONGO_URL = `${process.env.SCRIPT_MONGO_URL}&readPreference=secondaryPreferred`;
    }

    if (!this.MONGO_URL) {
      throw new Error(
        "You need to export system environment variable MONGO_URL with connection string to SpringMath DB"
      );
    }

    this.TYPE = TYPE;
    if (!["benchmark", "classwide", "individual"].includes(this.TYPE)) {
      throw new Error("Parameter TYPE is mandatory. It should have one of values: benchmark, classwide or individual");
    }

    if (ORGID) {
      this.ORGID = ORGID;
    }

    if (SCHOOLYEAR) {
      this.SCHOOLYEAR = SCHOOLYEAR;
    }

    if (Object.keys(OPTIONS).length) {
      this.shouldGenerateInterventionNotes = OPTIONS.shouldGenerateInterventionNotes;
    }

    this.db = new Database(this.MONGO_URL);

    this.rows = {};

    this.benchmarks = { "8S52Gz5o85hRkECgq": "Fall", nEsbWokBWutTZFkTh: "Winter", cjCMnZKARBJmG8suT: "Spring" };

    this.csvGenerator = new CsvGenerator({ ORGID, TYPE, SCHOOLYEAR, OPTIONS });
  }

  getDataFromDB = async () => {
    await this.db.getDataFromDB({ ORGID: this.ORGID, TYPE: this.TYPE, SCHOOLYEAR: this.SCHOOLYEAR });
  };

  getIndividualInterventionRecommendationWeeks = async () => {
    if (this.shouldGenerateInterventionNotes) {
      this.individualInterventionRecommendationWeeks = await this.getNumberOfRecommendedIndividualInterventionWeeksFromFourWeekRule();
    }
  };

  parseIndividualAssessmentResult = (student, assResMeasure, whenEndedDate, assessmentResultId) => {
    const assessmentResult = this.db.individualAssessmentResultsById[assessmentResultId];
    if (assessmentResult) {
      const { studentGroupId } = assessmentResult;
      const studentGroup = this.db.studentGroupById[studentGroupId];
      if (studentGroup && assResMeasure.studentScores && assResMeasure.studentScores[0]) {
        if (!(this.rows[student.schoolYear][student._id] instanceof IndividualReportRow)) {
          this.createReportRow(student, studentGroup);
        }

        this.rows[student.schoolYear][student._id].addIndividualScore({
          assessmentName: assResMeasure.assessmentName,
          score: assResMeasure.studentScores[0],
          whenEndedDate
        });
      }
    }
  };

  createReportRow = (student, studentGroup) => {
    const teacherId = studentGroup.ownerIds && studentGroup.ownerIds[0];
    const teacher = this.db.teacherById[teacherId] || { _id: teacherId };

    const district = this.db.district || {};
    const school = this.db.schoolById[studentGroup.siteId] || {};
    const studentGroupEnrollment =
      this.db.sgeByStudentGroupId[studentGroup._id].find(sge => sge.studentId === student._id) || {};

    const interventionNotesToSet = {};
    if (this.shouldGenerateInterventionNotes) {
      interventionNotesToSet.getNumberOfStudentsInClass = this.getNumberOfStudentsInClass(studentGroup._id);
      interventionNotesToSet.getHadRecommendedIndividualIntervention = this.getHadRecommendedIndividualIntervention(
        studentGroup._id,
        student._id
      );
      interventionNotesToSet.getNumberOfEligibleIndividualInterventionWeeksFromFourWeekRuleStudentRow = this.getNumberOfEligibleIndividualInterventionWeeksFromFourWeekRuleStudentRow(
        studentGroup._id,
        student._id
      );
      interventionNotesToSet.hadClasswideInterventionRecommendedInFallOrWinterRow = this.hadClasswideInterventionRecommendedInFallOrWinterRow(
        studentGroup._id,
        student._id
      );
      interventionNotesToSet.getWasIndividualInterventionScheduledFromFourWeekRuleRow = this.getWasIndividualInterventionScheduledFromFourWeekRuleRow(
        studentGroup._id,
        student._id
      );
      interventionNotesToSet.getNumberOfWeeksWhenIndividualInterventionWasRecommendedFromFourWeekRuleRow = this.getNumberOfWeeksWhenIndividualInterventionWasRecommendedFromFourWeekRuleRow(
        studentGroup._id,
        student._id
      );
    }

    const studentData = {
      studentGroup,
      teacher,
      district,
      school,
      student,
      studentGroupEnrollment,
      benchmarkPeriods: this.db.benchmarkPeriods,
      shouldGenerateInterventionNotes: this.shouldGenerateInterventionNotes,
      interventionNotesToSet
    };

    const rowSchoolYear = this.TYPE === "individual" ? student.schoolYear : studentGroup.schoolYear;
    const rowStudentId = this.TYPE === "individual" ? student._id : `${studentGroup._id}${student._id}`;

    let ClassnameToCreate;
    switch (this.TYPE) {
      case "individual":
        ClassnameToCreate = IndividualReportRow;
        break;

      case "classwide":
        ClassnameToCreate = ClasswideReportRow;
        break;

      case "benchmark":
        ClassnameToCreate = BenchmarkReportRow;
        break;

      default:
        break;
    }

    this.rows[rowSchoolYear][rowStudentId] = new ClassnameToCreate(studentData);
  };

  getBenchmarkAssessmentResultsForStudentGroup = studentGroupId => {
    return this.db.benchmarkAssessmentResultsByStudentGroupId[studentGroupId] || [];
  };

  getShortDate = date => {
    return new Date(date).toISOString().slice(0, 10);
  };

  getNumberOfRecommendedIndividualInterventionWeeksFromFourWeekRule = async () => {
    const existingStudentGroups = Object.values(this.db.studentGroupById) || [];
    const individualRecommendationWeeksByStudentGroup = {};
    const scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumberByStudentGroup = {};
    const numberOfEligibleWeekByStudentIdByStudentGroup = {};
    const numberOfStudentsInClass = {};
    const studentGroupsWithHistory = existingStudentGroups.filter(
      esg => esg.history && esg.history.find(esgh => esgh.type === "classwide")
    );
    const studentGroupsIds = studentGroupsWithHistory.map(sg => sg._id);
    const assessmentResultsById = studentGroupsIds.reduce((a, studentGroupId) => {
      const assessmentResultsForStudentGroup = this.db.classwideAssessmentResultsByStudentGroupId[
        studentGroupId
      ].filter(ar => ar.status === "COMPLETED");
      for (const ar of assessmentResultsForStudentGroup) {
        // eslint-disable-next-line no-param-reassign
        a[ar._id] = ar;
      }
      return a;
    }, {});
    for (const studentGroup of studentGroupsWithHistory) {
      scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumberByStudentGroup[studentGroup._id] = {};
      const numberOfRecommendedWeeksByStudentId = {};
      const numberOfEligibleWeekByStudentId = {};
      const studentGroupEnrollments = this.db.sgeByStudentGroupId?.[studentGroup._id];
      if (studentGroupEnrollments) {
        numberOfStudentsInClass[studentGroup._id] = studentGroupEnrollments.filter(sge => sge.isActive).length;
        for (const sge of studentGroupEnrollments) {
          numberOfRecommendedWeeksByStudentId[sge.studentId] = 0;
          numberOfEligibleWeekByStudentId[sge.studentId] = 0;
        }
      }

      const studentGroupClasswideHistoryElements = studentGroup.history
        .filter(sgh => sgh.type === "classwide")
        .sort((a, b) => a.whenEnded.on - b.whenEnded.on);

      const firstClasswideIntervention = studentGroupClasswideHistoryElements[0];
      const firstClasswideInterventionDate = firstClasswideIntervention.whenEnded.on;
      if (!firstClasswideInterventionDate) {
        return {};
      }

      // Start = Monday, End - Sunday
      const startOfWeek = moment(firstClasswideInterventionDate).startOf("isoWeek");

      const startOfSchoolYear = moment(`${this.SCHOOLYEAR - 1}-08-01`).startOf("month");
      const endOfSchoolYear = moment(`${this.SCHOOLYEAR}-07-31`).endOf("month");

      const latestDateToCalculateTo = moment().isBetween(startOfSchoolYear, endOfSchoolYear)
        ? moment()
        : endOfSchoolYear;

      const currentTempDate = startOfWeek.clone();
      const allWeeksToUse = [];
      while (currentTempDate.isBefore(latestDateToCalculateTo)) {
        allWeeksToUse.push({
          startWeek: currentTempDate.clone(),
          endWeek: currentTempDate.clone().endOf("isoWeek"),
          absoluteWeek: getAbsoluteWeekNumber(currentTempDate.clone(), this.SCHOOLYEAR)
        });
        currentTempDate.add(1, "week");
      }
      const lastWeekNumber = allWeeksToUse[allWeeksToUse.length - 1].absoluteWeek;

      let studentIdsInRecommendationQueue = new Set();
      const chronologicalHistory = [];
      let eligibleQueue = [];
      const assessmentResults = Object.values(this.db.individualAssessmentResultsById);
      for (const weekToUse of allWeeksToUse) {
        const startWeekTimestamp = weekToUse.startWeek.valueOf();
        const endWeekTimestamp = weekToUse.endWeek.valueOf();
        const limitedHistory = studentGroupClasswideHistoryElements.filter(h => h.whenEnded.on <= endWeekTimestamp);
        const currentWeekHistoryScope = limitedHistory.filter(
          h => h.whenEnded.on >= startWeekTimestamp && h.whenEnded.on <= endWeekTimestamp
        );

        for (const studentId of studentIdsInRecommendationQueue) {
          numberOfRecommendedWeeksByStudentId[studentId] += 1;
        }
        for (const studentId of uniq(flatten(eligibleQueue))) {
          numberOfEligibleWeekByStudentId[studentId] += 1;
        }

        let individualInterventionQueue = [];
        eligibleQueue = [];

        for (const historyItem of currentWeekHistoryScope) {
          const timestampInfo = { ...historyItem.whenEnded };
          const assessmentResult = assessmentResultsById[historyItem.assessmentResultId];
          const modifiedStudentGroup = {
            ...studentGroup,
            currentClasswideSkill: historyItem,
            history: chronologicalHistory,
            individualInterventionQueue: [] // Fetch full queue each time including scheduled interventions
          };

          if (assessmentResult) {
            individualInterventionQueue =
              (await getIndividualInterventionQueueBasedOnFourWeekRule({
                studentGroup: modifiedStudentGroup,
                assessmentResult,
                timestampInfo
              })) || [];
          }

          chronologicalHistory.unshift(historyItem); // Update studentGroup history for next iteration
        }
        eligibleQueue.push(individualInterventionQueue);
        for (const queueEl of individualInterventionQueue) {
          studentIdsInRecommendationQueue.add(queueEl);
        }
        const individualAssessmentResults = assessmentResults.filter(
          // eslint-disable-next-line no-loop-func
          ar => studentIdsInRecommendationQueue.has(ar.studentId) && ar.created.on <= endWeekTimestamp
        );

        const finishedIndividualInterventionAssessmentResults = individualAssessmentResults.filter(
          ar => ar.status === "COMPLETED" && ar.created.on >= startWeekTimestamp && !ar.nextAssessmentResultId
        );
        const individualAssessmentResultsStudentIds = individualAssessmentResults.map(ar => ar.studentId);
        const finishedIndividualInterventionAssessmentResultsStudentIds = finishedIndividualInterventionAssessmentResults.map(
          ar => ar.studentId
        );
        if (individualAssessmentResultsStudentIds.length) {
          scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumberByStudentGroup[studentGroup._id][
            weekToUse.absoluteWeek
          ] = {
            weekNumberWhenScheduled: weekToUse.absoluteWeek,
            scheduledStudentIds: difference(
              individualAssessmentResultsStudentIds,
              finishedIndividualInterventionAssessmentResultsStudentIds
            ),
            lastWeekNumber
          };
          // Remove studentIds from queue if has individual in current week scope
          studentIdsInRecommendationQueue = new Set(
            difference([...studentIdsInRecommendationQueue], individualAssessmentResultsStudentIds)
          );
        }
      }
      individualRecommendationWeeksByStudentGroup[studentGroup._id] = numberOfRecommendedWeeksByStudentId;
      numberOfEligibleWeekByStudentIdByStudentGroup[studentGroup._id] = numberOfEligibleWeekByStudentId;
    }
    return {
      individualRecommendationWeeksByStudentGroup,
      scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumberByStudentGroup,
      numberOfEligibleWeekByStudentIdByStudentGroup,
      numberOfStudentsInClass
    };
  };

  getNumberOfStudentsInClass = studentGroupId =>
    get(this.individualInterventionRecommendationWeeks.numberOfStudentsInClass, studentGroupId, "N/A");

  getNumberOfEligibleIndividualInterventionWeeksFromFourWeekRuleStudentRow = (studentGroupId, studentId) =>
    get(
      this.individualInterventionRecommendationWeeks.numberOfEligibleWeekByStudentIdByStudentGroup,
      `${studentGroupId}.${studentId}`,
      "N/A"
    );

  getNumberOfRecommendedIndividualInterventionWeeksFromFourWeekRuleStudentRow = (studentGroupId, studentId) => {
    return get(
      this.individualInterventionRecommendationWeeks.individualRecommendationWeeksByStudentGroup,
      `${studentGroupId}.${studentId}`,
      "N/A"
    );
  };

  hadIndividualInterventionScheduledFromFourWeekRule = (studentGroupId, studentId) => {
    const scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumber = get(
      this.individualInterventionRecommendationWeeks
        .scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumberByStudentGroup,
      studentGroupId
    );
    let wasScheduled = false;
    let numberOfWeeks = "N/A";
    if (!scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumber) {
      return { wasScheduled, numberOfWeeks };
    }

    Object.values(scheduledIndividualInterventionStudentIdsByAbsoluteWeekNumber).forEach(interventionQueue => {
      if (interventionQueue.scheduledStudentIds.includes(studentId)) {
        numberOfWeeks = interventionQueue.lastWeekNumber - interventionQueue.weekNumberWhenScheduled;
        wasScheduled = true;
      }
    });
    return { wasScheduled, numberOfWeeks };
  };

  getWasIndividualInterventionScheduledFromFourWeekRuleRow = (studentGroupId, studentId) => {
    return this.hadIndividualInterventionScheduledFromFourWeekRule(studentGroupId, studentId).wasScheduled
      ? "Yes"
      : "No";
  };

  getNumberOfWeeksWhenIndividualInterventionWasRecommendedFromFourWeekRuleRow = (studentGroupId, studentId) => {
    const numberOfRecommended =
      this.getNumberOfRecommendedIndividualInterventionWeeksFromFourWeekRuleStudentRow(studentGroupId, studentId) ===
      "N/A"
        ? 0
        : this.getNumberOfRecommendedIndividualInterventionWeeksFromFourWeekRuleStudentRow(studentGroupId, studentId);
    const numberOfScheduled =
      this.hadIndividualInterventionScheduledFromFourWeekRule(studentGroupId, studentId).numberOfWeeks === "N/A"
        ? 0
        : this.hadIndividualInterventionScheduledFromFourWeekRule(studentGroupId, studentId).numberOfWeeks;

    return numberOfRecommended + numberOfScheduled;
  };

  hadClasswideInterventionRecommendedInFallOrWinterRow = (studentGroupId, studentId) => {
    const sortedBenchmarkIds = ["8S52Gz5o85hRkECgq", "nEsbWokBWutTZFkTh", "allPeriods"];

    const classwideAssessmentResultsForStudentGroup = this.db.classwideAssessmentResultsByStudentGroupId[
      studentGroupId
    ]?.find(ar => ar.scores.find(s => s.studentId === studentId) && sortedBenchmarkIds.includes(ar.benchmarkPeriodId));
    return classwideAssessmentResultsForStudentGroup?.previousAssessmentResultId
      ? `Yes, ${this.benchmarks[classwideAssessmentResultsForStudentGroup.benchmarkPeriodId]} - ${this.getShortDate(
          classwideAssessmentResultsForStudentGroup.created.on
        )}`
      : "No";
  };

  // Was recommended for Individual Intervention from screening in Fall/Winter benchmark periods
  getHadRecommendedIndividualIntervention = (studentGroupId, studentId) => {
    const sortedBenchmarkIds = ["8S52Gz5o85hRkECgq", "nEsbWokBWutTZFkTh"];
    const benchmarkAssessmentResults = this.getBenchmarkAssessmentResultsForStudentGroup(studentGroupId);
    let wasRecommendedAtAll = false;
    sortedBenchmarkIds.forEach(bmId => {
      const currentBenchmarkAssessmentResult = benchmarkAssessmentResults.find(bar => bar.benchmarkPeriodId === bmId);
      const percentAtRisk = get(currentBenchmarkAssessmentResult, "classwideResults.percentAtRisk");
      if (percentAtRisk <= 50) {
        const studentIdsNotMeetingTarget = get(
          currentBenchmarkAssessmentResult,
          "classwideResults.studentIdsNotMeetingTarget",
          []
        );
        const wasStudentRecommended = studentIdsNotMeetingTarget.includes(studentId);
        wasRecommendedAtAll = wasRecommendedAtAll || wasStudentRecommended;
      }
    });
    return wasRecommendedAtAll ? "Yes" : "No";
  };

  parseIndividualStudentHistory = (student, historyItem) => {
    if (historyItem.type === this.TYPE && historyItem.assessmentResultMeasures) {
      historyItem.assessmentResultMeasures.forEach(assResMeasure => {
        this.parseIndividualAssessmentResult(
          student,
          assResMeasure,
          historyItem.whenEnded.date,
          historyItem.assessmentResultId
        );
      });
    }
  };

  generateIndividualReportRows = () => {
    this.db.studentGroupEnrollments?.forEach(sge => {
      const student = this.db.studentById[sge.studentId];
      if (student && student.history) {
        student.history.forEach(historyItem => {
          if (!keyExists(this.rows, student.schoolYear)) {
            this.rows[student.schoolYear] = {};
          }
          this.parseIndividualStudentHistory(student, historyItem);
        });
      }
    });
  };

  addBenchmarkAssessmentScore = ({
    student,
    studentGroup,
    whenEndedDate,
    assessmentName,
    score,
    benchmarkPeriodId
  }) => {
    if (!(this.rows[studentGroup.schoolYear][`${studentGroup._id}${student._id}`] instanceof BenchmarkReportRow)) {
      this.createReportRow(student, studentGroup);
    }

    this.rows[studentGroup.schoolYear][`${studentGroup._id}${student._id}`].addBenchmarkScoreToBenchmarkPeriod({
      benchmarkPeriodId,
      whenEndedDate,
      assessmentName,
      score
    });
  };

  addClasswideAssessmentScore = ({
    student,
    studentGroup,
    whenEndedDate,
    assessmentName,
    isCurrentSkill,
    score,
    historyItemIndex
  }) => {
    if (!(this.rows[studentGroup.schoolYear][`${studentGroup._id}${student._id}`] instanceof ClasswideReportRow)) {
      this.createReportRow(student, studentGroup);
    }
    this.rows[studentGroup.schoolYear][`${studentGroup._id}${student._id}`].addClasswideScore({
      assessmentName,
      isCurrentSkill,
      score,
      whenEndedDate,
      historyItemIndex
    });
  };

  generateReportRows = () => {
    const studentGroups = Object.values(this.db.studentGroupById);
    if (studentGroups) {
      studentGroups.forEach(studentGroup => {
        this.parseStudentGroupHistory(studentGroup);
      });
    }
  };

  parseStudentGroupHistory = studentGroup => {
    let historyItemIndex = 1;
    if (studentGroup.history?.length) {
      const history = studentGroup.history.filter(historyItem => historyItem.type === this.TYPE);
      if (this.TYPE === "benchmark" && !history.length) {
        history.push(studentGroup.history[0]);
      }
      history.forEach(historyItem => {
        if (historyItem.assessmentResultMeasures) {
          historyItem.assessmentResultMeasures.forEach(assResMeasure => {
            this.parseAssessmentMeasure({ assResMeasure, studentGroup, historyItem, historyItemIndex });
          });
          historyItemIndex += 1;
        }
      });
    }
  };

  parseAssessmentMeasure = ({ assResMeasure, studentGroup, historyItem, historyItemIndex }) => {
    if (assResMeasure.studentResults) {
      assResMeasure.studentResults.forEach(studentResult => {
        const { studentId } = studentResult;
        const student = this.db.studentById[studentId];
        if (student) {
          if (!keyExists(this.rows, studentGroup.schoolYear)) {
            this.rows[studentGroup.schoolYear] = {};
          }
          this.addAssessmentMeasureScore({
            student,
            studentGroup,
            historyItem,
            assessmentName: assResMeasure.assessmentName,
            studentResult,
            historyItemIndex
          });
        }
      });
    }
  };

  addAssessmentMeasureScore = ({
    student,
    studentGroup,
    historyItem,
    assessmentName,
    studentResult,
    historyItemIndex
  }) => {
    if (this.TYPE === "benchmark") {
      if (historyItem.type === "classwide") {
        this.createReportRow(student, studentGroup);
      } else {
        this.addBenchmarkAssessmentScore({
          student,
          studentGroup,
          whenEndedDate: historyItem.whenEnded && historyItem.whenEnded.date,
          assessmentName,
          score: studentResult.score,
          benchmarkPeriodId: historyItem.benchmarkPeriodId
        });
      }
    } else if (this.TYPE === "classwide") {
      const isCurrentSkill =
        historyItem.assessmentId && studentGroup.currentClasswideSkill?.assessmentId === historyItem.assessmentId;
      this.addClasswideAssessmentScore({
        student,
        studentGroup,
        whenEndedDate: historyItem.whenEnded && historyItem.whenEnded.date,
        assessmentName,
        isCurrentSkill,
        score: studentResult.score,
        historyItemIndex
      });
    }
  };

  getReport = async () => {
    await this.getIndividualInterventionRecommendationWeeks();
    if (this.TYPE === "individual") {
      this.generateIndividualReportRows();
    } else {
      this.generateReportRows();
    }
    // this.csvGenerator.saveCsv(csvContent);
    delete this.db;
    return this.csvGenerator.generateCsvContent(this.rows);
  };
}

export async function generateReport({
  selectedSchoolYear,
  selectedOrgid,
  selectedType,
  shouldGenerateInterventionNotes
}) {
  const report = new ReportGenerator({
    TYPE: selectedType,
    ORGID: selectedOrgid,
    SCHOOLYEAR: selectedSchoolYear,
    OPTIONS: { shouldGenerateInterventionNotes }
  });
  await report.getDataFromDB();
  return report.getReport();
}
