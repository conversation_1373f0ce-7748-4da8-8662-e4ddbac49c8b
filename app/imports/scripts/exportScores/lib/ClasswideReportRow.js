import ReportRow from "./ReportRow";

export default class ClasswideReportRow extends ReportRow {
  constructor({ studentGroup, teacher, district, school, student, studentGroupEnrollment }) {
    super({ studentGroup, teacher, district, school, student, studentGroupEnrollment });
    this.statsAreCounted = false;
    this.isCurrentSkillInHistory = false;

    this.outputFields["classwide intervention count of progress monitoring scores"] = "N/A";
    this.outputFields["classwide intervention number of skills mastered"] = "N/A";
    this.outputFields["classwide intervention average weeks per skill"] = "N/A";
    this.outputFields["classwide intervention date started"] = "N/A";
    this.outputFields["classwide intervention last score entry date"] = "N/A";
  }

  addClasswideScore = ({ assessmentName, isCurrentSkill, score, whenEndedDate, historyItemIndex }) => {
    if (isCurrentSkill) {
      this.isCurrentSkillInHistory = true;
    }

    this.outputFields[`classwide intervention progress monitoring skill-${historyItemIndex}`] = assessmentName;
    this.outputFields[
      `classwide intervention progress monitoring progress monitoring score-${historyItemIndex}`
    ] = score;
    this.outputFields[`classwide intervention progress monitoring score entry date-${historyItemIndex}`] = whenEndedDate
      ? whenEndedDate.toISOString()
      : whenEndedDate;
  };

  calculateClasswideRowStatistics = () => {
    const numberOfScores = Object.keys(this.outputFields).filter(student => /score-/.test(student)).length;
    this.outputFields["classwide intervention count of progress monitoring scores"] = numberOfScores;

    const skillsFieldsKeys = Object.keys(this.outputFields).filter(student => /skill-/.test(student));
    const skillsNames = [];
    skillsFieldsKeys.forEach(skillFieldKey => {
      const skillName = this.outputFields[skillFieldKey];
      if (!skillsNames.includes(skillName)) {
        skillsNames.push(skillName);
      }
    });
    const numberOfSkillsMastered = skillsNames.length - (this.isCurrentSkillInHistory ? 1 : 0);
    this.outputFields["classwide intervention number of skills mastered"] = numberOfSkillsMastered;

    const classwideProgressKeys = Object.keys(this.outputFields).filter(student =>
      /classwide intervention progress/.test(student)
    );

    // calculating average weeks per skill
    const allSkilllDates = [];
    skillsNames.forEach(skillName => {
      classwideProgressKeys.forEach(classwideProgressKey => {
        if (skillName === this.outputFields[classwideProgressKey]) {
          const classwideProgressDateKey = `classwide intervention progress monitoring score entry date-${
            classwideProgressKey.split("-")[1]
          }`;
          const skillDate = this.outputFields[classwideProgressDateKey];
          allSkilllDates.push(new Date(skillDate).valueOf());
        }
      });
    });

    const maxScoreEntryDate = Math.max(...allSkilllDates);
    const minScoreEntryDate = Math.min(...allSkilllDates);
    const howLongHePracticedSkills = maxScoreEntryDate - minScoreEntryDate;
    const weekInMilliseconds = 7 * 24 * 60 * 60 * 1000;
    const weeksForAllSkill = howLongHePracticedSkills === 0 ? 1 : howLongHePracticedSkills / weekInMilliseconds;
    const averageWeeksPerSkills = parseFloat(weeksForAllSkill / numberOfSkillsMastered).toFixed(1);
    this.outputFields["classwide intervention average weeks per skill"] = averageWeeksPerSkills;
    // eslint-disable-next-line no-restricted-globals
    this.outputFields["classwide intervention date started"] = !isNaN(new Date(minScoreEntryDate).getTime())
      ? new Date(minScoreEntryDate).toISOString()
      : "N/A";
    // eslint-disable-next-line no-restricted-globals
    this.outputFields["classwide intervention last score entry date"] = !isNaN(new Date(maxScoreEntryDate).getTime())
      ? new Date(maxScoreEntryDate).toISOString()
      : "N/A";

    this.statsAreCounted = true;
  };
}
