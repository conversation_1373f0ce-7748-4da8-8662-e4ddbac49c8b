/*
  INTRODUCTION

  This is a script for extracting data from the production database and faking any sensitive data while preserving the relationships between collections for the new dataset.
  This script works for one organization at a time.
  Currently the following collections and properties are scrubbed:
   - Organization
      - _id
      - name
      - details
   - Sites
      - _id
      - orgid
      - stateInformation
      - name
   - BenchmarkWindows
      - _id
      - siteId
      - orgid
   - Students
      - _id
      - orgid
      - demographic
        - birthDate
        - birthDateTimeStamp
      - identity
        - name
          - firstName
          - lastName
          - middleName
        - identification
          - localId
          - stateId
      - history
      - currentSkill
        - assessmentResultId
   - StudentGroups
      - _id
      - orgid
      - siteId
      - ownerIds
      - courseId
      - sectionId
      - name
      - history
      - currentAssessmentResultIds
      - currentClasswideSkill
        - assessmentResultId
   - StudentGroupEnrollments
      - _id
      - orgid
      - siteId
      - studentGroupId
      - studentId
   - AssessmentResults
      - _id
      - orgid
      - siteId
      - studentId
      - classwideResults
        - studentIdsNotMeetingTargets
      - measures
        - studentResults
          - studentId
          - firstName
          - lastName

   Data is scrubbed using mongod<PERSON>'s id generator, fakerjs for personal data and arbitrary values for other properties

   USAGE

   1. Run npm install in the schoolScrubber directory
   2. Set env variables
      a) MONGO_PROD_URL -- this is where data is pulled from
      b) MONGO_TARGET_URL -- this is where scrubbed data is inserted
      c) SCHOOL_YEAR
   3. Run the script with the following command
      npm start -- --orgId=YOUR_ORG_ID --targetOrgId=TARGET_ORG_ID
   4. The data can be accessed with a newly created demo user.
      - Use <NAME_EMAIL>
      - Use the same password as for other users used for development

   Note: The script will exit upon error
 */

/* eslint-disable no-await-in-loop */

import { cloneDeep, flatMapDepth, uniq } from "lodash";
import { USER } from "./constants";
import {
  scrubAssessmentResults,
  scrubBenchmarkWindows,
  scrubGroupsWithoutAssessmentResultsDependencies,
  anonymizeSites,
  scrubStudentGroupEnrollments,
  scrubStudentGroupPropertiesDependentOnAssessmentResults,
  scrubStudentPropertiesDependentOnAssessmentResults,
  scrubStudentsWithoutAssessmentResultsDependencies,
  removeHelperKeysFor,
  anonymizeUsers
} from "./utils/anonymizationHelpers";

const { MongoClient } = require("mongodb");
const { ObjectId } = require("mongodb");

const createOrg = (org, isCopyMode) => {
  const newOrg = cloneDeep(org);
  newOrg._id = "LyGDeCXKYkoiB9SCW";
  newOrg.name = `Demo Data Template`;
  if (!isCopyMode) {
    newOrg._id = new ObjectId().toString();
    newOrg.name = `DEMO_${getDate()}`;
    newOrg.details = { primaryContact: {} };
  }
  return newOrg;
};

const createUserFor = (orgid, sites, existingUser, schoolYears) => {
  const user = cloneDeep(existingUser || USER);
  user.profile.orgid = orgid;
  // TODO(fmazur) - all newly created users are admins?
  user.profile.siteAccess = addSiteAccessFor(sites, true, schoolYears);
  return user;
};

function addSiteAccessFor(sites, isAdmin = true, schoolYears) {
  return schoolYears.map(schoolYear =>
    sites.map(site => ({
      role: isAdmin ? "arbitraryIdadmin" : "arbitraryIdteacher",
      siteId: site._id,
      schoolYear,
      isActive: true,
      isDefault: true
    }))
  );
}

function getDate() {
  const date = new Date();
  return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
}

function createStudentsBySkill(sites, groupedAssessments) {
  const studentsBySiteTemplate = {
    studentsBelowInstructionalTarget: [],
    studentsBelowMasteryTarget: [],
    studentsWithoutSkillHistory: []
  };
  const documentsToInsert = [];
  sites.forEach(site => {
    groupedAssessments.forEach(ga => {
      const studentBySite = {
        ...studentsBySiteTemplate,
        _id: new ObjectId().toString(),
        skillName: ga.skillName,
        assessmentGroupId: ga._id,
        siteId: site._id
      };
      documentsToInsert.push(studentBySite);
    });
  });
  return documentsToInsert;
}

async function populateStudentsBySkill({
  studentsWithIndividualInterventions,
  uploadAssessmentsDb: assessmentsDb,
  uploadGroupedAssessmentsDb: groupedAssessmentsDb,
  uploadStudentsBySkillDb: studentsBySkillDb, // This handle is for db that you want to upload to
  uploadStudentGroupEnrollmentsDb: studentGroupEnrollmentsDb,
  schoolYear
}) {
  // eslint-disable-next-line no-restricted-syntax
  for (const student of studentsWithIndividualInterventions) {
    if (student.currentSkill.assessmentId) {
      const sge = await studentGroupEnrollmentsDb.findOne({
        studentId: student._id,
        schoolYear,
        isActive: true
      });

      const assessment = await assessmentsDb.findOne({ _id: student.currentSkill.assessmentId });
      if (assessment && sge) {
        const assessmentGroup = await groupedAssessmentsDb.findOne({
          assessmentMeasures: assessment.monitorAssessmentMeasure
        });
        if (assessmentGroup) {
          if (!student.history || !student.history.length) {
            await studentsBySkillDb.updateOne(
              {
                siteId: sge.siteId,
                assessmentGroupId: assessmentGroup._id
              },
              { $addToSet: { studentsWithoutSkillHistory: student._id } }
            );
          } else {
            const latestHistoryEntry = student.history[0];
            const updateQuery = { assessmentGroupId: assessmentGroup._id, siteId: sge.siteId };
            if (latestHistoryEntry.assessmentId === student.currentSkill.assessmentId) {
              const latestScore = parseInt(latestHistoryEntry.assessmentResultMeasures[0].studentResults[0].score);
              const scoreTargets = latestHistoryEntry.assessmentResultMeasures[0].targetScores;
              if (latestScore < scoreTargets[0]) {
                await studentsBySkillDb.updateOne(updateQuery, {
                  $addToSet: { studentsBelowInstructionalTarget: student._id }
                });
              } else if (latestScore < scoreTargets[1]) {
                await studentsBySkillDb.updateOne(updateQuery, {
                  $addToSet: { studentsBelowMasteryTarget: student._id }
                });
              }
            } else {
              await studentsBySkillDb.updateOne(updateQuery, {
                $addToSet: { studentsWithoutSkillHistory: student._id }
              });
            }
          }
        }
      }
    }
  }
}

export async function runSchoolScrubber({
  targetOrgId,
  shouldCreateNewSites = true,
  schoolYear = 2024,
  siteIds,
  shouldMergeSites = false,
  shouldAnonymize = false,
  shouldNotIncludeSelectedSchoolYear = false,
  isCopyMode = false
}) {
  if (isCopyMode) {
    // eslint-disable-next-line no-param-reassign
    targetOrgId = "";
  }
  const sourceOrgId = Meteor.settings.public.DATA_COPY_SOURCE_ORG_ID;

  if (!sourceOrgId) {
    throw new Error("No source organization ID specified");
  }

  if (sourceOrgId === targetOrgId) {
    throw new Error("Cannot scrub data within the same organization");
  }

  console.log(
    `Start copying historical data from org: ${sourceOrgId}, school year: ${schoolYear} to org: ${targetOrgId}`
  );

  // const SOURCE_MONGO_URL = Meteor.settings.DATA_COPY_MONGO_URL;
  const SOURCE_MONGO_URL = process.env.MONGO_URL;
  const sourceDbOptions = { useUnifiedTopology: true };
  if (SOURCE_MONGO_URL && (SOURCE_MONGO_URL.includes("ssl=true") || SOURCE_MONGO_URL.includes("tls=true"))) {
    sourceDbOptions.tls = true;
    sourceDbOptions.tlsInsecure = true;
  }
  const sourceDbConnection = await MongoClient.connect(SOURCE_MONGO_URL, sourceDbOptions);
  const sourceDb = sourceDbConnection.db(sourceDbConnection.s.databaseName);

  const organizationDb = sourceDb.collection("Organizations");

  const organization = await organizationDb.findOne({ _id: sourceOrgId });

  const assessmentResultsDb = sourceDb.collection("AssessmentResults");
  const benchmarkWindowsDb = sourceDb.collection("BenchmarkWindows");
  const studentGroupEnrollmentsDb = sourceDb.collection("StudentGroupEnrollments");
  const studentGroupsDb = sourceDb.collection("StudentGroups");
  const studentsDb = sourceDb.collection("Students");
  const sitesDb = sourceDb.collection("Sites");
  const usersDb = sourceDb.collection("users");

  const query = {
    orgid: sourceOrgId,
    schoolYear: shouldNotIncludeSelectedSchoolYear ? { $ne: schoolYear } : schoolYear
  };
  let assessmentResults = await assessmentResultsDb.find(query).toArray();
  let benchmarkWindows = await benchmarkWindowsDb.find(query).toArray();
  let studentGroupEnrollments = await studentGroupEnrollmentsDb
    .find(siteIds ? { ...query, siteId: { $in: siteIds } } : query)
    .toArray();
  let studentGroups = await studentGroupsDb.find(siteIds ? { ...query, siteId: { $in: siteIds } } : query).toArray();

  const owners = uniq(
    flatMapDepth(
      studentGroups,
      sg => {
        return sg.secondaryTeachers ? [sg.ownerIds, sg.secondaryTeachers] : [sg.ownerIds];
      },
      2
    )
  );
  const studentIds = Array.from(new Set(studentGroupEnrollments.map(sge => sge.studentId)));
  const students = await studentsDb.find({ ...query, _id: { $in: studentIds } }).toArray();
  const users = await usersDb
    .find({
      _id: { $in: owners }
    })
    .toArray();

  let sitesFilter = { orgid: sourceOrgId };
  if (siteIds && siteIds.length) {
    sitesFilter = { orgid: sourceOrgId, _id: { $in: siteIds } };
  }
  let sites = await sitesDb.find(sitesFilter).toArray();
  let oldSiteIds = sites.map(site => site._id);

  if (shouldMergeSites) {
    sites = [sites[0]];
    oldSiteIds = [oldSiteIds[0]];
    assessmentResults = assessmentResults.map(am => ({
      ...am,
      scores: am.scores.map(score => ({ ...score, siteId: oldSiteIds[0] }))
    }));
    benchmarkWindows = benchmarkWindows.map(bw => ({ ...bw, siteId: oldSiteIds[0] }));
    studentGroupEnrollments = studentGroupEnrollments.map(sge => ({ ...sge, siteId: oldSiteIds[0] }));
    studentGroups = studentGroups.map(sg => ({ ...sg, siteId: oldSiteIds[0] }));
  }
  console.log("assessmentResults.length: ", assessmentResults.length);
  console.log("benchmarkWindows.length: ", benchmarkWindows.length);
  console.log("studentGroupEnrollments.length: ", studentGroupEnrollments.length);
  console.log("studentGroups.length: ", studentGroups.length);
  console.log("students.length: ", students.length);
  console.log("users.length: ", users.length);

  await sourceDbConnection.close();

  // NOTE(fmazur) - change to mongodb uri if targeting different environment, remember to update targetOrgId as well
  let TARGET_MONGO_URL = process.env.MONGO_URL;
  if (isCopyMode) {
    TARGET_MONGO_URL = "mongodb://localhost:27017/meteor";
  }
  const targetDbOptions = { useUnifiedTopology: true };
  if (TARGET_MONGO_URL && (TARGET_MONGO_URL.includes("ssl=true") || TARGET_MONGO_URL.includes("tls=true"))) {
    targetDbOptions.tls = true;
    targetDbOptions.tlsInsecure = true;
  }
  const targetDbConnection = await MongoClient.connect(TARGET_MONGO_URL, targetDbOptions);
  const targetDb = targetDbConnection.db(targetDbConnection.s.databaseName);

  const uploadOrganizationDb = targetDb.collection("Organizations");
  const uploadSitesDb = targetDb.collection("Sites");
  const uploadUsersDb = targetDb.collection("users");

  let targetOrg;
  if (targetOrgId) {
    targetOrg = await uploadOrganizationDb.findOne({ _id: targetOrgId });
    const sourceSiteSchoolNumbers = sites.map(({ stateInformation }) => stateInformation.schoolNumber);
    if (!shouldCreateNewSites) {
      sites = await uploadSitesDb.find({ orgid: targetOrgId }).toArray();
    }
    const existingTargetSites = await uploadSitesDb
      .find({ orgid: targetOrgId, "stateInformation.schoolNumber": { $in: sourceSiteSchoolNumbers } })
      .toArray();
    if (existingTargetSites.length) {
      throw new Error("Historical data already copied");
    }
  } else {
    targetOrg = createOrg(organization, isCopyMode);
  }
  const newOrgId = targetOrg._id;
  let anonymizedSites;
  if (targetOrgId && !shouldCreateNewSites) {
    anonymizedSites = sites.map((site, index) => ({ ...site, bak_id: oldSiteIds[index] }));
  } else {
    if (shouldAnonymize) {
      console.log("Anonymizing sites...");
    } else {
      console.log("Processing sites...");
    }
    const sitesInTargetOrg = await uploadSitesDb.find({ orgid: targetOrgId }).toArray();
    anonymizedSites = anonymizeSites({ sites, newOrgId, shouldAnonymize, targetOrg, sitesInTargetOrg });
  }
  console.log("Scrubbing benchmark windows...");
  const anonymizedBenchmarkWindows = scrubBenchmarkWindows(benchmarkWindows, anonymizedSites);
  console.log("Scrubbing assessments...");
  const anonymizedStudentsWithoutAssessmentResultsDependencies = scrubStudentsWithoutAssessmentResultsDependencies({
    students,
    newOrgId,
    shouldAnonymize
  });
  if (shouldAnonymize) {
    console.log("Anonymizing users...");
  } else {
    console.log("Processing users...");
  }
  const anonymizedUsers = anonymizeUsers({
    users,
    orgid: newOrgId,
    schoolYear,
    anonymizedSites,
    shouldAnonymize,
    shouldNotIncludeSelectedSchoolYear
  });
  console.log("Scrubbing student groups...");
  const anonymizedGroupsWithoutAssessmentResultsDependencies = scrubGroupsWithoutAssessmentResultsDependencies({
    groups: studentGroups,
    anonymizedUsers,
    anonymizedSites,
    shouldAnonymize
  });
  console.log("Scrubbing assessments...");
  const anonymizedStudentGroupEnrollments = scrubStudentGroupEnrollments(
    studentGroupEnrollments,
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedGroupsWithoutAssessmentResultsDependencies
  );
  console.log("Scrubbing assessment results...");
  const anonymizedAssessmentResults = scrubAssessmentResults(
    assessmentResults,
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedGroupsWithoutAssessmentResultsDependencies
  );
  console.log("Finishing scrubbing data...");
  const completelyAnonymizedGroups = scrubStudentGroupPropertiesDependentOnAssessmentResults(
    anonymizedGroupsWithoutAssessmentResultsDependencies,
    anonymizedAssessmentResults,
    anonymizedStudentsWithoutAssessmentResultsDependencies
  );
  const completelyAnonymizedStudents = scrubStudentPropertiesDependentOnAssessmentResults(
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedAssessmentResults
  );
  console.log("Removing helper keys...");

  const studentsWithoutHelperKeys = removeHelperKeysFor(completelyAnonymizedStudents);
  const groupsWithoutHelperKeys = removeHelperKeysFor(completelyAnonymizedGroups);
  const assessmentResultsWithoutHelperKeys = removeHelperKeysFor(anonymizedAssessmentResults);
  const sitesWithoutHelperKeys = removeHelperKeysFor(anonymizedSites);
  const studentGroupEnrollmentsWithoutHelperKeys = removeHelperKeysFor(anonymizedStudentGroupEnrollments);
  const usersWithoutHelperKeys = removeHelperKeysFor(anonymizedUsers);
  const uploadAssessmentResultsDb = targetDb.collection("AssessmentResults");
  const uploadBenchmarkWindowsDb = targetDb.collection("BenchmarkWindows");
  const uploadStudentGroupEnrollmentsDb = targetDb.collection("StudentGroupEnrollments");
  const uploadStudentGroupsDb = targetDb.collection("StudentGroups");
  const uploadStudentsDb = targetDb.collection("Students");
  const uploadStudentsBySkillDb = targetDb.collection("StudentsBySkill");
  const uploadAssessmentsDb = targetDb.collection("Assessments");
  const uploadGroupedAssessmentsDb = targetDb.collection("GroupedAssessments");
  const uploadGroupedAssessments = await uploadGroupedAssessmentsDb.find().toArray();

  console.log("Generating studentsBySkill collection...");

  const studentsBySkill = createStudentsBySkill(sitesWithoutHelperKeys, uploadGroupedAssessments);

  console.log("Creating users...");
  // TODO(fmazur) - do we need to move all admin users?
  // TODO(fmazur) - what about multi role users eg data admin and a coach
  // TODO(fmazur) - to be implemented in 50248
  const existingAdminUser = await uploadUsersDb.findOne({
    "profile.orgid": newOrgId,
    "profile.siteAccess.role": "arbitraryIdadmin"
  });
  let schoolYears = [schoolYear];
  if (shouldNotIncludeSelectedSchoolYear) {
    schoolYears = Array.from(new Set(anonymizedStudentGroupEnrollments.map(sge => sge.schoolYear)));
  }
  const user = createUserFor(newOrgId, anonymizedSites, existingAdminUser, schoolYears);
  if (!targetOrgId) {
    await uploadUsersDb.findOneAndDelete({ _id: user._id });
  } else {
    await uploadUsersDb.updateOne(
      { _id: user._id, "profile.orgid": newOrgId },
      { $push: { "profile.siteAccess": { $each: user.profile.siteAccess } } }
    );
  }

  try {
    if (assessmentResultsWithoutHelperKeys.length) {
      console.log("Inserting assessments...");
      await uploadAssessmentResultsDb.insertMany(assessmentResultsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadAssessmentResultsDb failed:", error);
    throw new Error(error);
  }
  try {
    if (anonymizedBenchmarkWindows.length) {
      console.log("Inserting benchmark windows...");
      await uploadBenchmarkWindowsDb.insertMany(anonymizedBenchmarkWindows);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadBenchmarkWindowsDb failed:", error);
    throw new Error(error);
  }
  try {
    if (studentGroupEnrollmentsWithoutHelperKeys.length) {
      console.log("Inserting student group enrollments...");
      await uploadStudentGroupEnrollmentsDb.insertMany(studentGroupEnrollmentsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentGroupEnrollmentsDb failed:", error);
    throw new Error(error);
  }
  try {
    if (groupsWithoutHelperKeys.length) {
      console.log("Inserting student groups...");
      await uploadStudentGroupsDb.insertMany(groupsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentGroupsDb failed:", error);
    throw new Error(error);
  }
  try {
    if (studentsWithoutHelperKeys.length) {
      console.log("Inserting students...");
      await uploadStudentsDb.insertMany(studentsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentsDb failed:", error);
    throw new Error(error);
  }
  if (targetOrgId && shouldCreateNewSites) {
    try {
      if (sitesWithoutHelperKeys.length) {
        console.log("Inserting sites...");
        const sitesInsert = await uploadSitesDb.insertMany(sitesWithoutHelperKeys);
        console.log(`Site inserted: `, sitesInsert);
      }
    } catch (error) {
      console.log("Inserting scrubbed data to uploadSitesDb failed:", error);
      throw new Error(error);
    }
    try {
      if (usersWithoutHelperKeys.length) {
        console.log("Inserting users...");
        await uploadUsersDb.insertMany([...usersWithoutHelperKeys]);
      }
    } catch (error) {
      console.log("Inserting scrubbed data to uploadUsersDb failed:", error);
      throw new Error(error);
    }
  }
  if (!targetOrgId) {
    try {
      if (sitesWithoutHelperKeys.length) {
        console.log("Inserting sites...");
        await uploadSitesDb.insertMany(sitesWithoutHelperKeys);
      }
    } catch (error) {
      console.log("Inserting scrubbed data to uploadSitesDb failed:", error);
      throw new Error(error);
    }
    try {
      console.log("Inserting users...");
      await uploadUsersDb.insertMany([user, ...usersWithoutHelperKeys]);
    } catch (error) {
      console.log("Inserting scrubbed data to uploadUsersDb failed:", error);
      throw new Error(error);
    }
    try {
      console.log("Inserting organizations...");
      await uploadOrganizationDb.insertOne(targetOrg);
    } catch (error) {
      console.log("Inserting scrubbed data to uploadOrganizationDb failed:", error);
      throw new Error(error);
    }
  }

  if (targetOrgId && !shouldCreateNewSites) {
    const targetExistingSites = await uploadSitesDb.find({ orgid: targetOrgId }).toArray();
    const sitesUpdatePromises = [];
    targetExistingSites.forEach(site => {
      if (site.schoolYear < schoolYear) {
        sitesUpdatePromises.push(
          new Promise(resolve => {
            uploadSitesDb.updateOne({ _id: site._id }, { $set: { schoolYear } });
            resolve();
          })
        );
      }
    });

    await Promise.all(sitesUpdatePromises);
  }

  try {
    console.log("Populating students by skill...");
    if (studentsBySkill.length && Math.max(...schoolYears) > schoolYear) {
      await uploadStudentsBySkillDb.insertMany(studentsBySkill);
      const studentsWithIndividualInterventions = await uploadStudentsDb
        .find({
          orgid: newOrgId,
          currentSkill: { $exists: true }
        })
        .toArray();
      await populateStudentsBySkill({
        studentsWithIndividualInterventions,
        uploadAssessmentsDb,
        uploadGroupedAssessmentsDb,
        uploadStudentsBySkillDb,
        uploadStudentGroupEnrollmentsDb,
        schoolYear
      });
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadOrganizationDb failed:", error);
    throw new Error(error);
  }

  console.log("Successfully inserted scrubbed data to target DB");

  await targetDbConnection.close();
  return true;
}
