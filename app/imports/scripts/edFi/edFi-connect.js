import { zipObject } from "lodash";

const axios = require("axios");
const random = require("lodash/random");
const get = require("lodash/get");

export function EdFiConnectionException(statusCode, statusMessage) {
  this.statusCode = statusCode;
  this.statusMessage = statusMessage;
  this.name = "EdFiConnectionException";
  return `${statusCode}, ${statusMessage}, EdFiConnectionException`;
}

export async function getAuthToken({ apiUrl, clientId, clientSecret }) {
  const authInstance = axios.create({
    baseURL: apiUrl,
    timeout: 60000,
    auth: {
      username: clientId,
      password: clientSecret
    },
    headers: {
      "Content-Type": "application/json"
    }
  });
  const {
    data: { access_token: accessToken }
  } = await authInstance
    .post("/oauth/token", {
      grant_type: "client_credentials"
    })
    .catch(({ response = {}, code, message }) => {
      throw new EdFiConnectionException(response.status || code, response.statusText || message);
    });

  return accessToken;
}

async function edFiGetRequest({
  edfiInstance,
  itemsToFetch,
  limit = 500,
  offset = 0,
  useTotalCount = false,
  specificFieldQuery = "",
  useComposites = false
}) {
  const compositeLimit = 100;
  if (useComposites) {
    let isDataFetched = false;
    let compositeOffset = 0;
    const requestResults = [];
    while (!isDataFetched) {
      const url = `${itemsToFetch}?limit=${compositeLimit}&offset=${compositeOffset}`;
      // eslint-disable-next-line no-await-in-loop
      const { data } = (await edfiInstance.get(url)) || [];
      requestResults.push(...data);
      isDataFetched = data?.length < compositeLimit;
      compositeOffset += compositeLimit;
    }
    return requestResults;
  }
  return new Promise(resolve => {
    setTimeout(() => {
      const url = `${itemsToFetch}?limit=${limit}&offset=${offset}&totalCount=${useTotalCount}${specificFieldQuery}`;
      const request = edfiInstance.get(url);
      resolve(request);
    }, random(100, 400, false));
  });
}

async function fetchDocumentsFromEndpoint({
  edfiInstance,
  itemsToFetch,
  specificFieldQuery = "",
  fetchAll = true,
  limit
}) {
  const promises = [];
  const requestResults = [];
  const shouldUseTotalCount = ![
    "staffs",
    "students",
    "studentSectionAssociations",
    "staffSectionAssociations"
  ].includes(itemsToFetch);
  const initialRecords = await edFiGetRequest({
    edfiInstance,
    itemsToFetch,
    limit,
    specificFieldQuery,
    useTotalCount: shouldUseTotalCount
  });
  if (!fetchAll && limit === 1) {
    const resultWithStatus = {
      ...initialRecords.data[0],
      status: initialRecords.status,
      statusText: initialRecords.statusText
    };
    requestResults.push(resultWithStatus);
  } else {
    const { data } = initialRecords;
    const results = Array.isArray(data) ? data : [data];
    requestResults.push(...results);
  }
  const totalCount = fetchAll ? initialRecords.headers["total-count"] : limit;
  if (initialRecords.data?.length >= limit) {
    for (let i = limit; i < totalCount; i += limit) {
      promises.push(
        edFiGetRequest({
          edfiInstance,
          itemsToFetch,
          offset: i,
          limit,
          specificFieldQuery
        })
      );
    }
  }

  return { promises, requestResults };
}

const recursiveObjectPromiseAll = obj => {
  const keys = Object.keys(obj);
  return Promise.all(
    keys.map(key => {
      const value = obj[key];
      if (typeof value === "object" && !value.then) {
        return recursiveObjectPromiseAll(value);
      }
      return value;
    })
  ).then(result => zipObject(keys, result));
};

export async function getEdFiCompositeData({
  apiUrl,
  clientId,
  clientSecret,
  schoolYear = "",
  itemsToFetch,
  limit = 500,
  query = {},
  timeout = 60000,
  temporaryFilters = {},
  useComposites
}) {
  const accessToken = await getAuthToken({ apiUrl, clientId, clientSecret });
  const schoolYearPath = schoolYear ? `${schoolYear}/` : "";
  const edfiInstance = axios.create({
    baseURL: `${apiUrl}/composites/v1/${schoolYearPath}ed-fi/`,
    timeout,
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  const compositeNameByItemsToFetch = {
    students: "sections",
    // students: "schools",
    staffs: "schools",
    sections: "staffs"
  };

  const requestPromises = [];
  get(Object.values(temporaryFilters), 0, []).forEach(itemId => {
    requestPromises[itemId] = edFiGetRequest({
      edfiInstance,
      limit,
      itemsToFetch: `enrollment/${compositeNameByItemsToFetch[itemsToFetch]}/${itemId}/${itemsToFetch}`,
      specificFieldQuery: Object.keys(query).length ? query : "",
      useTotalCount: false,
      useComposites
    });
  });
  const resp = await recursiveObjectPromiseAll(requestPromises);
  const dataResult = Object.entries(resp)
    .map(([k, r]) => (r.data ? r.data : r).map(val => ({ ...val, ancestorItemId: k })))
    .flat(2);
  return { data: dataResult };
}

export async function getEdFiData({
  apiUrl,
  clientId,
  clientSecret,
  schoolYear = "",
  itemsToFetch,
  query = {},
  timeout = 60000,
  fetchAll,
  limit = 500
}) {
  const requestResults = [];
  try {
    const accessToken = await getAuthToken({ apiUrl, clientId, clientSecret });
    const schoolYearPath = schoolYear ? `${schoolYear}/` : "";
    const edfiInstance = axios.create({
      baseURL: `${apiUrl}/data/v3/${schoolYearPath}ed-fi/`,
      timeout,
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    const promises = [];

    // Forbid from using more than one query fields
    if (Object.keys(query).length === 1) {
      const fetchAllAvailableDocumentsCalls = [];
      Object.entries(query).forEach(([key, values]) => {
        values.forEach(value => {
          const specificFieldQuery = `&${key}=${value}`;
          fetchAllAvailableDocumentsCalls.push(
            fetchDocumentsFromEndpoint({
              edfiInstance,
              itemsToFetch,
              specificFieldQuery,
              limit,
              fetchAll
            })
          );
        });
      });
      const fetchAllAvailableDocumentsCallsResults = await Promise.all(fetchAllAvailableDocumentsCalls);
      promises.push(...fetchAllAvailableDocumentsCallsResults.map(o => o.promises).flat());
      requestResults.push(...fetchAllAvailableDocumentsCallsResults.map(o => o.requestResults).flat());
    } else {
      const fetchedDocuments = await fetchDocumentsFromEndpoint({ edfiInstance, itemsToFetch, fetchAll, limit });
      promises.push(...fetchedDocuments.promises);
      requestResults.push(...fetchedDocuments.requestResults);
    }

    const results = await Promise.all(promises);

    if (results?.length) {
      requestResults.push(...results.map(r => r.data).flat());
    }
  } catch (e) {
    const { response = {}, code, message } = e;
    throw new EdFiConnectionException(response.status || code, response.statusText || message);
  }

  return { data: requestResults };
}

export async function testEdFiConnection({ apiUrl, clientId, clientSecret, schoolYear = "" }) {
  const { data } = await getEdFiData({
    apiUrl,
    clientId,
    clientSecret,
    schoolYear,
    itemsToFetch: "schools",
    fetchAll: false,
    limit: 1
  });
  const { status, statusText } = data[0];
  const numberOfItems = data.length;

  return {
    numberOfItems,
    statusCode: status,
    statusMessage: statusText,
    ...(!numberOfItems && { statusCode: 1 })
  };
}
