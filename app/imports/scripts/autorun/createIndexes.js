import { Meteor } from "meteor/meteor";

import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { Assessments } from "/imports/api/assessments/assessments";
import { AuditLogs } from "/imports/api/auditLogs/auditLogs";
import { BenchmarkWindows } from "/imports/api/benchmarkWindows/benchmarkWindows";
import { GroupedAssessments } from "/imports/api/groupedAssessments/groupedAssessments";
import { News } from "/imports/api/news/news";
import { Organizations } from "/imports/api/organizations/organizations";
import { RosterImportItems } from "/imports/api/rosterImportItems/rosterImportItems";
import { RosterImports } from "/imports/api/rosterImports/rosterImports";
import { Rules } from "/imports/api/rules/rules";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Sites } from "/imports/api/sites/sites";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Students } from "/imports/api/students/students";
import { StudentsBySkill } from "/imports/api/studentsBySkill/studentsBySkill";
import { Users as users } from "/imports/api/users/users";

Meteor.startup(() => {
  console.log("Ensuring necessary collection indexes exist");

  AssessmentGrowth.createIndex({ grade: 1 }, { name: "grade_1", background: true });
  AssessmentResults.createIndex(
    { orgid: 1, type: 1, schoolYear: 1, status: 1 },
    { name: "status_OPEN_orgid_1_type_1_schoolYear_1", background: true, partialFilterExpression: { status: "OPEN" } }
  );
  AssessmentResults.createIndex(
    { studentGroupId: 1, benchmarkPeriodId: 1, type: 1 },
    {
      name: "type_benchmark_studentGroupId_1_benchmarkPeriodId_1",
      background: true,
      partialFilterExpression: { type: "benchmark" }
    }
  );
  AssessmentResults.createIndex({ studentGroupId: 1 }, { name: "studentGroupId_1", background: true });
  AssessmentResults.createIndex(
    { schoolYear: 1, status: 1, "measures.studentResults.studentId": 1, type: 1, studentGroupId: 1 },
    { name: "schoolYear_1_status_1_measures.studentResults.studentId_1_type_1_studentGroupId_1", background: true }
  );
  AssessmentResults.createIndex(
    { schoolYear: 1, "measures.percentMeetingTarget": 1 },
    { name: "schoolYear_1_measures.percentMeetingTarget_1", background: true }
  );
  AssessmentResults.createIndex({ schoolYear: 1 }, { name: "schoolYear_1", background: true });
  AssessmentResults.createIndex(
    { previousAssessmentResultId: 1, studentId: 1 },
    { name: "previousAssessmentResultId_1_studentId_1", background: true }
  );
  AssessmentResults.createIndex(
    { schoolYear: 1, benchmarkPeriodId: 1, orgid: 1, type: 1, status: 1, studentGroupId: 1, _id: 1 },
    {
      name: "type_benchmark_schoolYear_1_benchmarkPeriodId_1_orgid_1_type_1_status_1_studentGroupId_1__id_1",
      background: true,
      partialFilterExpression: { type: "benchmark" }
    }
  );
  AssessmentResults.createIndex(
    { type: 1, schoolYear: 1, grade: 1, studentGroupId: 1 },
    { name: "type_1_schoolYear_1_grade_1_studentGroupId_1", background: true }
  );
  AssessmentResults.createIndex(
    { type: 1, schoolYear: 1, status: 1, studentId: 1 },
    { name: "type_1_schoolYear_1_status_1_studentId_1", background: true }
  );
  AssessmentResults.createIndex({ studentId: 1 }, { name: "studentId_1", background: true });
  AssessmentResults.createIndex(
    { schoolYear: 1, grade: 1, benchmarkPeriodId: 1, status: 1, type: 1, orgid: 1 },
    { name: "schoolYear_1_grade_1_benchmarkPeriodId_1_status_1_type_1_orgid_1", background: true }
  );
  AssessmentResults.createIndex({ orgid: 1, schoolYear: 1 }, { name: "orgid_1_schoolYear_1", background: true });
  AssessmentResults.createIndex(
    { schoolYear: 1, status: 1, studentGroupId: 1 },
    { name: "schoolYear_1_status_1_studentGroupId_1", background: true }
  );
  Assessments.createIndex({ associatedGrades: 1 }, { name: "associatedGrades_1", background: true });
  Assessments.createIndex({ monitorAssessmentMeasure: 1 }, { name: "monitorAssessmentMeasure_1" });
  AuditLogs.createIndex({ orgid: 1 }, { name: "orgid_1" });
  BenchmarkWindows.createIndex(
    { siteId: 1, benchmarkPeriodId: 1 },
    { name: "siteId_1_benchmarkPeriodId_1", background: true }
  );
  GroupedAssessments.createIndex({ assessmentMeasures: 1 }, { name: "assessmentMeasures_1" });
  News.createIndex({ messageActive: 1, type: 1 }, { name: "messageActive_1_type_1" });
  Organizations.createIndex({ "details.state": 1 }, { name: "details.state_1", background: true });
  RosterImportItems.createIndex({ orgid: 1 }, { name: "orgid_1", background: true });
  RosterImportItems.createIndex({ rosterImportId: 1 }, { name: "rosterImportId_1" });
  RosterImportItems.createIndex({ "data.teacherEmail": 1 }, { name: "data.teacherEmail_1", background: true });
  RosterImports.createIndex({ orgid: 1, "started.date": 1 }, { name: "orgid_1_started.date_1", background: true });
  Rules.createIndex(
    { "attributeValues.assessmentId": 1, "attributeValues.benchmarkPeriod": 1, "attributeValues.grade": 1 },
    {
      name: "attributeValues.assessmentId_1_attributeValues.benchmarkPeriod_1_attributeValues.grade_1",
      background: true
    }
  );
  Rules.createIndex({ grade: 1 }, { name: "grade_1", background: true });
  Rules.createIndex({ "prerequisite.ruleId": 1 }, { name: "prerequisite.ruleId_1", background: true });
  Rules.createIndex({ "attributeValues.grade": 1 }, { name: "attributeValues.grade_1", background: true });
  ScreeningAssignments.createIndex(
    { grade: 1, benchmarkPeriodId: 1 },
    { name: "grade_1_benchmarkPeriodId_1", background: true }
  );
  Sites.createIndex({ orgid: 1 }, { name: "orgid_1", background: true });
  StudentGroupEnrollments.createIndex(
    { siteId: 1, isActive: 1, schoolYear: 1, orgid: 1 },
    { name: "siteId_1_isActive_1_schoolYear_1_orgid_1", background: true }
  );
  StudentGroupEnrollments.createIndex(
    { schoolYear: 1, studentGroupId: 1, studentId: 1 },
    { name: "schoolYear_1_studentGroupId_1_studentId_1", background: true }
  );
  StudentGroupEnrollments.createIndex(
    { studentGroupId: 1, isActive: 1 },
    { name: "studentGroupId_1_isActive_1", background: true }
  );
  StudentGroupEnrollments.createIndex(
    { studentId: 1, isActive: 1 },
    { name: "studentId_1_isActive_1", background: true }
  );
  StudentGroupEnrollments.createIndex(
    { orgid: 1, schoolYear: 1, isActive: 1, studentId: 1 },
    { name: "orgid_1_schoolYear_1_isActive_1_studentId_1", background: true }
  );
  StudentGroupEnrollments.createIndex(
    { schoolYear: 1, isActive: 1, orgid: 1 },
    { name: "schoolYear_1_isActive_1_orgid_1", background: true }
  );
  StudentGroups.createIndex({ siteId: 1 }, { name: "siteId_1", background: true });
  StudentGroups.createIndex(
    { orgid: 1, schoolYear: 1, isActive: 1, siteId: 1 },
    { name: "orgid_1_schoolYear_1_isActive_1_siteId_1", background: true }
  );
  StudentGroups.createIndex(
    { schoolYear: 1, siteId: 1, orgid: 1, grade: 1 },
    { name: "schoolYear_1_siteId_1_orgid_1_grade_1", background: true }
  );
  StudentGroups.createIndex(
    { schoolYear: 1, sectionId: 1, siteId: 1, orgid: 1 },
    { name: "schoolYear_1_sectionId_1_siteId_1_orgid_1", background: true }
  );
  StudentGroups.createIndex(
    { orgid: 1, siteId: 1, grade: 1, "history.0.enrolledStudentIds": 1, isActive: 1 },
    { name: "orgid_1_siteId_1_grade_1_history.0.enrolledStudentIds_1_isActive_1", background: true }
  );
  StudentGroups.createIndex({ orgid: 1, siteId: 1, grade: 1 }, { name: "orgid_1_siteId_1_grade_1", background: true });
  StudentGroups.createIndex(
    { sectionId: 1, orgid: 1, siteId: 1 },
    { name: "sectionId_1_orgid_1_siteId_1", background: true }
  );
  StudentGroups.createIndex(
    { schoolYear: 1, isActive: 1, ownerIds: 1 },
    { name: "schoolYear_1_isActive_1_ownerIds_1", background: true }
  );
  StudentGroups.createIndex({ ownerIds: 1 }, { name: "ownerIds_1", background: true });
  StudentGroups.createIndex({ secondaryTeachers: 1 }, { name: "secondaryTeachers_1", background: true });
  StudentGroups.createIndex(
    { schoolYear: 1, isActive: 1, secondaryTeachers: 1 },
    { name: "schoolYear_1_isActive_1_secondaryTeachers_1", background: true }
  );
  StudentGroups.createIndex(
    { isActive: 1, schoolYear: 1, siteId: 1, _id: 1 },
    { name: "isActive_1_schoolYear_1_siteId_1__id_1", background: true }
  );
  StudentGroups.createIndex(
    { orgid: 1, isActive: 1, schoolYear: 1, grade: 1, _id: 1 },
    { name: "orgid_1_isActive_1_schoolYear_1_grade_1__id_1" }
  );
  Students.createIndex({ orgid: 1, fileUploadId: 1 }, { name: "orgid_1_fileUploadId_1", background: true });
  Students.createIndex({ "demographic.gender": 1, _id: 1 }, { name: "demographic.gender_1__id_1", background: true });
  Students.createIndex(
    { "identity.identification.localId": 1, orgid: 1, schoolYear: 1 },
    { name: "identity.identification.localId_1_orgid_1_schoolYear_1", background: true }
  );
  Students.createIndex(
    { "identity.identification.stateId": 1, orgid: 1, schoolYear: 1 },
    { name: "identity.identification.stateId_1_orgid_1_schoolYear_1", background: true }
  );
  StudentsBySkill.createIndex({ siteId: 1 }, { name: "siteId_1" });
  users.createIndex({ username: 1 }, { unique: true, name: "username_1", background: true, sparse: true });
  users.createIndex(
    { "emails.address": 1 },
    {
      unique: true,
      name: "emails.address_1",
      background: true,
      sparse: true
    }
  );
  users.createIndex(
    { "services.resume.loginTokens.hashedToken": 1 },
    { unique: true, name: "services.resume.loginTokens.hashedToken_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.resume.loginTokens.token": 1 },
    { unique: true, name: "services.resume.loginTokens.token_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.resume.haveLoginTokensToDelete": 1 },
    { name: "services.resume.haveLoginTokensToDelete_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.resume.loginTokens.when": 1 },
    { name: "services.resume.loginTokens.when_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.email.verificationTokens.token": 1 },
    { unique: true, name: "services.email.verificationTokens.token_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.password.reset.token": 1 },
    { unique: true, name: "services.password.reset.token_1", background: true, sparse: true }
  );
  users.createIndex(
    { "services.accessTokens.tokens.hashedToken": 1 },
    { name: "loginLinks:services.accessTokens", background: true }
  );
  users.createIndex(
    { "services.password.reset.when": 1 },
    { name: "services.password.reset.when_1", background: true, sparse: true }
  );
  users.createIndex({ "profile.orgid": 1 }, { name: "profile.orgid_1", background: true });
  users.createIndex({ "profile.siteAccess.siteId": 1 }, { name: "profile.siteAccess.siteId_1", background: true });
  users.createIndex(
    { "profile.siteAccess.role": 1, "profile.orgid": 1 },
    { name: "profile.siteAccess.role_1_profile.orgid_1", background: true }
  );
  users.createIndex(
    { "services.password.enroll.token": 1 },
    { unique: true, name: "services.password.enroll.token_1", sparse: true }
  );
  users.createIndex({ "profile.name.last": 1 }, { name: "profile.name.last_1", background: true });
  users.createIndex({ "services.password.enroll.when": 1 }, { name: "services.password.enroll.when_1", sparse: true });
  users.createIndex(
    { "services.resume.loginTokens.0": 1 },
    { name: "services.resume.loginTokens.0_1", background: true, sparse: true }
  );
  users.createIndex(
    { warn: 1, activityStamp: 1 },
    { name: "warn_1_activityStamp_1", background: true, partialFilterExpression: { warn: true } }
  );
  users.createIndex({ "services.azureAdB2c.issuerUserId": 1 }, { name: "services.azureAdB2c.issuerUserId_1" });
});
