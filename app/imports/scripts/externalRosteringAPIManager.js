import addOAuthInterceptor from "axios-oauth-1.0a";
import { flattenDeep, get, groupBy, keyBy, pick } from "lodash";
import moment from "moment";
import { getCurrentDate } from "../api/helpers/getCurrentDate";
import { getMeteorUser, ninjalog } from "../api/utilities/utilities";

const qs = require("querystring");
const axios = require("axios");

function sliceItemsIntoChunks(items, chunkSize) {
  const res = [];
  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize);
    res.push(chunk);
  }
  return res;
}

function ConnectionException({ statusCode, statusMessage, statusType, url }) {
  this.statusCode = statusCode;
  this.statusMessage = statusMessage;
  this.name = "ExternalRosteringAPIConnectionException";
  if (statusType) {
    this.statusType = statusType;
  }
  if (url) {
    this.url = url;
  }
  const statusNameByCode = {
    400: "Bad Request",
    401: "Unauthorized",
    403: "Forbidden",
    404: "Not Found"
  };
  const parsedStatusCode = statusCode ? statusNameByCode[statusCode] : "";
  const parsedStatusMessage = statusMessage || "";
  const parsedStatusType = statusType || "";
  const parsedUrl = url ? `URL that caused exception: ${url}` : "";
  const elements = [parsedStatusCode, parsedStatusType, parsedStatusMessage, parsedUrl].filter(f => f);
  this.message = elements.join("\n");
}

const unexpectedResponseDataElements = [
  "request_id",
  "status",
  "total",
  "count",
  "limit",
  "offset",
  "pagination",
  "error"
];

export class ExternalRosteringAPIManager {
  constructor({
    orgid,
    apiUrl,
    authUrl,
    clientId,
    clientSecret,
    algorithm = "HMAC-SHA256",
    shouldUseScopes = false,
    shouldUseSequentialRequests = false,
    shouldIgnoreEnrollmentStartDate = false,
    shouldIgnoreEnrollmentEndDate = false,
    userIdentifiers = {},
    limit = 500
  }) {
    this.orgid = orgid;
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.apiUrl = apiUrl;
    this.authUrl = authUrl;
    this.algorithm = algorithm;
    this.shouldUseScopes = shouldUseScopes;
    this.shouldUseSequentialRequests = shouldUseSequentialRequests;
    this.shouldIgnoreEnrollmentStartDate = shouldIgnoreEnrollmentStartDate;
    this.shouldIgnoreEnrollmentEndDate = shouldIgnoreEnrollmentEndDate;
    this.userIdentifiers = userIdentifiers;
    this.limit = limit;
    this.ready = false;
    return (async () => {
      await this.init();
      return this;
    })();
  }

  async init() {
    let bearerToken = null;
    try {
      const request = await axios.request({
        url: this.authUrl || `${this.apiUrl}/oauth/token`,
        method: "post",
        auth: {
          username: this.clientId,
          password: this.clientSecret
        },
        data: qs.stringify({
          grant_type: "client_credentials",
          ...(this.shouldUseScopes
            ? {
                scope:
                  "https://purl.imsglobal.org/spec/or/v1p1/scope/roster.readonly https://purl.imsglobal.org/spec/or/v1p1/scope/roster-demographics.readonly"
              }
            : {})
        })
      });
      bearerToken = request?.data?.access_token;
      // eslint-disable-next-line no-empty
    } catch {}

    if (!bearerToken) {
      // Create a client whose requests will be signed
      this.clientInstance = axios.create({
        baseURL: this.apiUrl
      });

      // Specify the OAuth options
      const options = {
        algorithm: this.algorithm,
        key: this.clientId,
        secret: this.clientSecret
      };

      // Add interceptor that signs requests
      addOAuthInterceptor(this.clientInstance, options);
    } else {
      this.clientInstance = await axios.create({
        baseURL: this.apiUrl,
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${bearerToken}`
        }
      });
    }

    return this;
  }

  async fetchAllDataFromEndpoint({ baseEndpoint, query = {}, fields = [], isRetry = false }) {
    const respData = [];
    let isDataFetched = false;
    let offset = 0;
    // Fetch until data no longer arrives from endpoint.
    while (!isDataFetched) {
      const constructQuery = Object.entries(query).map(([key, value]) => `${key}=${value}`);
      const parsedQuery = Object.keys(query).length ? `&${constructQuery.join("&")}` : "";

      // Required waiting to properly calculate offset
      let data = [];
      const resourceUrl = `${baseEndpoint}?offset=${offset}&limit=${this.limit}${parsedQuery}`;
      try {
        // eslint-disable-next-line no-await-in-loop
        ({ data } = await this.get({ resourceUrl }));
      } catch ({ response = {}, code, message }) {
        ninjalog.warning({
          msg: "Error",
          context: "fetchAllDataFromEndpoint",
          val: { response, code, message }
        });
        if (response.status === 401 && !isRetry) {
          // eslint-disable-next-line no-await-in-loop
          await this.init();
          return this.fetchAllDataFromEndpoint({ baseEndpoint, query, fields, isRetry: true });
        }
        const isMissingDataForFetchedEndpoint = response.status === 404 && response.data.code !== "ResourceNotFound";
        if (!isMissingDataForFetchedEndpoint) {
          throw new ConnectionException({
            statusCode: response.status || code,
            statusTest: response.statusText || message,
            statusType: response.data?.code,
            url: resourceUrl
          });
        }
      }
      unexpectedResponseDataElements.forEach(element => {
        delete data[element];
      });
      let parsedData = Object.values(data).flat(2);
      if (fields.length) {
        // eslint-disable-next-line no-loop-func
        parsedData = parsedData.map(datum => pick(datum, fields));
      }
      if (Array.isArray(parsedData)) {
        respData.push(...parsedData);
      } else {
        respData.push(parsedData);
      }
      isDataFetched = parsedData.length < this.limit;
      offset += this.limit;
    }
    return flattenDeep(respData);
  }

  async fetchAndGroupResourceDataWithClassIds({ resourceName, classIds, query }) {
    const dataWithClassId = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const classId of classIds) {
      const fetchedData = await this.fetchAllDataFromEndpoint({
        baseEndpoint: `/classes/${classId}/${resourceName}`,
        query: query || { filter: "status='active'" }
      });
      dataWithClassId.push(flattenDeep(fetchedData).map(data => ({ ...data, classId })));
    }

    return dataWithClassId.flat(1);
  }

  /*
  Fetching for filters then import now
  - getAllSchools /schools (org with type: "school") - available schools
  - getClassesForSchool /schools/{school_id}/classes (contains reference to school so can be grouped by schoolId) - available classes and grades
 	- getTeachersForClass /classes/{class_id}/teachers (teachersByClassId) - available teachers
 	- getStudentsForClass /classes/{class_id}/students (studentsByClassId) - students for selected classes
 	- getDemographics /demographics/{id} (birthDate data, needs to be fetched for each student)
 	Cron import based on saved filters
  - getSchool /schools/{id}
  - getClassesForSchool /schools/{school_id}/classes (filter out ones not matching filters)
 	- getTeachersForClass /classes/{class_id}/teachers (teachersByClassId)
 	- getStudentsForClass /classes/{class_id}/students (studentsByClassId)
 	- getDemographics /demographics/{id} (birthDate data, needs to be fetched for each student)
 	- getEnrollmentsForSchools /schools/{school_id}/enrollments
   */

  async fetchSchool(schoolId) {
    return this.fetchAllDataFromEndpoint({
      baseEndpoint: `/schools/${schoolId}`,
      query: {
        filter: "status='active'"
      },
      fields: ["sourcedId", "name", "identifier", "parent.sourcedId"]
    });
  }

  async fetchClassesForSchool(schoolId) {
    return this.fetchAllDataFromEndpoint({
      baseEndpoint: `/schools/${schoolId}/classes`,
      query: {
        filter: "status='active'"
      },
      fields: ["location", "school.sourcedId", "classCode", "sourcedId", "title", "course.sourcedId", "grades"]
    });
  }

  async getSchool({ schoolIds }) {
    if (this.shouldUseSequentialRequests) {
      const schools = [];
      // eslint-disable-next-line no-restricted-syntax
      for await (const schoolId of schoolIds) {
        schools.push(...(await this.fetchSchool(schoolId)));
      }
      return flattenDeep(schools);
    }

    return Promise.all(
      schoolIds.map(schoolId => {
        return this.fetchSchool(schoolId);
      })
    ).then(values => {
      return flattenDeep(values);
    });
  }

  async getAllSchools() {
    return this.fetchAllDataFromEndpoint({
      baseEndpoint: "/schools",
      query: {
        filter: "status='active'"
      },
      fields: ["sourcedId", "name", "identifier", "parent.sourcedId"]
    });
  }

  async getClassesForSchool({ schoolIds, shouldFetchEnrollments = false, teacherIds }) {
    // Contains reference to school in classDocument.school.sourcedId
    let enrollments = [];
    if (shouldFetchEnrollments) {
      const teacherRoleNames = ["teacher", ...(this.userIdentifiers?.teacher || [])];
      const filter = teacherRoleNames.map(role => `role='${role}'`).join(" OR ");
      try {
        enrollments = await this.getEnrollmentsForSchools({
          schoolIds,
          query: {
            filter
          }
        });
        if (!enrollments.length) {
          throw new Error("No school enrollments found for staff");
        }
      } catch {
        try {
          // eslint-disable-next-line no-restricted-syntax
          for await (const role of teacherRoleNames) {
            const roleEnrollments = await this.getEnrollmentsForSchools({
              schoolIds,
              query: {
                filter: `role='${role}'`
              }
            });
            enrollments.push(...roleEnrollments);
          }
        } catch {
          try {
            enrollments = await this.getEnrollmentsForSchools({
              schoolIds,
              query: {
                filter: "role!='student'"
              }
            });
          } catch (e) {
            console.log("Error while fetching enrollments", e);
          }
        }
      }
      enrollments = enrollments.filter(
        enrollment => enrollment.status === "active" && teacherRoleNames.includes(enrollment.role.toLowerCase())
      );
    }

    let classes = [];
    if (this.shouldUseSequentialRequests) {
      // eslint-disable-next-line no-restricted-syntax
      for await (const schoolId of schoolIds) {
        classes.push(...(await this.fetchClassesForSchool(schoolId)));
      }
    } else {
      classes = await Promise.all(
        schoolIds.map(schoolId => {
          return this.fetchClassesForSchool(schoolId);
        })
      ).then(values => flattenDeep(values));
    }

    if (enrollments.length) {
      return classes
        .map(c => {
          let assignedTeachers = enrollments.filter(
            e =>
              e.classId === c.sourcedId &&
              e.primary === "true" &&
              (!teacherIds || teacherIds.includes(e?.user?.sourcedId))
          );
          if (!assignedTeachers.length) {
            assignedTeachers = enrollments.filter(
              e => e.classId === c.sourcedId && (!teacherIds || teacherIds.includes(e?.user?.sourcedId))
            );
          }
          return {
            ...c,
            teacherId: assignedTeachers[0]?.user?.sourcedId
          };
        })
        .filter(c => c.teacherId);
    }
    return classes;
  }

  async getTeachersForSchool({ schoolIds }) {
    const dataWithSchoolId = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const schoolId of schoolIds) {
      const teachers = await this.fetchAllDataFromEndpoint({
        baseEndpoint: `/schools/${schoolId}/teachers`,
        query: {
          filter: "status='active'"
        }
        // fields: ["sourcedId", "email", "givenName", "familyName", "identifier", "role"]
      });
      dataWithSchoolId.push(flattenDeep(teachers).map(data => ({ ...data, schoolId })));
    }

    const roles = this.userIdentifiers?.teacher || [];
    if (roles.length) {
      const otherUsers = [];
      // eslint-disable-next-line no-restricted-syntax
      for await (const role of roles) {
        try {
          const users = await this.fetchAllDataFromEndpoint({
            baseEndpoint: `/users`,
            query: {
              filter: `status='active' AND role='${role}'`
            }
            // fields: ["sourcedId", "email", "givenName", "familyName", "identifier", "role"]
          });
          otherUsers.push(...users);
        } catch (e) {
          console.log(`Error while fetching users with the ${role} role for orgid: ${this.orgid}`, e);
        }
      }

      const otherUsersWithSchoolId = [];
      otherUsers.forEach(otherUser => {
        otherUser?.orgs?.forEach(({ sourcedId: schoolId }) => {
          if (schoolIds.includes(schoolId)) {
            otherUsersWithSchoolId.push({ ...otherUser, schoolId });
          }
        });
      });
      dataWithSchoolId.push(flattenDeep(otherUsersWithSchoolId));
    }

    return dataWithSchoolId.flat(1);
  }

  async getClassesForTeacher({ teacherIds }) {
    const dataWithSchoolId = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const teacherId of teacherIds) {
      const fetchedData = await this.fetchAllDataFromEndpoint({
        baseEndpoint: `/teachers/${teacherId}/classes`,
        query: {
          filter: "status='active'"
        },
        fields: ["location", "school.sourcedId", "classCode", "sourcedId", "title", "course.sourcedId", "grades"]
      });
      dataWithSchoolId.push(flattenDeep(fetchedData).map(data => ({ ...data, teacherId })));
    }

    return dataWithSchoolId.flat(1);
  }

  async getTeachersForClass({ classIds }) {
    return this.fetchAndGroupResourceDataWithClassIds({
      resourceName: "teachers",
      classIds,
      query: {
        filter: "status='active'"
      }
      // fields: ["sourcedId", "email", "givenName", "familyName", "identifier", "role"]
    });
  }

  /**
    Fetch students for selected schools
    Iterate over selected classIds
    Find matching enrollments
    Extend student document with schoolId and classId
 */
  async getStudentsForSchool({ schoolIds, classIds, enrollments }) {
    let activeEnrollments = enrollments;
    if (!activeEnrollments.length) {
      activeEnrollments = await this.getEnrollmentsForSchools({ schoolIds });
    }
    const enrollmentsByStudentId = groupBy(activeEnrollments, "user.sourcedId");

    let dataWithSchoolId = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const schoolId of schoolIds) {
      const fetchedData = await this.fetchAllDataFromEndpoint({
        baseEndpoint: `/schools/${schoolId}/students`,
        query: {
          filter: "status='active'"
        },
        fields: ["sourcedId", "familyName", "givenName", "grades"]
      });
      dataWithSchoolId.push(flattenDeep(fetchedData).map(data => ({ ...data, schoolId })));
    }

    dataWithSchoolId = dataWithSchoolId.flat(1);

    const parsedData = [];
    classIds.forEach(classId => {
      dataWithSchoolId.forEach(student => {
        const studentEnrollmentForClassId = enrollmentsByStudentId?.[student.sourcedId]?.find(
          enrollment => enrollment.classId === classId && enrollment.schoolId === student.schoolId
        );
        if (studentEnrollmentForClassId) {
          parsedData.push({
            ...student,
            classId
          });
        }
      });
    });

    return parsedData;
  }

  async fetchAllDataFromEndpointWithAlternativeFilters({ baseEndpoint, filters = [] }) {
    let data = [];

    let filterIndex = 0;
    let hasResponse = false;

    do {
      try {
        const filter = filters[filterIndex];
        // eslint-disable-next-line no-await-in-loop
        data = await this.fetchAllDataFromEndpoint({
          baseEndpoint,
          query: {
            filter
          }
        });
        hasResponse = true;
      } catch (e) {
        filterIndex += 1;
        if (filterIndex >= filters.length) {
          throw new Error(`Error while fetching data from the ${baseEndpoint} endpoint: ${e.message}`);
        }
      }
    } while (!hasResponse && filterIndex < filters.length);

    return data;
  }

  async getUsersByQuery(query = "") {
    if (!query) {
      return [];
    }

    const filters = [
      `givenName~'${query}' OR familyName~'${query}' OR middleName~'${query}' OR sourcedId~'${query}' OR identifier~'${query}'`,
      `givenName~'${query}' OR familyName~'${query}' OR middleName~'${query}' OR sourcedId~'${query}'`,
      `givenName~'${query}' OR familyName~'${query}' OR middleName~'${query}' OR identifier~'${query}'`,
      `givenName='${query}' OR familyName='${query}' OR middleName='${query}' OR sourcedId='${query}' OR identifier='${query}'`
    ];

    let data = [];
    try {
      data = await this.fetchAllDataFromEndpointWithAlternativeFilters({
        baseEndpoint: "/users",
        filters
      });
    } catch {
      console.log("Error while fetching data from the /users endpoint using multiple filters:", filters);
    }

    return data.sort((a, b) =>
      a.sourcedId.toString().localeCompare(b.sourcedId.toString(), undefined, {
        numeric: true
      })
    );
  }

  async getEnrollmentsByUserId(query) {
    if (!query) {
      return [];
    }
    const data = await this.fetchAllDataFromEndpoint({
      baseEndpoint: "/enrollments",
      query
    });

    return data;
  }

  async getClassesByQuery(query = "") {
    if (!query) {
      return [];
    }

    const filters = [
      `title~'${query}' OR classCode~'${query}' OR sourcedId~'${query}'`,
      `title='${query}' OR classCode='${query}' OR sourcedId='${query}'`
    ];

    let data = [];
    try {
      data = await this.fetchAllDataFromEndpointWithAlternativeFilters({
        baseEndpoint: "/classes",
        filters
      });
    } catch {
      console.log("Error while fetching data from the /classes endpoint using multiple filters:", filters);
    }

    const schools = await this.fetchAllDataFromEndpoint({
      baseEndpoint: `/schools`
    });

    const schoolBySourcedId = keyBy(schools, "sourcedId");

    return data
      .sort((a, b) =>
        a.sourcedId.toString().localeCompare(b.sourcedId.toString(), undefined, {
          numeric: true
        })
      )
      .map(datum => ({ ...datum, schoolName: schoolBySourcedId[datum.school?.sourcedId].name || "N/A" }));
  }

  async getEnrollmentsByClassId(query) {
    if (!query) {
      return [];
    }
    const data = await this.fetchAllDataFromEndpoint({
      baseEndpoint: "/enrollments",
      query
    });

    return data;
  }

  async getSchoolsByQuery(query = "") {
    if (!query) {
      return [];
    }

    const filters = [
      `name~'${query}' OR identifier~'${query}' OR sourcedId~'${query}'`,
      `name='${query}' OR identifier='${query}' OR sourcedId='${query}'`,
      `name~'${query}' OR sourcedId~'${query}'`,
      `name~'${query}' OR identifier~'${query}'`
    ];

    let data = [];
    try {
      data = await this.fetchAllDataFromEndpointWithAlternativeFilters({
        baseEndpoint: "/schools",
        filters
      });
    } catch {
      console.log("Error while fetching data from the /schools endpoint using multiple filters:", filters);
    }

    return data.sort((a, b) =>
      a.sourcedId.toString().localeCompare(b.sourcedId.toString(), undefined, {
        numeric: true
      })
    );
  }

  async getEnrollmentsBySchoolId(query) {
    if (!query) {
      return [];
    }
    const data = await this.fetchAllDataFromEndpoint({
      baseEndpoint: "/enrollments",
      query
    });

    return data;
  }

  async fetchDemographicsForStudents(query) {
    return this.fetchAllDataFromEndpoint({
      baseEndpoint: "/demographics",
      query: {
        ...query
      },
      fields: ["birthDate", "birthdate", "sourcedId", "status"]
    });
  }

  async getDemographics({ studentIds }) {
    const numberOfStudentIdsPerRequest = 45;

    const queries = sliceItemsIntoChunks(studentIds, numberOfStudentIdsPerRequest).map(chunkOfStudentIds => {
      const filter = chunkOfStudentIds.map(studentId => `sourcedId='${studentId}'`).join(" OR ");
      return {
        filter
      };
    });

    if (this.shouldUseSequentialRequests) {
      const demographics = [];
      // eslint-disable-next-line no-restricted-syntax
      for await (const query of queries) {
        demographics.push(...(await this.fetchDemographicsForStudents(query)));
      }
      return demographics;
    }

    return Promise.all(queries.map(query => this.fetchDemographicsForStudents(query))).then(values =>
      flattenDeep(values)
    );
  }

  async getEnrollmentsForSchools({ schoolIds, query = { filter: "status='active'" } }) {
    const dataWithSchoolIdAndClassId = [];
    const customDate = get(await getMeteorUser(), "profile.customDate");
    const currentDate = moment((await getCurrentDate(customDate, this.orgid)) || new Date());

    // eslint-disable-next-line no-restricted-syntax
    for await (const schoolId of schoolIds) {
      const enrollments = await this.fetchAllDataFromEndpoint({
        baseEndpoint: `/schools/${schoolId}/enrollments`,
        query: {
          ...query
        },
        fields: [
          "role",
          "primary",
          "status",
          "beginDate",
          "endDate",
          "user.sourcedId",
          "school.sourcedId",
          "class.sourcedId"
        ]
      });
      dataWithSchoolIdAndClassId.push(
        flattenDeep(
          enrollments.filter(
            enrollment =>
              (!enrollment.beginDate ||
                this.shouldIgnoreEnrollmentStartDate ||
                moment(enrollment.beginDate) <= currentDate) &&
              (!enrollment.endDate ||
                this.shouldIgnoreEnrollmentEndDate ||
                moment(enrollment.endDate) >= currentDate) &&
              enrollment.status === "active"
          )
        ).map(data => ({ ...data, classId: data?.class?.sourcedId, schoolId }))
      );
    }
    return dataWithSchoolIdAndClassId.flat(1);
  }

  async test() {
    const { status, statusText, data } = await this.get({
      resourceUrl: "/schools?offset=0&limit=1&filter=status='active'&fields=sourcedId,name"
    }).catch(({ response = {}, code, message }) => {
      throw new ConnectionException({
        statusCode: response.status || code,
        statusMessage: response.statusText || message
      });
    });
    const numberOfItems = data?.orgs?.length || data?.schools?.length || 0;
    return {
      numberOfItems,
      statusCode: status,
      statusMessage: statusText,
      data,
      ...(!numberOfItems && { statusCode: 1 })
    };
  }

  async get({ resourceUrl, options }) {
    const currentEnvironment = Meteor.settings.public.ENVIRONMENT;
    if (currentEnvironment === "LOCAL") {
      console.log("Rostering request:", resourceUrl.includes("demographics") ? resourceUrl.slice(0, 85) : resourceUrl);
    }
    const url = `/ims/oneroster/v1p1${resourceUrl}`;
    return this.clientInstance.get(url, options);
  }

  mapFunctionsByResource({ temporaryFilters = {}, queryText = "", query }) {
    const {
      classIds = [],
      schoolIds = [],
      studentIds = [],
      teacherIds = [],
      enrollments = [],
      shouldFetchEnrollments
    } = temporaryFilters;
    return {
      schoolById: () => this.getSchool({ schoolIds }),
      schools: () => this.getAllSchools(),
      classes: () =>
        this.getClassesForSchool({
          schoolIds,
          shouldFetchEnrollments,
          teacherIds: temporaryFilters.teacherIds
        }),
      teachersForSchool: () => this.getTeachersForSchool({ schoolIds }),
      classesForTeacher: () => this.getClassesForTeacher({ teacherIds }),
      teachersForClass: () => this.getTeachersForClass({ classIds }),
      studentsForSchool: () => this.getStudentsForSchool({ schoolIds, classIds, enrollments }),
      demographics: () => this.getDemographics({ studentIds }),
      enrollments: () => this.getEnrollmentsForSchools({ schoolIds }),
      usersByQuery: () => this.getUsersByQuery(queryText),
      enrollmentsByUserId: () => this.getEnrollmentsByUserId(query),
      classesByQuery: () => this.getClassesByQuery(queryText),
      enrollmentsByClassId: () => this.getEnrollmentsByClassId(query),
      schoolsByQuery: () => this.getSchoolsByQuery(queryText),
      enrollmentsBySchoolId: () => this.getEnrollmentsBySchoolId(query)
    };
  }
}
