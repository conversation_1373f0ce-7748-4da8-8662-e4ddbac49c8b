import { Accounts } from "meteor/accounts-base";

import "/imports/api/loadingCounter/loadingCounter";
import "/imports/api/messageNotices/messageNotices";
import "/imports/api/authentication/inactivity/startup";
import "/imports/api/ssoAzureAdB2c/client/azureAdB2cClient";
import "/imports/api/ssoAzureAdB2c/client/accountsAzureAdB2cClient";
import "./googleAnalytics";

import "./highcharts";

Accounts.onLoginFailure(data => {
  if (data?.error?.reason?.includes("Please log in again")) {
    window.location = "/login";
  }
});
