import { ROLE_IDS } from "/tests/cypress/support/common/constants";
import { getValidSiteAccess } from "./routeUtils";

describe("imports/startup/client/routeUtils.tests.js tests", () => {
  describe("getValidSiteAccess", () => {
    it("should return null when site access is empty", () => {
      expect(getValidSiteAccess({ roles: ["teacher"] })).toEqual(null);
    });
    it("should return valid site access when user has required active role", () => {
      const siteAccess = [getSiteAccessObject()];
      const expectedSiteAccess = getSiteAccessObject({ shouldUseRoleId: true });
      expect(
        getValidSiteAccess({
          roles: ["teacher"],
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it("should return null when user has required inactive role", () => {
      const siteAccess = [getSiteAccessObject({ isActive: false })];
      const expectedSiteAccess = null;
      expect(
        getValidSiteAccess({
          roles: ["teacher"],
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it("should return valid site access when user has multiple active roles, and one of them fits requirements", () => {
      const siteAccess = [getSiteAccessObject(), getSiteAccessObject({ role: "admin" })];
      const expectedSiteAccess = getSiteAccessObject({ role: "admin", shouldUseRoleId: true });
      expect(
        getValidSiteAccess({
          roles: ["admin"],
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return valid site access when user has required active teacher role, but not in required school year`, () => {
      const siteAccess = [getSiteAccessObject(), getSiteAccessObject({ schoolYear: 2019 })];
      const expectedSiteAccess = getSiteAccessObject({ schoolYear: 2020, role: "teacher", shouldUseRoleId: true });
      expect(
        getValidSiteAccess({
          roles: ["teacher"],
          schoolYear: 2021,
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it("should return valid latest school year site access for teacher role", () => {
      const siteAccess = [
        getSiteAccessObject({ schoolYear: 2020, role: "teacher", siteId: "2" }),
        getSiteAccessObject({ schoolYear: 2021, role: "teacher", siteId: "2" })
      ];
      const expectedSiteAccess = getSiteAccessObject({
        schoolYear: 2021,
        role: "teacher",
        shouldUseRoleId: true,
        siteId: "2"
      });
      expect(
        getValidSiteAccess({
          roles: ["teacher"],
          siteId: "2",
          schoolYear: 2021,
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it("should return valid latest school year site access for teacher roles for user with multiple roles", () => {
      const siteAccess = [
        getSiteAccessObject({ schoolYear: 2020, role: "admin", siteId: "2" }),
        getSiteAccessObject({ schoolYear: 2020, role: "teacher", siteId: "2" }),
        getSiteAccessObject({ schoolYear: 2021, role: "admin", siteId: "2" }),
        getSiteAccessObject({ schoolYear: 2021, role: "teacher", siteId: "2" })
      ];
      const expectedSiteAccessTeacher = getSiteAccessObject({
        schoolYear: 2021,
        role: "teacher",
        shouldUseRoleId: true,
        siteId: "2"
      });
      const expectedSiteAccessAdmin = getSiteAccessObject({
        schoolYear: 2020,
        role: "admin",
        shouldUseRoleId: true,
        siteId: "2"
      });
      expect(
        getValidSiteAccess({
          roles: ["teacher"],
          siteId: "2",
          schoolYear: 2021,
          siteAccess
        })
      ).toEqual(expectedSiteAccessTeacher);
      expect(
        getValidSiteAccess({
          roles: ["admin"],
          siteId: "2",
          schoolYear: 2020,
          siteAccess
        })
      ).toEqual(expectedSiteAccessAdmin);
    });
    ["teacher", "admin"].forEach(role => {
      it(`should return null when user has required active ${role} role, but without required siteId`, () => {
        const siteAccess = [getSiteAccessObject({ role }), getSiteAccessObject({ role, schoolYear: 2019 })];
        const expectedSiteAccess = null;
        expect(
          getValidSiteAccess({
            roles: [role],
            schoolYear: 2020,
            siteId: "2",
            siteAccess
          })
        ).toEqual(expectedSiteAccess);
      });
    });
    it(`should return valid site access when user has required active admin role, regardless of school year`, () => {
      const siteAccess = [getSiteAccessObject({ role: "admin", schoolYear: 0 })];
      const expectedSiteAccess = getSiteAccessObject({ role: "admin", schoolYear: 0, shouldUseRoleId: true });
      expect(
        getValidSiteAccess({
          roles: ["admin"],
          schoolYear: 2021,
          siteId: "1",
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return valid site access when user has required active dataAdmin role with access to all sites`, () => {
      const siteAccess = [getSiteAccessObject({ role: "dataAdmin", schoolYear: 0, siteId: "allSites" })];
      const expectedSiteAccess = getSiteAccessObject({
        role: "dataAdmin",
        schoolYear: 0,
        siteId: "allSites",
        shouldUseRoleId: true
      });
      expect(
        getValidSiteAccess({
          roles: ["dataAdmin"],
          schoolYear: 2021,
          siteId: "1",
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return valid site access when user has required active dataAdmin role with matching siteId`, () => {
      const siteAccess = [getSiteAccessObject({ role: "dataAdmin", schoolYear: 0, siteId: "1" })];
      const expectedSiteAccess = getSiteAccessObject({
        role: "dataAdmin",
        schoolYear: 0,
        siteId: "1",
        shouldUseRoleId: true
      });
      expect(
        getValidSiteAccess({
          roles: ["dataAdmin"],
          schoolYear: 2021,
          siteId: "1",
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return null when user has required active dataAdmin role but without matching siteId`, () => {
      const siteAccess = [getSiteAccessObject({ role: "dataAdmin", schoolYear: 0, siteId: "2" })];
      const expectedSiteAccess = null;
      expect(
        getValidSiteAccess({
          roles: ["dataAdmin"],
          schoolYear: 2021,
          siteId: "1",
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return valid site access when user has required active dataAdmin role with access to all sites, site is not defined and access is only for org Data Admins`, () => {
      const siteAccess = [getSiteAccessObject({ role: "dataAdmin", schoolYear: 0, siteId: "allSites" })];
      const expectedSiteAccess = getSiteAccessObject({
        role: "dataAdmin",
        schoolYear: 0,
        siteId: "allSites",
        shouldUseRoleId: true
      });
      expect(
        getValidSiteAccess({
          roles: ["dataAdmin"],
          schoolYear: 2021,
          isForOrgDataAdminsOnly: true,
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    it(`should return null when user has required active dataAdmin role, with access to only 1 site, site is not defined and access is only for org Data Admins`, () => {
      const siteAccess = [getSiteAccessObject({ role: "dataAdmin", schoolYear: 0, siteId: "2" })];
      const expectedSiteAccess = null;
      expect(
        getValidSiteAccess({
          roles: ["dataAdmin"],
          schoolYear: 2021,
          isForOrgDataAdminsOnly: true,
          siteAccess
        })
      ).toEqual(expectedSiteAccess);
    });
    ["universalDataAdmin", "support", "universalCoach", "superAdmin"].forEach(role => {
      it(`should return valid site access when user has required ${role} role, regardless of siteId or school year`, () => {
        const siteAccess = [getSiteAccessObject({ role, schoolYear: 0 })];
        const expectedSiteAccess = getSiteAccessObject({ role, schoolYear: 0, shouldUseRoleId: true });
        expect(
          getValidSiteAccess({
            roles: ["admin", role],
            schoolYear: 2021,
            siteId: "2",
            siteAccess
          })
        ).toEqual(expectedSiteAccess);
      });
    });
    [2019, 2020, 2021].forEach(schoolYear => {
      it(`should return valid site access for ${schoolYear} year when admin has active access to any school year `, () => {
        const siteAccess = [getSiteAccessObject({ role: "admin", schoolYear: 2019 })];
        const expectedSiteAccess = getSiteAccessObject({ role: "admin", schoolYear: 2019, shouldUseRoleId: true });
        expect(
          getValidSiteAccess({
            roles: ["admin"],
            schoolYear,
            siteAccess
          })
        ).toEqual(expectedSiteAccess);
      });
    });
  });
});

function getSiteAccessObject({
  role = "teacher",
  siteId = "1",
  schoolYear = 2020,
  isActive = true,
  isDefault = true,
  shouldUseRoleId = false
} = {}) {
  return {
    role: shouldUseRoleId ? ROLE_IDS[role] : role,
    siteId,
    schoolYear,
    isActive,
    isDefault
  };
}
