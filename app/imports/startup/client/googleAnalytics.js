import { Meteor } from "meteor/meteor";
import ReactGA from "react-ga4";

Meteor.startup(() => {
  const ENVIRONMENT = Meteor.settings.public.ENVIRONMENT || "DEV";
  const GOOGLE_ANALYTICS_CODE = Meteor.settings.public.GOOGLE_ANALYTICS_CODE || "G-GLFYM9LMNC"; // fallback to existing

  if (ENVIRONMENT === "PROD" && GOOGLE_ANALYTICS_CODE) {
    /* eslint-disable */
    ReactGA.initialize(GOOGLE_ANALYTICS_CODE);
    /* eslint-enable */
  }
});
