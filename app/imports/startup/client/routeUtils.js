import { Meteor } from "meteor/meteor";
import _get from "lodash/get";
import getCurrentApplicationVersion from "./version";
import { getCurrentSchoolYear, getMeteorUserId, getMeteorUserSync } from "/imports/api/utilities/utilities";
import { ROLE_IDS } from "/tests/cypress/support/common/constants";

export const checkLoggedIn = ({ ctx }) => {
  if (!getMeteorUserId()) {
    const redirect = getRedirect(ctx);
    if (redirect && typeof redirect === "function") {
      if (
        ctx.location.pathname?.length > 1 &&
        !ctx.location.pathname.includes("unauthorized") &&
        !ctx.location.pathname.includes("logout")
      ) {
        localStorage.setItem("redirect", ctx.location.pathname);
      }
      redirect("/login");
    }
  }
  if (Meteor.loggingIn()) {
    (async () =>
      new Promise(resolve => {
        setTimeout(() => {
          checkLoggedIn({ ctx });
          resolve();
        }, 100);
      }))();
  }
  Meteor.call("inactivity:defibNow");
};

export const checkHasDashboard = ({ ctx }) => {
  const hasDashboard = _get(getMeteorUserSync(), "profile.hasDashboard");
  if (!hasDashboard) {
    getRedirect(ctx)("/");
    throw new Meteor.Error("User does not have a required permission");
  }
};

function sortTeacherSiteAccessBySchoolYear(siteAccess) {
  const teacherEntries = [];
  const otherEntries = [];
  siteAccess.forEach((sa, index) => {
    if (sa.role.includes("teacher")) {
      teacherEntries.push(sa);
    } else {
      otherEntries.push({ index, sa });
    }
  });
  teacherEntries.sort((a, b) => b.schoolYear - a.schoolYear);
  const result = [];
  let teacherIndex = 0;

  for (let i = 0; i < siteAccess.length; i++) {
    if (siteAccess[i].role.includes("teacher")) {
      result.push(teacherEntries[teacherIndex++]);
    } else {
      const found = otherEntries.find(obj => obj.index === i);
      result.push(found.sa);
    }
  }
  return result;
}

export const getValidSiteAccess = ({ roles, schoolYear, siteId, siteAccess = [], isForOrgDataAdminsOnly = false }) => {
  const validSiteAccess = sortTeacherSiteAccessBySchoolYear(siteAccess).find(sa => {
    if (roles.includes(sa.role) && sa.isActive) {
      return (
        !schoolYear ||
        ["universalDataAdmin", "support", "universalCoach", "superAdmin", "downloader"].includes(sa.role) ||
        (sa.role === "dataAdmin" &&
          ((!isForOrgDataAdminsOnly && !siteId) || ["none", "allSites"].includes(sa.siteId) || siteId === sa.siteId)) ||
        (sa.role === "admin" && (!siteId || siteId === sa.siteId)) ||
        (sa.role === "teacher" && (!siteId || siteId === sa.siteId))
      );
    }
    return null;
  });
  if (!validSiteAccess) {
    return null;
  }
  return { ...validSiteAccess, role: ROLE_IDS[validSiteAccess.role] };
};

const checkIsRole = args => {
  const { ctx, roles, schoolYear, siteId, redirect, isForOrgDataAdminsOnly = false } = args;
  Meteor.call("Roles:getCurrentLoggedInUserSiteAccess", siteId, (err, siteAccess) => {
    if (!err) {
      const orgid = _get(ctx, "match.params.orgid");
      const validSiteAccess = getValidSiteAccess({
        roles,
        schoolYear,
        siteId,
        isForOrgDataAdminsOnly,
        siteAccess,
        currentSchoolYear: getCurrentSchoolYear(getMeteorUserSync(), orgid)
      });

      if (!validSiteAccess) {
        redirect(`/no-access`);
      } else if (validSiteAccess !== siteAccess[0]) {
        Meteor.call("users:changePrimaryRole", { roleObject: validSiteAccess }, error => {
          if (error) {
            console.log("Error while changing user primary role", error);
          }
        });
      }
    } else {
      redirect("/");
    }
  });
};

function getRedirect(ctx) {
  return _get(ctx, "history.push");
}

export const checkIsUniversalDataAdminOrSuperAdmin = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({ ctx, roles: ["universalDataAdmin", "superAdmin"], schoolYear, siteId, redirect: getRedirect(ctx) });
};

export const checkIsDataAdminOrUniversalDataAdminOrSuperAdmin = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({
    ctx,
    roles: ["dataAdmin", "universalDataAdmin", "superAdmin"],
    schoolYear,
    siteId,
    redirect: getRedirect(ctx)
  });
};

export const checkIsOrgDataAdminOrUniversalDataAdminOrSuperAdmin = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({
    ctx,
    roles: ["dataAdmin", "universalDataAdmin", "superAdmin"],
    schoolYear,
    siteId,
    isForOrgDataAdminsOnly: true,
    redirect: getRedirect(ctx)
  });
};

export const checkIsDownloaderOrSuperAdmin = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({ ctx, roles: ["downloader", "superAdmin"], schoolYear, siteId, redirect: getRedirect(ctx) });
};

export const checkIsSuperAdmin = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({ ctx, roles: ["superAdmin"], schoolYear, siteId, redirect: getRedirect(ctx) });
};

export const checkIsSupportOrUniversalCoach = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({ ctx, roles: ["support", "universalCoach"], schoolYear, siteId, redirect: getRedirect(ctx) });
};

export const checkIsAdminSupportOrUniversalCoach = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({ ctx, roles: ["admin", "support", "universalCoach"], schoolYear, siteId, redirect: getRedirect(ctx) });
};

export const checkIsTeacherOrAdminOrSupportOrUniversalCoach = ({ ctx, schoolYear, siteId }) => {
  checkIsRole({
    ctx,
    roles: ["teacher", "admin", "support", "universalCoach"],
    schoolYear,
    siteId,
    redirect: getRedirect(ctx)
  });
};

export const setApplicationVersion = () => {
  window.currentVersion = () => getCurrentApplicationVersion();
};
