export function benchmarkWindows({ siteId, startDate, endDate, userId, orgid }) {
  return {
    _id: "test_benchnark_window",
    schoolYear: 2017,
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    siteId: siteId || "test_elementary_site_id",
    startDate: startDate || new Date("2016-08-03T05:00:00.000+0000"),
    endDate: endDate || new Date("2016-11-06T06:00:00.000+0000"),
    orgid: orgid || "test_organization_id",
    created: {
      by: userId || "TEST",
      on: Date.now(),
      date: new Date()
    },
    lastModified: {
      by: userId || "TEST",
      on: Date.now(),
      date: new Date()
    }
  };
}

export default benchmarkWindows;
