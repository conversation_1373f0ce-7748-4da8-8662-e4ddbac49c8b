export function rule({ numOfAssessments }) {
  const skills = [];
  for (let i = 1; i <= (numOfAssessments || 1); i++) {
    skills.push({
      assessmentId: `test_assessment_${i}`,
      interventions: [
        {
          interventionId: "test_intervention_1",
          isActive: true
        }
      ]
    });
  }
  return {
    _id: "test_class_wide_rule_1",
    orgid: "test_organization_id",
    grade: "03",
    skills,
    enabled: true,
    created: {
      by: "TEST",
      date: new Date(),
      on: Date.now()
    },
    lastModified: {
      by: "TEST",
      date: new Date(),
      on: Date.now()
    },
    ruleDefinitionId: "test_rule_definition",
    conditionTypeId: "INTAClassWideDecisionTree"
  };
}
