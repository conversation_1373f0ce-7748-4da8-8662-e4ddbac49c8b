export const currentClasswideSkill1 = {
  assessmentId: "assessment_3",
  assessmentName: "Fact Families: Add/Subtract 0-20",
  interventions: [],
  targets: [14, 28, 300],
  whenStarted: {
    by: "",
    on: 0,
    date: "2017-01-27T16:29:41.714+0000"
  },
  assessmentResultId: "current_skill_result_id",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh"
};

export const currentClasswideSkill2 = {
  assessmentId: "assessment_3",
  assessmentName: "Fact Families: Add/Subtract 0-20",
  interventions: [],
  targets: [14, 28, 300],
  whenStarted: {
    by: "",
    on: 0,
    date: "2017-05-07T16:29:41.714+0000"
  },
  assessmentResultId: "current_skill_result_id",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh"
};

// All students were enrolled for all the assessments
export const exampleClasswideHistory1 = [
  // Classwide Intervention 4
  {
    assessmentId: "assessment_2",
    assessmentName: "Classwide Intervention Assessment 2",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-20T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-27T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 3 - assessment 2
  {
    assessmentId: "assessment_2",
    assessmentName: "Classwide Intervention Assessment 2",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-13T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-20T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 2 - assessment1
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-06T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-13T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 1 - assessment1
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-06T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Benchmark
  {
    assessmentId: "benchmark_assessment_id",
    assessmentName: "Winter Benchmark Assessment",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "benchmark",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  }
];

// Devin Potter was not enrolled during Intervention 3 (left the school for a week)
// Nina Farmer was enrolled but not assessed during Intervention 3 and 4
// Melanie Fox was enrolled and assessed during all classwide assessments
export const exampleClasswideHistory2 = [
  // Classwide Intervention 4 (Week 56)
  {
    assessmentId: "assessment_2",
    assessmentName: "Classwide Intervention Assessment 2",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-20T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-27T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Devin_Potter_DEMO_STUDENT"
          },
          {
            studentId: "Melanie_Fox_DEMO_STUDENT"
          }
          // {
          //   studentId: 'Nina_Farmer_DEMO_STUDENT',
          // },
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 3 - assessment 2 (Week 55)
  {
    assessmentId: "assessment_2",
    assessmentName: "Classwide Intervention Assessment 2",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-13T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-20T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          // Devin potter is not here!
          {
            studentId: "Melanie_Fox_DEMO_STUDENT"
          }
          // {
          //   studentId: 'Nina_Farmer_DEMO_STUDENT',
          // },
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      // Devin potter is not enrolled this week!
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 2 - assessment1 (Week 54)
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-06T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-13T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Devin_Potter_DEMO_STUDENT"
          },
          {
            studentId: "Melanie_Fox_DEMO_STUDENT"
          },
          {
            studentId: "Nina_Farmer_DEMO_STUDENT"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 1 - assessment1 (Week 53)
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-06T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Devin_Potter_DEMO_STUDENT"
          },
          {
            studentId: "Melanie_Fox_DEMO_STUDENT"
          },
          {
            studentId: "Nina_Farmer_DEMO_STUDENT"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Benchmark
  {
    assessmentId: "benchmark_assessment_id",
    assessmentName: "Winter Benchmark Assessment",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-02T16:29:41.733+0000"
    },
    assessmentResultMeasures: [],
    type: "benchmark",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  }
];

export const exampleClasswideHistory3 = [
  {
    assessmentId: "assessment_3",
    assessmentName: "Classwide Intervention Assessment 3",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-05-01T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-06-01T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Devin_Potter_DEMO_STUDENT"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: ["Devin_Potter_DEMO_STUDENT"]
  }
];

export const exampleClasswideHistory4 = [
  {
    assessmentId: "assessment_4",
    assessmentName: "Classwide Intervention Assessment 4",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-05-01T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-05-12T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Devin_Potter_DEMO_STUDENT"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: ["Devin_Potter_DEMO_STUDENT"]
  }
];

// Individual Current Skill
export const exampleIndividualCurrentSkill = {
  benchmarkAssessmentId: "benchmark_1",
  benchmarkAssessmentName: "benchmark_1",
  benchmarkAssessmentTargets: [13, 26, 300],
  assessmentId: "assessment_3",
  assessmentName: "assessment_3",
  assessmentTargets: [16, 31, 300],
  interventions: [
    {
      interventionId: "GBqyqxcE6oA6Lo2X7",
      interventionLabel: "Intervention Adviser - Timed Trial",
      interventionAbbrv: "TT"
    },
    {
      interventionId: "YAfRn9TocxMptjqkF",
      interventionLabel: "Intervention Adviser - Response Cards",
      interventionAbbrv: "RC"
    }
  ],
  assessmentResultId: "QzhpsgM67MCikBGyS",
  whenStarted: {
    by: "",
    on: 1487273351736.0,
    date: "2017-02-03T19:29:11.736+0000"
  },
  benchmarkPeriodId: "nEsbWokBWutTZFkTh"
};

// Student was given 2 drilldowns and interventions on weeks
// 53, 54, 55, 57 (week 56 was not assessed)
export const exampleIndividualHistory1 = [
  // Intervention 4 - assessment_2 (week 57) after a week missed
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "assessment_2",
    assessmentName: "assessment_2",
    assessmentTargets: [16, 31, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "PG9YoEgg5YazRpnB3",
    whenStarted: {
      by: "",
      on: 1487273343380.0,
      date: "2017-01-20T19:29:03.380+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "benchmark_1",
        assessmentName: "benchmark_1",
        cutoffTarget: 26,
        targetScores: [13, 26, 300],
        medianScore: 5,
        studentScores: [5],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "5",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      },
      {
        assessmentId: "assessment_2",
        assessmentName: "assessment_2",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 17,
        studentScores: [17],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "17",
            meetsTarget: false,
            individualRuleOutcome: "at"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273351736.0,
      date: "2017-02-03T19:29:11.736+0000"
    }
  },
  // Intervention 3 - assessment_2 (week 55)
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "assessment_2",
    assessmentName: "assessment_2",
    assessmentTargets: [16, 31, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "PG9YoEgg5YazRpnB3",
    whenStarted: {
      by: "",
      on: 1487273343380.0,
      date: "2017-01-13T19:29:03.380+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "benchmark_1",
        assessmentName: "benchmark_1",
        cutoffTarget: 26,
        targetScores: [13, 26, 300],
        medianScore: 5,
        studentScores: [5],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "5",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      },
      {
        assessmentId: "assessment_2",
        assessmentName: "assessment_2",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 17,
        studentScores: [17],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "17",
            meetsTarget: false,
            individualRuleOutcome: "at"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273351736.0,
      date: "2017-01-20T19:29:11.736+0000"
    }
  },
  // Intervention 2 - assessment_1 (week 54)
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "assessment_1",
    assessmentName: "assessment_1",
    assessmentTargets: [16, 31, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "PG9YoEgg5YazRpnB3",
    whenStarted: {
      by: "",
      on: 1487273343380.0,
      date: "2017-01-06T19:29:03.380+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "benchmark_1",
        assessmentName: "benchmark_1",
        cutoffTarget: 26,
        targetScores: [13, 26, 300],
        medianScore: 5,
        studentScores: [5],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "5",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      },
      {
        assessmentId: "assessment_1",
        assessmentName: "assessment_1",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 17,
        studentScores: [17],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "17",
            meetsTarget: false,
            individualRuleOutcome: "at"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273351736.0,
      date: "2017-01-13T19:29:11.736+0000"
    }
  },
  // Intervention 1 - assessment1 (week 53)
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "assessment_1",
    assessmentName: "assessment_1",
    assessmentTargets: [16, 31, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "Bro2MLd2RWpga8FLo",
    whenStarted: {
      by: "",
      on: 1487273336054.0,
      date: "2017-01-03T19:28:56.054+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "benchmark_1",
        assessmentName: "benchmark_1",
        cutoffTarget: 26,
        targetScores: [13, 26, 300],
        medianScore: 3,
        studentScores: [3],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "3",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      },
      {
        assessmentId: "assessment_1",
        assessmentName: "assessment_1",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 8,
        studentScores: [8],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "8",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273343380.0,
      date: "2017-01-06T19:29:03.380+0000"
    }
  },
  // Drill Down #2 (week 53)
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "drilldown_2",
    assessmentName: "drilldown_2",
    assessmentTargets: [16, 31, 300],
    interventions: [],
    assessmentResultId: "zbd7pyYs8ytFGJn26",
    whenStarted: {
      by: "",
      on: 1487273327122.0,
      date: "2017-01-02T19:28:47.122+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "drilldown_2",
        assessmentName: "drilldown_2",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 5,
        studentScores: [5],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "5",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273336054.0,
      date: "2017-01-02T19:28:56.054+0000"
    }
  },
  // Drill Down #1 (week 53)
  {
    benchmarkAssessmentId: "benchmark_1",
    benchmarkAssessmentName: "benchmark_1",
    benchmarkAssessmentTargets: [13, 26, 300],
    assessmentId: "drilldown_1",
    assessmentName: "drilldown_1",
    assessmentTargets: [16, 31, 300],
    interventions: [],
    assessmentResultId: "zbd7pyYs8ytFGJn26",
    whenStarted: {
      by: "",
      on: 1487273327122.0,
      date: "2017-01-02T19:28:47.122+0000"
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "drilldown_1",
        assessmentName: "drilldown_1",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 5,
        studentScores: [5],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "Chelsea_Roberts_DEMO_STUDENT",
            status: "COMPLETE",
            firstName: "Chelsea",
            lastName: "Roberts",
            score: "5",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ],
    type: "individual",
    whenEnded: {
      by: "",
      on: 1487273336054.0,
      date: "2017-01-02T19:28:56.054+0000"
    }
  }
];

export const classwideHistoryForWorseScoresRatio = [
  // Classwide Intervention 4
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-02-20T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-02-27T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Melanie_Fox_DEMO_STUDENT",
            score: "1"
          },
          {
            studentId: "Nina_Farmer_DEMO_STUDENT",
            score: "1"
          },
          {
            studentId: "Kelly_Frank_DEMO_STUDENT",
            score: "1"
          },
          {
            studentId: "Pheng_Vang_DEMO_STUDENT",
            score: "10"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  },
  // Classwide Intervention 3 - assessment 2
  {
    assessmentId: "assessment_1",
    assessmentName: "Classwide Intervention Assessment 1",
    interventions: [],
    targets: [14, 28, 300],
    whenStarted: {
      by: "",
      on: 0,
      date: "2017-01-13T16:29:31.857+0000"
    },
    assessmentResultId: "result_id",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    whenEnded: {
      by: "",
      on: 0,
      date: "2017-01-20T16:29:41.733+0000"
    },
    assessmentResultMeasures: [
      {
        studentResults: [
          {
            studentId: "Melanie_Fox_DEMO_STUDENT",
            score: "10"
          },
          {
            studentId: "Nina_Farmer_DEMO_STUDENT",
            score: "10"
          },
          {
            studentId: "Kelly_Frank_DEMO_STUDENT",
            score: "10"
          },
          {
            studentId: "Pheng_Vang_DEMO_STUDENT",
            score: "10"
          }
        ]
      }
    ],
    type: "classwide",
    enrolledStudentIds: [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ]
  }
];

export const otherBenchmarkIndvidualIntervention = {
  benchmarkAssessmentId: "previousBenchmark",
  benchmarkAssessmentName: "previousBenchmark",
  benchmarkAssessmentTargets: [13, 26, 300],
  assessmentId: "previousAssessment",
  assessmentName: "previousAssessment",
  assessmentTargets: [16, 31, 300],
  interventions: [
    {
      interventionId: "56attvmgjtrjMsFif",
      interventionLabel: "Intervention Adviser - Cover Copy and Compare",
      interventionAbbrv: "CCC"
    },
    {
      interventionId: "JC4A2Jx6gKfqLe9LF",
      interventionLabel: "Intervention Adviser - Guided Practice",
      interventionAbbrv: "GP"
    }
  ],
  assessmentResultId: "previousBenchmarkAssessment",
  whenStarted: {
    by: "",
    on: 1487273336054.0,
    date: "2016-11-12T19:28:56.054+0000"
  },
  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
  assessmentResultMeasures: [
    {
      assessmentId: "previosBenchmark_1",
      assessmentName: "previosBenchmark_1",
      cutoffTarget: 26,
      targetScores: [13, 26, 300],
      medianScore: 3,
      studentScores: [3],
      percentMeetingTarget: 0,
      numberMeetingTarget: 0,
      totalStudentsAssessed: 1,
      studentResults: [
        {
          studentId: "Chelsea_Roberts_DEMO_STUDENT",
          status: "COMPLETE",
          firstName: "Chelsea",
          lastName: "Roberts",
          score: "3",
          meetsTarget: false,
          individualRuleOutcome: "below"
        }
      ]
    },
    {
      assessmentId: "previousBenchmark_2",
      assessmentName: "previousBenchmark_2",
      cutoffTarget: 31,
      targetScores: [16, 31, 300],
      medianScore: 8,
      studentScores: [8],
      percentMeetingTarget: 0,
      numberMeetingTarget: 0,
      totalStudentsAssessed: 1,
      studentResults: [
        {
          studentId: "Chelsea_Roberts_DEMO_STUDENT",
          status: "COMPLETE",
          firstName: "Chelsea",
          lastName: "Roberts",
          score: "8",
          meetsTarget: false,
          individualRuleOutcome: "below"
        }
      ]
    }
  ],
  type: "individual",
  whenEnded: {
    by: "",
    on: 1487273336054.0,
    date: "2016-11-13T19:28:56.054+0000"
  }
};
