import defaultsDeep from "lodash/defaultsDeep";
import { getTimestampInfo } from "/imports/api/helpers/getTimestampInfo";
import { setupTestContext } from "/imports/api/rosterImports/rosterImportsProcessor.testHelpers";

const { districtID, districtName, schoolYear: defaultSchoolYear, orgid: defaultOrgid } = setupTestContext();

export function getRosterImportItem(overrides = {}) {
  return defaultsDeep(overrides, {
    orgid: "test_organization_id",
    created: {
      by: "TEST",
      on: Date.now(),
      date: new Date()
    },
    rosterImportId: "someFileUploadId",
    data: {
      districtID: "123",
      districtName: "Some District",
      schoolID: "23451",
      schoolName: "Some Schoole",
      teacherID: "124643",
      teacherLastName: "Teacher",
      teacherFirstName: "Some",
      teacherEmail: "<EMAIL>",
      className: "Some Class",
      classSectionID: "65e",
      studentLocalID: "3462346",
      studentStateID: "8765564",
      studentLastName: "Student",
      studentFirstName: "Some",
      studentBirthDate: new Date("2005-02-28"),
      springMathGrade: "4"
    }
  });
}

function getOptionalStudentData() {
  return {};
}

function getRosterImportDraftItem({
  orgid = defaultOrgid,
  byDateOn = getTimestampInfo("db_mock"),
  rosterImportId = "test_rosterImportId",
  schoolYear = defaultSchoolYear
} = {}) {
  return {
    orgid,
    created: byDateOn,
    lastModified: byDateOn,
    rosterImportId,
    data: {
      districtID,
      districtName,
      teacherID: "1457",
      teacherLastName: "Forester",
      teacherFirstName: "Jeanette",
      teacherEmail: "<EMAIL>",
      studentBirthDate: new Date(),
      springMathGrade: "02",
      schoolYear,
      ...getOptionalStudentData()
    }
  };
}

function getSunnySlopeSiteUploadData({ districtID, districtName } = {}) {
  let returnObject = {
    schoolID: "60",
    schoolName: "Sunny Slope Elementary"
  };
  if (districtName && districtID) {
    Object.assign(returnObject, {
      districtID,
      districtName
    });
  }
  return returnObject;
}

function getSunnySlopeTeacherUploadData(teacherUploadData) {
  return (
    teacherUploadData || {
      teacherID: "1457",
      teacherLastName: "Forester",
      teacherFirstName: "Jeanette",
      teacherEmail: "<EMAIL>"
    }
  );
}

function getSunnySlopeGroupUploadData() {
  return {
    className: "Forest 2",
    classSectionID: "2F",
    courseName: "Forest 2 (2F)",
    springMathGrade: "02"
  };
}

export async function getAllSunnySlopeSiteItems({
  orgid = defaultOrgid,
  byDateOn,
  rosterImportId = "test_rosterImportId",
  schoolYear = defaultSchoolYear,
  teacherData
} = {}) {
  byDateOn ??= await getTimestampInfo("db_mock");
  return [
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getSunnySlopeSiteUploadData({ districtID, districtName }),
        ...getSunnySlopeTeacherUploadData(teacherData),
        ...getSunnySlopeGroupUploadData(),
        studentLocalID: "685123",
        studentStateID: "3999000685555",
        studentLastName: "Anderson",
        studentFirstName: "Adam",
        studentBirthDate: new Date("Jan 03 2005"),
        schoolYear,
        ...getOptionalStudentData()
      }
    },
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getSunnySlopeSiteUploadData({ districtID, districtName }),
        ...getSunnySlopeTeacherUploadData(teacherData),
        ...getSunnySlopeGroupUploadData(),
        studentLocalID: "685124",
        studentStateID: "3999000685556",
        studentLastName: "Bane",
        studentFirstName: "Bill",
        studentBirthDate: new Date("Feb 03 2005"),
        schoolYear,
        ...getOptionalStudentData()
      }
    },
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getSunnySlopeSiteUploadData({ districtID, districtName }),
        ...getSunnySlopeTeacherUploadData(teacherData),
        ...getSunnySlopeGroupUploadData(),
        studentLocalID: "685125",
        studentStateID: "3999000685557",
        studentLastName: "Conary",
        studentFirstName: "Chris",
        studentBirthDate: new Date("Mar 03 2005"),
        schoolYear,
        ...getOptionalStudentData()
      }
    }
  ];
}

function getGoldenGroupUploadData() {
  return {
    className: "Golden 2",
    classSectionID: "2G",
    courseName: "Golden 2 (2G)",
    springMathGrade: "02"
  };
}

function getGoldenGroupTeacherUploadData() {
  return {
    teacherID: "1458",
    teacherLastName: "Bond",
    teacherFirstName: "James",
    teacherEmail: "<EMAIL>"
  };
}

export async function getAllGoldenGroupUploadItems({
  orgid = defaultOrgid,
  byDateOn,
  rosterImportId = "test_rosterImportId",
  schoolYear = defaultSchoolYear
} = {}) {
  byDateOn ??= await getTimestampInfo("db_mock");
  return [
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getGoldenGroupTeacherUploadData(),
        ...getGoldenGroupUploadData(),
        studentLocalID: "685126",
        studentStateID: "3999000685558",
        studentLastName: "Dawson",
        studentFirstName: "Daniel",
        studentBirthDate: new Date("Apr 03 2005"),
        schoolYear,
        ...getOptionalStudentData()
      }
    },
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getGoldenGroupTeacherUploadData(),
        ...getGoldenGroupUploadData(),
        studentLocalID: "685127",
        studentStateID: "3999000685559",
        studentLastName: "Ender",
        studentFirstName: "Gail",
        studentBirthDate: new Date("May 03 2005"),
        schoolYear,
        ...getOptionalStudentData()
      }
    }
  ];
}

export function getCookieGroupTeacherUploadData() {
  return {
    teacherID: "1459",
    teacherLastName: "Cook",
    teacherFirstName: "William",
    teacherEmail: "<EMAIL>"
  };
}

function getRainySlopeSiteUploadData({ districtID, districtName } = {}) {
  let returnObject = {
    schoolID: "61",
    schoolName: "Rainy Slope Elementary"
  };
  if (districtName && districtID) {
    Object.assign(returnObject, {
      districtID,
      districtName
    });
  }
  return returnObject;
}

function getCookieGroupUploadData() {
  return {
    className: "Cookie 3",
    classSectionID: "3",
    courseName: "Cookie 3 (3)",
    springMathGrade: "02"
  };
}

export async function getAllCookieGroupItems({
  orgid = defaultOrgid,
  byDateOn,
  rosterImportId = "test_rosterImportId",
  schoolYear = defaultSchoolYear
} = {}) {
  byDateOn ??= await getTimestampInfo("db_mock");
  return [
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getCookieGroupTeacherUploadData(),
        ...getCookieGroupUploadData(),
        studentLocalID: "685128",
        studentStateID: "3999000685551",
        studentLastName: "Fowler",
        studentFirstName: "Frank",
        studentBirthDate: new Date("Jun 02 2004"),
        schoolYear,
        ...getOptionalStudentData()
      }
    },
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getCookieGroupTeacherUploadData(),
        ...getCookieGroupUploadData(),
        studentLocalID: "685129",
        studentStateID: "3999000685552",
        studentLastName: "Houston",
        studentFirstName: "Harry",
        studentBirthDate: new Date("Jul 03 2004"),
        schoolYear,
        ...getOptionalStudentData()
      }
    }
  ];
}

function getNextGroupTeacherUploadData() {
  return {
    teacherID: "1459",
    teacherLastName: "Downright",
    teacherFirstName: "Abigail",
    teacherEmail: "<EMAIL>"
  };
}

function getNextGroupUploadData() {
  return {
    className: "Abi 4",
    classSectionID: "4",
    courseName: "Abi 4 (4)",
    springMathGrade: "04"
  };
}

export async function getAllNextGroupItems({
  orgid = defaultOrgid,
  byDateOn,
  rosterImportId = "test_rosterImportId",
  schoolYear = defaultSchoolYear
} = {}) {
  byDateOn ??= await getTimestampInfo("db_mock");
  return [
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getNextGroupTeacherUploadData(),
        ...getNextGroupUploadData(),
        studentLocalID: "222122",
        studentStateID: "2229000685222",
        studentLastName: "Zelman",
        studentFirstName: "Zoe",
        studentBirthDate: new Date("Jun 02 2002"),
        schoolYear,
        ...getOptionalStudentData()
      }
    },
    {
      orgid,
      created: byDateOn,
      lastModified: byDateOn,
      rosterImportId,
      data: {
        ...getRainySlopeSiteUploadData({ districtID, districtName }),
        ...getNextGroupTeacherUploadData(),
        ...getNextGroupUploadData(),
        studentLocalID: "111121",
        studentStateID: "11119000685111",
        studentLastName: "Wilkinson",
        studentFirstName: "Warren",
        studentBirthDate: new Date("Jun 02 2002"),
        schoolYear,
        ...getOptionalStudentData()
      }
    }
  ];
}

export async function getAllRosterImportItems() {
  return [
    ...await getAllSunnySlopeSiteItems(),
    ...await getAllGoldenGroupUploadItems(),
    ...await getAllCookieGroupItems(),
    ...await getAllNextGroupItems()
  ];
}

export function moveStudentItemsBetweenGroups({ from, to, rosterImportItems }) {
  const itemsCopy = [...rosterImportItems];
  const groupIndexes = {
    cookie: [5, 6],
    golden: [3, 4],
    sunnySlope: [0, 1, 2],
    next: [7, 8]
  };
  const nextGroupData = getNextStudentGroupData(to);
  groupIndexes[from].forEach(index => {
    itemsCopy[index].data = Object.assign({}, itemsCopy[index].data, nextGroupData);
  });
  return itemsCopy;
}

export function getNextStudentGroupData(to) {
  switch (to) {
    case "cookie":
      return {
        ...getRainySlopeSiteUploadData(),
        ...getCookieGroupUploadData()
      };
    case "golden":
      return {
        ...getRainySlopeSiteUploadData(),
        ...getGoldenGroupUploadData()
      };
    case "sunnySlope":
      return {
        ...getSunnySlopeSiteUploadData(),
        ...getSunnySlopeGroupUploadData()
      };
    case "next":
      return {
        ...getRainySlopeSiteUploadData(),
        ...getNextGroupUploadData()
      };
  }
}

export function getUpdatedItemsWithAddedEnrollments({ to, rosterImportItems, studentsToAdd = 2 }) {
  const itemsCopy = [...rosterImportItems];
  for (let i = 0; i < studentsToAdd; i++) {
    const newItem = getRosterImportDraftItem();
    newItem.data = Object.assign(newItem.data, getStudentData(), getNextStudentGroupData(to));
    itemsCopy.push(newItem);
  }
  return itemsCopy;
}

function getStudentData() {
  return {
    studentLocalID: Math.round(Math.random() * 10000).toString(),
    studentStateID: Math.round(Math.random() * 100000).toString(),
    studentLastName: "Lastname",
    studentFirstName: "Firstname"
  };
}
