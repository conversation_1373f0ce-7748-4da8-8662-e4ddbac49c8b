export function getFakeCurrentClasswideSkill(assessmentId, lowTarget, highTarget) {
  return {
    assessmentId,
    benchmarkPeriodId: "winterId",
    targets: [lowTarget, highTarget, 300]
  };
}

export function getFakeClasswideRuleResults(assessmentId) {
  return {
    nextSkill: {
      assessmentId
    }
  };
}

export function getFakeClasswideAssessmentResults(assessmentId, medianScore, benchmarkPeriod) {
  return {
    type: "classwide",
    benchmarkPeriodId: benchmarkPeriod || "winterId",
    measures: [{ assessmentId, medianScore }]
  };
}

export function getFakeIndividualsUpdateSet(
  currentSkillAssId,
  currentSkillBMID,
  currentSkillInterventions,
  oldAssessmentId,
  oldBMID,
  oldInterventions,
  oldMedianScore,
  lowTarget,
  highTarget
) {
  return {
    currentSkill: currentSkillAssId
      ? {
          assessmentId: currentSkillAssId,
          benchmarkAssessmentId: currentSkillBMID,
          interventions: currentSkillInterventions,
          type: "individual",
          benchmarkPeriodId: "bmPeriod"
        }
      : null,
    history: oldAssessmentId
      ? [
          {
            assessmentId: oldAssessmentId,
            benchmarkAssessmentId: oldBMID,
            interventions: oldInterventions,
            type: "individual",
            benchmarkPeriodId: "bmPeriod",
            assessmentTargets: [lowTarget, highTarget, 300],
            assessmentResultMeasures: [
              {
                assessmentId: oldAssessmentId,
                medianScore: oldMedianScore
              }
            ]
          }
        ]
      : null
  };
}

export const testStudentGroupId = "TEST_STUDENT_GROUP_1";
export const testAssessmentResultId = "TEST_ASSESSMENT_RESULT_1";
export const classwideInterventionId = "testClasswideResultId";
export const benchmarkPeriodId = "nEsbWokBWutTZFkTh";
export const schoolYear = 2019;
export const studentWithIndividualInterventionEligibility = "FH75tvoSZAe9RDbhe";
export const studentWithIndividualInterventionStarted = "Fx9dBeurvJzBzCY9h";
export const assessmentIds = [
  "firstAssessmentId",
  "secondAssessmentId",
  "thirdAssessmentId",
  "fourthAssessmentId",
  "fifthAssessmentId",
  "sixthAssessmentId"
];
const grade = "01";
export const activeScreeningAssessment = {
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "OPEN",
  created: { on: 123 },
  scores: [
    {
      status: "COMPLETE",
      value: "15"
    },
    {
      status: "STARTED",
      value: null
    },
    {
      status: "CANCELLED",
      value: null
    }
  ],
  assessmentIds: assessmentIds.slice(0, 3)
};
export const completeScreeningAssessment = {
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "COMPLETED",
  created: { on: 123 },
  scores: [
    {
      status: "COMPLETE",
      value: "15"
    }
  ],
  measures: {
    percentMeetingTarget: 27,
    numberMeetingTarget: 6,
    totalStudentsAssessed: 22,
    studentResults: [
      {
        status: "COMPLETE",
        value: "15"
      }
    ]
  },
  classwideResults: {},
  lastModified: {},
  nextAssessmentResultId: {},
  ruleResults: {},
  assessmentIds
};
export const passedScreeningResult = {
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  grade,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "COMPLETED",
  scores: [
    {
      status: "COMPLETE",
      value: "15"
    }
  ],
  measures: [
    {
      percentMeetingTarget: 100,
      numberMeetingTarget: 2,
      totalStudentsAssessed: 2,
      grade,
      targetScores: [8, 15, 100],
      studentScores: [15, 15],
      studentResults: [
        {
          status: "COMPLETE",
          value: "15",
          meetsTarget: true
        },
        {
          status: "COMPLETE",
          value: "15",
          meetsTarget: true
        }
      ]
    }
  ],
  classwideResults: {
    percentMeetingTarget: 100,
    percentAtRisk: 0,
    totalStudentsMeetingAllTargets: 0,
    totalStudentsAssessedOnAllMeasures: 2,
    studentIdsNotMeetingTarget: []
  },
  lastModified: {},
  nextAssessmentResultId: {},
  ruleResults: {
    passed: true
  },
  assessmentIds
};
export const studentGroupWithActiveScreening = {
  _id: testStudentGroupId,
  history: [],
  currentAssessmentResultIds: [testAssessmentResultId]
};
export const studentGroupWithCompleteScreening = {
  _id: testStudentGroupId,
  grade,
  history: [
    {
      assessmentResultId: testAssessmentResultId,
      type: "benchmark"
    },
    {
      assessmentResultId: "other_benchmark",
      type: "benchmark"
    }
  ],
  currentAssessmentResultIds: []
};
export const studentGroupInClasswideIntervention = {
  _id: testStudentGroupId,
  history: [
    {
      assessmentResultId: testAssessmentResultId,
      type: "benchmark"
    }
  ],
  currentAssessmentResultIds: [classwideInterventionId],
  currentClasswideSkill: {}
};
export const studentGroupWithProgressedClasswideIntervention = {
  _id: testStudentGroupId,
  history: [
    {
      assessmentResultId: classwideInterventionId,
      type: "classwide"
    },
    {
      assessmentResultId: testAssessmentResultId,
      type: "benchmark"
    }
  ],
  currentAssessmentResultIds: ["nextClasswideIntervention"],
  currentClasswideSkill: {}
};
export const studentGroupWithInterventionEligibility = {
  _id: testStudentGroupId,
  history: [
    {
      assessmentResultId: testAssessmentResultId,
      type: "benchmark"
    }
  ],
  currentAssessmentResultIds: ["indIntervention"],
  individualInterventionQueue: [studentWithIndividualInterventionEligibility, "otherStudent"]
};
export const classwideFailedScreeningAssessment = {
  _id: testAssessmentResultId,
  benchmarkPeriodId,
  schoolYear,
  studentGroupId: testStudentGroupId,
  type: "benchmark",
  status: "COMPLETED",
  created: { on: 123 },
  scores: [
    {
      status: "COMPLETE",
      value: "15"
    }
  ],
  measures: {},
  classwideResults: {},
  lastModified: {},
  nextAssessmentResultId: classwideInterventionId,
  ruleResults: {},
  assessmentIds
};
export const individuallyFailedScreeningAssessment = {
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "COMPLETED",
  created: { on: 123 },
  scores: [
    {
      status: "COMPLETE",
      value: "0"
    },
    {
      status: "COMPLETE",
      value: "0"
    }
  ],
  measures: {},
  lastModified: {},
  ruleResults: {
    passed: true,
    nextSkill: null
  },
  classwideResults: {
    percentMeetingTarget: 60,
    percentAtRisk: 40,
    totalStudentsMeetingAllTargets: 3,
    totalStudentsAssessedOnAllMeasures: 5,
    studentIdsNotMeetingTarget: [studentWithIndividualInterventionEligibility, studentWithIndividualInterventionStarted]
  },
  assessmentIds
};
export const classwideInterventionStarted = {
  _id: classwideInterventionId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "classwide",
  status: "OPEN",
  created: { on: 123 },
  scores: [
    {
      status: "STARTED",
      value: ""
    }
  ]
};
export const classwideInterventionWithScores = {
  _id: classwideInterventionId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "classwide",
  status: "COMPLETED",
  created: { on: 123 },
  scores: [
    {
      status: "COMPLETE",
      value: "10"
    },
    {
      status: "COMPLETE",
      value: "20"
    }
  ],
  measures: {},
  classwideResults: {},
  lastModified: {},
  nextAssessmentResultId: {},
  ruleResults: {}
};
export const assessmentResultWithNoScores = {
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "OPEN",
  created: { on: 123 },
  scores: [],
  assessmentIds
};
export const faultyAssessmentResult = {
  _id: "failtyAssessmentResult",
  studentGroupId: testStudentGroupId,
  benchmarkPeriodId,
  schoolYear,
  type: "benchmark",
  status: "COMPLETED",
  created: { on: 123 },
  scores: [],
  assessmentIds
};
export const firstSkillIdForGrade1 = "firstSkillId";
const firstSkillNameForGrade1 = "firstSkill";
export const rulesForGrade = {
  grade: "01",
  skills: [
    {
      assessmentId: firstSkillIdForGrade1,
      assessmentName: "Sums to 6",
      interventions: []
    }
  ]
};
export const firstSkillIdForGrade1Assessment = {
  _id: firstSkillIdForGrade1,
  name: firstSkillNameForGrade1,
  strands: [
    {
      scores: [
        {
          externalId: "number_correct",
          targets: [
            {
              grade: "01",
              periods: [
                {
                  benchmarkPeriodId,
                  values: [1, 2, 3]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

export function generateAssessment({
  _id = firstSkillIdForGrade1,
  grade = "01",
  benchmarkPeriodId = benchmarkPeriodId,
  name = firstSkillNameForGrade1
}) {
  return {
    _id,
    name,
    strands: [
      {
        scores: [
          {
            externalId: "number_correct",
            targets: [
              {
                grade,
                periods: [
                  {
                    benchmarkPeriodId,
                    values: [1, 2, 3]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  };
}

export function generateAssessmentResult({
  _id,
  studentGroupId,
  grade = "01",
  type = "benchmark",
  status = "COMPLETED",
  benchmarkPeriodId = benchmarkPeriodId,
  assessmentIds = [],
  scores = []
}) {
  return {
    _id,
    orgid: "orgid",
    schoolYear,
    studentGroupId,
    status,
    grade,
    type,
    assessmentIds,
    scores: scores.map(({ studentId, value }) => ({ status: "COMPLETE", studentId, value })),
    benchmarkPeriodId
  };
}
