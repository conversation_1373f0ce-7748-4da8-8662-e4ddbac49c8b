import moment from "moment";
import { testAssessmentResultId, testStudentGroupId } from "./assessmentResults.testData";

const getDefaultTestAssessmentResult = () => ({
  _id: testAssessmentResultId,
  studentGroupId: testStudentGroupId,
  type: "classwide",
  scores: [],
  measures: [
    {
      studentScores: [],
      studentResults: []
    }
  ]
});

const defaultMasteryTarget = 50;

export const buildAssesmentResultMeasure = ({
  studentIndexes,
  failingIndexes,
  masteryTarget = defaultMasteryTarget,
  assessmentId,
  studentIdPostfix = ""
}) => {
  const totalStudents = studentIndexes.length;
  const passingStudents = totalStudents - failingIndexes.length;
  const percentMeetingTarget = (passingStudents / totalStudents) * 100;
  const assessmentResultMeasure = {
    studentScores: [],
    studentResults: [],
    assessmentId,
    assessmentName: `assessmentName_${assessmentId}`,
    cutoffTarget: masteryTarget,
    medianScore: percentMeetingTarget > 50 ? masteryTarget : 0,
    percentMeetingTarget,
    numberMeetingTarget: passingStudents,
    totalStudentsAssessed: totalStudents,
    targetScores: [Math.ceil(masteryTarget * 0.5), masteryTarget, masteryTarget * 5]
  };

  studentIndexes.forEach(studentIndex => {
    const isPassing = !failingIndexes.includes(studentIndex);
    assessmentResultMeasure.studentScores.push(isPassing ? masteryTarget : 0);
    assessmentResultMeasure.studentResults.push({
      studentId: `studentId${studentIndex}${studentIdPostfix}`,
      status: "COMPLETE",
      firstName: `studentFirstName_${studentIndex}${studentIdPostfix}`,
      lastName: `studentLastName_${studentIndex}${studentIdPostfix}`,
      score: isPassing ? masteryTarget : 0,
      meetsTarget: isPassing,
      individualRuleOutcome: isPassing ? "above" : "below"
    });
  });
  return assessmentResultMeasure;
};

export const buildAssessmentResult = ({
  numberOfStudents,
  failingIndexes,
  masteryTarget = defaultMasteryTarget,
  assessmentId
}) => {
  const assessmentResult = getDefaultTestAssessmentResult();
  assessmentResult.assessmentIds = [assessmentId];
  const studentIndexes = [...Array(numberOfStudents).keys()]; // for 5 it will generate an array [0, 1, 2, 3, 4]
  studentIndexes.forEach(studentIndex => {
    const isPassing = !failingIndexes.includes(studentIndex);
    assessmentResult.scores.push({
      _id: `studentScoreId_${studentIndex}`,
      assessmentId,
      status: "COMPLETE",
      studentId: `studentId${studentIndex}`,
      value: isPassing ? masteryTarget : 0
    });
  });
  assessmentResult.measures = [
    buildAssesmentResultMeasure({
      studentIndexes,
      failingIndexes,
      masteryTarget,
      assessmentId
    })
  ];

  const numberOfFailingStudents = failingIndexes.length;
  assessmentResult.classwideResults = {
    percentMeetingTarget: (1 - numberOfFailingStudents / numberOfStudents) * 100,
    percentAtRisk: (numberOfFailingStudents / numberOfStudents) * 100,
    totalStudentsMeetingAllTargets: numberOfStudents - numberOfFailingStudents,
    totalStudentsAssessedOnAllMeasures: numberOfStudents,
    studentIdsNotMeetingTarget: failingIndexes.map(studentIndex => `studentId${studentIndex}`)
  };

  return assessmentResult;
};

export const buildHistoryItem = ({
  numberOfStudents,
  failingIndexes,
  masteryTarget = defaultMasteryTarget,
  assessmentId,
  howManyWeeksAgo,
  studentIdPostfix = ""
}) => {
  const studentIndexes = [...Array(numberOfStudents).keys()]; // for 5 it will generate an array [0, 1, 2, 3, 4]
  const historyItem = {
    assessmentId,
    whenEnded: {
      date: moment().subtract(7 * (howManyWeeksAgo + 1), "days")
    },
    whenStarted: {
      // 2 days prior for no real reason yet.
      date: moment().subtract(7 * (howManyWeeksAgo + 1) + 2, "days")
    },
    type: "classwide",
    enrolledStudentIds: studentIndexes.map(studentIndex => `studentId${studentIndex}${studentIdPostfix}`),
    assessmentResultMeasures: [
      buildAssesmentResultMeasure({ studentIndexes, failingIndexes, masteryTarget, assessmentId, studentIdPostfix })
    ]
  };
  return historyItem;
};

export const buildHistory = ({
  numberOfWeeks,
  assessments,
  numberOfStudents,
  failingIndexes,
  masteryTarget,
  studentIdPostfix = ""
}) => {
  const historyItemIndexes = [...Array(numberOfWeeks).keys()];
  return historyItemIndexes.map(index =>
    buildHistoryItem({
      numberOfStudents,
      failingIndexes,
      masteryTarget,
      assessmentId: Array.isArray(assessments) ? assessments[index] : assessments,
      howManyWeeksAgo: index,
      studentIdPostfix
    })
  );
};
