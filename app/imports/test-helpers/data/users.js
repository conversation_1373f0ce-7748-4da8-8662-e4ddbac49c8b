function bootstrapPassword(environment) {
  switch (environment) {
    case "DEMO":
      return "$2a$10$5f4uCgCpll1htS6I8tFObuW7HF2L2TqzlBsq0K84Ay/A.uoX4wgFW";
    default:
      return "$2a$10$/a3ZYVws0EbfU7AzXDJIA.43tCYkSKtx33OKBPjKyv76DZOSQl596";
  }
}

export function user(firstName, lastName, role) {
  return {
    profile: {
      onboarded: true,
      orgid: "test_organization_id",
      localId: "local_teacer_id",
      siteAccess: [
        {
          role: role || "teacher",
          siteId: "test_elementary_site_id",
          schoolYear: 2017,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: firstName || "Test",
        last: lastName || "User",
        middle: null
      },
      created: {
        by: "TEST",
        on: Date.now(),
        date: new Date()
      },
      lastModified: {
        by: "TEST",
        on: Date.now(),
        date: new Date()
      }
    }
  };
}
export function superAdminUser(environment) {
  // TODO: Super user does not have an org or site access :P
  return {
    _id: "super_admin_user_id",
    createdAt: "2016-07-21T03:14:30.535+0000",
    services: {
      password: {
        bcrypt: bootstrapPassword(environment)
      }
    },
    emails: [
      {
        address: "<EMAIL>",
        verified: false
      }
    ],
    profile: user("Super", "Admin", "arbitraryIdsuperAdmin").profile
  };
}

export function adminUser(environment) {
  return {
    _id: "admin_user_id",
    createdAt: "2016-07-21T03:14:30.535+0000",
    services: {
      password: {
        bcrypt: bootstrapPassword(environment)
      }
    },
    emails: [
      {
        address: "<EMAIL>",
        verified: false
      }
    ],
    profile: user("Admin", "User", "arbitraryIdadmin").profile
  };
}

export function teacherUser(environment) {
  return {
    _id: "teacher_user_id",
    createdAt: "2016-07-21T03:14:30.535+0000",
    services: {
      password: {
        bcrypt: bootstrapPassword(environment)
      }
    },
    emails: [
      {
        address: "<EMAIL>",
        verified: false
      }
    ],
    profile: user("Teacher", "User", "arbitraryIdteacher").profile
  };
}

export function dataAdminUser(environment) {
  return {
    _id: "data_admin_user_id",
    createdAt: "2016-07-21T03:14:30.535+0000",
    services: {
      password: {
        bcrypt: bootstrapPassword(environment)
      }
    },
    emails: [
      {
        address: "<EMAIL>",
        verified: false
      }
    ],
    profile: user("Data", "Admin", "arbitraryIddataAdmin").profile
  };
}
