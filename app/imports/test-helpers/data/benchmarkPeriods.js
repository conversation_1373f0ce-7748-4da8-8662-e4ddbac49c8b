export function getBenchmarkPeriods() {
  return [
    {
      _id: "nEsbWokBWutTZFkTh",
      name: "Winter",
      label: "winter-period",
      sortOrder: 2,
      startDate: {
        default: {
          month: 1,
          day: 1
        }
      },
      endDate: {
        default: {
          month: 3,
          day: 31
        }
      },
      created: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      },
      lastModified: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      }
    },
    {
      _id: "8S52Gz5o85hRkECgq",
      name: "Fall",
      label: "fall-period",
      sortOrder: 1,
      startDate: {
        default: {
          month: 8,
          day: 1
        }
      },
      endDate: {
        default: {
          month: 12,
          day: 31
        }
      },
      created: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      },
      lastModified: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      }
    },
    {
      _id: "cjCMnZKARBJmG8suT",
      name: "Spring",
      label: "spring-period",
      sortOrder: 3,
      startDate: {
        default: {
          month: 4,
          day: 1
        }
      },
      endDate: {
        default: {
          month: 7,
          day: 31
        }
      },
      created: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      },
      lastModified: {
        by: "DDP",
        on: Date.now(),
        date: new Date()
      }
    },
    {
      _id: "allPeriods",
      name: "All",
      label: "all-periods",
      sortOrder: 4,
      startDate: {
        default: {
          month: 7,
          day: 31
        }
      },
      endDate: {
        default: {
          month: 7,
          day: 31
        }
      },
      created: {
        by: "SpringMathDev",
        on: 1537394400000.0,
        date: new Date("2018-09-20T22:36:32.656+0000")
      },
      lastModified: {
        by: "SpringMathDev",
        on: 1537394400000.0,
        date: new Date("2018-09-20T22:36:32.656+0000")
      }
    }
  ];
}

export default getBenchmarkPeriods;
