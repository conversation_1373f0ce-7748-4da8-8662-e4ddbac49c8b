export const noScoresGrowthResult = {
  fallToWinter: [
    {
      fall: {
        assessmentName: "Sums to 6",
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Sums to 6",
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Sums to 12",
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        score: null,
        totalStudentsAssessed: null
      }
    },
    {
      fall: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      }
    },
    {
      fall: {
        assessmentName: "Quantity Comparison 20-99",
        assessmentId: "cAphxERJMPfAfjuxH",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Quantity Comparison 20-99",
        assessmentId: "cAphxERJMPfAfjuxH",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Quantity Comparisons 101-999",
        assessmentId: "rW2MuMrLkvdiMdtoA",
        score: null,
        totalStudentsAssessed: null
      }
    }
  ],
  winterToSpring: [
    {
      winter: {
        assessmentName: "Sums to 12",
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        score: null,
        totalStudentsAssessed: null
      },
      spring: {
        assessmentName: "Sums to 20",
        assessmentId: "7arH8a6z3BAEdEARm",
        score: null,
        totalStudentsAssessed: null
      }
    },
    {
      winter: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      },
      spring: {
        assessmentName: "Subtraction 0-20",
        assessmentId: "RAicL73Hwai6BSCPH",
        score: null,
        totalStudentsAssessed: null
      }
    },
    {
      winter: {
        assessmentName: "Fact Families: Add/Subtract 0-5",
        assessmentId: "DnjN9x7Gkzno9yZbj",
        score: null,
        totalStudentsAssessed: null
      },
      spring: {
        assessmentName: "Fact Families: Add/Subtract 0-9",
        assessmentId: "FxYDJWbMdT8QiZwcD",
        score: null,
        totalStudentsAssessed: null
      }
    },
    {
      winter: {
        assessmentName: "Fact Families: Add/Subtract 0-5",
        assessmentId: "DnjN9x7Gkzno9yZbj",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Fact Families: Add/Subtract 0-9",
        assessmentId: "FxYDJWbMdT8QiZwcD",
        score: null,
        totalStudentsAssessed: null
      }
    }
  ]
};
export const validScoresGrowthResult = {
  fallToWinter: [
    {
      fall: {
        assessmentName: "Sums to 6",
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        score: 100,
        totalStudentsAssessed: 26
      },
      classwide: {
        assessmentName: "Sums to 6",
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Sums to 12",
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        score: 69,
        totalStudentsAssessed: 26
      }
    },
    {
      fall: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: 54,
        totalStudentsAssessed: 26
      }
    },
    {
      fall: {
        assessmentName: "Quantity Comparison 20-99",
        assessmentId: "cAphxERJMPfAfjuxH",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentName: "Quantity Comparison 20-99",
        assessmentId: "cAphxERJMPfAfjuxH",
        score: null,
        totalStudentsAssessed: null
      },
      winter: {
        assessmentName: "Quantity Comparisons 101-999",
        assessmentId: "rW2MuMrLkvdiMdtoA",
        score: 69,
        totalStudentsAssessed: 26
      }
    }
  ],
  winterToSpring: [
    {
      winter: {
        assessmentName: "Sums to 12",
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        score: 69,
        totalStudentsAssessed: 26
      },
      spring: {
        assessmentName: "Sums to 20",
        assessmentId: "7arH8a6z3BAEdEARm",
        score: 85,
        totalStudentsAssessed: 20
      }
    },
    {
      winter: {
        assessmentName: "Subtraction 0-5",
        assessmentId: "J7J7fw6x3CsYLHHx7",
        score: 54,
        totalStudentsAssessed: 26
      },
      spring: {
        assessmentName: "Subtraction 0-20",
        assessmentId: "RAicL73Hwai6BSCPH",
        score: 54,
        totalStudentsAssessed: 28
      }
    },
    {
      winter: {
        assessmentName: "Fact Families: Add/Subtract 0-5",
        assessmentId: "DnjN9x7Gkzno9yZbj",
        score: 0,
        totalStudentsAssessed: 26
      },
      spring: {
        assessmentName: "Fact Families: Add/Subtract 0-9",
        assessmentId: "FxYDJWbMdT8QiZwcD",
        score: 38,
        totalStudentsAssessed: 26
      }
    },
    {
      winter: {
        assessmentName: "Fact Families: Add/Subtract 0-5",
        assessmentId: "DnjN9x7Gkzno9yZbj",
        score: 0,
        totalStudentsAssessed: 26
      },
      classwide: {
        assessmentName: "Fact Families: Add/Subtract 0-9",
        assessmentId: "FxYDJWbMdT8QiZwcD",
        score: 60,
        totalStudentsAssessed: 15
      }
    }
  ]
};
export const lowClasswideScoreGrowthResult = {
  fallToWinter: [
    {
      fall: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        score: null,
        totalStudentsAssessed: null
      },
      classwide: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families: Add/Subtract 0-9",
        score: null,
        totalStudentsAssessed: null
      }
    }
  ],
  winterToSpring: []
};
export const overInstructionalClasswideGrowthResults = {
  fallToWinter: [
    {
      fall: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        score: 85,
        totalStudentsAssessed: 20
      },
      classwide: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families: Add/Subtract 0-9",
        score: 100,
        totalStudentsAssessed: 10
      }
    }
  ],
  winterToSpring: []
};
export const multipleGroupsGrowthResults = {
  fallToWinter: [
    {
      fall: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        score: 52, // average
        totalStudentsAssessed: 60 // sum
      },
      classwide: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families: Add/Subtract 0-9",
        score: 77,
        totalStudentsAssessed: 60
      }
    }
  ],
  winterToSpring: []
};
export const multipleGroupsWithIncompleteGrowthResults = {
  fallToWinter: [
    {
      fall: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        score: 68, // average
        totalStudentsAssessed: 40 // sum
      },
      classwide: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families: Add/Subtract 0-9",
        score: 85,
        totalStudentsAssessed: 40
      }
    }
  ],
  winterToSpring: []
};
export const expectedChartDataForValidResults = {
  fallToWinter: {
    categories: [
      { name: "Sums to 6", n: 26 },
      { name: "Sums to 6", n: null },
      { name: "Sums to 12", n: 26 },
      null,
      { name: "Subtraction 0-5", n: null },
      { name: "Subtraction 0-5", n: null },
      { name: "Subtraction 0-5", n: 26 },
      null,
      { name: "Quantity Comparison 20-99", n: null },
      { name: "Quantity Comparison 20-99", n: null },
      { name: "Quantity Comparisons 101-999", n: 26 }
    ],
    scores: [
      { y: 100, color: "#f9bf68", labelText: "100%" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 69, color: "#70a1d9", labelText: "69%" },
      null,
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 54, color: "#70a1d9", labelText: "54%" },
      null,
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 69, color: "#70a1d9", labelText: "69%" }
    ]
  },
  winterToSpring: {
    categories: [
      { name: "Sums to 12", n: 26 },
      { name: "Sums to 20", n: 20 },
      null,
      { name: "Subtraction 0-5", n: 26 },
      { name: "Subtraction 0-20", n: 28 },
      null,
      { name: "Fact Families: Add/Subtract 0-5", n: 26 },
      { name: "Fact Families: Add/Subtract 0-9", n: 26 },
      null,
      { name: "Fact Families: Add/Subtract 0-5", n: 26 },
      { name: "Fact Families: Add/Subtract 0-9", n: 15 }
    ],
    scores: [
      { y: 69, color: "#70a1d9", labelText: "69%" },
      { y: 85, color: "#aee57c", labelText: "85%" },
      null,
      { y: 54, color: "#70a1d9", labelText: "54%" },
      { y: 54, color: "#aee57c", labelText: "54%" },
      null,
      { y: 0, color: "#70a1d9", labelText: "0%" },
      { y: 38, color: "#aee57c", labelText: "38%" },
      null,
      { y: 0, color: "#70a1d9", labelText: "0%" },
      { y: 60, color: "#541f21", labelText: "60%" }
    ]
  }
};
export const expectedNoScoreChartData = {
  fallToWinter: {
    categories: [
      { name: "Sums to 6", n: null },
      { name: "Sums to 6", n: null },
      { name: "Sums to 12", n: null },
      null,
      { name: "Subtraction 0-5", n: null },
      { name: "Subtraction 0-5", n: null },
      { name: "Subtraction 0-5", n: null },
      null,
      { name: "Quantity Comparison 20-99", n: null },
      { name: "Quantity Comparison 20-99", n: null },
      { name: "Quantity Comparisons 101-999", n: null }
    ],
    scores: [
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      null,
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      null,
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" },
      { y: 0, color: "#70a1d9", labelText: "N/A" }
    ]
  },
  winterToSpring: {
    categories: [
      { name: "Sums to 12", n: null },
      { name: "Sums to 20", n: null },
      null,
      { name: "Subtraction 0-5", n: null },
      { name: "Subtraction 0-20", n: null },
      null,
      { name: "Fact Families: Add/Subtract 0-5", n: null },
      { name: "Fact Families: Add/Subtract 0-9", n: null },
      null,
      { name: "Fact Families: Add/Subtract 0-5", n: null },
      { name: "Fact Families: Add/Subtract 0-9", n: null }
    ],
    scores: [
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      { y: 0, color: "#aee57c", labelText: "N/A" },
      null,
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      { y: 0, color: "#aee57c", labelText: "N/A" },
      null,
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      { y: 0, color: "#aee57c", labelText: "N/A" },
      null,
      { y: 0, color: "#70a1d9", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" }
    ]
  }
};
export const expectedChartDataForLowClasswideScore = {
  fallToWinter: {
    scores: [
      { y: 0, color: "#f9bf68", labelText: "N/A" },
      { y: 0, color: "#541f21", labelText: "N/A" }
    ],
    categories: [
      { name: "Sums to 6", n: null },
      { name: "Fact Families: Add/Subtract 0-9", n: null }
    ]
  }
};
export const expectedChartDataForOverInstructionalClasswideResults = {
  fallToWinter: {
    scores: [
      { y: 85, color: "#f9bf68", labelText: "85%" },
      { y: 100, color: "#541f21", labelText: "100%" }
    ],
    categories: [
      { name: "Sums to 6", n: 20 },
      { name: "Fact Families: Add/Subtract 0-9", n: 10 }
    ]
  }
};
export const expectedChartDataForMultipleGroupsGrowth = {
  fallToWinter: {
    scores: [
      { y: 52, color: "#f9bf68", labelText: "52%" }, // average growth across all groups
      { y: 77, color: "#541f21", labelText: "77%" }
    ],
    categories: [
      { name: "Sums to 6", n: 60 },
      { name: "Fact Families: Add/Subtract 0-9", n: 60 }
    ]
  }
};
export const expectedChartDataForMultipleGroupsWithIncompleteGrowth = {
  fallToWinter: {
    scores: [
      { y: 68, color: "#f9bf68", labelText: "68%" }, // average growth across all groups
      { y: 85, color: "#541f21", labelText: "85%" }
    ],
    categories: [
      { name: "Sums to 6", n: 40 },
      { name: "Fact Families: Add/Subtract 0-9", n: 40 }
    ]
  }
};
export const fakeAssessmentsCollection = [
  { _id: "yxBaWDvbNjBJLcaHQ", name: "Sums to 6" },
  { _id: "ZtJxX6rjuoRwAqRFo", name: "Sums to 12" },
  { _id: "J7J7fw6x3CsYLHHx7", name: "Subtraction 0-5" },
  { _id: "cAphxERJMPfAfjuxH", name: "Quantity Comparison 20-99" },
  { _id: "rW2MuMrLkvdiMdtoA", name: "Quantity Comparisons 101-999" },
  { _id: "DnjN9x7Gkzno9yZbj", name: "Fact Families: Add/Subtract 0-5" },
  { _id: "7arH8a6z3BAEdEARm", name: "Sums to 20" },
  { _id: "RAicL73Hwai6BSCPH", name: "Subtraction 0-20" },
  { _id: "FxYDJWbMdT8QiZwcD", name: "Fact Families: Add/Subtract 0-9" }
];
export const assessmentComparisonMap = {
  fallToWinter: [
    {
      fall: "yxBaWDvbNjBJLcaHQ",
      classwide: "yxBaWDvbNjBJLcaHQ",
      winter: "ZtJxX6rjuoRwAqRFo"
    },
    {
      fall: "J7J7fw6x3CsYLHHx7",
      classwide: "J7J7fw6x3CsYLHHx7",
      winter: "J7J7fw6x3CsYLHHx7"
    },
    {
      fall: "cAphxERJMPfAfjuxH",
      classwide: "cAphxERJMPfAfjuxH",
      winter: "rW2MuMrLkvdiMdtoA"
    }
  ],
  winterToSpring: [
    {
      winter: "ZtJxX6rjuoRwAqRFo",
      spring: "7arH8a6z3BAEdEARm"
    },
    {
      winter: "J7J7fw6x3CsYLHHx7",
      spring: "RAicL73Hwai6BSCPH"
    },
    {
      winter: "DnjN9x7Gkzno9yZbj",
      spring: "FxYDJWbMdT8QiZwcD"
    },
    {
      winter: "DnjN9x7Gkzno9yZbj",
      classwide: "FxYDJWbMdT8QiZwcD"
    }
  ]
};
export const groupHistory = [
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    assessmentName: "Fact Families:  Add/Subtract 0-9",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families:  Add/Subtract 0-9",
        medianScore: 44,
        cutoffTarget: 40,
        percentMeetingTarget: 60,
        numberMeetingTarget: 6,
        totalStudentsAssessed: 10,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        assessmentResultType: "classwide",
        studentScores: [40, 40, 40, 40, 40, 40, 40, 40, 20, 10, 10, 10, 10, 10, 10], // 60%
        targetScores: [20, 40, 300]
      }
    ],
    enrolledStudentIds: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"],
    targets: [20, 40, 300],
    type: "classwide"
  },
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    assessmentName: "Fact Families:  Add/Subtract 0-9",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Families:  Add/Subtract 0-9",
        medianScore: 34,
        percentMeetingTarget: 40,
        numberMeetingTarget: 4,
        totalStudentsAssessed: 10,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        assessmentResultType: "classwide",
        studentScores: [40, 40, 40, 40, 40, 40, 20, 20, 20, 10, 10, 10, 10, 10, 10],
        targetScores: [20, 40, 300]
      }
    ],
    enrolledStudentIds: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"],
    targets: [30, 40, 300],
    type: "classwide"
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
    assessmentResultMeasures: [
      {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        medianScore: 20,
        percentMeetingTarget: 85,
        numberMeetingTarget: 17,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "cjCMnZKARBJmG8suT",
        assessmentResultType: "benchmark"
      },
      {
        assessmentId: "RAicL73Hwai6BSCPH",
        assessmentName: "Subtraction 0-20",
        medianScore: 23,
        percentMeetingTarget: 54,
        numberMeetingTarget: 15,
        totalStudentsAssessed: 28
      },
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        assessmentName: "Fact Fams: Add/Subt 0-9",
        medianScore: 18,
        percentMeetingTarget: 38,
        numberMeetingTarget: 10,
        totalStudentsAssessed: 26
      }
    ]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        assessmentName: "Sums to 12",
        medianScore: 18,
        percentMeetingTarget: 69,
        numberMeetingTarget: 18,
        totalStudentsAssessed: 26,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        assessmentResultType: "benchmark"
      },
      {
        assessmentId: "J7J7fw6x3CsYLHHx7",
        assessmentName: "Subtraction 0-5",
        medianScore: 21,
        percentMeetingTarget: 54,
        numberMeetingTarget: 14,
        totalStudentsAssessed: 26
      },
      {
        assessmentId: "DnjN9x7Gkzno9yZbj",
        assessmentName: "Fact Families: Add/Subtract 0-5",
        medianScore: 9,
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 26
      },
      {
        assessmentId: "rW2MuMrLkvdiMdtoA",
        assessmentName: "Quantity Comparisons 101-999",
        medianScore: 24,
        percentMeetingTarget: 69,
        numberMeetingTarget: 18,
        totalStudentsAssessed: 26
      }
    ]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    assessmentResultMeasures: [
      {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        medianScore: 18,
        percentMeetingTarget: 100,
        numberMeetingTarget: 26,
        totalStudentsAssessed: 26
      }
    ]
  }
];

export const simpleComparison = {
  fallToWinter: [
    {
      fall: "yxBaWDvbNjBJLcaHQ",
      classwide: "FxYDJWbMdT8QiZwcD"
    }
  ],
  winterToSpring: []
};

export const historyWithInstructionalTargetVal = [
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        percentMeetingTarget: 60, // below 50 is failing
        medianScore: 45,
        cutoffTarget: 40,
        numberMeetingTarget: 6,
        totalStudentsAssessed: 10,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        studentScores: [45, 45, 45, 45, 45, 45, 25, 25, 25, 25], // 60% scored over 40 mastery target and 100% scored over 20 instructional target TODO(Mateusz) 15 students
        targetScores: [20, 40, 300]
      }
    ],
    type: "classwide",
    targets: [20, 40, 300],
    enrolledStudentIds: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    assessmentResultMeasures: [
      {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        medianScore: 20,
        percentMeetingTarget: 85,
        numberMeetingTarget: 17,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "8S52Gz5o85hRkECgq",
        assessmentResultType: "benchmark"
      }
    ]
  }
];

export const commonGroupHistory1 = [
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        percentMeetingTarget: 100, // below 50 is failing
        medianScore: 45,
        cutoffTarget: 40,
        numberMeetingTarget: 20,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        studentScores: [45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45],
        targetScores: [20, 40, 300]
      }
    ],
    enrolledStudentIds: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20"
    ],
    type: "classwide",
    targets: [20, 40, 300]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    assessmentResultMeasures: [
      {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        medianScore: 20,
        percentMeetingTarget: 85,
        numberMeetingTarget: 17,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "8S52Gz5o85hRkECgq",
        assessmentResultType: "benchmark"
      }
    ]
  }
];

export const commonGroupHistory2 = [
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        percentMeetingTarget: 70, // below 50 is failing
        medianScore: 45,
        cutoffTarget: 40,
        numberMeetingTarget: 14,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        studentScores: [45, 45, 45, 45, 45, 45, 45, 15, 15, 15, 45, 45, 45, 45, 45, 45, 45, 15, 15, 15],
        targetScores: [20, 40, 300]
      }
    ],
    type: "classwide",
    targets: [20, 40, 300],
    enrolledStudentIds: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20"
    ]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    assessmentResultMeasures: [
      {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        medianScore: 20,
        percentMeetingTarget: 50,
        numberMeetingTarget: 10,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "8S52Gz5o85hRkECgq",
        assessmentResultType: "benchmark"
      }
    ]
  }
];

export const commonGroupHistory3 = [
  {
    assessmentId: "FxYDJWbMdT8QiZwcD",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        percentMeetingTarget: 60, // below 50 is failing
        medianScore: 45,
        cutoffTarget: 40,
        numberMeetingTarget: 12,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        studentScores: [45, 45, 45, 45, 45, 45, 15, 15, 15, 15, 45, 45, 45, 45, 45, 45, 15, 15, 15, 15],
        targetScores: [20, 40, 300]
      }
    ],
    type: "classwide",
    targets: [20, 40, 300],
    enrolledStudentIds: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20"
    ]
  },
  {
    type: "benchmark",
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    assessmentResultMeasures: [
      {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        assessmentName: "Sums to 6",
        medianScore: 20,
        percentMeetingTarget: 20,
        numberMeetingTarget: 4,
        totalStudentsAssessed: 20,
        benchmarkPeriodId: "8S52Gz5o85hRkECgq",
        assessmentResultType: "benchmark"
      }
    ]
  }
];
