import { getUserInContext } from "./saveGroupData.testData";

const teacherRole = "arbitraryIdteacher";
const schoolYear = 2019;
const getTeacher = getUserInContext({ role: teacherRole, schoolYear });
export default siteId => [
  getTeacher({
    _id: "teacher_user_id1",
    siteIds: [siteId],
    name: {
      first: "Teacher1",
      last: "User1"
    }
  }),
  getTeacher({
    _id: "teacher_user_id2",
    siteIds: [siteId],
    name: {
      first: "Teacher2",
      last: "User2"
    }
  }),
  getTeacher({
    _id: "teacher_user_id3",
    siteIds: [siteId],
    name: {
      first: "Teacher3",
      last: "User3"
    }
  }),
  {
    _id: "admin_user",
    emails: [
      {
        address: "<EMAIL>"
      }
    ],
    profile: {
      siteAccess: [
        {
          role: "arbitraryIdadmin",
          siteId,
          schoolYear
        }
      ],
      name: {
        first: "<PERSON>",
        last: "Coach"
      }
    }
  },
  {
    _id: "data_admin_user_id",
    emails: [
      {
        address: "<EMAIL>"
      }
    ],
    profile: {
      siteAccess: [
        {
          role: "arbitraryIddataAdmin",
          siteId,
          schoolYear
        }
      ],
      name: {
        first: "Dataadmin",
        last: "Dataadmin"
      }
    }
  },
  {
    _id: "super_admin_user_id",
    emails: [
      {
        address: "<EMAIL>"
      }
    ],
    profile: {
      siteAccess: [
        {
          role: "arbitraryIdsuperAdmin",
          siteId,
          schoolYear
        }
      ],
      name: {
        first: "Superadmin",
        last: "Superadmin"
      }
    }
  }
];
