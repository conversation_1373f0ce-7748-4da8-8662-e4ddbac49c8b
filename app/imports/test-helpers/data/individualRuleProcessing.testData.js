import { ScreeningAssignments } from "../../api/screeningAssignments/screeningAssignments";
import { Rules } from "../../api/rules/rules";
import { Students } from "../../api/students/students";
import { Assessments } from "../../api/assessments/assessments";
import { Interventions } from "../../api/interventions/interventions";
import { AssessmentResults } from "../../api/assessmentResults/assessmentResults";

const grade5FallBenchmarkAssessment = {
  _id: "aqikjxjsFL3bwTfSm",
  name: "Fact Families: Multiplication/Division 0-12",
  associatedGrades: ["K", "01", "02", "03", "04", "05", "06", "07", "08", "HS"],
  vendorId: "bL8Em59Jzc7cQEpEQ",
  externalId: "FACTS_MULT_DIV_0_TO_12",
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          externalId: "number_correct",
          targets: [
            {
              grade: "05",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  minValue: 0,
                  maxValue: 300,
                  values: [28, 56, 225]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  minValue: 0,
                  maxValue: 300,
                  values: [28, 56, 225]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  minValue: 0,
                  maxValue: 300,
                  values: [28, 56, 225]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  monitorAssessmentMeasure: "28"
};

const grade5FallAssessment1 = {
  _id: "hCWWDaZE2fusxuSJ9",
  name: "Multiplication 0-12",
  associatedGrades: ["K", "01", "02", "03", "04", "05", "06", "07", "08", "HS"],
  vendorId: "bL8Em59Jzc7cQEpEQ",
  externalId: "MULTIPLY_0_TO_12",
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          externalId: "number_correct",
          targets: [
            {
              grade: "05",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  minValue: 0,
                  maxValue: 300,
                  values: [23, 46, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  minValue: 0,
                  maxValue: 300,
                  values: [23, 46, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  minValue: 0,
                  maxValue: 300,
                  values: [23, 46, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  monitorAssessmentMeasure: "69"
};

const grade5And6Assessment = {
  _id: "E7mZ6Aw9Y4MBE2S5c",
  name: "Multiplication 5-9",
  associatedGrades: ["K", "01", "02", "03", "04", "05", "06", "07", "08", "HS"],
  vendorId: "bL8Em59Jzc7cQEpEQ",
  externalId: "MULTIPLY_5_TO_9",
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          externalId: "number_correct",
          targets: [
            {
              grade: "05",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                }
              ]
            },
            {
              grade: "06",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  minValue: 0,
                  maxValue: 300,
                  values: [20, 40, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  monitorAssessmentMeasure: "68"
};

const grade6WinterBenchmarkAssessment = {
  _id: "ixBf3pfD9gHXFvJQr",
  name: "Distributive Property of Expression",
  monitorAssessmentMeasure: "145",
  associatedGrades: ["K", "01", "02", "03", "04", "05", "06", "07", "08", "HS"],
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          externalId: "number_correct",
          targets: [
            {
              grade: "06",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  minValue: 0,
                  maxValue: 300,
                  values: [6, 11, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  minValue: 0,
                  maxValue: 300,
                  values: [6, 11, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  minValue: 0,
                  maxValue: 300,
                  values: [6, 11, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

const grade5WinterScreeningAssignment = {
  _id: "sivmepE8zDY4icY53",
  grade: "05",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
  assessmentIds: ["GWzcFb2APLvktZ96a", "j3zAzRRuqXCfh3zFs", "RW74o5pNzjfbadBGZ"]
};

const grade5FallScreeningAssignment = {
  _id: "29aTTozPyZdNugTui",
  grade: "05",
  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
  assessmentIds: ["aqikjxjsFL3bwTfSm", "Smm39q4tPmM9TephC", "J8aGfDwkaHFN2cCAM", "3PPCM9w37uGt3Djzc"]
};

const grade6WinterScreeningAssignment = {
  _id: "eDC2jhWQssRiZXgSR",
  grade: "06",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
  assessmentIds: ["ixBf3pfD9gHXFvJQr", "AADFvWuqszqS2fKPM", "5d2704addc002c224a248650", "FLSwETh7aCXLDpNNC"]
};

const grade5WinterRootRule = {
  _id: "e422f42ed220e3",
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "winter-period",
    assessmentId: "GWzcFb2APLvktZ96a"
  },
  outcomes: {
    above: null,
    at: {
      assessmentId: "GWzcFb2APLvktZ96a",
      interventionIds: ["GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "HRWpJGk7cGfmrX7Qw",
      interventionIds: []
    }
  }
};

const grade5FallRootRule = {
  _id: "e422f42ed220cc",
  name: "Fact Families: Multiplication/Division 0-12",
  description: "Intervention Adviser Fact Families: Multiplication / Division 0-12",
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "aqikjxjsFL3bwTfSm"
  },
  outcomes: {
    above: null,
    at: {
      assessmentId: "aqikjxjsFL3bwTfSm",
      interventionIds: ["GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "hCWWDaZE2fusxuSJ9",
      interventionIds: ""
    }
  },
  rootRuleId: "e422f42ed220cc"
};

const grade5FallRule1 = {
  _id: "e422f42ed220cd",
  name: "Multiplication 0-12",
  description: "Intervention Adviser Multiplication 0-12",
  rootRuleId: grade5FallRootRule._id,
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "hCWWDaZE2fusxuSJ9"
  },
  outcomes: {
    above: {
      assessmentId: "qghrmCYKiRTCRQFbY",
      interventionIds: []
    },
    at: {
      assessmentId: "hCWWDaZE2fusxuSJ9",
      interventionIds: ["GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "oCNYMYX3WE8xhRhZR",
      interventionIds: []
    }
  }
};

const grade5FallRule2 = {
  _id: "e422f42ed220d0",
  name: "Multiplication 5-9",
  description: "Intervention Adviser Multiplication 5-9 - #68",
  rootRuleId: grade5FallRootRule._id,
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "E7mZ6Aw9Y4MBE2S5c"
  },
  outcomes: {
    above: {
      assessmentId: "hCWWDaZE2fusxuSJ9",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    },
    at: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    }
  }
};

const grade5FallRule3 = {
  _id: "e422f42ed220d1",
  name: "Division 0-12",
  description: "Intervention Adviser Division 0-12 - #72",
  rootRuleId: grade5FallRootRule._id,
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "qghrmCYKiRTCRQFbY"
  },
  outcomes: {
    above: {
      assessmentId: "aqikjxjsFL3bwTfSm",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    },
    at: {
      assessmentId: "qghrmCYKiRTCRQFbY",
      interventionIds: ["GBqyqxcE6oA6Lo2X7", "YAfRn9TocxMptjqkF"]
    },
    below: {
      assessmentId: "N4vDf85G4rsQp7FPF",
      interventionIds: []
    }
  }
};

const grade5FallRule4 = {
  _id: "e422f42ed22287",
  name: "Multiplication 0-9",
  description: "Intervention Adviser Multiplication 0-9 - #41",
  rootRuleId: "e422f42ed220de",
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "oCNYMYX3WE8xhRhZR"
  },
  outcomes: {
    above: {
      interventionIds: [],
      assessmentId: "E7mZ6Aw9Y4MBE2S5c"
    },
    at: {
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"],
      assessmentId: "oCNYMYX3WE8xhRhZR"
    },
    below: {
      interventionIds: [],
      assessmentId: "XSdsR5SMeczFaEfHW"
    }
  }
};

const grade5FallRule5 = {
  _id: "e422f42ed220ce",
  name: "Multiplication 0-9",
  description: "Intervention Adviser Multiplication 0-9",
  rootRuleId: grade5FallRootRule._id,
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "fall-period",
    assessmentId: "oCNYMYX3WE8xhRhZR"
  },
  outcomes: {
    above: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: []
    },
    at: {
      assessmentId: "oCNYMYX3WE8xhRhZR",
      interventionIds: ["GBqyqxcE6oA6Lo2X7", "YAfRn9TocxMptjqkF"]
    },
    below: {
      assessmentId: "XSdsR5SMeczFaEfHW",
      interventionIds: []
    }
  }
};

const grade6WinterRootRule = {
  _id: "NSxGWK5xsAaYKjzQg",
  prerequisite: {
    ruleId: "e422f42ed220c0",
    level: "above"
  },
  name: "Distributive Property of Expression",
  description: "Intervention Advisor - Distributive Property of Expression #145",
  attributeValues: {
    grade: "06",
    benchmarkPeriod: "winter-period",
    assessmentId: "ixBf3pfD9gHXFvJQr"
  },
  outcomes: {
    above: null,
    at: {
      assessmentId: "ixBf3pfD9gHXFvJQr",
      interventionIds: ["GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "kmS5FqgMj76hFyDf6",
      interventionIds: ""
    }
  }
};

const grade6WinterRule1 = {
  _id: "2ADcDjMnw88Ck4P85",
  name: "Multiplication 5-9",
  description: "Intervention Adviser Multiplication 5-9 - #68",
  rootRuleId: grade6WinterRootRule._id,
  attributeValues: {
    grade: "06",
    benchmarkPeriod: "winter-period",
    assessmentId: "E7mZ6Aw9Y4MBE2S5c"
  },
  outcomes: {
    above: {
      assessmentId: "hCWWDaZE2fusxuSJ9",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    },
    at: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "XSdsR5SMeczFaEfHW",
      interventionIds: []
    }
  }
};

const grade6WinterRule2 = {
  _id: "e422f42ed22225",
  name: "Multiplication 0-9",
  description: "Intervention Adviser Multiplication 0-9",
  rootRuleId: "e422f42ed220c0",
  attributeValues: {
    grade: "06",
    benchmarkPeriod: "winter-period",
    assessmentId: "oCNYMYX3WE8xhRhZR"
  },
  outcomes: {
    above: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: []
    },
    at: {
      assessmentId: "oCNYMYX3WE8xhRhZR",
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "XSdsR5SMeczFaEfHW",
      interventionIds: []
    }
  }
};

const grade6WinterRule3 = {
  _id: "7N4JvBdR5xDczL74b",
  name: "Multiplication 0-5",
  description: "Intervention Adviser Multiplication 0-5 - #66",
  rootRuleId: grade6WinterRootRule._id,
  attributeValues: {
    grade: "06",
    benchmarkPeriod: "winter-period",
    assessmentId: "XSdsR5SMeczFaEfHW"
  },
  outcomes: {
    above: {
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"],
      assessmentId: "E7mZ6Aw9Y4MBE2S5c"
    },
    at: {
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"],
      assessmentId: "XSdsR5SMeczFaEfHW"
    },
    below: {
      interventionIds: [],
      assessmentId: "ZtJxX6rjuoRwAqRFo"
    }
  }
};

const grade6WinterRule4 = {
  _id: "6PTfLGgzgpM7dZDMtb",
  name: "Division 0-12",
  description: "Intervention Adviser Division 0-12 - #72",
  rootRuleId: grade6WinterRootRule._id,
  attributeValues: {
    grade: "06",
    benchmarkPeriod: "winter-period",
    assessmentId: "qghrmCYKiRTCRQFbY"
  },
  outcomes: {
    above: {
      assessmentId: "E7mZ6Aw9Y4MBE2S5c",
      interventionIds: []
    },
    at: {
      assessmentId: "qghrmCYKiRTCRQFbY",
      interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "qghrmCYKiRTCRQFbY",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    }
  }
};

const assessmentResultFailed = {
  _id: "yyg2xQmRuP6DKdADD",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
  orgid: "test_organization_id",
  schoolYear: 2018,
  status: "COMPLETED",
  studentGroupId: "TEST_2018_6cShSTZBa2vN",
  studentId: "FH75tvoSZAe9RDbhe",
  type: "individual",
  previousAssessmentResultId: "QfM7kEmduSk3iHYuE",
  grade: "05",
  rootRuleId: "e422f42ed220e3",
  scores: [
    {
      _id: "5QwmkXuvArRhjCwMv",
      assessmentId: "GWzcFb2APLvktZ96a",
      orgid: "test_organization_id",
      siteId: "test_elementary_site_id",
      status: "COMPLETE",
      studentId: "FH75tvoSZAe9RDbhe",
      value: "0"
    },
    {
      _id: "F6tinqs4fkRhdBYLB",
      assessmentId: "GWzcFb2APLvktZ96a",
      orgid: "test_organization_id",
      siteId: "test_elementary_site_id",
      status: "STARTED",
      studentId: "FH75tvoSZAe9RDbhe"
    }
  ],
  assessmentIds: ["GWzcFb2APLvktZ96a", "GWzcFb2APLvktZ96a"],
  individualSkills: {
    benchmarkAssessmentId: "GWzcFb2APLvktZ96a",
    benchmarkAssessmentName: "Convert Improper Fractions to Mixed Numbers",
    benchmarkAssessmentTargets: [11, 23, 300],
    assessmentId: "GWzcFb2APLvktZ96a",
    assessmentName: "Convert Improper Fractions to Mixed Numbers",
    assessmentTargets: [11, 23, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "yyg2xQmRuP6DKdADD"
  },
  measures: [
    {
      assessmentId: "GWzcFb2APLvktZ96a",
      assessmentName: "Convert Improper Fractions to Mixed Numbers",
      cutoffTarget: 23,
      targetScores: [11, 23, 300],
      medianScore: 0,
      studentScores: [0],
      percentMeetingTarget: 0,
      numberMeetingTarget: 0,
      totalStudentsAssessed: 1,
      studentResults: [
        {
          studentId: "FH75tvoSZAe9RDbhe",
          status: "COMPLETE",
          firstName: "Florence",
          lastName: "Cunningham",
          score: "0",
          meetsTarget: false,
          individualRuleOutcome: "below"
        }
      ]
    }
  ],
  ruleResults: {
    passed: false,
    nextSkill: {
      assessmentId: "GWzcFb2APLvktZ96a",
      assessmentName: "Convert Improper Fractions to Mixed Numbers",
      interventions: [
        {
          interventionId: "56attvmgjtrjMsFif",
          interventionLabel: "Intervention Adviser - Cover Copy and Compare",
          interventionAbbrv: "CCC"
        },
        {
          interventionId: "JC4A2Jx6gKfqLe9LF",
          interventionLabel: "Intervention Adviser - Guided Practice",
          interventionAbbrv: "GP"
        }
      ]
    },
    nextAssessmentResultId: "SiKb7jaRB5uR9Nhk4"
  },
  nextAssessmentResultId: "SiKb7jaRB5uR9Nhk4"
};

const passedRule = {
  _id: "e422f42ed220e4",
  rootRuleId: "e422f42ed220e3",
  attributeValues: {
    grade: "05",
    benchmarkPeriod: "winter-period",
    assessmentId: "HRWpJGk7cGfmrX7Qw"
  },
  outcomes: {
    above: {
      assessmentId: "GWzcFb2APLvktZ96a",
      interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
    },
    at: {
      assessmentId: "HRWpJGk7cGfmrX7Qw",
      interventionIds: ["GBqyqxcE6oA6Lo2X7"]
    },
    below: {
      assessmentId: "hWtYyiZvipMb4hyGk",
      interventionIds: []
    }
  },
  enabled: true,
  created: {
    by: "RX3SWtt9cRkSj9HTr",
    on: 1457467520831.0
  },
  lastModified: {
    by: "SYSTEM",
    on: 1473256304743.0
  },
  ruleDefinitionId: "Cduhb8tn82xEfjnar",
  conditionTypeId: "INTADecisionTree",
  schoolYear: 2017,
  priorYearId: "TxTama58qK8TceFcC",
  priorYearRootId: "hZqbaq6JkmXfeXPu7"
};

const assessmentResultLastBenchmarkSkillPassed = {
  _id: "QT867GKnBcY7ZTMye",
  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
  orgid: "test_organization_id",
  created: {
    by: "",
    on: 1521191780893,
    date: "2018-03-16T09:16:20.893Z"
  },
  schoolYear: 2018,
  status: "OPEN",
  studentGroupId: "TEST_2018_6cShSTZBa2vN",
  studentId: "FH75tvoSZAe9RDbhe",
  type: "individual",
  previousAssessmentResultId: "fkQroniRGwK65nNqN",
  grade: "05",
  rootRuleId: "e422f42ed220e8",
  scores: [
    {
      _id: "knBceQbbdodCJSxbM",
      assessmentId: "RW74o5pNzjfbadBGZ",
      orgid: "test_organization_id",
      siteId: "test_elementary_site_id",
      status: "COMPLETE",
      studentId: "FH75tvoSZAe9RDbhe",
      value: "90"
    },
    {
      _id: "XrSLKeem7kv2EKMMq",
      assessmentId: "RW74o5pNzjfbadBGZ",
      orgid: "test_organization_id",
      siteId: "test_elementary_site_id",
      status: "STARTED",
      studentId: "FH75tvoSZAe9RDbhe"
    }
  ],
  assessmentIds: ["RW74o5pNzjfbadBGZ", "RW74o5pNzjfbadBGZ"],
  individualSkills: {
    benchmarkAssessmentId: "RW74o5pNzjfbadBGZ",
    benchmarkAssessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
    benchmarkAssessmentTargets: [40, 80, 300],
    assessmentId: "RW74o5pNzjfbadBGZ",
    assessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
    assessmentTargets: [40, 80, 300],
    interventions: [],
    assessmentResultId: "QT867GKnBcY7ZTMye"
  },
  classwideResults: {
    percentMeetingTarget: 100,
    percentAtRisk: 0,
    totalStudentsMeetingAllTargets: 1,
    totalStudentsAssessedOnAllMeasures: 1,
    studentIdsNotMeetingTarget: []
  },
  measures: [
    {
      assessmentId: "RW74o5pNzjfbadBGZ",
      assessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
      cutoffTarget: 40,
      targetScores: [40, 80, 300],
      medianScore: 90,
      studentScores: [90],
      percentMeetingTarget: 100,
      numberMeetingTarget: 1,
      totalStudentsAssessed: 1,
      studentResults: [
        {
          studentId: "FH75tvoSZAe9RDbhe",
          status: "COMPLETE",
          firstName: "Florence",
          lastName: "Cunningham",
          score: "90",
          meetsTarget: true,
          individualRuleOutcome: "above"
        }
      ]
    }
  ]
};

const studentGroupPassed = {
  _id: "TEST_2018_6cShSTZBa2vN",
  orgid: "test_organization_id",
  siteId: "test_elementary_site_id",
  grade: "05",
  created: {
    by: "TEST",
    on: 1501711540084,
    date: "2017-08-02T22:05:40.084Z"
  },
  lastModified: {
    by: "file_upload",
    on: 1515083741827,
    date: "2018-01-04T16:35:41.827Z"
  },
  type: "CLASS",
  ownerIds: ["teacher_user_id"],
  isActive: true,
  sectionId: "fqaFT_05",
  name: "Test 05 (6ss1D-fqaFT_05-)",
  schoolYear: 2018,
  rosterImportId: "sLwEoo7zTXpWrouwk",
  history: [
    {
      type: "benchmark",
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      assessmentResultId: "fkQroniRGwK65nNqN",
      whenEnded: {
        by: "",
        on: 1521191777831,
        date: "2018-03-16T09:16:17.831Z"
      },
      whenStarted: null,
      assessmentResultMeasures: [
        {
          assessmentId: "GWzcFb2APLvktZ96a",
          assessmentName: "Convert Improper Fractions to Mixed Numbers",
          cutoffTarget: 11,
          targetScores: [11, 23, 300],
          medianScore: 50,
          studentScores: [1, 50, 50, 50, 50],
          percentMeetingTarget: 80,
          numberMeetingTarget: 4,
          totalStudentsAssessed: 5,
          studentResults: [
            {
              studentId: "FH75tvoSZAe9RDbhe",
              status: "COMPLETE",
              firstName: "Florence",
              lastName: "Cunningham",
              score: "1",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "Fx9dBeurvJzBzCY9h",
              status: "COMPLETE",
              firstName: "Isaiah",
              lastName: "Johnston",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "tMqvnppN3iuQeQcWR",
              status: "COMPLETE",
              firstName: "Ryan",
              lastName: "Powell",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "PJn9edSfHMiuMKSNX",
              status: "COMPLETE",
              firstName: "Raymond",
              lastName: "Townsend",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "DcmNa6RLSE2iY4aRo",
              status: "COMPLETE",
              firstName: "Emilie",
              lastName: "Vaughn",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            }
          ],
          benchmarkPeriodId: "nEsbWokBWutTZFkTh",
          grade: "05",
          assessmentResultType: "benchmark"
        },
        {
          assessmentId: "j3zAzRRuqXCfh3zFs",
          assessmentName: "Add/Subtract Fractions with Unlike Denominators",
          cutoffTarget: 3,
          targetScores: [3, 6, 300],
          medianScore: 50,
          studentScores: [1, 50, 50, 50, 50],
          percentMeetingTarget: 80,
          numberMeetingTarget: 4,
          totalStudentsAssessed: 5,
          studentResults: [
            {
              studentId: "FH75tvoSZAe9RDbhe",
              status: "COMPLETE",
              firstName: "Florence",
              lastName: "Cunningham",
              score: "1",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "Fx9dBeurvJzBzCY9h",
              status: "COMPLETE",
              firstName: "Isaiah",
              lastName: "Johnston",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "tMqvnppN3iuQeQcWR",
              status: "COMPLETE",
              firstName: "Ryan",
              lastName: "Powell",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "PJn9edSfHMiuMKSNX",
              status: "COMPLETE",
              firstName: "Raymond",
              lastName: "Townsend",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "DcmNa6RLSE2iY4aRo",
              status: "COMPLETE",
              firstName: "Emilie",
              lastName: "Vaughn",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "above"
            }
          ],
          benchmarkPeriodId: "nEsbWokBWutTZFkTh",
          grade: "05",
          assessmentResultType: "benchmark"
        },
        {
          assessmentId: "RW74o5pNzjfbadBGZ",
          assessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
          cutoffTarget: 40,
          targetScores: [40, 80, 300],
          medianScore: 50,
          studentScores: [1, 50, 50, 50, 50],
          percentMeetingTarget: 80,
          numberMeetingTarget: 4,
          totalStudentsAssessed: 5,
          studentResults: [
            {
              studentId: "FH75tvoSZAe9RDbhe",
              status: "COMPLETE",
              firstName: "Florence",
              lastName: "Cunningham",
              score: "1",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "Fx9dBeurvJzBzCY9h",
              status: "COMPLETE",
              firstName: "Isaiah",
              lastName: "Johnston",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "at"
            },
            {
              studentId: "tMqvnppN3iuQeQcWR",
              status: "COMPLETE",
              firstName: "Ryan",
              lastName: "Powell",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "at"
            },
            {
              studentId: "PJn9edSfHMiuMKSNX",
              status: "COMPLETE",
              firstName: "Raymond",
              lastName: "Townsend",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "at"
            },
            {
              studentId: "DcmNa6RLSE2iY4aRo",
              status: "COMPLETE",
              firstName: "Emilie",
              lastName: "Vaughn",
              score: "50",
              meetsTarget: true,
              individualRuleOutcome: "at"
            }
          ]
        }
      ],
      enrolledStudentIds: [
        "FH75tvoSZAe9RDbhe",
        "Fx9dBeurvJzBzCY9h",
        "tMqvnppN3iuQeQcWR",
        "PJn9edSfHMiuMKSNX",
        "DcmNa6RLSE2iY4aRo"
      ]
    }
  ],
  currentAssessmentResultIds: ["CWeX3LB22tbKAKLFY"],
  individualInterventionQueue: [],
  students: [
    {
      _id: "Se4JMBCuwcmXy2rco",
      orgid: "test_organization_id",
      rosterImportId: "sLwEoo7zTXpWrouwk",
      siteId: "test_elementary_site_id",
      grade: "05",
      studentGroupId: "TEST_2018_6cShSTZBa2vN",
      studentId: "FH75tvoSZAe9RDbhe",
      isActive: true,
      schoolYear: 2018,
      giftedAndTalented: null,
      ELL: null,
      IEP: null,
      title1: "",
      freeReducedLunch: null,
      created: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      },
      lastModified: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      }
    },
    {
      _id: "cmfWY3SLyQ3zPm89n",
      orgid: "test_organization_id",
      rosterImportId: "sLwEoo7zTXpWrouwk",
      siteId: "test_elementary_site_id",
      grade: "05",
      studentGroupId: "TEST_2018_6cShSTZBa2vN",
      studentId: "Fx9dBeurvJzBzCY9h",
      isActive: true,
      schoolYear: 2018,
      giftedAndTalented: null,
      ELL: null,
      IEP: null,
      title1: "",
      freeReducedLunch: null,
      created: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      },
      lastModified: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      }
    },
    {
      _id: "rrsymbMwYFHoq2uGP",
      orgid: "test_organization_id",
      rosterImportId: "sLwEoo7zTXpWrouwk",
      siteId: "test_elementary_site_id",
      grade: "05",
      studentGroupId: "TEST_2018_6cShSTZBa2vN",
      studentId: "tMqvnppN3iuQeQcWR",
      isActive: true,
      schoolYear: 2018,
      giftedAndTalented: null,
      ELL: null,
      IEP: null,
      title1: "",
      freeReducedLunch: null,
      created: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      },
      lastModified: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      }
    },
    {
      _id: "rnpB7A5zdazSXipSp",
      orgid: "test_organization_id",
      rosterImportId: "sLwEoo7zTXpWrouwk",
      siteId: "test_elementary_site_id",
      grade: "05",
      studentGroupId: "TEST_2018_6cShSTZBa2vN",
      studentId: "PJn9edSfHMiuMKSNX",
      isActive: true,
      schoolYear: 2018,
      giftedAndTalented: null,
      ELL: null,
      IEP: null,
      title1: "",
      freeReducedLunch: null,
      created: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      },
      lastModified: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      }
    },
    {
      _id: "zwxDn6DFtddPDhj4W",
      orgid: "test_organization_id",
      rosterImportId: "sLwEoo7zTXpWrouwk",
      siteId: "test_elementary_site_id",
      grade: "05",
      studentGroupId: "TEST_2018_6cShSTZBa2vN",
      studentId: "DcmNa6RLSE2iY4aRo",
      isActive: true,
      schoolYear: 2018,
      giftedAndTalented: null,
      ELL: null,
      IEP: null,
      title1: "",
      freeReducedLunch: null,
      created: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      },
      lastModified: {
        by: "file_upload",
        on: 1515083741827,
        date: "2018-01-04T16:35:41.827Z"
      }
    }
  ]
};

const student = {
  _id: "FH75tvoSZAe9RDbhe",
  orgid: "test_organization_id",
  schoolYear: 2018,
  districtNumber: "138",
  created: {
    by: "file_upload",
    on: 1515083741827.0
  },
  lastModified: {
    by: "file_upload",
    on: 1515083741827.0
  },
  rosterImportId: "sLwEoo7zTXpWrouwk",
  grade: "05",
  demographic: {
    birthDate: "2006-05-17",
    ethnicity: "",
    gender: "F",
    gt: "",
    sped: "",
    ell: "",
    title: "",
    birthDateTimeStamp: 1147824000000.0
  },
  identity: {
    name: {
      firstName: "Florence",
      lastName: "Cunningham",
      middleName: ""
    },
    identification: {
      localId: "1523709",
      stateId: "4013335"
    }
  },
  history: [
    {
      benchmarkAssessmentId: "GWzcFb2APLvktZ96a",
      benchmarkAssessmentName: "Convert Improper Fractions to Mixed Numbers",
      benchmarkAssessmentTargets: [11, 23, 300],
      assessmentId: "GWzcFb2APLvktZ96a",
      assessmentName: "Convert Improper Fractions to Mixed Numbers",
      assessmentTargets: [11, 23, 300],
      interventions: [
        {
          interventionId: "56attvmgjtrjMsFif",
          interventionLabel: "Intervention Adviser - Cover Copy and Compare",
          interventionAbbrv: "CCC"
        },
        {
          interventionId: "JC4A2Jx6gKfqLe9LF",
          interventionLabel: "Intervention Adviser - Guided Practice",
          interventionAbbrv: "GP"
        }
      ],
      assessmentResultId: "yyg2xQmRuP6DKdADD",
      whenStarted: {
        by: "",
        on: 1520598468315.0
      },
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      message: {
        messageCode: "59",
        dismissed: false
      },
      assessmentResultMeasures: [
        {
          assessmentId: "GWzcFb2APLvktZ96a",
          assessmentName: "Convert Improper Fractions to Mixed Numbers",
          cutoffTarget: 23,
          targetScores: [11, 23, 300],
          medianScore: 0,
          studentScores: [0],
          percentMeetingTarget: 0,
          numberMeetingTarget: 0,
          totalStudentsAssessed: 1,
          studentResults: [
            {
              studentId: "FH75tvoSZAe9RDbhe",
              status: "COMPLETE",
              firstName: "Florence",
              lastName: "Cunningham",
              score: "0",
              meetsTarget: false,
              individualRuleOutcome: "below"
            }
          ]
        }
      ],
      type: "individual",
      whenEnded: {
        by: "",
        on: 1520598498789.0
      }
    },
    {
      benchmarkAssessmentId: "GWzcFb2APLvktZ96a",
      benchmarkAssessmentName: "Convert Improper Fractions to Mixed Numbers",
      benchmarkAssessmentTargets: [11, 23, 300],
      assessmentId: "HRWpJGk7cGfmrX7Qw",
      assessmentName: "Divide 2-Digit into 3-4 Digit w/Remainders",
      assessmentTargets: [4, 7, 100],
      interventions: [],
      assessmentResultId: "QfM7kEmduSk3iHYuE",
      whenStarted: {
        by: "",
        on: 1520598462438.0
      },
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      message: {
        messageCode: "51",
        dismissed: false
      },
      assessmentResultMeasures: [
        {
          assessmentId: "HRWpJGk7cGfmrX7Qw",
          assessmentName: "Divide 2-Digit into 3-4 Digit w/Remainders",
          cutoffTarget: 7,
          targetScores: [4, 7, 100],
          medianScore: 10,
          studentScores: [10],
          percentMeetingTarget: 100,
          numberMeetingTarget: 1,
          totalStudentsAssessed: 1,
          studentResults: [
            {
              studentId: "FH75tvoSZAe9RDbhe",
              status: "COMPLETE",
              firstName: "Florence",
              lastName: "Cunningham",
              score: "10",
              meetsTarget: true,
              individualRuleOutcome: "above"
            }
          ]
        }
      ],
      type: "individual",
      whenEnded: {
        by: "",
        on: 1520598468315.0
      }
    }
  ],
  currentSkill: {
    benchmarkAssessmentId: "GWzcFb2APLvktZ96a",
    benchmarkAssessmentName: "Convert Improper Fractions to Mixed Numbers",
    benchmarkAssessmentTargets: [11, 23, 300],
    assessmentId: "GWzcFb2APLvktZ96a",
    assessmentName: "Convert Improper Fractions to Mixed Numbers",
    assessmentTargets: [11, 23, 300],
    interventions: [
      {
        interventionId: "56attvmgjtrjMsFif",
        interventionLabel: "Intervention Adviser - Cover Copy and Compare",
        interventionAbbrv: "CCC"
      },
      {
        interventionId: "JC4A2Jx6gKfqLe9LF",
        interventionLabel: "Intervention Adviser - Guided Practice",
        interventionAbbrv: "GP"
      }
    ],
    assessmentResultId: "SiKb7jaRB5uR9Nhk4",
    whenStarted: {
      by: "",
      on: 1520598498789.0
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    message: {
      messageCode: "52",
      dismissed: false
    }
  }
};

const studentFinishingSkillTree = {
  ...student,
  history: [...student.history],
  currentSkill: {
    benchmarkAssessmentId: "RW74o5pNzjfbadBGZ",
    benchmarkAssessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
    benchmarkAssessmentTargets: [40, 80, 300],
    assessmentId: "RW74o5pNzjfbadBGZ",
    assessmentName: "Quantity Compare of Fractions, Whole Numbers & Decimals",
    assessmentTargets: [40, 80, 300],
    interventions: [],
    assessmentResultId: "QT867GKnBcY7ZTMye",
    whenStarted: {
      by: "ZJ6BF4XKa6tfTKYH3",
      on: 1647962922156
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    message: {
      messageCode: "55",
      dismissed: false
    }
  }
};

const assessment = {
  _id: "GWzcFb2APLvktZ96a",
  name: "Convert Improper Fractions to Mixed Numbers",
  associatedGrades: ["04", "05", "06", "07", "08"],
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          externalId: "number_correct",
          targets: [
            {
              grade: "04",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  values: [11, 23, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  values: [11, 23, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  values: [11, 23, 300]
                }
              ]
            },
            {
              grade: "05",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  values: [11, 23, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  values: [11, 23, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  values: [11, 23, 300]
                }
              ]
            },
            {
              grade: "06",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  values: [11, 23, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  values: [11, 23, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  values: [11, 23, 300]
                }
              ]
            },
            {
              grade: "07",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  values: [14, 29, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  values: [14, 29, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  values: [14, 29, 300]
                }
              ]
            },
            {
              grade: "08",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                  values: [14, 29, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                  values: [14, 29, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                  values: [14, 29, 300]
                }
              ]
            }
          ]
        },
        {
          name: "Number Errors",
          externalId: "number_errors",
          targets: []
        },
        {
          name: "Accuracy",
          externalId: "accuracy",
          targets: []
        },
        {
          name: "Total Items",
          externalId: "total_items",
          targets: []
        }
      ]
    }
  ],
  monitorAssessmentMeasure: "64"
};

const interventionRC = {
  _id: "YAfRn9TocxMptjqkF",
  name: "Intervention Adviser - Response Cards",
  abbreviation: "RC"
};

const interventionTT = {
  _id: "GBqyqxcE6oA6Lo2X7",
  name: "Intervention Adviser - Timed Trial",
  abbreviation: "TT"
};

const interventionCCC = {
  _id: "56attvmgjtrjMsFif",
  name: "Intervention Adviser - Cover Copy and Compare",
  abbreviation: "CCC"
};

const interventionGP = {
  _id: "JC4A2Jx6gKfqLe9LF",
  name: "Intervention Adviser - Guided Practice",
  abbreviation: "GP"
};

const interventions = [
  interventionRC,
  interventionTT,
  interventionCCC,
  interventionGP
];

const passedInterventionOutcome1 = {
  _id: "56attvmgjtrjMsFif",
  name: "Intervention Adviser - Cover Copy and Compare",
  abbreviation: "CCC"
};

const passedInterventionOutcome2 = {
  _id: "JC4A2Jx6gKfqLe9LF",
  name: "Intervention Adviser - Guided Practice",
  abbreviation: "GP"
};

function setupProcessIndivualRuleHSData({
  studentId,
  studentId2,
  fourweekRuleStudentId,
  studentGroupId,
  studentGroupId2,
  rootRuleId,
  benchmarkPeriodId,
  passingLastAssessmentResultId
}) {
  const orgid = "orgid";
  const siteId = "siteId";
  const startingAssessmentResultId = "startingAssessmentResultId";
  const previousAssessmentResultId = "previousAssessmentResultId";
  const grade = "HS";
  const assessmentIdBySkillNumber = {
    firstClassSkill: "firstAssessmentId",
    secondClassSkill: "secondAssessmentId",
    thirdClassSkill: "thirdAssessmentId"
  };

  const skillHierarchy = {
    secondTree: {
      node0: {
        name: "Count Objects 1-10, Circle Answer",
        assessmentId: `${assessmentIdBySkillNumber.secondClassSkill}_0`
      },
      node1: {
        name: "Count Objects Aloud 1-20",
        assessmentId: `${assessmentIdBySkillNumber.secondClassSkill}_1`
      },
      node2: {
        name: "Number Names 0-10",
        assessmentId: `${assessmentIdBySkillNumber.secondClassSkill}_2`
      }
    },
    thirdTree: {
      node0: {
        name: "Count Objects 1-20, Circle Answer",
        assessmentId: `${assessmentIdBySkillNumber.thirdClassSkill}_0`
      },
      node1: {
        name: "Count Objects Aloud 10-20",
        assessmentId: `${assessmentIdBySkillNumber.thirdClassSkill}_1`
      },
      node2: {
        name: "Number Names 1-20",
        assessmentId: `${assessmentIdBySkillNumber.thirdClassSkill}_2`
      }
    }
  };

  // eslint-disable-next-line no-underscore-dangle
  const assessments = [
    ...Object.values(skillHierarchy)
      .map(tree => {
        return Object.values(tree).map(node => {
          return {
            _id: node.assessmentId,
            name: node.name,
            strands: [
              {
                scores: [
                  {
                    name: node.name,
                    externalId: "number_correct",
                    targets: [{ grade: "HS", periods: [{ benchmarkPeriodId, values: [1, 5, 10] }] }]
                  }
                ]
              }
            ]
          };
        });
      })
      .flat(2)
  ];
  function getCustomStudentIdAssessmentResult(sId, aId, rId, sgId) {
    return {
      _id: `${startingAssessmentResultId}_${sId}`,
      benchmarkPeriodId,
      orgid,
      schoolYear: 2023,
      status: "COMPLETED",
      studentGroupId: sgId,
      studentId: sId,
      type: "benchmark",
      grade,
      ...(rId ? { rId } : {}),
      assessmentIds: Object.values(assessmentIdBySkillNumber).map(s => `${s}_0`),
      measures: [
        {
          assessmentId: aId,
          medianScore: 0,
          studentScores: [0],
          percentMeetingTarget: 0,
          numberMeetingTarget: 0,
          totalStudentsAssessed: 1,
          studentResults: [
            {
              studentId: sId,
              status: "COMPLETE",
              score: "0",
              meetsTarget: false,
              individualRuleOutcome: "below"
            }
          ]
        }
      ],
      scores: [
        {
          _id: "yyynvq8weFx4wFK8L",
          assessmentId: aId,
          orgid,
          siteId,
          status: "STARTED",
          studentId: sId
        }
      ]
    };
  }
  const passingLastAssessmentResult = {
    _id: passingLastAssessmentResultId,
    benchmarkPeriodId,
    orgid,
    schoolYear: 2023,
    status: "OPEN",
    studentGroupId: studentGroupId2,
    studentId: studentId2,
    type: "individual",
    previousAssessmentResultId,
    grade,
    rootRuleId: skillHierarchy.secondTree.node0.assessmentId,
    assessmentIds: [skillHierarchy.secondTree.node0.assessmentId],
    individualSkills: {
      benchmarkAssessmentId: skillHierarchy.secondTree.node0.assessmentId,
      benchmarkAssessmentName: skillHierarchy.secondTree.node0.name,
      benchmarkAssessmentTargets: [7, 14, 100],
      assessmentId: skillHierarchy.secondTree.node0.assessmentId,
      assessmentName: skillHierarchy.secondTree.node0.name,
      assessmentTargets: [7, 14, 100],
      interventions: [
        {
          interventionId: "56attvmgjtrjMsFif",
          interventionLabel: "Intervention Adviser - Cover Copy and Compare",
          interventionAbbrv: "CCC"
        },
        {
          interventionId: "JC4A2Jx6gKfqLe9LF",
          interventionLabel: "Intervention Adviser - Guided Practice",
          interventionAbbrv: "GP"
        }
      ],
      assessmentResultId: passingLastAssessmentResultId
    },
    scores: [
      {
        _id: "tBgoMeJd5M96RA6hT",
        assessmentId: skillHierarchy.secondTree.node0.assessmentId,
        orgid,
        siteId,
        status: "COMPLETE",
        studentId: studentId2,
        value: "30"
      }
    ],
    classwideResults: {
      percentMeetingTarget: null,
      percentAtRisk: 100,
      totalStudentsMeetingAllTargets: 1,
      totalStudentsAssessedOnAllMeasures: 0,
      studentIdsNotMeetingTarget: []
    },
    measures: [
      {
        assessmentId: skillHierarchy.secondTree.node0.assessmentId,
        assessmentName: skillHierarchy.secondTree.node0.name,
        cutoffTarget: 14,
        targetScores: [7, 14, 100],
        medianScore: 30,
        studentScores: [30],
        percentMeetingTarget: 100,
        numberMeetingTarget: 1,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: studentId2,
            status: "COMPLETE",
            score: "30",
            meetsTarget: true,
            individualRuleOutcome: "above"
          }
        ]
      }
    ]
  };

  const fullSkillTreeRules = Object.values(skillHierarchy).map(treeNodes => {
    return [
      // Root Rule, node 0 goalSkillAssessmentId
      {
        _id: treeNodes.node0.assessmentId,
        name: treeNodes.node0.name,
        attributeValues: {
          grade: "HS",
          benchmarkPeriod: "all-periods",
          assessmentId: treeNodes.node0.assessmentId
        },
        outcomes: {
          above: null,
          at: {
            assessmentId: treeNodes.node0.assessmentId,
            interventionIds: ["GBqyqxcE6oA6Lo2X7", "YAfRn9TocxMptjqkF"]
          },
          below: {
            assessmentId: treeNodes.node1.assessmentId,
            interventionIds: ""
          }
        },
        rootRuleId: treeNodes.node0.assessmentId
      },
      // 1 level deep, node 1
      {
        _id: treeNodes.node1.assessmentId,
        name: treeNodes.node1.name,
        rootRuleId: treeNodes.node0.assessmentId,
        attributeValues: {
          grade: "HS",
          benchmarkPeriod: "all-periods",
          assessmentId: treeNodes.node1.assessmentId
        },
        outcomes: {
          above: {
            assessmentId: treeNodes.node2.assessmentId,
            interventionIds: []
          },
          at: {
            assessmentId: treeNodes.node1.assessmentId,
            interventionIds: ["GBqyqxcE6oA6Lo2X7"]
          },
          below: {
            assessmentId: treeNodes.node1.assessmentId,
            interventionIds: ["JC4A2Jx6gKfqLe9LF"]
          }
        }
      },
      // 2 levels deep, node 2
      {
        _id: treeNodes.node2.assessmentId,
        name: treeNodes.node2.name,
        rootRuleId: treeNodes.node0.assessmentId,
        attributeValues: {
          grade: "HS",
          benchmarkPeriod: "all-periods",
          assessmentId: treeNodes.node2.assessmentId
        },
        outcomes: {
          above: {
            assessmentId: treeNodes.node0.assessmentId,
            interventionIds: ["oRLcohGFunw5ZqoAN", "JC4A2Jx6gKfqLe9LF", "56attvmgjtrjMsFif"]
          },
          at: {
            assessmentId: treeNodes.node2.assessmentId,
            interventionIds: ["YAfRn9TocxMptjqkF"]
          },
          below: {
            assessmentId: treeNodes.node2.assessmentId,
            interventionIds: ["JC4A2Jx6gKfqLe9LF", "oRLcohGFunw5ZqoAN"]
          }
        }
      }
    ];
  });
  const assessmentResults = [
    getCustomStudentIdAssessmentResult(
      studentId,
      skillHierarchy.secondTree.node0.assessmentId,
      rootRuleId,
      studentGroupId
    ),
    passingLastAssessmentResult,
    getCustomStudentIdAssessmentResult(
      fourweekRuleStudentId,
      skillHierarchy.thirdTree.node0.assessmentId,
      null,
      studentGroupId2
    )
  ];
  const students = [
    {
      _id: studentId,
      grade: "HS"
    },
    {
      _id: studentId2,
      grade: "HS",
      currentSkill: {
        benchmarkAssessmentId: skillHierarchy.secondTree.node0.assessmentId,
        benchmarkAssessmentName: skillHierarchy.secondTree.node0.name,
        benchmarkAssessmentTargets: [1, 5, 10],
        assessmentId: skillHierarchy.secondTree.node1.assessmentId,
        assessmentName: skillHierarchy.secondTree.node1.name,
        assessmentTargets: [1, 5, 10],
        interventions: [],
        assessmentResultId: passingLastAssessmentResultId,
        benchmarkPeriodId,
        message: {
          messageCode: "51",
          dismissed: false
        }
      }
    },
    {
      _id: fourweekRuleStudentId,
      grade: "HS",
      identity: {
        name: {
          firstName: "first2",
          lastName: "last2"
        }
      }
    }
  ];
  const studentGroups = [
    {
      _id: studentGroupId,
      grade: "HS",
      currentClasswideSkill: {
        assessmentId: skillHierarchy.secondTree.node0.assessmentId,
        assessmentName: skillHierarchy.secondTree.node0.name
      }
    },
    {
      _id: studentGroupId2,
      grade: "HS",
      currentClasswideSkill: {
        assessmentId: skillHierarchy.thirdTree.node0.assessmentId,
        assessmentName: skillHierarchy.secondTree.node0.name
      },
      individualInterventionQueue: [fourweekRuleStudentId]
    }
  ];
  const screeningAssignmentDoc = {
    assessmentIds: [
      assessmentIdBySkillNumber.firstClassSkill,
      skillHierarchy.secondTree.node0.assessmentId,
      skillHierarchy.thirdTree.node0.assessmentId
    ],
    benchmarkPeriodId,
    grade: "HS"
  };
  return {
    skillHierarchy,
    assessments,
    getCustomStudentIdAssessmentResult,
    passingLastAssessmentResult,
    fullSkillTreeRules,
    assessmentResults,
    students,
    studentGroups,
    screeningAssignmentDoc
  };
}

const assignNextTreeStudentPassedTestSetup = async () => {
  await ScreeningAssignments.insertAsync(grade5WinterScreeningAssignment);
  await Rules.insertAsync(grade5WinterRootRule);
  await Rules.insertAsync(passedRule);
  await Interventions.insertAsync(passedInterventionOutcome1);
  await Interventions.insertAsync(passedInterventionOutcome2);
  await AssessmentResults.insertAsync(assessmentResultFailed);
  await Students.insertAsync(studentFinishingSkillTree);
  await Assessments.insertAsync(assessment);
};

const grade5FallRules = [
  grade5FallRootRule,
  grade5FallRule1,
  grade5FallRule2,
  grade5FallRule3,
  grade5FallRule4,
  grade5FallRule5
];

const grade6WinterRules = [
  grade6WinterRootRule,
  grade6WinterRule1,
  grade6WinterRule2,
  grade6WinterRule3,
  grade6WinterRule4
];

const screeningAssignments = [
  grade5FallScreeningAssignment,
  grade6WinterScreeningAssignment
];

export {
  grade5And6Assessment,
  grade5FallBenchmarkAssessment,
  grade5FallAssessment1,
  grade5FallRootRule,
  grade5FallRules,
  grade6WinterRootRule,
  grade6WinterRules,
  grade6WinterBenchmarkAssessment,
  screeningAssignments,
  interventions,
  interventionRC,
  interventionTT,
  interventionCCC,
  interventionGP,
  assessmentResultLastBenchmarkSkillPassed,
  studentGroupPassed,
  setupProcessIndivualRuleHSData,
  assignNextTreeStudentPassedTestSetup
};
