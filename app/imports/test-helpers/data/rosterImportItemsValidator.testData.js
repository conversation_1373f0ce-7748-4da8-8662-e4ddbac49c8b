import { normalizeRosterImportItems } from "/imports/api/rosterImportItems/normalizeRosterImportItems";

export const validMockData = [
  {
    data: {
      schoolID: "12345",
      districtID: "12345",
      districtName: "SunnySlope",
      springMathGrade: "K",
      schoolName: "ABC 12345",
      studentLocalID: "1234",
      studentStateID: "00000000",
      studentLastName: "studentLastName1",
      studentFirstName: "studentFirstName1",
      teacherLastName: "Last",
      teacherFirstName: "First",
      teacherID: "1000",
      teacherEmail: "<EMAIL>",
      classSectionID: "someSectionID-K",
      className: "someClassName-K"
    }
  },
  {
    data: {
      schoolID: "44444",
      districtID: "12345",
      districtName: "SunnySlope",
      springMathGrade: "0",
      schoolName: "ABC 44444",
      studentLocalID: "5678",
      studentStateID: "00000001",
      studentLastName: "studentLastName2",
      studentFirstName: "studentFirstName2",
      teacherLastName: "Last",
      teacherFirstName: "First",
      teacherID: "1000",
      teacherEmail: "<EMAIL>",
      classSectionID: "someSectionID-0",
      className: "someClassName-0"
    }
  },
  {
    data: {
      schoolID: "39544",
      districtID: "12345",
      districtName: "SunnySlope",
      springMathGrade: "8",
      schoolName: "ABC 39544",
      studentLocalID: "9012",
      studentStateID: "00000002",
      studentLastName: "studentLastName3",
      studentFirstName: "studentFirstName3",
      teacherLastName: "Last",
      teacherFirstName: "First",
      teacherID: "1000",
      teacherEmail: "<EMAIL>",
      classSectionID: "someSectionID-8",
      className: "someClassName-8"
    }
  }
];

export function generateItem({
  schoolID = "12345",
  districtID = "12345",
  districtName = "SunnySlope",
  springMathGrade = "K",
  schoolName = "ABC 12345",
  studentLocalID = generateRandomId(),
  studentStateID = generateRandomId(8),
  studentLastName = "someStudentLastName",
  studentFirstName = "someStudentFirstName",
  teacherLastName = "Last",
  teacherFirstName = "First",
  teacherID = "1000",
  teacherEmail = "<EMAIL>",
  classSectionID = "someSectionID",
  className = "someClassName"
} = {}) {
  return {
    data: {
      schoolID,
      districtID,
      districtName,
      springMathGrade,
      schoolName,
      studentLocalID,
      studentStateID,
      studentLastName,
      studentFirstName,
      teacherLastName,
      teacherFirstName,
      teacherID,
      teacherEmail,
      classSectionID,
      className
    }
  };
}

export function generateNormalizedItem(csvData = {}) {
  const csvItem = generateItem(csvData);
  csvItem.data = normalizeRosterImportItems(csvItem.data);
  return csvItem;
}

export function generateRandomId(length = 4) {
  return Math.round(Math.random() * Math.pow(length, 10)).toString();
}
