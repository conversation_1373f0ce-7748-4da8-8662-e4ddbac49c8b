import React, { createContext, useContext, useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import { withRouter } from "react-router";
import { Meteor } from "meteor/meteor";
import url from "url";
import { UserContext } from "./UserContext";

const buildOrganizationContext = () => {
  return {
    orgId: null,
    org: null,
    setOrgId: () => {},
    setOrg: () => {},
    sitesInOrg: [],
    refetchSitesInOrg: () => {}
  };
};

export const OrganizationContext = createContext(buildOrganizationContext());

// TODO(fmazur) - figure out how to implement useTracker to await for db changes for Organization
export const OrganizationContextProvider = withRouter(({ children, match }) => {
  const { userOrgId, userId } = useContext(UserContext);
  const [org, setOrg] = useState(null);
  const [orgId, setOrgId] = useState(null);
  const [sitesInOrg, setSitesInOrg] = useState([]);

  const getUrlFallbackValues = () => {
    const currentPath = window.location.pathname;
    const isPrintPage = currentPath.includes("/print/");

    if (!isPrintPage) {
      return null;
    }

    const parsedQuery = url.parse(window.location.href, true).query;
    return {
      fallbackOrgId: parsedQuery.orgid
    };
  };

  const urlFallback = useMemo(() => getUrlFallbackValues(), []);

  useEffect(() => {
    if (!userId) {
      setOrg(null);
      setOrgId(null);
      setSitesInOrg([]);
    }
  }, [userId]);

  // TODO(fmazur) - investigate why this triggers eg on clicking on logo
  // TODO(fmazur) - should only trigger on deps
  useEffect(() => {
    if (!userId) {
      return;
    }

    const { orgid: routeOrgId } = match.params;
    const fallbackOrgId = urlFallback?.fallbackOrgId;

    // Use fallback orgId when normal sources are not available
    if (!userOrgId && !routeOrgId && !fallbackOrgId) {
      return;
    }

    // TODO(fmazur) - what about data admin with coach role
    // TODO(fmazur) - do we need to only look at primaryRole?
    let resolvedOrgId = userOrgId;
    if (userOrgId === "allOrgs") {
      resolvedOrgId = routeOrgId || fallbackOrgId;
    } else if (!resolvedOrgId) {
      resolvedOrgId = routeOrgId || fallbackOrgId;
    }

    if (resolvedOrgId === orgId) {
      return;
    }
    setOrgId(resolvedOrgId);
    if (!resolvedOrgId) {
      return;
    }

    Meteor.call("Organizations:getOrganizationById", resolvedOrgId, (err, result) => {
      if (!err && result) {
        setOrg({ ...result, orgId: result.orgid });
      } else {
        setOrg(null);
      }
    });
  }, [userId, userOrgId, match.params.orgid, urlFallback]);

  const fetchSitesInOrg = () => {
    if (userId && orgId) {
      Meteor.call("Sites:getSitesInOrg", orgId, (err, resp) => {
        if (!err) {
          // TODO(fmazur) - filter available sites per user roles
          setSitesInOrg(resp);
        } else if (urlFallback && orgId) {
          setSitesInOrg([]);
        }
      });
    }
  };

  useEffect(() => {
    fetchSitesInOrg();
  }, [userId, orgId, urlFallback]);

  return (
    <OrganizationContext.Provider
      value={{
        org,
        orgId,
        setOrgId,
        setOrg,
        sitesInOrg,
        refetchSitesInOrg: fetchSitesInOrg
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
});

OrganizationContextProvider.propTypes = {
  children: PropTypes.node,
  match: PropTypes.object
};
