import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { useParams } from "react-router-dom";
import { isEqual } from "lodash";
import url from "url";
import { OrganizationContext } from "./OrganizationContext";
import { UserContext } from "./UserContext";
import { SchoolYearContext } from "./SchoolYearContext";
import { StudentGroups } from "../api/studentGroups/studentGroups";
import { BenchmarkWindows } from "../api/benchmarkWindows/benchmarkWindows";
import { getCurrentDateSync } from "../api/helpers/getCurrentDate";

export const SiteContext = createContext({
  siteId: null,
  siteName: "",
  site: null,
  studentGroupsInSite: [],
  userStudentGroups: [],
  currentBenchmarkWindow: null,
  isLoading: false,
  refetchStudentGroupsInSite: () => {}
});

export const SiteContextProvider = ({ children, siteId }) => {
  const { currentSiteAccess, userId, user } = useContext(UserContext);
  const { sitesInOrg, orgId, org } = useContext(OrganizationContext);
  const { isTestOrg } = org || {};
  const { schoolYear, customDate } = useContext(SchoolYearContext);
  const { studentGroupId: routeStudentGroupId, siteId: routeSiteId } = useParams();

  const [potentialSite, setPotentialSite] = useState(null);
  const [studentGroupsInSite, setStudentGroupsInSite] = useState([]);
  const [currentBenchmarkWindow, setCurrentBenchmarkWindow] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const getUrlFallbackValues = () => {
    const currentPath = window.location.pathname;
    const isPrintPage = currentPath.includes("/print/");

    if (!isPrintPage) {
      return null;
    }

    const parsedQuery = url.parse(window.location.href, true).query;
    return {
      fallbackOrgId: parsedQuery.orgid,
      fallbackSiteId: parsedQuery.siteId,
      fallbackSchoolYear: parsedQuery.schoolYear ? parseInt(parsedQuery.schoolYear) : null,
      fallbackStudentGroupId: parsedQuery.studentGroupId
    };
  };

  const urlFallback = getUrlFallbackValues();

  // Use fallback values when normal context values are not available
  const effectiveOrgId = orgId || urlFallback?.fallbackOrgId;
  const effectiveSchoolYear = schoolYear || urlFallback?.fallbackSchoolYear;
  const effectiveSiteId = siteId || urlFallback?.fallbackSiteId;

  const userStudentGroups = useTracker(() => {
    if (!userId || !effectiveSchoolYear || !effectiveSiteId || !effectiveOrgId) {
      return [];
    }

    // TODO(fmazur) - replace this with use effect that filters based on user orgid, user siteIds or primary role? and schoolYear
    const subscription = Meteor.subscribe(
      "StudentGroupsAssociatedWithUser",
      effectiveSchoolYear,
      effectiveSiteId,
      effectiveOrgId
    );
    if (subscription.ready()) {
      return StudentGroups.find({}, { sort: { name: 1 } }).fetch();
    }
    return [];
    // TODO(fmazur) - maybe remove dependency list to auto update if needed?
  }, [userId, effectiveSchoolYear, effectiveSiteId, effectiveOrgId]);

  const currentStudentGroupData = useTracker(() => {
    const effectiveStudentGroupId = routeStudentGroupId || urlFallback?.fallbackStudentGroupId;
    if (!effectiveStudentGroupId) {
      return null;
    }

    const subscription = Meteor.subscribe("StudentGroupById", effectiveStudentGroupId);
    if (subscription.ready()) {
      return StudentGroups.findOne(effectiveStudentGroupId);
    }
    return null;
  });

  const benchmarkWindowsData = useTracker(() => {
    if (!effectiveSiteId) {
      return [];
    }

    const subscription = Meteor.subscribe("BenchmarkWindowsForSchool", effectiveSiteId);
    if (subscription.ready()) {
      return BenchmarkWindows.find({ siteId: effectiveSiteId }).fetch();
    }
    return [];
  }, [effectiveSiteId]);

  useEffect(() => {
    if (sitesInOrg.length) {
      // TODO(fmazur) - use current schoolYear siteId of a user if available otherwise use context siteId
      const site = sitesInOrg.find(s => s._id === (effectiveSiteId || currentSiteAccess?.siteId));
      setPotentialSite(site);
      setIsLoading(false);
    }
  }, [sitesInOrg, effectiveSiteId, currentSiteAccess?.siteId, urlFallback]);

  const fetchStudentGroupsInSite = () => {
    Meteor.call(
      "StudentGroups:getGroups",
      { orgId: effectiveOrgId, siteId: effectiveSiteId, schoolYear: effectiveSchoolYear },
      (err, result) => {
        if (!err) {
          setStudentGroupsInSite(result);
        }
      }
    );
  };

  useEffect(() => {
    // TODO(fmazur) - limit data maybe for usage for all roles
    if (effectiveOrgId && effectiveSiteId && effectiveSchoolYear) {
      fetchStudentGroupsInSite();
    }
  }, [effectiveOrgId, effectiveSiteId, effectiveSchoolYear]);

  useEffect(() => {
    if (!userId) {
      setPotentialSite(null);
      setStudentGroupsInSite([]);
    }
  }, [userId]);

  useEffect(() => {
    if (user && effectiveOrgId && effectiveSiteId && effectiveSchoolYear && benchmarkWindowsData.length) {
      const currentDate = getCurrentDateSync(customDate, isTestOrg);
      const benchmarkWindow = benchmarkWindowsData.find(
        bw =>
          bw.schoolYear === effectiveSchoolYear &&
          bw.siteId === effectiveSiteId &&
          bw.startDate <= currentDate &&
          bw.endDate >= currentDate
      );
      setCurrentBenchmarkWindow(benchmarkWindow || null);
    }
  }, [user, effectiveOrgId, benchmarkWindowsData, effectiveSchoolYear, effectiveSiteId]);

  useEffect(() => {
    const effectiveStudentGroupId = routeStudentGroupId || urlFallback?.fallbackStudentGroupId;
    if (currentStudentGroupData && effectiveStudentGroupId) {
      const updatedGroups = [...studentGroupsInSite];
      const index = updatedGroups.findIndex(group => group._id === effectiveStudentGroupId);
      if (!isEqual(updatedGroups[index], currentStudentGroupData)) {
        if (index >= 0) {
          updatedGroups[index] = currentStudentGroupData;
        } else {
          updatedGroups.push(currentStudentGroupData);
        }
        setStudentGroupsInSite(updatedGroups);
      }
    }
  }, [currentStudentGroupData, routeStudentGroupId, urlFallback?.fallbackStudentGroupId, studentGroupsInSite]);

  return (
    <SiteContext.Provider
      value={{
        siteId: userId ? effectiveSiteId || potentialSite?._id || routeSiteId || null : null,
        siteName: potentialSite?.name || "",
        site: potentialSite || null,
        studentGroupsInSite: studentGroupsInSite || [],
        userStudentGroups: userStudentGroups || [],
        currentBenchmarkWindow,
        benchmarkWindows: benchmarkWindowsData,
        isLoading,
        refetchStudentGroupsInSite: fetchStudentGroupsInSite
      }}
    >
      {children}
    </SiteContext.Provider>
  );
};

SiteContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
