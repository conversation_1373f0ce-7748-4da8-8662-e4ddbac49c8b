import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useParams } from "react-router-dom";
import { isEqual } from "lodash";
import url from "url";
import { StudentGroupContext } from "./StudentGroupContext";
import { UserContext } from "./UserContext";

const buildStudentContext = () => {
  return {
    studentId: null,
    student: null,
    studentsInGroup: []
  };
};

export const StudentContext = createContext(buildStudentContext());

export const StudentContextProvider = ({ children }) => {
  const { studentId: routeStudentId } = useParams();
  const { studentsInStudentGroup } = useContext(StudentGroupContext);
  const { userId } = useContext(UserContext);
  const [viewedStudent, setViewedStudent] = useState(null);

  const getUrlFallbackValues = () => {
    const currentPath = window.location.pathname;
    const isPrintPage = currentPath.includes("/print/");

    if (!isPrintPage) {
      return null;
    }

    const parsedQuery = url.parse(window.location.href, true).query;
    return {
      fallbackStudentId: parsedQuery.studentId
    };
  };

  const urlFallback = getUrlFallbackValues();
  const effectiveStudentId = routeStudentId || urlFallback?.fallbackStudentId;

  useEffect(() => {
    const potentialStudent = studentsInStudentGroup.find(s => s._id === effectiveStudentId);

    if (!isEqual(viewedStudent, potentialStudent) && effectiveStudentId) {
      if (potentialStudent) {
        setViewedStudent(potentialStudent);
      }
    }
  }, [effectiveStudentId, studentsInStudentGroup, urlFallback, viewedStudent]);

  useEffect(() => {
    if (!userId) {
      setViewedStudent(null);
    }
  }, [userId]);

  return (
    <StudentContext.Provider
      value={{
        studentId: viewedStudent?._id || effectiveStudentId || null,
        student: viewedStudent || null,
        studentsInGroup: studentsInStudentGroup || []
      }}
    >
      {children}
    </StudentContext.Provider>
  );
};

StudentContextProvider.propTypes = {
  children: PropTypes.node
};
