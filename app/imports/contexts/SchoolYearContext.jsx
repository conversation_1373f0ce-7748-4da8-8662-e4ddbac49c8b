import React, { createContext, useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import url from "url";
import { UserContext } from "./UserContext";
import { OrganizationContext } from "./OrganizationContext";
import { getLatestAvailableSchoolYear } from "../api/utilities/utilities";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";
import { getBenchmarkPeriodByDate } from "/imports/api/benchmarkPeriods/methods";

export const SchoolYearContext = createContext({
  schoolYear: null,
  latestAvailableSchoolYear: null,
  customDate: null,
  isInActiveSchoolYear: false,
  currentBenchmarkPeriod: null,
  setSchoolYear: () => {},
  setLatestAvailableSchoolYear: () => {},
  setCustomDate: () => {}
});

export const SchoolYearContextProvider = ({ children }) => {
  const { user } = useContext(UserContext);
  const { benchmarkPeriods } = useContext(StaticDataContext);
  const { orgId, org } = useContext(OrganizationContext);
  const { benchmarkPeriodsGroupId, isTestOrg } = org || {};

  const [schoolYear, setSchoolYear] = useState(null);
  const [latestAvailableSchoolYear, setLatestAvailableSchoolYear] = useState(null);
  const [customDate, setCustomDate] = useState(null);
  const [isInActiveSchoolYear, setIsInActiveSchoolYear] = useState(false);
  const [currentBenchmarkPeriod, setCurrentBenchmarkPeriod] = useState(null);

  useEffect(() => {
    if (Object.keys(org || {}).length) {
      setCurrentBenchmarkPeriod(
        getBenchmarkPeriodByDate({ customDate, benchmarkPeriodsGroupId, isTestOrg, benchmarkPeriods })
      );
    } else {
      setCurrentBenchmarkPeriod(null);
    }
  }, [benchmarkPeriods, org, customDate]);

  const getUrlSchoolYear = () => {
    const currentPath = window.location.pathname;
    const isPrintPage = currentPath.includes("/print/");

    if (!isPrintPage) {
      return null;
    }

    const parsedQuery = url.parse(window.location.href, true).query;
    return parsedQuery.schoolYear ? parseInt(parsedQuery.schoolYear) : null;
  };

  useEffect(() => {
    if (!user) {
      return;
    }

    const routeSchoolYear = getUrlSchoolYear();
    if (routeSchoolYear) {
      setSchoolYear(routeSchoolYear);
      setLatestAvailableSchoolYear(routeSchoolYear);
      return;
    }

    const init = async () => {
      const latest = await getLatestAvailableSchoolYear(user, orgId);
      setLatestAvailableSchoolYear(latest);

      let resolvedSchoolYear = latest;

      if (!user?.profile?.customDate?.length && user?.profile?.selectedSchoolYear) {
        resolvedSchoolYear = user.profile.selectedSchoolYear;
      }

      setSchoolYear(resolvedSchoolYear);

      const custom = user?.profile?.customDate ?? null;
      setCustomDate(custom);
    };

    // eslint-disable-next-line no-console
    init().catch(console.error);
  }, [user, orgId]);

  useEffect(() => {
    const inActiveSchoolYear = customDate?.length ? true : !schoolYear || schoolYear === latestAvailableSchoolYear;
    setIsInActiveSchoolYear(inActiveSchoolYear);
  }, [customDate, schoolYear, latestAvailableSchoolYear]);

  return (
    <SchoolYearContext.Provider
      value={{
        schoolYear,
        setSchoolYear,
        latestAvailableSchoolYear,
        setLatestAvailableSchoolYear,
        isInActiveSchoolYear,
        customDate,
        setCustomDate,
        currentBenchmarkPeriod
      }}
    >
      {children}
    </SchoolYearContext.Provider>
  );
};

SchoolYearContextProvider.propTypes = {
  children: PropTypes.node
};
