import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { useHistory, useParams } from "react-router-dom";
import { isEqual } from "lodash";
import url from "url";
import Alert from "react-s-alert";

import { isAdminOrUniversalCoach } from "/imports/api/roles/methods";
import * as helpers from "/imports/ui/components/student-groups/helperFunction";
import { Students } from "/imports/api/students/students";

import { SiteContext } from "./SiteContext";
import { UserContext } from "./UserContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

export const StudentGroupContext = createContext({
  studentGroupId: null,
  studentGroup: null,
  studentsInStudentGroup: [],
  showInterventionModal: false,
  studentsWithIndividualRuleNotProcessed: [],
  alreadyCompletedTreeData: null,
  processIndividualRule: () => {},
  setAlreadyCompletedTreeData: () => {},
  endInterventionHandling: () => {}
  // TODO(fmazur) - add studentEnrollmentsInStudentGroup
  // TODO(fmazur) - add map from studentsInStudentGroup to get grades same way as GradesWithStudentGroupsInSite publication
});

export const StudentGroupContextProvider = ({ children }) => {
  const { studentGroupId: routeStudentGroupId } = useParams();
  const history = useHistory();
  const { studentGroupsInSite } = useContext(SiteContext);
  const { userId, user } = useContext(UserContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const [viewedStudentGroup, setViewedStudentGroup] = useState(null);
  const [studentsInStudentGroup, setStudentsInStudentGroup] = useState([]);
  const [showInterventionModal, setShowInterventionModal] = useState(false);
  const [studentsWithIndividualRuleNotProcessed, setStudentsWithIndividualRuleNotProcessed] = useState([]);
  const [alreadyCompletedTreeData, setAlreadyCompletedTreeData] = useState(null);

  const getUrlFallbackValues = () => {
    const currentPath = window.location.pathname;
    const isPrintPage = currentPath.includes("/print/");

    if (!isPrintPage) {
      return null;
    }

    const parsedQuery = url.parse(window.location.href, true).query;
    return {
      fallbackStudentGroupId: parsedQuery.studentGroupId,
      fallbackSchoolYear: parsedQuery.schoolYear ? parseInt(parsedQuery.schoolYear) : null,
      fallbackSiteId: parsedQuery.siteId,
      fallbackOrgId: parsedQuery.orgid
    };
  };

  const addUnprocessedStudent = student => {
    setShowInterventionModal(true);
    setStudentsWithIndividualRuleNotProcessed([...studentsWithIndividualRuleNotProcessed, student]);
  };

  const getStudentNameByStudentId = studentId => {
    const student = studentsInStudentGroup.find(s => s._id === studentId);
    if (!student) return "";
    const { firstName, lastName } = student.identity.name;
    return `${firstName} ${lastName}`;
  };

  const handleIndividualRuleProcessingError = (err, studentId) => {
    const allSkillTreesCompleted = err.message.match(
      /Student already completed all individual intervention skill trees/
    );
    const duplicateIndividualInterventionError = err.message.match(/Student already has an assessment result document/);
    const alreadyCompletedTree = err.message.match(/Already completed this progress monitoring tree/);
    const isAdminUser = isAdminOrUniversalCoach(viewedStudentGroup.siteId, user?.profile?.siteAccess);
    if (duplicateIndividualInterventionError && isAdminUser) {
      addUnprocessedStudent(studentsInStudentGroup.find(s => s._id === studentId));
    } else if (duplicateIndividualInterventionError && !isAdminUser) {
      helpers.displayError(`${getStudentNameByStudentId(studentId)} previously had an individual intervention.
      Please talk to your coach/admin about starting a new intervention.`);
    } else if (alreadyCompletedTree) {
      setAlreadyCompletedTreeData(err.details);
    } else if (allSkillTreesCompleted) {
      Alert.warning(err.reason || err.message, { timeout: 8000 });
    } else {
      helpers.displayError(`There was an error: ${err.message}`);
    }
  };

  const endInterventionHandling = () => {
    setShowInterventionModal(false);
    setStudentsWithIndividualRuleNotProcessed([]);
    setAlreadyCompletedTreeData(null);
  };

  const processIndividualRule = ({ assessmentResultId, allowNoScore, studentId, cb }) => {
    Meteor.call(
      "processIndividualRule",
      {
        assessmentResultId,
        allowNoScore,
        studentId
      },
      err => {
        if (err) {
          handleIndividualRuleProcessingError(err, studentId);
          return null;
        }

        return cb && cb();
      }
    );
  };

  const urlFallback = getUrlFallbackValues();
  const effectiveStudentGroupId = routeStudentGroupId || urlFallback?.fallbackStudentGroupId;

  useEffect(() => {
    const potentialStudentGroup = studentGroupsInSite.find(sg => sg._id === effectiveStudentGroupId);

    if (!isEqual(viewedStudentGroup, potentialStudentGroup) && effectiveStudentGroupId) {
      if (potentialStudentGroup) {
        setViewedStudentGroup(potentialStudentGroup);
      }
    }
  }, [effectiveStudentGroupId, studentGroupsInSite, urlFallback, viewedStudentGroup]);

  const studentsFromSubscription = useTracker(() => {
    if (!viewedStudentGroup?._id) {
      return [];
    }

    const subscription = Meteor.subscribe("Students:StudentsInStudentGroup", viewedStudentGroup._id);
    if (subscription.ready()) {
      return Students.find({}).fetch();
    }
    return [];
  }, [viewedStudentGroup?._id]);

  useEffect(() => {
    setStudentsInStudentGroup(studentsFromSubscription);
  }, [studentsFromSubscription]);

  useEffect(() => {
    if (!userId) {
      setViewedStudentGroup(null);
      setStudentsInStudentGroup([]);
    }
  }, [userId]);

  useEffect(() => {
    if (routeStudentGroupId && viewedStudentGroup && viewedStudentGroup.schoolYear !== schoolYear) {
      history.push(`/school-overview/${viewedStudentGroup.orgid}/all/${viewedStudentGroup.siteId}`);
    }
  }, [schoolYear]);

  return (
    <StudentGroupContext.Provider
      value={{
        studentGroupId: viewedStudentGroup?._id || effectiveStudentGroupId || null,
        studentGroup: viewedStudentGroup || null,
        studentsInStudentGroup: studentsInStudentGroup || [],
        showInterventionModal,
        studentsWithIndividualRuleNotProcessed,
        alreadyCompletedTreeData,
        processIndividualRule,
        setAlreadyCompletedTreeData,
        endInterventionHandling
      }}
    >
      {children}
    </StudentGroupContext.Provider>
  );
};

StudentGroupContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
