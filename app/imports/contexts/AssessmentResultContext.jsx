import React, { createContext, useContext } from "react";
import PropTypes from "prop-types";
import { useTracker } from "meteor/react-meteor-data";
import { SchoolYearContext } from "./SchoolYearContext";
import { StudentGroupContext } from "./StudentGroupContext";
import { AssessmentResults } from "../api/assessmentResults/assessmentResults";

export const AssessmentResultContext = createContext({
  currentAssessmentResultsForGroup: []
});

export const AssessmentResultContextProvider = ({ children }) => {
  const { schoolYear } = useContext(SchoolYearContext);
  const { studentGroupId, studentGroup } = useContext(StudentGroupContext);

  const assessmentResults = useTracker(() => {
    if (studentGroupId && schoolYear) {
      const assessmentResultsSub = Meteor.subscribe("AssessmentResults:CurrentAssessmentResultsInStudentGroup", {
        studentGroupId,
        schoolYear
      });
      if (assessmentResultsSub.ready()) {
        return AssessmentResults.find().fetch();
      }
    }
    return [];
  }, [studentGroupId, schoolYear]);

  return (
    <AssessmentResultContext.Provider
      value={{
        currentAssessmentResultsForGroup: studentGroup?.currentAssessmentResultIds
          ? assessmentResults.filter(ar => studentGroup.currentAssessmentResultIds.includes(ar._id))
          : assessmentResults
      }}
    >
      {children}
    </AssessmentResultContext.Provider>
  );
};

AssessmentResultContextProvider.propTypes = {
  children: PropTypes.node
};
