import "../imports/startup/server";

import { WebApp } from "meteor/webapp";
import { toLower } from "lodash";

const allowedOrigins = [
  `https://app.${toLower(Meteor.settings.public.ENVIRONMENT)}.springmath.org`,
  `https://app.${toLower(Meteor.settings.public.ENVIRONMENT)}2.springmath.org`,
  `https://app.springmath.org`,
  `https://app2.springmath.org`,
  "http://localhost:3000"
];

WebApp.rawConnectHandlers.use((req, res, next) => {
  const { origin } = req.headers;

  if (origin && !allowedOrigins.includes(origin)) {
    res.writeHead(403);
    res.end();
    return;
  }

  res.setHeader("X-Frame-Options", "SAMEORIGIN");
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("Referrer-Policy", "no-referrer");

  if (req.headers["x-forwarded-proto"] === "https" || req.connection.encrypted) {
    res.setHeader("Strict-Transport-Security", "max-age=63072000; includeSubDomains; preload");
  }

  res.setHeader(
    "Content-Security-Policy",
    [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com http://www.youtube.com https://www.youtube.com https://www.googletagmanager.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      `media-src 'self' ${Meteor.settings.public.S3_BASE_URL ||
        "https://springmath-web-data.s3.us-east-va.io.cloud.ovh.us"}`,
      "img-src 'self' data:",
      "object-src 'none'",
      "base-uri 'self'",
      "connect-src 'self' https://apm-engine.meteor.com https://engine.montiapm.com https://www.google-analytics.com https://analytics.google.com https://www.googletagmanager.com wss:",
      "frame-ancestors 'self'",
      "frame-src 'self' https://dialog.filepicker.io https://www.filepicker.io http://www.youtube.com https://www.youtube.com"
    ].join("; ")
  );

  res.setHeader("Cache-Control", "no-store, no-cache");
  res.setHeader("Pragma", "no-cache");

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader("Access-Control-Allow-Origin", origin);
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
    res.setHeader("Access-Control-Allow-Credentials", "true");
  }

  if (req.method === "OPTIONS") {
    if (origin && allowedOrigins.includes(origin)) {
      res.writeHead(204);
    } else {
      res.writeHead(403);
    }
    res.end();
    return;
  }

  next();
});

WebApp.httpServer.on("upgrade", (req, socket) => {
  const { origin } = req.headers;

  if (origin && !allowedOrigins.includes(origin)) {
    socket?.destroy();
  }
});
