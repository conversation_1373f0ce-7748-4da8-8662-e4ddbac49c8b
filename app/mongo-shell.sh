#!/bin/bash

# SpringMath MongoDB Shell Connection Script
# This script connects to the local MongoDB instance on port 3001

echo "🔗 Connecting to SpringMath MongoDB on port 3001..."

# Check if MongoDB is running on port 3001
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ MongoDB is running on port 3001"
    echo "🗄️  Database: meteor"
    echo "📍 Connection: mongodb://localhost:3001/meteor"
    echo ""
    
    # Connect to the database
    mongosh --port 3001 meteor
else
    echo "❌ MongoDB is not running on port 3001"
    echo "💡 Start MongoDB first with: npm run start:mongo"
    echo "   Or use the full development environment: npm start"
    exit 1
fi
