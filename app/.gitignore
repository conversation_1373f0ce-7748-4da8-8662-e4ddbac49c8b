mupx.json
mup.json
.DS_Store
node_modules
npm-debug.log
junit.xml
deployment_token.json
client/bundle.css
# Ignore Visual Studio code files -- ask <PERSON> if you have questions
typings
jsconfig.json
.vscode
#end Visual Studio Code files
.idea
settings.json
settings-demo.json
settings-dev.json
settings-qa.json
settings-prod.json
settings-stage.json
.nyc_output
.coverage
/cypress
.eslintcache
/.scripts/yearMigration/dist/

# Local MongoDB database
local-db/
