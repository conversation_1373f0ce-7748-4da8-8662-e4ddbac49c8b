#!/bin/bash

# SpringMath Development Startup Script
# This script starts MongoDB on port 3001 and then starts Meteor with external MongoDB

echo "🚀 Starting SpringMath Development Environment..."

# Create local database directory if it doesn't exist
mkdir -p ./local-db

# Check if MongoDB is already running on port 3001
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ MongoDB is already running on port 3001"
else
    echo "🔧 Starting MongoDB on port 3001..."
    mongod --port 3001 --dbpath ./local-db --fork --logpath ./local-db/mongod.log
    
    # Wait a moment for MongoDB to start
    sleep 2
    
    # Verify MongoDB started successfully
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
        echo "✅ MongoDB started successfully on port 3001"
    else
        echo "❌ Failed to start MongoDB on port 3001"
        echo "📋 Check the log file: ./local-db/mongod.log"
        exit 1
    fi
fi

echo "🌟 Starting Meteor with external MongoDB..."
echo "📍 MongoDB URL: mongodb://localhost:3001/meteor"
echo "🌐 App will be available at: http://localhost:3000"
echo "🔍 Debugger will be available at: ws://127.0.0.1:9229"
echo ""

# Start Meteor with external MongoDB
MONGO_URL="mongodb://localhost:3001/meteor" meteor --inspect --settings settings.json
