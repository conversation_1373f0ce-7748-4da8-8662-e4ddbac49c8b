#!/bin/bash

# SpringMath Development Stop Script
# This script stops the MongoDB instance running on port 3001

echo "🛑 Stopping SpringMath Development Environment..."

# Find and kill MongoDB process on port 3001
MONGO_PID=$(lsof -Pi :3001 -sTCP:LISTEN -t)

if [ -n "$MONGO_PID" ]; then
    echo "🔧 Stopping MongoDB (PID: $MONGO_PID)..."
    kill $MONGO_PID
    
    # Wait a moment for graceful shutdown
    sleep 2
    
    # Check if it's still running
    if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  MongoDB didn't stop gracefully, forcing shutdown..."
        kill -9 $MONGO_PID
    fi
    
    echo "✅ MongoDB stopped successfully"
else
    echo "ℹ️  MongoDB is not running on port 3001"
fi

echo "🏁 Development environment stopped"
