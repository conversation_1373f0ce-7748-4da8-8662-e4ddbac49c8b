import {
  checkCurrentClasswideSkill,
  clickVisibleTestId,
  clickVisibleText,
  dropFile,
  expandInterventionForStudent,
  getSchoolYearLabel,
  reactSelectByText,
  setCustomDate,
  waitForLoadingToDisappear
} from "../support/common/utils";
import { clickElementByTextWithinSideNav, clickDashboardTab } from "../support/common/navigating";
import {
  inputAndCalculateClasswideScores,
  inputAndSaveInterventionScore,
  inputScreeningScores,
  saveScreeningScores
} from "../support/enterScoreHelper";

/*
  There is no need to verify whether date was updated properly due to all tests in this spec are time dependant
  which would cause failure if setting custom date was not working properly

  1. Create fresh group with new teacher and student file upload
  2. Pass last 60% Fall Screening resulting in 2 individual intervention recommendations then schedule and advance one individual intervention skill
  3. Fail last 60% Winter Screening resulting in classwide intervention then advance classwide intervention skill
  4. Fully Pass Spring Screening to setup clean four week rule state
  5. Get four week rule recommendation based on Classwide Skill advancement throughout 4-6 weeks for different students
*/

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");

/**
 * Groups used: Self created group
 * Modifies: Adds student group, screenings in all periods
 * Can't be rerun without test database restart
 * Requires site
 */
describe("Custom date functionality", () => {
  const customClassName = "Custom Date Class (77777777)";

  it("should be possible to create a new student group with a custom date", () => {
    cy.loginAs({ role: "superAdmin" });
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.findByTestId("test_organization_id_row")
      .scrollIntoView()
      .should("be.visible")
      .within(() => {
        cy.findByTestId("orgLink").click();
      });
    setCustomDate(`${CURRENT_SCHOOL_YEAR - 1}-09-01`);
    clickVisibleTestId("manage_test_elementary_site_id");

    clickVisibleText("Add Class & Teacher");
    cy.findByPlaceholderText("Class Name").type("Custom Date Class");
    cy.findByPlaceholderText("Class Section ID").type("77777777");
    reactSelectByText("#select-teacher", "Add new teacher");
    cy.findByPlaceholderText("Last name").type("customDateLastName");
    cy.findByPlaceholderText("First name").type("customDateFirstName");
    cy.findByPlaceholderText("Teacher ID").type("777777");
    cy.findByPlaceholderText("Email").type("<EMAIL>");
    cy.findByText("Upload students", { exact: false }).should("be.visible");
    cy.findByText("Next")
      .should("be.visible")
      .click();
    dropFile("customDateUploadStudents.csv");
    clickVisibleText("Finalize Upload");
    cy.get("[data-testid$='_row']")
      .should("have.length", 5)
      .should("be.visible");
  });
  it("should start fall screening and individual intervention", () => {
    cy.loginAs({ role: "coach" });
    // Go to Fall benchmark period
    setCustomDate(`${CURRENT_SCHOOL_YEAR - 1}-09-01`);
    navigateToStudentGroup("Kindergarten", "Custom Date Class");
    clickDashboardTab(customClassName, "screeningTab");
    clickVisibleTestId("beginScreening");
    // clickVisibleTestId("close-and-assign-screening-button");
    // Fail 2/5 students to get individual recommendation queue
    inputScreeningScores(true, 8, true);
    saveScreeningScores();
    clickDashboardTab(customClassName, "studentsTab");
    waitForLoadingToDisappear(10000, false);
    cy.get(".studentListIndividualInterventionQueue").should("be.visible");
    cy.findAllByTestId("scheduleInterventionCheckbox").should("have.length", 2);
    // Leave one recommendation not scheduled for next screening
    cy.findAllByTestId("scheduleInterventionCheckbox")
      .eq(1)
      .click();
    clickVisibleText("Begin Individual Intervention");
    cy.get(".studentListIndividualInterventionList").should("be.visible");
    clickDashboardTab(customClassName, "individualInterventionTab");
    cy.findByText("Count Objects Aloud 1-20").should("be.visible");
    expandInterventionForStudent("Potato Mashed");
    cy.get(".skill-details")
      .first()
      .within(() => {
        cy.findByText("Count Objects Aloud 1-20", { exact: true }).should("be.visible");
      });
    inputAndSaveInterventionScore(99);
    cy.findByText("Number Names 0-10").should("be.visible");
    expandInterventionForStudent("Potato Mashed");
    cy.get(".skill-details")
      .first()
      .within(() => {
        cy.findByText("Number Names 0-10", { exact: true }).should("be.visible");
      });
  });
  it("should start winter screening and schedule classwide", () => {
    cy.loginAs({ role: "coach" });
    // Go to Winter benchmark period
    setCustomDate(`${CURRENT_SCHOOL_YEAR}-01-01`);
    navigateToStudentGroup("Kindergarten", "Custom Date Class");
    clickDashboardTab(customClassName, "screeningTab");
    clickVisibleText(`Begin Winter ${getSchoolYearLabel()} screening`);
    // Fail 3 different students than on Fall screening
    inputScreeningScores(true, 12, false);
    saveScreeningScores();
    clickDashboardTab(customClassName, "studentsTab");
    waitForLoadingToDisappear(10000, false);
    // Verify that previously scheduled recommendation is kept
    cy.get(".studentListIndividualInterventionQueue").should("be.visible");
    setCustomDate(`${CURRENT_SCHOOL_YEAR}-01-02`);
    clickDashboardTab(customClassName, "classwideInterventionTab");
    checkCurrentClasswideSkill("Count Objects 1-10, Circle Answer");
    inputAndCalculateClasswideScores({ isPassing: true });
    clickVisibleTestId("saveScoreBtn");
    waitForLoadingToDisappear();
    checkCurrentClasswideSkill("Identify Number – Draw Circles 1-10");
  });
  it("should start spring screening", () => {
    cy.loginAs({ role: "coach" });
    // Go to Spring benchmark period
    setCustomDate(`${CURRENT_SCHOOL_YEAR}-04-01`);
    navigateToStudentGroup("Kindergarten", "Custom Date Class");
    clickDashboardTab(customClassName, "screeningTab");
    clickVisibleText(`Begin Spring ${getSchoolYearLabel()} screening`);
    // Fail 0 to have clean state for four week rule since we didn't advance classwide for past 4weeks+
    inputScreeningScores(true, 0, true); // fail 3/5 students to get classwide and no individual
    saveScreeningScores();
    clickDashboardTab(customClassName, "studentsTab");
    waitForLoadingToDisappear(10000, false);
    // Verify that previously scheduled recommendation is kept
    cy.get(".studentListIndividualInterventionQueue").should("be.visible");
    cy.get(".studentListIndividualInterventionList").should("be.visible");
    clickDashboardTab(customClassName, "screeningTab");
    cy.findAllByTestId("viewScreening")
      .should("be.visible")
      .should("have.length", 3);
  });

  after(() => {
    setCustomDate();
  });
});

function navigateToStudentGroup(gradeName, partialClassName) {
  clickElementByTextWithinSideNav(gradeName);
  cy.get(".studentGroupProgress")
    .first()
    .should("be.visible")
    .within(() => {
      cy.findByText(partialClassName, { exact: false })
        .should("be.visible")
        .click();
    });
}
