import { generateNewUserEmail } from "../support/common/emailUtils";
import {
  assertSchoolOverviewHeader,
  clickVisibleTestId,
  clickVisibleText,
  getSchoolYearLabel,
  waitForDetachedDom
} from "../support/common/utils";
import { changeSchoolYear } from "../support/common/navigating";

/**
 * Groups used: -
 * Modifies: User roster - adds universal coach
 * Can be rerun without test database restart
 * Requires superAdmin account and organization with site that can view Program Evaluation in current and historical schoolYear
 */
describe("Universal Coach user:", () => {
  const PREVIOUS_SCHOOL_YEAR = Cypress.env("PREVIOUS_SCHOOL_YEAR");
  const previousSchoolYearLabel = `${PREVIOUS_SCHOOL_YEAR - 1}-${PREVIOUS_SCHOOL_YEAR % 100}`;

  const newUniversalCoachEmail = generateNewUserEmail("universalCoach");
  it("can be added by a super admin", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.findByText("Client List").should("be.visible");
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_manage-users");
    clickVisibleTestId("addUniversalCoachButton");
    cy.get("#txtUniversalCoachEmail").type(newUniversalCoachEmail);
    cy.get("#txtUniversalCoachFirstName").type("John");
    cy.get("#txtUniversalCoachLastName").type("Doe");
    clickVisibleText("Send Invite Email");

    cy.findByText("Invite Sent. Please contact the new user to inform them the invite has been sent.").should(
      "be.visible"
    );
  });

  it("can then log in, view stats and navigate to any organization as coach", () => {
    cy.loginAs({ email: newUniversalCoachEmail });

    clickVisibleTestId("orgStats_Test/Demo Organization");
    cy.findAllByTestId("orgStatColumn_Test/Demo Organization").should("have.length", 3);

    clickVisibleText("Test/Demo Organization");
    clickVisibleTestId("viewAsCoach_Test Elementary Site");
    assertSchoolOverviewHeader();
  });

  it("changes school year after entering Program Evaluation page", () => {
    cy.loginAs({ email: newUniversalCoachEmail });
    clickVisibleText("Test/Demo Organization");
    clickVisibleTestId("viewAsCoach_Test Elementary Site");
    clickVisibleTestId("userContextMenu");
    clickVisibleTestId("programEvaluation");
    cy.findByText(`Program Evaluation for Test Elementary Site (${getSchoolYearLabel()})`).should("be.visible");
    assertProgramEvaluationIndividualInterventionTableLengthAtLeast(5);
    changeSchoolYear(`schoolYear${PREVIOUS_SCHOOL_YEAR}`);
    cy.findByTestId("navbar-school-year").should("have.text", previousSchoolYearLabel);
    cy.findByText(`Program Evaluation for Test Elementary Site (${previousSchoolYearLabel})`).should("be.visible");
    assertProgramEvaluationIndividualInterventionTableLengthAtLeast(1);
  });

  it("changes school year before entering Program Evaluation page", () => {
    cy.loginAs({ email: newUniversalCoachEmail });
    clickVisibleText("Test/Demo Organization");
    clickVisibleTestId("viewAsCoach_Test Elementary Site");
    changeSchoolYear(`schoolYear${PREVIOUS_SCHOOL_YEAR}`);
    waitForDetachedDom();
    cy.findByTestId("classwideInterventionSectionHeader").should("be.visible");
    cy.findByTestId("navbar-school-year").should("have.text", previousSchoolYearLabel);
    clickVisibleTestId("userContextMenu");
    clickVisibleTestId("programEvaluation");
    cy.findByText(`Program Evaluation for Test Elementary Site (${previousSchoolYearLabel})`).should("be.visible");
  });

  it("can see district reporting page for current school year", () => {
    cy.loginAs({ email: newUniversalCoachEmail });
    clickVisibleText("Test/Demo Organization");
    clickVisibleTestId("userContextMenu");
    clickVisibleTestId("districtReporting");
    cy.findByText(`District Reporting for Test/Demo Organization (${getSchoolYearLabel()})`, { timeout: 15000 }).should(
      "be.visible"
    );
    cy.findByText("Schools included:").should("be.visible");
    cy.findByTestId("districtReporting_individualInterventionTable")
      .scrollIntoView()
      .should("be.visible");
  });

  it("changes school year after entering district reporting page", () => {
    cy.loginAs({ email: newUniversalCoachEmail });
    clickVisibleText("Test/Demo Organization");
    clickVisibleTestId("userContextMenu");
    clickVisibleTestId("districtReporting");
    cy.findByText(`District Reporting for Test/Demo Organization (${getSchoolYearLabel()})`, { timeout: 15000 }).should(
      "be.visible"
    );
    cy.findByTestId("districtReporting_individualInterventionTable")
      .scrollIntoView()
      .should("be.visible");
    changeSchoolYear(`schoolYear${PREVIOUS_SCHOOL_YEAR}`);
    cy.findByTestId("navbar-school-year").should("have.text", previousSchoolYearLabel);
    cy.findByText(`District Reporting for Test/Demo Organization (${previousSchoolYearLabel})`).should("be.visible");
  });
});

function assertProgramEvaluationIndividualInterventionTableLengthAtLeast(rowNum) {
  waitForDetachedDom();
  cy.findByTestId("programEvaluation_individualInterventionTable").within(() => {
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.findAllByTestId("rowIndvSummary")
      .first()
      .scrollIntoView()
      .should("be.visible");
    cy.get(".individual-interventions-group")
      .children()
      .should("have.length.gte", rowNum);
  });
}
