import { SCHOOL_ITEMS } from "../support/common/constants";
import {
  clickVisibleText,
  dropFile,
  reactSelectByText,
  waitForAlertToDisappear,
  waitForDetachedDom
} from "../support/common/utils";

/**
 * Groups used: grade5group1
 * Modifies: Adds student group, adds student to different group
 * Can't be rerun without test database restart
 * Requires site
 */
describe("File Upload - Add Students:", () => {
  describe("Data Admin", () => {
    beforeEach(() => {
      cy.loginAs({ role: "dataAdmin" });
    });
    it("adds a class and then sees added class", () => {
      cy.findByTestId(SCHOOL_ITEMS.elementary)
        .should("be.visible")
        .within(() => {
          clickVisibleText("Manage");
        });
      clickVisibleText("Add Class & Teacher");

      cy.findByPlaceholderText("Class Name").type("Test 05#3");
      cy.findByPlaceholderText("Class Section ID").type("6ss1D-fqaFT_05_3-");
      cy.get("#select-grade").select("05");
      reactSelectByText("#select-teacher", "<EMAIL>");
      clickVisibleText("Next");
      waitForAlertToDisappear();
      dropFile("exampleUploadStudent.csv");
      waitForDetachedDom(500);
      clickVisibleText("Finalize Upload");
      cy.findByTestId("Anderson_row").within(() => {
        cy.findByText("Anderson, John").should("be.visible");
      });
      cy.findByTestId("groupHeader").within(() => {
        cy.contains(/\(6ss1D-fqaFT_05_3-\)/).should("be.visible");
      });
    });
  });
});
