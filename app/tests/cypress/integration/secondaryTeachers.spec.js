import { goToStudentListFor } from "../support/common/navigating";
import { TEST_GROUPS } from "../support/common/constants";
import { reactSelectByText } from "../support/common/utils";

describe("Secondary teachers:", () => {
  const groupWithAddedSecondaryTeacher = TEST_GROUPS.grade5group2.name;
  const groupWithRemovedSecondaryTeacher = TEST_GROUPS.grade5group1.name;
  describe("Data Admin", () => {
    const secondaryTeacherName = "TIES Demo User";
    it("adds secondary teacher", () => {
      cy.loginAs({ role: "dataAdmin" });
      goToStudentListFor(groupWithAddedSecondaryTeacher);
      cy.findByText("Manage Class").click();

      cy.findByText("Add new secondary teacher").click();
      reactSelectByText("[data-testid='select-secondary-teacher']", secondaryTeacherName);

      cy.findByText("Save").click();
      cy.findByText("Class saved successfully").should("be.visible");
    });

    it("removes secondary teacher", () => {
      cy.loginAs({ role: "dataAdmin" });
      goToStudentListFor(groupWithRemovedSecondaryTeacher);
      cy.findByText("Manage Class").click();

      cy.findAllByTestId("secondary-teacher-name")
        .first()
        .should("contain", secondaryTeacherName)
        .should("be.visible");

      cy.findAllByTestId("remove-secondary-teacher")
        .first()
        .click();

      cy.findByTestId("secondary-teacher-name")
        .should("not.contain", secondaryTeacherName)
        .should("be.visible");
      cy.findByText("Save").click();
      cy.findByText("Class saved successfully").should("be.visible");
    });
  });
  describe("Secondary Teacher", () => {
    it("sees classes with secondary access", () => {
      cy.loginAs({ role: "demoTeacher" });
      cy.findAllByText(groupWithAddedSecondaryTeacher).should("have.length", 2);
      cy.findAllByText(groupWithAddedSecondaryTeacher)
        .eq(1)
        .should("be.visible");
      cy.findByText(groupWithRemovedSecondaryTeacher, { timeout: 2000 }).should("not.exist");
    });
  });
});
