import { randomizeCase } from "../support/common/emailUtils";
import { assertSchoolOverviewHeader, clickVisibleTestId, clickVisibleText } from "../support/common/utils";
import { CONTEXT_MENU_ROLE_TEST_IDS } from "../support/common/constants";

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");
const PREVIOUS_SCHOOL_YEAR = Cypress.env("PREVIOUS_SCHOOL_YEAR");

/**
 * Groups used: -
 * Modifies: User roster - adds roles to dataAdmin/admin
 * Can't be rerun without test database restart
 * Requires superAdmin/dataAdmin/admin accounts
 */
describe("Switching roles: ", () => {
  describe("Super Admin", () => {
    it("should be able to add admin role to an existing data admin", () => {
      cy.loginAs({ role: "superAdmin" });
      const email = randomizeCase("<EMAIL>");
      clickVisibleText("Test/Demo Organization");
      clickVisibleTestId("addCoachUserMain");
      cy.findByText("Create Coach/Administrator Account").should("be.visible");
      cy.findByText("Submit").scrollIntoView();
      clickVisibleText("Test Elementary Site");
      cy.findByTestId("txtCoachEmail").type(email);
      cy.findByTestId("txtCoachFirstName").type("Data");
      cy.findByTestId("txtCoachLastName").type("Admin");
      clickVisibleText("Submit");
      cy.contains(
        `Existing user tied to the email ${email} was updated with a coach role for this organization.`
      ).should("be.visible");
    });
    it("should be able to add dataAdmin role to an existing coach", () => {
      cy.loginAs({ role: "superAdmin" });
      clickVisibleText("Test/Demo Organization");
      clickVisibleTestId("addDataAdminUserMain");
      cy.findByText("This is the account setup screen.").should("be.visible");
      cy.findByTestId("txtDataAdminEmail").type(randomizeCase("<EMAIL>"));
      cy.findByTestId("txtDataAdminFirstName").type("Doesn't");
      cy.findByTestId("txtDataAdminLastName").type("Matter");
      clickVisibleText("Submit");
      cy.findByText("Existing user tied to this email was updated with data admin role for this organization.").should(
        "be.visible"
      );
    });
  });
  describe("User with data admin privileges originally", () => {
    beforeEach(() => {
      cy.loginAs({ role: "dataAdmin" });
    });
    it("should initially log in as data admin and be able to change role to admin", () => {
      cy.findByText("Export current roster").should("be.visible");
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_ROLE_TEST_IDS.admin);
      assertSchoolOverviewHeader();
    });
    it("should now log in as admin and be able to to change primary role back to data admin", () => {
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      cy.findByTestId(`schoolYear${PREVIOUS_SCHOOL_YEAR}`).should("be.visible");
      cy.findByTestId(`schoolYear${CURRENT_SCHOOL_YEAR}`).should("be.visible");
      cy.findByTestId(CONTEXT_MENU_ROLE_TEST_IDS.dataAdmin).click();
      cy.findByText("Export current roster").should("be.visible");
    });
    it("should log in as data admin by default again", () => {
      cy.findByText("Export current roster").should("be.visible");
    });
  });
  describe("User with coach user privileges originally", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    it("should initially log in as admin and be able to change role to data admin", () => {
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      cy.findByTestId(CONTEXT_MENU_ROLE_TEST_IDS.dataAdmin).click();
      cy.findByText("Export current roster").should("be.visible");
    });
    it("should now log in as data admin and be able to change primary role back to admin", () => {
      cy.findByText("Export current roster").should("be.visible");
      clickVisibleTestId("userContextMenu");
      cy.findByTestId(CONTEXT_MENU_ROLE_TEST_IDS.admin).click();
      assertSchoolOverviewHeader();
    });
    it("should log in as admin by default again", () => {
      assertSchoolOverviewHeader();
    });
  });
});
