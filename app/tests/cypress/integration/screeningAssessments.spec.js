import { clickVisibleTestId, clickVisibleText } from "../support/common/utils";
import { clickDashboardTab, clickElementByTextWithinSideNav } from "../support/common/navigating";
import { TEST_GROUPS } from "../support/common/constants";

/**
 * Groups used: -
 * Modifies: -
 * Can be rerun without test database restart
 * Requires -
 */
describe("Generate Screening Assessments", () => {
  describe("Teacher", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
      clickDashboardTab(TEST_GROUPS.gradeKgroup1.name, "studentsTab");
      cy.findByText("Roster").should("be.visible");
      cy.findByTestId("siteSelectorId")
        .should("be.visible")
        .contains("Test Elementary Site");
      clickVisibleTestId("userContextMenu");
      clickVisibleText("Screening Assessments");
      clickElementByTextWithinSideNav("Grade 01");
    });

    it("is able to see assessments in correct order for Elementary school", () => {
      cy.findByText("Generate Screening Assessments").should("be.visible");
      cy.findAllByTestId("assessment-screening-container")
        .should("have.length", 3)
        .first()
        .contains("Fall Screening")
        .parent()
        .within(() => {
          cy.findByText("Sums to 6").should("be.visible");
          cy.findByText("Subtraction 0-5").should("be.visible");
          cy.findByText("Quantity Comparison 20-99").should("be.visible");
        });
      cy.findAllByTestId("assessment-screening-container")
        .eq(1)
        .contains("Winter Screening")
        .parent()
        .within(() => {
          cy.findByText("Sums to 12").should("be.visible");
          cy.findByText("Subtraction 0-9").should("be.visible");
          cy.findByText("Fact Families: Add/Subtract 0-5").should("be.visible");
        });
      cy.findAllByTestId("assessment-screening-container")
        .eq(2)
        .contains("Spring Screening")
        .parent()
        .within(() => {
          cy.findByText("Sums to 20").should("be.visible");
          cy.findByText("Subtraction 0-20").should("be.visible");
          cy.findByText("Fact Families: Add/Subtract 0-9").should("be.visible");
          cy.findByText("Quantity Comparisons 101-999").should("be.visible");
        });
    });

    it("is able to see that sorting is correct when moving between grades", () => {
      clickElementByTextWithinSideNav("Grade 05");
      assertAssessmentScreeningContainerOrder();
      clickElementByTextWithinSideNav("Grade 06");
      assertAssessmentScreeningContainerOrder();
    });
  });
});

function assertAssessmentScreeningContainerOrder() {
  cy.findAllByTestId("assessment-screening-container")
    .should("have.length", 3)
    .eq(0)
    .contains("Fall Screening");
  cy.findAllByTestId("assessment-screening-container")
    .eq(1)
    .contains("Winter Screening");
  cy.findAllByTestId("assessment-screening-container")
    .eq(2)
    .contains("Spring Screening");
}
