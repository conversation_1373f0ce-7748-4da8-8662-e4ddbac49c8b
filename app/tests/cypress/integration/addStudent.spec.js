import { TEST_GROUPS } from "../support/common/constants";
import { goToStudentListFor } from "../support/common/navigating";
import { clickVisibleTestId, clickVisibleText, typeByTestId } from "../support/common/utils";

const existingArchivedId = "1234567";
const existingActiveId = "7654321";
const dummyId = "1111111";
const studentLastName = "Dylan";
const studentFirstName = "Bob";
const studentDOB = "2006-05-17";

function fillNewStudentData() {
  cy.findByTestId("addStudentForm").should("be.visible");
  typeByTestId("newStudentLastName", studentLastName);
  typeByTestId("newStudentFirstName", studentFirstName);
  typeByTestId("newStudentDOB", studentDOB);
}

/**
 * Groups used: grade4group1
 * Modifies: -
 * Can be rerun without test database restart
 */
describe("Data admin", () => {
  beforeEach(() => {
    cy.loginAs({ role: "dataAdmin" });
    goToStudentListFor(TEST_GROUPS.grade4group1.name);
  });
  it("should not be able to add a student with the same localId as other active student", () => {
    clickVisibleTestId("addNewStudentButton");
    fillNewStudentData();
    typeByTestId("newStudentLocalId", existingActiveId);
    typeByTestId("newStudentStateId", dummyId);
    clickVisibleText("Save");
    cy.findByText("Student with Local ID: 7654321 - Gosling, Samuel, already exists in this organization").should(
      "be.visible"
    );
  });
  it("should not be able to add a student with the same localId as other archived student", () => {
    clickVisibleTestId("addNewStudentButton");
    fillNewStudentData();
    typeByTestId("newStudentLocalId", existingArchivedId);
    typeByTestId("newStudentStateId", dummyId);
    clickVisibleText("Save");
    cy.findByText(
      "Student with Local ID: 1234567 - Jackson, Samuel (Archived), already exists in this organization"
    ).should("be.visible");
  });
  it("should not be able to add a student with the same stateId as other active student ", () => {
    clickVisibleTestId("addNewStudentButton");
    fillNewStudentData();
    typeByTestId("newStudentLocalId", dummyId);
    typeByTestId("newStudentStateId", existingActiveId);
    clickVisibleText("Save");
    cy.findByText("Student with State ID: 7654321 - Gosling, Samuel, already exists in this organization").should(
      "be.visible"
    );
  });
  it("should not be able to add a student with the same stateId as other archived student ", () => {
    clickVisibleTestId("addNewStudentButton");
    fillNewStudentData();
    typeByTestId("newStudentLocalId", dummyId);
    typeByTestId("newStudentStateId", existingArchivedId);
    clickVisibleText("Save");
    cy.findByText(
      "Student with State ID: 1234567 - Jackson, Samuel (Archived), already exists in this organization"
    ).should("be.visible");
  });
});
