import { clickVisibleTestId, waitForDetachedDom } from "../support/common/utils";

/**
 * Groups used: -
 * Modifies: Org permission to support users
 * Can't be rerun without test database restart
 * Requires User with 2 org access
 */
describe("Manage Users view:", () => {
  const ROLE_NAMES = {
    support: "support",
    superAdmin: "superAdmin"
  };
  beforeEach(() => {
    cy.loginAs({ role: ROLE_NAMES.superAdmin });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_manage-users");
  });

  it("allows to remove access to an organization for a support user role", () => {
    selectRole(ROLE_NAMES.support);
    cy.findByText("<EMAIL>").should("be.visible");
    verifyNumberOfOrgAccessByLastName("User", 2);
    cy.findAllByText("Other Organization")
      .first()
      .should("be.visible");
    cy.findByText("Test/Demo Organization").should("be.visible");
    removeLastOrganizationAccessOption();
    cy.reload();
    waitForDetachedDom();
    verifyNumberOfOrgAccessByLastName("User", 1);
  });

  it("doesn't allow the super admin to remove its own account", () => {
    selectRole(ROLE_NAMES.superAdmin);
    cy.findByText("<EMAIL>").should("be.visible");
    cy.findByTestId("user-row")
      .first()
      .within(() => {
        cy.findByTestId("user-row-checkbox", { timeout: 1000 }).should("not.exist");
      });
  });

  it("allows to remove a support user", () => {
    selectRole(ROLE_NAMES.support);
    cy.findByText("Delete Selected Users").should("be.visible");
    cy.findByText("<EMAIL>").should("be.visible");
    removeUserByLastName("User");
    cy.get("#user-role-selector .active")
      .should("be.visible")
      .should("have.text", "Support User");
    cy.findByText("<EMAIL>", { timeout: 1000 }).should("not.exist");
  });
});

function selectRole(roleName) {
  clickVisibleTestId(`manage_${roleName}`);
}

function verifyNumberOfOrgAccessByLastName(lastName, orgAccessNumber) {
  cy.findByTestId(`user_last_name_${lastName}`)
    .parent()
    .within(() => {
      cy.get(".select__multi-value").should("have.length", orgAccessNumber);
    });
}

function removeLastOrganizationAccessOption() {
  cy.get("#selectActiveOrganizations").within(() => {
    cy.get(".select__multi-value__remove")
      .last()
      .click();
  });
  waitForDetachedDom();
}

function removeUserByLastName(lastName) {
  cy.findByTestId(`user_last_name_${lastName}`)
    .parent()
    .within(() => {
      cy.get("input[type=checkbox]")
        .should("be.visible")
        .click();
    });
  cy.findByText("Delete Selected Users")
    .should("not.be.disabled")
    .click();
  cy.findByText("Yes, delete selected users").click();
  cy.findByText("Selected users removed successfully").should("be.visible");
}
