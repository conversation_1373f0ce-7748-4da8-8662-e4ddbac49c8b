import { generateNewUserEmail, randomizeCase } from "../support/common/emailUtils";
import { addCoachToSchool, assertSchoolOverviewHeader } from "../support/common/utils";
import { SCHOOL_ITEMS, SITE_IDS } from "../support/common/constants";

/**
 * Groups used: -
 * Modifies: User roster
 * Can be rerun without test database restart
 */
describe("Add new Coach", () => {
  const newCoachEmail = generateNewUserEmail("coach");
  it("Data Admin adds new Coach user", () => {
    cy.loginAs({ role: "dataAdmin" });
    cy.findByTestId(SCHOOL_ITEMS.elementary).should("be.visible");
    addCoachToSchool(SITE_IDS.highSchool);
    const email = randomizeCase(newCoachEmail);
    cy.get("#txtCoachEmail").type(email);
    cy.get("#txtCoachFirstName").type("John");
    cy.get("#txtCoachLastName").type("Doe");
    cy.findByText("Submit").click();

    cy.contains(
      `User with email ${email} added. Please inform the user that the invite has been sent if you aren't using SSO exclusively.`
    ).should("be.visible");
  });
  it("New Coach user can log in and see school overview", () => {
    cy.loginAs({ email: newCoachEmail });
    assertSchoolOverviewHeader();
  });
});
