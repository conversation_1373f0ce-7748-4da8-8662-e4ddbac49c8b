import { clickVisibleText, dropFile } from "../support/common/utils";

/**
 * Groups used: -
 * Modifies: External Assessment Scores
 * Can be rerun without test database restart
 * Requires organization
 */
describe("External Assessment Scores Upload:", () => {
  describe("Super Admin: ", () => {
    beforeEach(() => {
      cy.loginAs({ role: "superAdmin" });
      clickVisibleText("Test/Demo Organization");
      cy.findByText("Export current roster").should("not.be.disabled");
      cy.findByText("Import External Assessment Scores", { exact: false }).click();
    });

    describe("valid files: ", () => {
      it("should upload both state and district scores", () => {
        dropFile("externalAssessments_valid.csv");

        cy.findByTestId("studentSubtotal").should("contain", 6);
        cy.findByTestId("state-scoresSubtotal").should("contain", 6);
        cy.findByTestId("district-scoresSubtotal").should("contain", 6);

        clickVisibleText("Finalize Upload");
        cy.findByText("Import External Assessment Scores", { exact: false }).should("be.visible");
      });

      it("should upload only district scores if no state scores are present", () => {
        dropFile("externalAssessments_districtOnly.csv");

        cy.findByTestId("studentSubtotal").should("contain", 2);
        cy.findByTestId("state-scoresSubtotal").should("contain", 0);
        cy.findByTestId("district-scoresSubtotal").should("contain", 2);

        clickVisibleText("Finalize Upload");
        cy.findByText("Import External Assessment Scores", { exact: false }).should("be.visible");
      });

      it("should upload only state scores if no district scores are present", () => {
        dropFile("externalAssessments_stateOnly.csv");

        cy.findByTestId("studentSubtotal").should("contain", 2);
        cy.findByTestId("state-scoresSubtotal").should("contain", 2);
        cy.findByTestId("district-scoresSubtotal").should("contain", 0);

        clickVisibleText("Finalize Upload");
        cy.findByText("Import External Assessment Scores", { exact: false }).should("be.visible");
      });

      it("should upload both state and district scores if a mix of the two is present", () => {
        dropFile("externalAssessments_mixed.csv");

        cy.findByTestId("studentSubtotal").should("contain", 2);
        cy.findByTestId("state-scoresSubtotal").should("contain", 1);
        cy.findByTestId("district-scoresSubtotal").should("contain", 2);

        clickVisibleText("Finalize Upload");
        cy.findByText("Import External Assessment Scores", { exact: false }).should("be.visible");
      });
    });

    describe("invalid files: ", () => {
      it("should see validation errors", () => {
        // EMPTY ROSTER
        dropFile("emptyCSV.csv");
        cy.findByText("The upload tool found no parsable data.").should("be.visible");

        // DISTRICT ONLY WITH MISSING FIELD VALUES
        dropFile("externalAssessments_districtOnlyMissingFields.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain(
              "There is a problem with district assessment fields. Make sure to provide a value for assessment name and spring or fall score fields. Please see row: 2."
            );
            expect(text).to.contain(
              "At least one spring district assessment field contains a value. Make sure to provide a value for districtAssessmentSpringScaleScore. Please see row: 3."
            );
          });

        cy.reload();

        // STATE ONLY WITH MISSING FIELD VALUES
        dropFile("externalAssessments_stateOnlyMissingFields.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain(
              "At least one state assessment field contains a value. Make sure to provide a value for stateAssessmentName. Please see row: 2."
            );
            expect(text).to.contain(
              "At least one state assessment field contains a value. Make sure to provide a value for stateAssessmentProficient. Please see row: 3."
            );
          });

        cy.reload();

        // ATTEMPTING TO ASSIGN EXTERNAL SCORES TO A STUDENT WHO DOESN'T EXIST IN THE DB
        dropFile("externalAssessments_newStudent.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain(
              "Student: Brown John with localID 123 and stateID 1247 doesn't exist in 2024 year. Please see row: 3."
            );
          });

        cy.reload();

        // ATTEMPTING TO USE OLD SCHOOL YEAR IN EXTERNAL SCORES TO A STUDENT WHO EXISTS IN 2024 IN THE DB
        dropFile("externalAssessments_existingStudentWithPreviousYear.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain(
              "Student: Aiden Logan with localID 3598203 and stateID 6437882 doesn't exist in 2017 year. Please see row: 2."
            );
          });

        cy.reload();

        // ATTEMPTING TO USE DIFFERENT YEARS IN ONE EXTERNAL SCORES UPLOAD
        dropFile("externalAssessments_differentAssessmentYears.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain(
              "Your CSV file contains different values: 2023, 2024 for the AssessmentYear field.\n" +
                "\n" +
                "Please use the same year for the AssessmentYear field in the provided file."
            );
          });

        cy.reload();

        // FILE WITH MISSING VALUES
        dropFile("externalAssessments_missingValues.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain("Student first name must be at least 1 characters. Please see row: 2.");
            expect(text).to.contain("Student state ID must be at least 1 characters. Please see row: 3.");
          });

        cy.reload();

        // FILE WITH MISSING FIELDS
        dropFile("externalAssessments_missingFields.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain("Your CSV file is missing the following fields:");
            expect(text).to.contain("DistrictAssessmentSpringScaleScore");
            expect(text).to.contain(
              "Please make sure you are using the latest CSV Template for Assessment Score File Uploads."
            );
          });

        cy.reload();

        // FILE WITH UNSUPPORTED FIELDS
        dropFile("externalAssessments_extraFields.csv");
        cy.get("textarea")
          .invoke("text")
          .then(text => {
            expect(text).to.contain("Unsupported fields found:");
            expect(text).to.contain("ExtraField");
            expect(text).to.contain(
              "Please make sure you are using the latest CSV Template for Assessment Score File Uploads."
            );
          });

        cy.reload();
      });
    });
  });
});
