import { goToStudentListFor } from "../support/common/navigating";
import { EMAILS, ORGID, ROLE_IDS, SITE_IDS, TEST_GROUPS } from "../support/common/constants";
import { clickVisibleTestId, clickVisibleText } from "../support/common/utils";

/**
 * Groups used: grade4group1
 * Modifies: -
 * Can be rerun without test database restart
 * Requires student groups with students and archived students
 */
describe("Search for", () => {
  describe("Student:", () => {
    beforeEach(() => {
      cy.loginAs({ role: "dataAdmin" });
      goToStudentListFor(TEST_GROUPS.grade4group1.name);
    });
    it("should return active and archived students", () => {
      clickVisibleText("Student Search");
      cy.findByTestId("studentFirstName").type("Samuel");
      clickVisibleTestId("studentSearchButton");
      cy.contains("<PERSON>, <PERSON> (Archived)").should("be.visible");
      cy.findByText("<PERSON><PERSON><PERSON>, <PERSON>").should("be.visible");
    });
    it("should provide a link to Unarchived Students when found student is archived", () => {
      const existingArchivedId = "1234567";
      clickVisibleText("Student Search");
      cy.findByTestId("studentLocalId").type(existingArchivedId);
      clickVisibleTestId("studentSearchButton");
      clickVisibleTestId("unarchiveLink");
      cy.findByText("Unarchive Students").should("be.visible");
      cy.findByText("Jackson, Samuel").should("be.visible");
    });
  });
  describe("Teacher:", () => {
    it("should return a teacher when searching by last name", () => {
      cy.loginAs({ role: "dataAdmin" });
      cy.findByText("Test/Demo Organization").should("be.visible");
      clickVisibleText("Manage Accounts");
      clickVisibleText("Search Teacher");
      cy.findByTestId("teacherLastName").type("Teacher");
      clickVisibleTestId("teachersSearchButton");
      cy.findByText("Teacher").should("be.visible");
      cy.findByText("MultiSite").should("be.visible");
      cy.findByText("<EMAIL>").should("be.visible");
      cy.findByText("Test Elementary Site").should("be.visible");
      cy.findByText("Test High School Site").should("be.visible");
      clickVisibleText("Dummy Site");
      cy.findByTestId("multiSiteTeacher_Active").should("be.visible");
      cy.url().should("include", `${ORGID}/site/${SITE_IDS.dummy}/?id=multiSiteTeacher&role=${ROLE_IDS.teacher}`);
    });
  });
  describe("Coach:", () => {
    it("should return a coach when searching by email", () => {
      cy.loginAs({ role: "dataAdmin" });
      cy.findByText("Test/Demo Organization").should("be.visible");
      clickVisibleText("Manage Accounts");
      clickVisibleText("Search Coach");
      cy.findByTestId("coachesEmail").type(EMAILS.coach);
      clickVisibleTestId("coachesSearchButton");
      cy.findByTestId("coaches-search-row-admin_user_id").within(() => {
        cy.findByText("User").should("be.visible");
        cy.findByText("Admin").should("be.visible");
        cy.findByText(EMAILS.coach.toLowerCase()).should("be.visible");
        cy.findByText("Dummy Site").should("be.visible");
        cy.findByText("Test Elementary Site").should("be.visible");
        clickVisibleText("Test High School Site");
      });
      cy.findByTestId("admin_user_id_Active").should("be.visible");
      cy.url().should("include", `${ORGID}/site/${SITE_IDS.highSchool}/?id=admin_user_id&role=${ROLE_IDS.admin}`);
    });
  });
});
