import { generateNewUserEmail } from "../support/common/emailUtils";
import { clickVisibleText } from "../support/common/utils";
import { TEST_GROUPS } from "../support/common/constants";

/**
 * Groups used: grade5group1
 * Modifies: Adds primary and secondary teacher
 * Can't be rerun without test database restart
 * Requires Site with group
 */
describe("Manage Accounts:", () => {
  const newPrimaryTeacherEmail = generateNewUserEmail("primary_teacher");
  const newSecondaryTeacherEmail = generateNewUserEmail("secondary_teacher");

  describe("Data Admin", () => {
    beforeEach(() => {
      cy.loginAs({ role: "dataAdmin" });
      cy.findByText("Test/Demo Organization").should("be.visible");
      clickVisibleText("Manage Accounts");
      cy.get(".student-group-list")
        .first()
        .within(() => {
          clickVisibleText("Test Elementary Site");
        });
      cy.findByText("Add Teacher").click();
      // eslint-disable-next-line cypress/unsafe-to-chain-command
      cy.get(".conOverviewHeader")
        .first()
        .scrollIntoView()
        .within(() => {
          cy.findByText("Add Teacher").should("be.visible");
        });
    });
    it("adds new primary Teacher", () => {
      cy.findByPlaceholderText("Last name").type("Johnson");
      cy.findByPlaceholderText("First name").type("John");
      cy.findByPlaceholderText("Teacher ID").type("7654323");
      cy.findByPlaceholderText("Email").type(newPrimaryTeacherEmail);

      cy.findByText("Select a class").click();
      cy.findByText(TEST_GROUPS.grade5group1.name).click();
      cy.findByPlaceholderText("Section ID").should("have.value", "fqaFT_05");
      cy.findByPlaceholderText("Grade").should("have.value", "05");
      cy.findByText("Select teacher type").click();
      cy.findByText("Primary").click();

      cy.findByText("Add teacher to the selected student group").click();
      cy.findByText("Yes, add new teacher").click();

      cy.findByText("New primary teacher successfully added", { exact: false }).should("be.visible");
    });

    it("adds new secondary Teacher", () => {
      cy.findByPlaceholderText("Last name").type("Smith");
      cy.findByPlaceholderText("First name").type("John");
      cy.findByPlaceholderText("Teacher ID").type("7654324");
      cy.findByPlaceholderText("Email").type(newSecondaryTeacherEmail);

      cy.findByText("Select a class").click();
      cy.findByText(TEST_GROUPS.grade5group1.name).click();
      cy.findByPlaceholderText("Section ID").should("have.value", "fqaFT_05");
      cy.findByPlaceholderText("Grade").should("have.value", "05");
      cy.findByText("Select teacher type").click();
      cy.findByText("Secondary").click();

      cy.findByText("Add teacher to the selected student group").click();

      cy.findByText("New secondary teacher successfully added", { exact: false }).should("be.visible");
    });
  });
  describe("New Primary teacher", () => {
    it("logs in and sees the owned group", () => {
      cy.loginAs({ email: newPrimaryTeacherEmail });
      cy.findByTestId(`student-group_${TEST_GROUPS.grade5group1.name}`).should("be.visible");
    });
  });
  describe("New Secondary teacher", () => {
    it("logs in and sees the group with secondary access", () => {
      cy.loginAs({ email: newSecondaryTeacherEmail });
      cy.findByTestId(`student-group_${TEST_GROUPS.grade5group1.name}`).should("be.visible");
    });
  });
});
