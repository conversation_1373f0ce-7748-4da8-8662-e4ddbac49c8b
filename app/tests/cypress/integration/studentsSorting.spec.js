import { clickVisibleTestId, startScreeningFor } from "../support/common/utils";
import { TEST_GROUPS } from "../support/common/constants";
import { inputScreeningScores, saveScreeningScores } from "../support/enterScoreHelper";
import { clickElementByTextWithinSideNav, clickDashboardTab } from "../support/common/navigating";

/**
 * Groups used: grade2group3
 * Modifies: Screening
 * Can't be rerun without test database restart
 * Requires group without screening and any group with student roster
 */
describe("Students sorting:", () => {
  describe("Teacher", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
    });

    it("sorts students in screening score entry page", () => {
      clickDashboardTab(TEST_GROUPS.gradeKgroup1.name, "studentsTab");
      cy.findByText("Roster").should("be.visible");
      startScreeningFor(TEST_GROUPS.grade2group3.name);
      cy.findByTestId("new-screening-sort-by").should("have.text", "Last, First");
      verifyFirstStudentName("<PERSON>, <PERSON>");
      clickVisibleTestId("new-screening-sort-by");
      cy.findByTestId("new-screening-sort-by").should("have.text", "First / Last");
      verifyFirstStudentName("Abran Zack");
    });

    it("sorts students in students tab", () => {
      clickElementByTextWithinSideNav(TEST_GROUPS.grade2group3.name);
      clickVisibleTestId("continue-button");
      inputScreeningScores(false);
      saveScreeningScores();
      cy.findByTestId("classwideInterventionTab").should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade2group3.name, "studentsTab");
      cy.findByTestId("sort-by").should("have.text", "Last, First");
      cy.get("[data-student-grade='02']")
        .first()
        .contains("Bull, Werner");
      cy.findByTestId("sort-by").click({ force: true });
      cy.findByTestId("sort-by").should("have.text", "First / Last");
      cy.get("[data-student-grade='02']")
        .first()
        .contains("Abran Zack");
    });

    it("sorts students in classwide intervention tab", () => {
      clickElementByTextWithinSideNav(TEST_GROUPS.grade2group3.name);
      clickDashboardTab(TEST_GROUPS.grade2group3.name, "classwideInterventionTab");
      cy.findByTestId("classwideInterventionTab")
        .should("be.visible")
        .should("have.class", "active");
      cy.findByTestId("sort-by").should("have.text", "Last, First");
      verifyFirstStudentName("Bull, Werner");
      clickVisibleTestId("sort-by");
      cy.findByTestId("sort-by").should("have.text", "First / Last");
      cy.findAllByTestId("student-test-name")
        .last()
        .contains("Werner Bull");
    });
  });
});

function verifyFirstStudentName(studentName) {
  cy.findAllByTestId("student-test-name")
    .first()
    .contains(studentName);
}
