import { clickVisibleText, waitForDetachedDom } from "./utils";

export function inputCredentialsAndLogIn(email, shouldCheckLoginLoading) {
  waitForDetachedDom();
  const password = "Ties.2016";
  cy.get('[data-testid="login-forgot-password-link"]').should("exist");
  cy.findByPlaceholderText("Email").type(email, { force: true });
  cy.findByPlaceholderText("Password").type(password, { force: true });
  clickVisibleText("Log In");
  if (shouldCheckLoginLoading) {
    cy.get('[data-testid="loading-text"').should("be.visible");
    cy.get('[data-testid="loading-text"', { timeout: 15000 }).should("not.exist");
    cy.get('[data-testid="loading-icon"]', { timeout: 15000 }).should("not.exist");
  }
  waitForDetachedDom();
}
