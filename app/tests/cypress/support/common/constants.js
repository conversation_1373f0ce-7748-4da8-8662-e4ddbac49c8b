export const ORGID = "test_organization_id";
export const MAILTRAP_API_TOKEN = "716c450bddebf81498331a2a1558d028";
export const BENCHMARK_PERIODS = [
  {
    name: "Fall",
    startDate: {
      month: 8.0,
      day: 1.0
    },
    endDate: {
      month: 12.0,
      day: 31.0
    }
  },
  {
    name: "Winter",
    startDate: {
      month: 1.0,
      day: 1.0
    },
    endDate: {
      month: 3.0,
      day: 31.0
    }
  },
  {
    name: "Spring",
    startDate: {
      month: 4.0,
      day: 1.0
    },
    endDate: {
      month: 7.0,
      day: 31.0
    }
  }
];
export const NAVBAR_SCHOOL_YEAR_TEST_ID = "navbar-school-year";
export const SCHOOL_ITEMS = {
  elementary: "schoolItem_Test Elementary Site",
  highSchool: "schoolItem_Test High School Site",
  dummy: "schoolItem_Dummy Site",
  sunny: "schoolItem_Sunny Slope Elementary",
  blocked: "schoolItem_Blocked Uploads Elementary"
};
export const SCHOOL_NAMES = {
  elementary: "Test Elementary Site",
  highSchool: "Test High School Site",
  dummy: "Dummy Site"
};
export const CONTEXT_MENU_SITE_IDS = {
  elementary: "site_test_elementary_site_id",
  highSchool: "site_test_high_school_site_id",
  dummy: "site_dummy_site_id"
};
export const SITE_IDS = {
  elementary: "test_elementary_site_id",
  highSchool: "test_high_school_site_id",
  dummy: "dummy_site_id"
};
export const EMAILS = {
  teacher: "<EMAIL>",
  demoTeacher: "<EMAIL>",
  coach: "<EMAIL>",
  dataAdmin: "<EMAIL>",
  superAdmin: "<EMAIL>",
  multiSiteTeacher: "<EMAIL>",
  universalDataAdmin: "<EMAIL>",
  supportUser: "<EMAIL>"
};
export const TEST_GROUPS = {
  gradeKgroup1: {
    name: "Test K (fqaFT_K)",
    purpose: "Single Student Screening Outcome"
  },
  gradeKgroup2: {
    name: "Test K#2 (fqaFT_K_2)",
    purpose: "Move Students"
  },
  gradeKgroup3: {
    name: "Test K 3 (K-3)",
    purpose: "Failing screening for individual intervention"
  },
  grade1group1: {
    name: "Test 01 (fqaFT_01)",
    purpose: "Move Students"
  },
  grade1group2: {
    name: "Test 01#2 (fqaFT_01_2)",
    purpose: "Failing screening for classwide intervention"
  },
  grade1group3: {
    name: "Test 01#3 (fqaFT_01_3)",
    purpose: "Instructional Videos"
  },
  grade1group4: {
    name: "Test 01#4 (fqaFT_01_4)",
    purpose: "Instructional Videos"
  },
  grade2group1: {
    name: "Test 02 (fqaFT_02)",
    purpose: "Classwide intervention"
  },
  grade2group2: {
    name: "Test 02#2 (fqaFT_02_2)",
    purpose: "Passing screening"
  },
  grade2group3: {
    name: "Test 02#3 (fqaFT_02_3) (9876)",
    purpose: "Student sorting"
  },
  grade3group1: {
    name: "Test 03 (fqaFT_03)",
    purpose: "Passing Goal Skills in Individual intervention"
  },
  grade3group2: {
    name: "Test 03#2 (fqaFT_03_2)",
    purpose: "Practicing Goal Skills in Individual intervention"
  },
  grade4group1: {
    name: "Test 04 (fqaFT_04)",
    grade: "04",
    purpose: "Pre-made student with both classwide and individual interventions"
  },
  grade5group1: {
    name: "Test 05 (fqaFT_05)",
    purpose: "Benchmark Assessment Printout"
  },
  grade5group2: {
    name: "Test 05#2 (fqaFT_05_2)",
    purpose: "Moving students, un-archiving, adding secondary teacher"
  },
  grade6group1: {
    name: "Test 06 (fqaFT_06)",
    purpose: "Individual Goal Skill printout without screening"
  },
  grade6group2: {
    name: "Test 06#2 (fqaFT_06_2)",
    partialName: "Test 06#2",
    purpose: "Forcing classwide intervention"
  },
  grade6group3: {
    name: "Test 06#3 (fqaFT_06_3)",
    partialName: "Test 06#3",
    purpose: "Moving students and retaining individual intervention eligibility"
  },
  grade6group4: {
    name: "Test 06#4 (fqaFT_06_4)",
    partialName: "Test 06#4",
    purpose: "Moving students and retaining individual intervention eligibility"
  },
  grade7group1: {
    name: "Test 07 (fqaFT_07)",
    purpose: "Follow Up Individual Interventions for Teacher"
  },
  grade7group2: {
    name: "Test 07#2 (fqaFT_07_2)",
    purpose: "Multiple students in single small group individual intervention"
  },
  grade8group1: {
    name: "Test 08 (fqaFT_08)",
    purpose: "Classwide Intervention with scores"
  },
  grade8group2: {
    name: "Test 08#2 (fqaFT_08_2)",
    purpose: "Instructional Videos Classwide Intervention without scores"
  },
  grade8group3: {
    name: "Test 08#3 (fqaFT_08_3)",
    purpose: "Group with 11 students and cwi with four week of scores"
  },
  grade8group4: {
    name: "Test 08#4 (fqaFT_08_4)",
    purpose: "Group with 12 students"
  },
  grade9group1: {
    name: "Test 09 (fqaFT_09)",
    purpose: "HighSchool Classwide intervention and Individual Drilldown intervention printout"
  },
  grade9group2: {
    name: "Test 09#2 (fqaFT_09#2)",
    purpose: "HighSchool Individual Goal Skill printout"
  }
};
export const COACH_GRADE_LABELS = {
  k: "Kindergarten",
  first: "1st Grade",
  second: "2nd Grade",
  third: "3rd Grade",
  fourth: "4th Grade",
  fifth: "5th Grade",
  sixth: "6th Grade",
  seventh: "7th Grade",
  eighth: "8th Grade",
  hs: "HS Grade"
};

export const ROLE_IDS = {
  teacher: "arbitraryIdteacher",
  admin: "arbitraryIdadmin",
  dataAdmin: "arbitraryIddataAdmin",
  universalDataAdmin: "arbitraryIduniversalDataAdmin",
  universalCoach: "arbitraryIduniversalCoach",
  superAdmin: "arbitraryIdsuperAdmin",
  support: "arbitraryIdsupport"
};

export const CONTEXT_MENU_ROLE_TEST_IDS = {
  teacher: `availableRoles_${ROLE_IDS.teacher}`,
  admin: `availableRoles_${ROLE_IDS.admin}`,
  dataAdmin: `availableRoles_${ROLE_IDS.dataAdmin}`
};
export const DOMAIN_URL = "http://localhost:3000";

/*
When Class Rules change use this db script to replace INTERVENTION_SKILLS_BY_GRADE_BY_INDEX

var classwideRules = db.Rules.find({ grade: { $exists: true } })
  .projection({ grade: 1, "skills.assessmentId": 1 })
  .toArray();
var assessmentNameById = db.Assessments.find({})
  .projection({ name: 1 })
  .toArray()
  .reduce((a, c) => {
    a[c._id] = c.name;
    return a;
  }, {});
var parsedClasswideRules = classwideRules.reduce((a, c) => {
  if (!a[c.grade]) {
    a[c.grade] = {};
  }
  c.skills.forEach((s, i) => {
    a[c.grade][i] = assessmentNameById[s.assessmentId];
  });
  return a;
}, {});

print(parsedClasswideRules);
 */

export const INTERVENTION_SKILLS_BY_GRADE_BY_INDEX = {
  "01": {
    "0": "Sums to 6",
    "1": "Sums to 12",
    "2": "Subtraction 0-5",
    "3": "Sums to 20",
    "4": "Subtraction 0-9",
    "5": "Fact Families: Add/Subtract 0-5",
    "6": "Fact Families: Add/Subtract 0-9",
    "7": "Subtraction 0-12",
    "8": "Subtraction 0-15",
    "9": "Subtraction 0-20",
    "10": "Fact Families: Addition/Subtraction 0-20"
  },
  "02": {
    "0": "Sums to 20",
    "1": "Subtraction 0-9",
    "2": "Subtraction 0-12",
    "3": "Subtraction 0-15",
    "4": "Subtraction 0-20",
    "5": "Quantity Comparison Sums and Differences to 20",
    "6": "Fact Families: Add/Subtract 0-9",
    "7": "Fact Families: Addition/Subtraction 0-20",
    "8": "Add 2-Digit Numbers without Regrouping",
    "9": "Subtract 2-digit Numbers without Regrouping",
    "10": "Create Equivalent Addition & Subtraction Problems using Associative Property & Near Easy Problems",
    "11": "Create Equivalent Addition & Subtraction Problems using Place Value & Decomposition",
    "12": "Add 2-Digit Numbers with Regrouping",
    "13": "Subtract 2-Digit Numbers with Regrouping",
    "14": "Addition 3-Digit Numbers with & without Regrouping",
    "15": "Subtraction 3-Digit Number with & without Regrouping"
  },
  "03": {
    "0": "Sums to 20",
    "1": "Subtraction 0-9",
    "2": "Fact Families: Add/Subtract 0-9",
    "3": "Fact Families: Addition/Subtraction 0-20",
    "4": "Addition 3-Digit Numbers with & without Regrouping",
    "5": "Subtraction 3-Digit Number with & without Regrouping",
    "6": "Add/Subtract 3-Digit Numbers with & without Regrouping",
    "7": "Multiplication 0-9",
    "8": "Multiplication 5-9",
    "9": "Division 0-9",
    "10": "Fact Families: Multiplication/Division 0-9",
    "11": "Multiplication 0-12",
    "12": "Division 0-12",
    "13": "Fact Families: Multiplication/Division 0-12",
    "14": "Multiply 1-Digit by 2-3-Digit without Regrouping",
    "15": "Multiply 1-Digit by 2-3-Digit with Regrouping",
    "16": "Divide 1-Digit into 2-3-Digit without Remainders",
    "17": "Place Fractions on Number Line (denominators: 2,4,8)"
  },
  "04": {
    "0": "Sums to 20",
    "1": "Subtraction 0-9",
    "2": "Addition 3-Digit Numbers with & without Regrouping",
    "3": "Subtraction 3-Digit Number with & without Regrouping",
    "4": "Multiplication 0-12",
    "5": "Division 0-12",
    "6": "Fact Families: Multiplication/Division 0-12",
    "7": "1-Digit Multiply by 2-3 Digit with & without regrouping",
    "8": "2-Digit Multiply by 2-Digit without Regrouping",
    "9": "2-Digit Multiply by 2-Digit with Regrouping",
    "10": "Divide 1-Digit into 2-3-Digit without Remainders",
    "11": "Divide 1-Digit into 1-2-Digit with Remainders",
    "12": "Quantity Comparison of Decimals to Hundredths",
    "13": "Place Fractions on Number Line (denominators: 1,2,3,4,5,6,8,10)",
    "14": "Quantity Compare for Fractions w/Unlike Denominators",
    "15": "Add/Subtract Mixed Numbers with Like Denominators and Regrouping",
    "16": "Create Equivalent Multiplication Problems by Factoring",
    "17": "Convert Fractions to Decimals and Decimals to Fractions",
    "18": "Add and Subtract Decimals to the Hundredths"
  },
  "05": {
    "0": "Sums to 20",
    "1": "Subtraction 0-9",
    "2": "Addition 3-Digit Numbers with & without Regrouping",
    "3": "Subtraction 3-Digit Number with & without Regrouping",
    "4": "Add and Subtract Decimals to the Hundredths",
    "5": "Multiplication 0-12",
    "6": "Division 0-12",
    "7": "Fact Families: Multiplication/Division 0-12",
    "8": "2-Digit Multiplied by 2-Digit with and without Regrouping",
    "9": "Divide 1-Digit into 1-2-Digit with Remainders",
    "10": "Find the Least Common Denominator",
    "11": "Simplify Fractions: A",
    "12": "Simplify Fractions: B",
    "13": "Simplify Fractions: C",
    "14": "Add/Subtract Fractions with Unlike Denominators",
    "15": "Convert Mixed Number to Improper Fraction",
    "16": "Convert Improper Fractions to Mixed Numbers",
    "17": "Add/Subtract Mixed Numbers with Like Denominators and Regrouping",
    "18": "Divide 2-Digit into 3-4 Digit with Remainders",
    "19": "Multiply/Divide Proper and Improper Fractions",
    "20": "Multiply/Divide Decimals"
  },
  "06": {
    "0": "2 Digit Addition With and Without Regrouping",
    "1": "2 Digit Subtraction With and Without Regrouping",
    "2": "Multi-Digit Multiplication With and Without Regrouping",
    "3": "Multi-Digit Division With & Without Remainders",
    "4": "Order of Operations",
    "5": "Find the Least Common Denominator",
    "6": "Simplify Fractions: A",
    "7": "Simplify Fractions: B",
    "8": "Simplify Fractions: C",
    "9": "Add/Subtract Fractions with Unlike Denominators",
    "10": "Add/Subtract Mixed Numbers with Like Denominators and Regrouping",
    "11": "Convert Improper Fractions to Mixed Numbers",
    "12": "Multiply/Divide Proper and Improper Fractions",
    "13": "Convert Mixed Number to Improper Fraction",
    "14": "Multiply & Divide Mixed Numbers",
    "15": "Mixed Fraction Operations",
    "16": "Distributive Property of Expression",
    "17": "Collect Like Terms",
    "18": "Substitute Whole Numbers to Solve Equations",
    "19": "Find Percent of a Whole Number",
    "20": "Add and Subtract Decimals to the Hundredths",
    "21": "Multiply/Divide Decimals",
    "22": "Multiply 2-Digit by 2-Digit with Decimals",
    "23": "Quantity Comparison with Integers",
    "24": "Graph in a Coordinate Plane"
  },
  "07": {
    "0": "2 Digit Addition With and Without Regrouping",
    "1": "2 Digit Subtraction With and Without Regrouping",
    "2": "Multi-Digit Multiplication With and Without Regrouping",
    "3": "Multi-Digit Division With & Without Remainders",
    "4": "Find the Least Common Denominator",
    "5": "Simplify Fractions: A",
    "6": "Simplify Fractions: B",
    "7": "Simplify Fractions: C",
    "8": "Add/Subtract Fractions with Unlike Denominators",
    "9": "Mixed Fraction Operations",
    "10": "Algebraic Proportions",
    "11": "Quantity Comparison with Integers",
    "12": "Add and Subtract with Integers",
    "13": "Add, Subtract, Multiply, and Divide Integers of Varied Sign",
    "14": "Find Percent of a Whole Number",
    "15": "Solve Equations with Percentages",
    "16": "Calculate Missing Value in Percentage Problem",
    "17": "Order of Operations",
    "18": "Inverse Operations - Add and Subtract",
    "19": "Inverse Operations - Multiply and Divide",
    "20": "Complex Fractions",
    "21": "Convert Decimals to Fractions",
    "22": "Convert Fractions to Decimals",
    "23": "Solve Two-Step Equations",
    "24": "Translate Verbal Expressions into Math Equations",
    "25": "Solve Two-Step Equations with Fractions"
  },
  "08": {
    "0": "2 Digit Addition With and Without Regrouping",
    "1": "2 Digit Subtraction With and Without Regrouping",
    "2": "Multi-Digit Multiplication With and Without Regrouping",
    "3": "Multi-Digit Division With & Without Remainders",
    "4": "Find the Least Common Denominator",
    "5": "Simplify Fractions: A",
    "6": "Simplify Fractions: B",
    "7": "Simplify Fractions: C",
    "8": "Add/Subtract Fractions with Unlike Denominators",
    "9": "Mixed Fraction Operations",
    "10": "Add, Subtract, Multiply, and Divide Integers of Varied Sign",
    "11": "Collect like terms to simplify expressions",
    "12": "Distributive property to simplify expressions",
    "13": "Add or Subtract with Exponents",
    "14": "Multiply with Exponents",
    "15": "Division With Exponents",
    "16": "Order of Operations II",
    "17": "Simplify Expressions",
    "18": "Solve One-Step Equations with Rational Numbers",
    "19": "Algebraic Proportions",
    "20": "Calculate Missing Value in Percentage Problem",
    "21": "Solve Two-Step Equations",
    "22": "Solve for Slope and Intercept Using Linear Function y=mx+b",
    "23": "Point on a Line",
    "24": "Solve Two-Step Equations with Fractions",
    "25": "Linear Combinations to Solve Equations",
    "26": "Substitute Equation to Solve Linear Equation",
    "27": "Use Comparison Method to Solve Systems of Linear Equations"
  },
  HS: {
    "0": "Mixed Operations",
    "1": "Find the Least Common Denominator",
    "2": "Simplify Fractions",
    "3": "Add/Subtract Fractions with Unlike Denominators",
    "4": "Multiply/Divide Proper and Improper Fractions",
    "5": "Convert Fractions to Decimals and Decimals to Fractions",
    "6": "Add and Subtract Decimals to the Hundredths",
    "7": "Multiply/Divide Decimals",
    "8": "Multiply 2-Digit by 2-Digit with Decimals",
    "9": "Order of Operations",
    "10": "Find Percent of a Whole Number",
    "11": "Algebraic Proportions",
    "12": "Complex Fractions",
    "13": "Add and Subtract with Integers",
    "14": "Add, Subtract, Multiply, and Divide Integers of Varied Sign",
    "15": "Solve Equations with Percentages",
    "16": "Calculate Missing Value in Percentage Problem",
    "17": "Translate Verbal Expressions into Math Equations",
    "18": "Mixed Inverse Operations - Add, Subtract, Multiply, Divide",
    "19": "Collect like terms to simplify expressions",
    "20": "Distributive property to simplify expressions",
    "21": "Add or Subtract with Exponents",
    "22": "Multiply with Exponents",
    "23": "Division With Exponents",
    "24": "Order of Operations II",
    "25": "Simplify Expressions",
    "26": "Solve Two-Step Equations",
    "27": "Solve Two-Step Equations with Fractions",
    "28": "Solve for Slope and Intercept Using Linear Function y=mx+b",
    "29": "Point on a Line",
    "30": "Linear Combinations to Solve Equations",
    "31": "Substitute Equation to Solve Linear Equation",
    "32": "Use Comparison Method to Solve Systems of Linear Equations"
  },
  K: {
    "0": "Count Objects 1-10, Circle Answer",
    "1": "Identify Number – Draw Circles 1-10",
    "2": "More/Less Quantity Discrimination w/Dots 1-10",
    "3": "Missing Number 0-11",
    "4": "Count Objects 1-20, Circle Answer",
    "5": "Identify Number - Draw Circles 1-20",
    "6": "More/Less Quantity Discrimination with Dots 1-20",
    "7": "Missing Number 0-20",
    "8": "Count Objects 1-10, Write Answer",
    "9": "Count Objects 1-20, Write Answer",
    "10": "Change Quantity of Dots to 10",
    "11": "Sums to 5 for Kindergarten",
    "12": "Subtraction 0-5 for Kindergarten",
    "13": "Sums to 12"
  }
};
