export default function replaceExternalWindow(targetUrl) {
  cy.get("@windowOpenStub").then(
    stub => {
      const firstStubCall = stub.getCall(0);
      if (!firstStubCall) {
        cy.log("Calls", firstStubCall, stub.getCall(1), stub.getCall(2));
      }
      const actualUrl = firstStubCall?.args[0] || "";
      const targetUrlWithoutDomain = targetUrl.replace("http://localhost:3000", "");
      expect(actualUrl).to.equal(targetUrlWithoutDomain);
    },
    { timeout: 30000 }
  );
  cy.window({ timeout: 30000 })
    .its("print", { timeout: 30000 }) // Ensure window has time to call window.print
    .should("be.called");
}
