import Datastore from "nedb-promises";

import fakeCollection from "/tests/helpers/mongoFaker";

class Collection {
  /* eslint-disable class-methods-use-this */
  constructor(collectionName) {
    // eslint-disable-next-line no-underscore-dangle
    this._name = collectionName;

    this.datastore = fakeCollection(Datastore.create());
  }

  allow() {}

  deny() {}

  createIndex() {}

  find(query, opts) {
    return this.datastore.find(query, opts);
  }

  findOne(query, opts) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.findOne(newQuery, opts);
  }

  async findOneAsync(query, opts) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.findOneAsync(newQuery, opts);
  }

  insert(document) {
    return this.datastore.insert(document);
  }

  async insertAsync(document) {
    return this.datastore.insertAsync(document);
  }

  update(query, set, options = {}) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.update(newQuery, set, options);
  }

  async updateAsync(query, set, options = {}) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.updateAsync(newQuery, set, options);
  }

  upsert(query, set) {
    return this.datastore.upsert(query, set);
  }

  remove(query, opts = { multi: true }) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.remove(newQuery, opts);
  }

  async removeAsync(query, opts = { multi: true }) {
    const newQuery = typeof query === "string" ? { _id: query } : query;
    return this.datastore.removeAsync(newQuery, opts);
  }

  rawCollection() {
    return {
      distinct: async (field, query) => {
        return [...new Set((await this.datastore.find(query).fetchAsync()).map(data => data[field]))];
      }
    };
  }
}

const Mongo = {
  Collection
};

export { Mongo };
