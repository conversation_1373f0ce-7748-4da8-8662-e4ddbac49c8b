import { Mongo } from "./mongo";

class MeteorError extends Error {
  constructor(error, reason, details) {
    super(reason);
    this.error = error;
    this.reason = reason;
    this.details = details;
    this.errorType = "MeteorError";
    this.name = "MeteorError";
  }
}

const Meteor = {
  methods() {},
  users: new Mongo.Collection("Users"),
  call() {},
  callAsync() {},
  user() {},
  userId() {},
  isServer: true,
  isClient: true,
  startup() {},
  subscribe() {
    return { ready: () => false };
  },
  publish() {},
  settings: {
    public: {}
  },
  wrapAsync() {
    return () => {};
  },
  Error: MeteorError
};

const Assets = {
  getTextAsync() {
    return Promise.resolve("");
  }
};

global.Assets = Assets;

export { Meteor, MeteorError, Assets };
