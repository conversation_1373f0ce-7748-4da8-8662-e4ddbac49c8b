import { Users } from "/imports/api/users/users";

const Accounts = {
  config() {},
  emailTemplates: {
    enrollAccount: {},
    resetPassword: {}
  },
  registerLoginHandler() {},
  createUser(userData) {
    const user = {
      ...userData,
      emails: [{ address: userData.email }]
    };
    delete user.email;

    return Users.insert(user);
  },
  async createUserAsync(userData) {
    const user = {
      ...userData,
      emails: [{ address: userData.email }]
    };
    delete user.email;

    return Users.insertAsync(user);
  },
  sendEnrollmentEmail() {}
};

export { Accounts };
