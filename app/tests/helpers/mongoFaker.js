import { groupBy, get, set, has, isArray, isObject } from "lodash";

async function findOne(query, collection) {
  return collection.findOne(query);
}

async function update(matchingQuery, setQuery, collection, options) {
  await collection.update(matchingQuery, setQuery, options);
}

async function insert(query, collection) {
  const data = await collection.insert(query);
  if (Array.isArray(data)) {
    return data.map(datum => datum._id);
  }
  return data._id;
}

async function remove(query, options, collection) {
  return collection.remove(query, options);
}

export default function fakeCollection(collection) {
  return {
    async findOne(query) {
      return findOne(query, collection);
    },

    async findOneAsync(query) {
      return collection.findOne(query);
    },

    async update(matchingQuery, setQuery, options) {
      const updatedSetQuery = processUpdateQuery(setQuery);
      await update(matchingQuery, updatedSetQuery, collection, options);
    },

    async updateAsync(matchingQuery, setQuery, options) {
      const updatedSetQuery = processUpdateQuery(setQuery);
      return collection.update(matchingQuery, updatedSetQuery, options);
    },

    async upsert(matchingQuery, setQuery) {
      await update(matchingQuery, setQuery, collection, { upsert: true });
    },

    find(query, options = {}) {
      const nedbOptions = processMongoDBOptions(options);

      const findAsync = async () => {
        const cursor = createCursorWithOptions(collection, query, nedbOptions);
        let result = await cursor;

        // Apply MongoDB-style projection manually if needed
        if (nedbOptions.mongoProjection) {
          result = result.map(doc => applyMongoProjection(doc, nedbOptions.mongoProjection));
        }

        return applySortingIfNeeded(result, nedbOptions.sort);
      };

      const handler = {
        get(target, name) {
          if (name in target) {
            return target[name];
          }
          // For methods not in target, try to handle them gracefully
          if (name === "fetch") {
            return target.fetch;
          }
          if (name === "count") {
            return target.count;
          }
          // For other methods, return a function that throws a helpful error
          return function() {
            throw new Error(
              `Method ${name} is not implemented in mongoFaker. Available methods: fetchAsync, fetch, countAsync, count, forEachAsync, mapAsync`
            );
          };
        }
      };

      return new Proxy(
        {
          fetchAsync: async () => findAsync(),
          fetch: () => {
            // Synchronous fetch for compatibility with existing code
            // This is a hack for testing - in real Meteor, fetch() is synchronous
            const cursor = createCursorWithOptions(collection, query, nedbOptions);
            let result = cursor;
            result = Array.isArray(result) ? result : [];

            // Apply MongoDB-style projection manually if needed
            if (nedbOptions.mongoProjection) {
              result = result.map(doc => applyMongoProjection(doc, nedbOptions.mongoProjection));
            }

            return applySortingIfNeeded(result, nedbOptions.sort);
          },
          countAsync: async () => (await findAsync()).length,
          count: () => {
            // Synchronous count for compatibility
            const cursor = createCursorWithOptions(collection, query, nedbOptions);
            const result = cursor;
            return Array.isArray(result) ? result.length : 0;
          },
          forEachAsync: async iterator => {
            const data = await findAsync();
            // eslint-disable-next-line no-restricted-syntax
            for (const item of data) {
              // eslint-disable-next-line no-await-in-loop
              await iterator(item);
            }
          },
          mapAsync: async iterator => {
            const data = await findAsync();
            return Promise.all(data.map(datum => iterator(datum)));
          }
        },
        handler
      );
    },

    async insert(query) {
      return insert(query, collection);
    },

    async insertAsync(document) {
      const insertedDocuments = await collection.insert(document);
      if (Array.isArray(insertedDocuments)) {
        return insertedDocuments.map(({ _id }) => _id);
      }
      return insertedDocuments._id;
    },

    async remove(query, options = {}) {
      return remove(query, options, collection);
    },

    async removeAsync(query, options = {}) {
      return collection.remove(query, options);
    }
  };
}

// Extract common MongoDB options processing
function processMongoDBOptions(options) {
  const { fields, sort, limit, skip, ...otherOptions } = options;

  // Convert MongoDB options to NeDB-compatible options
  const nedbOptions = {};

  // Handle projection: MongoDB uses 'fields', NeDB expects 'projection'
  // For NeDB compatibility, we need to handle nested field projections differently
  if (fields) {
    // Check if this is a MongoDB-style nested field projection
    const hasNestedFields = Object.keys(fields).some(key => key.includes("."));

    if (hasNestedFields) {
      // For nested field projections, we'll apply them manually after the query
      // Set projection to empty object to get all fields, then filter manually
      nedbOptions.projection = {};
      nedbOptions.mongoProjection = fields;
    } else {
      nedbOptions.projection = fields;
    }
  }

  // Handle other query options separately
  if (sort) nedbOptions.sort = sort;
  if (limit) nedbOptions.limit = limit;
  if (skip) nedbOptions.skip = skip;

  // Add any other options that are not projection-related
  Object.keys(otherOptions).forEach(key => {
    if (key !== "projection") {
      nedbOptions[key] = otherOptions[key];
    }
  });

  return nedbOptions;
}

// Extract common cursor creation and option application
function createCursorWithOptions(collection, query, nedbOptions) {
  // NeDB expects projection as second parameter, other options are applied via cursor methods
  const cursor = collection.find(query, nedbOptions.projection || {});

  // Apply other options via cursor methods
  if (nedbOptions.sort) cursor.sort(nedbOptions.sort);
  if (nedbOptions.limit) cursor.limit(nedbOptions.limit);
  if (nedbOptions.skip) cursor.skip(nedbOptions.skip);

  return cursor;
}

// Extract common update query processing
function processUpdateQuery(setQuery) {
  const updatedSetQuery = { ...setQuery };
  if (updatedSetQuery.$push) {
    removeSort(updatedSetQuery);
  }
  return updatedSetQuery;
}

// Helper function to apply sorting to results if NeDB doesn't handle nested fields properly
function applySortingIfNeeded(result, sortOptions) {
  if (!sortOptions || !isArray(result) || result.length === 0) {
    return result;
  }

  return result.sort((a, b) => {
    for (const [field, direction] of Object.entries(sortOptions)) {
      const aValue = get(a, field);
      const bValue = get(b, field);

      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      else if (aValue > bValue) comparison = 1;

      if (comparison !== 0) {
        return direction === 1 ? comparison : -comparison;
      }
    }
    return 0;
  });
}

// Apply MongoDB-style field projection manually
function applyMongoProjection(document, projection) {
  const result = {};

  // Always include _id unless explicitly excluded
  if (projection._id !== 0) {
    result._id = document._id;
  }

  // Group projection fields by their root field using lodash groupBy
  const projectionFields = Object.keys(projection).filter(fieldPath => projection[fieldPath] === 1);
  const fieldGroups = groupBy(projectionFields, fieldPath => fieldPath.split(".")[0]);

  // Process each root field
  Object.entries(fieldGroups).forEach(([rootField, fieldPaths]) => {
    const originalValue = get(document, rootField);

    if (isArray(originalValue)) {
      // For arrays, preserve the array structure and apply projections to each element
      result[rootField] = originalValue.map(item => projectArrayItem(item, fieldPaths, rootField));
    } else {
      // For non-arrays, project each field directly
      fieldPaths.forEach(fieldPath => {
        const value = get(document, fieldPath);
        if (value !== undefined) {
          set(result, fieldPath, value);
        }
      });
    }
  });

  return result;
}

// Helper function to project fields from an array item
function projectArrayItem(item, fieldPaths, rootField) {
  const projectedItem = {};

  fieldPaths.forEach(fieldPath => {
    if (fieldPath === rootField) {
      // If projecting the entire root field, include the whole item
      Object.assign(projectedItem, item);
    } else {
      // Extract the nested path relative to the root field
      const nestedPath = fieldPath.substring(rootField.length + 1);
      const value = get(item, nestedPath);

      // Special handling for nested arrays - preserve the entire array
      if (nestedPath.includes(".")) {
        const firstNestedField = nestedPath.split(".")[0];
        const firstNestedValue = get(item, firstNestedField);

        if (isArray(firstNestedValue) && !has(projectedItem, firstNestedField)) {
          set(projectedItem, firstNestedField, firstNestedValue);
        }
      }

      if (value !== undefined) {
        set(projectedItem, nestedPath, value);
      }
    }
  });

  return projectedItem;
}

// Removes sort modifier from push update that's unsupported in NEDB
function removeSort(obj) {
  Object.keys(obj).forEach(property => {
    if (property === "$sort") {
      delete obj[property];
    } else if (isObject(obj[property])) {
      removeSort(obj[property]);
    }
  });
}
