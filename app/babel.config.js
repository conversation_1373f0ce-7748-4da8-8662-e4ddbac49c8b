module.exports = {
  plugins: ["@babel/plugin-proposal-class-properties"],
  env: {
    testing: {
      presets: [
        [
          "@babel/preset-env",
          {
            useBuiltIns: "usage",
            corejs: 2
          }
        ],
        [
          "@babel/preset-react",
          {
            runtime: "automatic",
            development: process.env.NODE_ENV === "development",
            importSource: "@welldone-software/why-did-you-render"
          }
        ]
      ],
      plugins: [
        "add-module-exports",
        "module:babel-root-slash-import",
        "@babel/plugin-transform-flow-strip-types",
        "@babel/plugin-proposal-class-properties"
      ]
    },
    unittesting: {
      presets: [
        [
          "@babel/preset-env",
          {
            useBuiltIns: "usage",
            corejs: 2
          }
        ],
        "@babel/preset-react"
      ],
      plugins: [
        "@babel/plugin-proposal-object-rest-spread",
        "add-module-exports",
        "@babel/plugin-transform-flow-strip-types",
        "@babel/plugin-proposal-class-properties",
        "module:babel-root-slash-import",
        [
          "module-resolver",
          {
            alias: {
              "meteor/aldeed:simple-schema": "./tests/stubs/aldeedSimpleSchema",
              meteor: "./tests/stubs",
              "highcharts/highstock": "./tests/stubs/highcharts"
            }
          }
        ]
      ]
    }
  }
};
