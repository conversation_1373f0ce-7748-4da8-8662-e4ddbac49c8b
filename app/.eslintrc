# Any updates to this file needs to be replicated in all of our javascript projects
ecmaFeatures:
  modules: true
  jsx: true

# enable ES6
parserOptions:
  ecmaVersion: 6
  sourceType: "module"
  ecmaFeatures:
    jsx: true # enable React's JSX

parser: "babel-eslint"

# register plugins
plugins:
- meteor
- react
- flowtype
- prettier
- import

# use the rules of eslint-config-airbnb
# and the recommended rules of eslint-plugin-meteor
extends:
- airbnb/base
- plugin:meteor/recommended
- plugin:react/recommended
- plugin:import/errors
- plugin:import/warnings
- prettier

# registerenvironments
env:
  amd: true
  browser: true
  es6: true
  meteor: true
  mocha: true
  node: true

rules:
  # overwrite some rules (avoid semicolons)
  class-methods-use-this: [2, { "exceptMethods": ["componentWillUnmount", "componentDidMount"] }]
  flowtype/define-flow-type: 1
  flowtype/use-flow-type: 1
  func-names: 0
  global-require: 1
  import/no-extraneous-dependencies: 0
  import/no-cycle: 1
  import/no-unresolved: 0
  import/no-absolute-path: 0
  import/prefer-default-export: 0
  max-depth: 0
  max-nested-callbacks: 0
  max-params: 0
  max-statements: [1, 60]
  meteor/no-session: 1
  new-cap: [1, { "capIsNewExceptions": ["Maybe", "Optional", "OneOf", "When", "Then", "And", "Given", "ObjectIncluding"] }]
  no-bitwise: 0
  no-param-reassign: [2, { "props": false }]
  no-restricted-syntax: off
  no-plusplus: 0
  no-prototype-builtins: 2
  no-throw-literal: 0
  no-underscore-dangle: [2, { "allow": ["_id", "_routes"] }]
  no-use-before-define: [2, { "functions": false }]
  object-shorthand: [2, "always", { "avoidQuotes": false }]
  prefer-arrow-callback: 1
  radix: [2, "as-needed"]
  react/prop-types: 2
  prettier/prettier: 2
  import/extensions: [0, "ignorePackages", { js: "never", jsx: "never", }]

# This is done by project if you change this it only needs to be changed here.
# Globals

globals:
  # tests
  browser: false
  expect: false
  server: false
  TestUtils: false
  unitTesting: true
  WdioElement: false
  When: false
  WioBrowser: false
  jest: false
  beforeAll: false
  afterAll: false
  cy: false
