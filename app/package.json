{"name": "SpringMath", "version": "0.0.1", "description": "SpringMath is a Response to Intervention (RTI) Math assessment tool", "main": "client/main.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix .", "start": "TOOL_NODE_FLAGS='--max-old-space-size=2048' meteor --inspect --settings settings.json", "start:docker:local": "docker run -it --rm -p 4000:3000 --env-file ../.docker-local.env -e MONGO_URL=mongodb://host.docker.internal:3001/meteor?directConnection=true -e PORT=3000 -e ROOT_URL=http://localhost:4000 -e MONGO_OPLOG_URL=mongodb://host.docker.internal:3001/local?directConnection=true -e TOOL_NODE_FLAGS=--max-old-space-size=2048 springmath:1.1", "start:prod": "TOOL_NODE_FLAGS='--max-old-space-size=2048' meteor --inspect --settings settings.json", "start:external": "MONGO_URL=mongodb://localhost:3001/meteor meteor --inspect --settings settings.json", "start:mongo": "mkdir -p ./local-db && mongod --port 3001 --dbpath ./local-db --fork --logpath ./local-db/mongod.log", "stop:mongo": "./stop-dev.sh", "start4000": "MONGO_URL=mongodb://localhost:3001/meteor meteor --settings settings.json -p 4000", "start:bootstrap": "BOOTSTRAP_DB_MIN=true meteor --settings settings.json", "start:local:e2e": "./.scripts/start-local-e2e.sh", "tests:unit": "./.scripts/unit-test.sh", "tests:e2e": "./.scripts/e2e-test.sh", "cypress:file": "node ./.scripts/cypress-run-file.js", "unit": "BABEL_ENV=unittesting jest --config=package.json --maxWorkers=50%", "test:mocha": "./.scripts/test.sh", "test:watch": "BABEL_ENV=unittesting jest --watch", "start:docker": "./.scripts/start-local-docker.sh", "start:docker-arm": "./.scripts/start-local-docker.sh arm", "start:local-with-docker": "./.scripts/start-meteor-and-connect-to-docker.sh", "cypress": "cypress run", "cypress:open": "cypress open --e2e --browser electron", "eslint": "eslint", "prepare": "cd .. && husky install app/.husky"}, "repository": {"type": "git", "url": "git+https://github.com/edSpring/math-ninja.git"}, "keywords": [], "author": "TIES - edSpring", "license": "private", "bugs": {"url": "https://github.com/edSpring/math-ninja/issues"}, "moduleRoots": ["./"], "homepage": "https://github.com/edSpring/math-ninja#readme", "dependencies": {"@aws-sdk/client-s3": "^3.326.0", "@bytescale/sdk": "^3.53.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "axios": "^0.21.4", "axios-oauth-1.0a": "^0.3.6", "bcrypt": "^6.0.0", "bootstrap": "^5.1.3", "cron-job-manager": "^2.1.4", "crypto-js": "^3.3.0", "d3-3": "0.0.0", "diff": "^5.0.0", "faker": "^5.5.3", "file-saver": "^2.0.5", "filepicker-js": "^2.4.18", "font-awesome": "^4.7.0", "formik": "^2.2.9", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "html2pdf.js": "^0.10.1", "identity-obj-proxy": "^3.0.0", "jquery": "^3.7.1", "js-base64": "^2.6.4", "jszip": "^3.10.1", "jwt-simple": "^0.5.1", "lodash": "^4.17.21", "meteor-node-stubs": "^1.2.22", "mockdate": "^2.0.5", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "mongo-dot-notation": "^3.1.1", "mongodb": "^5.9.2", "papaparse": "^5.3.1", "posthog-js": "^1.260.2", "promise-retry": "^2.0.1", "prop-types": "^15.6.2", "query-string": "^6.14.1", "react": "^16.14.0", "react-bootstrap": "^2.5.0-beta.0", "react-confetti-explosion": "^3.0.0", "react-date-picker": "^8.4.0", "react-dom": "^16.14.0", "react-dropzone": "^3.13.4", "react-dual-listbox": "^2.2.0", "react-ga4": "^2.1.0", "react-paginate": "^8.1.3", "react-router-bootstrap": "^0.25.0", "react-router-dom": "^5.3.0", "react-s-alert": "^1.3.1", "react-select": "^5.3.2", "react-youtube": "^7.13.1", "simpl-schema": "^1.12.0", "url": "^0.11.0", "uuid": "^3.4.0", "yup": "^1.1.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-transform-flow-strip-types": "^7.16.0", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/runtime": "^7.16.0", "@babel/runtime-corejs2": "^7.16.0", "@swc/core": "^1.12.1", "@swc/jest": "^0.2.38", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.2", "@welldone-software/why-did-you-render": "^7.0.1", "babel-eslint": "^10.1.0", "babel-jest": "^30.0.2", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-relative-import": "^1.0.3", "babel-preset-es2015-without-strict": "0.0.2", "babel-root-slash-import": "^1.1.0", "chai": "^3.5.0", "cheerio": "^1.0.0-rc.3", "cypress": "^14.5.4", "cypress-plugin-xhr-toggle": "^1.2.1", "cypress-terminal-report": "^7.2.1", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.6", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^6.15.0", "eslint-import-resolver-meteor": "^0.4.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-flowtype": "^3.13.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-meteor": "^7.3.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.26.1", "husky": "^7.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jest-junit": "^16.0.0", "jsdom": "^26.1.0", "lint-staged": "^10.5.4", "mocha": "^8.4.0", "mocha-junit-reporter": "^1.23.3", "mocha-multi-reporters": "^1.5.1", "mongodb-memory-server": "^8.16.1", "nedb-promises": "^6.2.1", "prettier": "^1.19.1", "react-addons-test-utils": "^16.0.0-alpha.3", "sinon": "^9.2.4", "testdouble": "^3.16.4", "testdouble-jest": "^2.0.0"}, "jest": {"rootDir": "./", "testMatch": ["**/__tests__/**/*.js?(x)", "**/?(*.)(tests).js?(x)"], "collectCoverageFrom": ["tests/stubs/*.js", "imports/**/*.js*"], "coverageDirectory": ".coverage/", "collectCoverage": false, "setupFiles": ["<rootDir>/tests/helpers/fixPolyfill.js", "<rootDir>/tests/helpers/setupTestDouble.js", "<rootDir>/tests/helpers/setupEnzyme.js", "<rootDir>/tests/helpers/simpleConsoleLog.js"], "moduleNameMapper": {"^/tests/(.*)$": "<rootDir>/tests/$1", "^/(imports/.*)$": "<rootDir>/$1", "^@/(.*)$": "<rootDir>/$1", "\\.(css|less|scss)$": "identity-obj-proxy"}, "reporters": ["default", "jest-junit"], "transformIgnorePatterns": ["/node_modules/(?!(cheerio|mongodb-memory-server|uuid|bson|mongodb))"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost/"}, "transform": {"^.+\\.jsx?$": "@swc/jest"}}, "lint-staged": {"**/*.js": ["npm run eslint -- --fix"], "**/*.jsx": ["npm run eslint -- --fix"], "*.js": "eslint --cache --fix"}, "resolutions": {"cheerio": "1.0.0-rc.3"}}