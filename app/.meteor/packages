# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

meteor-base@1.5.2           # Packages every Meteor app needs to have
mongo@2.1.4              # The database Meteor supports right now
tracker@1.3.4                 # Meteor's client-side reactive programming library

standard-minifier-css@1.9.3   # CSS minifier run for production mode
standard-minifier-js@3.1.1    # JS minifier run for production mode
es5-shim@4.8.1               # ECMAScript 5 compatibility for older browsers.
ecmascript@0.16.13            # Enable ECMAScript2015+ syntax in app code

react-meteor-data
accounts-password@3.2.1
aldeed:simple-schema
email@3.1.2
shell-server@0.6.2
dynamic-import@0.7.4
reywood:publish-composite
hot-module-replacement@0.5.4
accounts-oauth@1.4.6
oauth@3.0.2
oauth2@1.3.3
service-configuration@1.3.5
blaze-html-templates
accounts-2fa@3.0.1
leonardoventurini:scss
fetch@0.1.6
quave:aggregate
jquery
montiapm:agent
