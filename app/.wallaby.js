process.env.BABEL_ENV = "unittesting";

module.exports = function(wallaby) {
  return {
    files: [
      "tests/stubs/**/*.js",
      "imports/**/!(*.tests).js*",
      "tests/helpers/fixPolyfill.js",
      "tests/helpers/runInFiber.js",
      "tests/helpers/setupEnzyme.js"
    ],
    tests: ["imports/**/*.tests.js*"],
    compilers: {
      "**/*.js*": wallaby.compilers.babel()
    },
    env: {
      type: "node",
      runner: "node"
    },

    testFramework: "jest",

    setup: function() {
      require("./tests/stubs/index");
    }
  };
};
