#!/bin/bash
echo "#####################"
echo "# RUNNING E2E TESTS #"
echo "#####################"

## For some strange reason <PERSON><PERSON> is starting mongod service after we
## stopped it in machine:pre step. To ensure that port 3001 is not in use
## we stop mongod one more time. Hacky....

## Stop mongod if there is any and start dockerized mongod
while nc -z localhost 3001; do sudo /etc/init.d/mongod stop; sudo stop mongod; sleep 3; done
rm -rf .meteor/local/db/mongod.lock .meteor/local/db/journal/

./.scripts/start-meteor-with-testing-db.sh
SPEC_LIST=$1

./.scripts/cypress-tests.sh $SPEC_LIST

E2E_TEST_RESULT=$?

echo "######################################"
echo "# E2E_TEST_RESULT "$E2E_TEST_RESULT"#"
echo "######################################"

kill -9 `ps ax | grep node | grep meteor | grep -v atom | awk '{print $1}'`

echo "####################"
echo "# RETURNING RESULT #"
echo "####################"

exit $E2E_TEST_RESULT
