const orgid = "XCDDj5FeAYaZzacYq";
const schoolYear = 2024;
const groups = db.StudentGroups.aggregate([
  { $match: { schoolYear, orgid } },
  // { $project: { name: 1, sectionId: 1, isActive: 1 } },
  { $sort: { "created.on": -1 } },
  { $group: { _id: "$sectionId", studentGroupsWithSameName: { $push: "$$ROOT" } } },
  {
    $match: {
      $or: [{ "studentGroupsWithSameName.isActive": true }, { "studentGroupsWithSameName.1": { $exists: true } }]
    }
  },
  {
    $match: {
      $or: [
        { "studentGroupsWithSameName.history.0": { $exists: true } },
        { "studentGroupsWithSameName.currentClasswideSkill": { $exists: true } }
      ]
    }
  }
  // {
  //     $project: {
  //         "studentGroupsWithSameName.history.assessmentResultMeasures": 0,
  //         "studentGroupsWithSameName.history.enrolledStudentIds": 0,
  //         "studentGroupsWithSameName.history.message": 0
  //     }
  // }
]);

// print(groups)
const groupsToSwitchPlace = [];
const groupsWithConflicts = [];
const groupsThatNeedManualWork = [];
const skippedGroups = [];

groups.forEach((group, index) => {
  const sgs = group.studentGroupsWithSameName;
  if (sgs.length > 2) {
    groupsThatNeedManualWork.push(group);
  } else if (
    (sgs.find(s => s.isActive === false && s.history?.[0]) && sgs.find(s => s.isActive === true && !s.history?.[0])) ||
    (sgs.find(s => s.isActive === false && s.currentClasswideSkill) &&
      sgs.find(s => s.isActive === true && !s.currentClasswideSkill))
  ) {
    groupsToSwitchPlace.push(group);
  } else if (
    (sgs[0]?.history && sgs[1]?.history) ||
    (sgs.find(s => s.isActive === true && s.currentClasswideSkill) &&
      sgs.find(s => s.isActive === false && s.history?.[0]))
  ) {
    groupsWithConflicts.push(group);
  } else {
    skippedGroups.push(group);
  }
});
print("Total number of groups: ", groups.length);
print("GroupsToSwitchPlace: ", groupsToSwitchPlace.length);
// print("GroupsWithConflicts: ", groupsWithConflicts.length);
if (groupsThatNeedManualWork.length) {
  print("GroupsThatNeedManualWork: ", groupsThatNeedManualWork.length);
}
if (skippedGroups.length) {
  print("SkippedGroups: ", skippedGroups.length);
}
print(groupsToSwitchPlace);
// print(groupsWithConflicts);
