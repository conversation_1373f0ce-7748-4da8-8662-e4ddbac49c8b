function mergeSites(sourceId, targetId, _orgid, dryRun = false) {
  const sourceSiteToDelete = "" || sourceId; // TODO to be provided
  const destinationSiteToKeep = "" || targetId; // TODO to be provided
  const orgid = "" || _orgid; // TODO to be provided
  print("\n*******************************");
  if (dryRun) {
    print("***** DRY RUN MODE - NO CHANGES WILL BE MADE *****");
  }
  const org = db.Organizations.findOne({ _id: orgid }, { name: 1 });
  if (!org) {
    print(`ERROR: Organization with _id '${orgid}' not found!`);
    return;
  }
  print(`ORG: ${org.name} - ${orgid}`);

  // get source school student groups
  const studentGroupsToMove = db.StudentGroups.find(
    { orgid, siteId: sourceSiteToDelete },
    { _id: 1, name: 1, sectionId: 1, grade: 1, siteId: 1 }
  ).toArray();
  if (!studentGroupsToMove.length) {
    print("NO STUDENT GROUPS TO MOVE, ERROR!");
    return;
  }
  print("Student Groups that get moved:");
  printjson(studentGroupsToMove);
  const studentGroupIdsToMove = studentGroupsToMove.map(sg => sg._id);

  // verify source site
  const sourceSchool = db.Sites.findOne({ orgid, _id: sourceSiteToDelete }, { _id: 1, name: 1, stateInformation: 1 });
  if (!sourceSchool) {
    print("SOURCE SCHOOL NOT AVAILABLE OR NOT IN ORG , ERROR!");
    return;
  }
  print("Source school:");
  printjson(sourceSchool);

  // verify destination site
  const destSchool = db.Sites.findOne({ orgid, _id: destinationSiteToKeep }, { _id: 1, name: 1, stateInformation: 1 });
  if (!destSchool) {
    print("DESTINATION SCHOOL NOT AVAILABLE OR NOT IN ORG , ERROR!");
    return;
  }
  print("Destination school:");
  printjson(destSchool);

  // ENROLLMENTS
  const studentEnrollmentsAffected = db.StudentGroupEnrollments.find({
    studentGroupId: { $in: studentGroupIdsToMove }
  }).toArray();
  const studentsToMoveFromEnrollments = studentEnrollmentsAffected.map(se => se.studentId);
  let studentsToMove = new Set(studentsToMoveFromEnrollments);
  studentsToMove = [...studentsToMove];

  if (dryRun) {
    print("WOULD CHANGE: ", studentEnrollmentsAffected.length, "enrollments");
  } else {
    const studentEnrollmentsAffectedRes = db.StudentGroupEnrollments.updateMany(
      { studentGroupId: { $in: studentGroupIdsToMove } },
      { $set: { siteId: destinationSiteToKeep } }
    );
    print("Changed: ", studentEnrollmentsAffectedRes.modifiedCount, "enrollments");
  }

  if (dryRun) {
    print("WOULD CHANGE: ", studentGroupIdsToMove.length, "groups");
  } else {
    const sgsUpdateRes = db.StudentGroups.updateMany(
      { _id: { $in: studentGroupIdsToMove } },
      { $set: { siteId: destinationSiteToKeep } }
    );
    print("Changed: ", sgsUpdateRes.modifiedCount, "groups");
  }

  // USERS
  const usersAffected = db.users
    .find({ "profile.orgid": orgid, "profile.siteAccess.siteId": sourceSiteToDelete })
    .toArray();
  usersAffected.forEach(user => {
    let updatedSiteAccess = user.profile.siteAccess;
    updatedSiteAccess.forEach(sa => {
      if (sa.siteId === sourceSiteToDelete) {
        sa.siteId = destinationSiteToKeep;
      }
    });
    //remove any dups this may have created
    updatedSiteAccess = updatedSiteAccess.filter(
      (v, i, a) =>
        a.findIndex(
          t =>
            t.isActive === v.isActive &&
            t.isDefault === v.isDefault &&
            t.role === v.role &&
            t.schoolYear.toString() === v.schoolYear.toString() &&
            t.siteId === v.siteId
        ) === i
    );
    if (dryRun) {
      print("WOULD UPDATE teacher with _id:", user._id);
    } else {
      const updateOp = db.users.updateOne({ _id: user._id }, { $set: { "profile.siteAccess": updatedSiteAccess } });
      print("updating teacher with _id:", user._id, "success:", updateOp.modifiedCount ? "Yes!" : "No!");
    }
  });

  //BENCHMARK WINDOWS
  const benchmarkWindowsToMove = db.BenchmarkWindows.find(
    { orgid, siteId: sourceSiteToDelete },
    { _id: 1, schoolYear: 1, benchmarkPeriodId: 1, siteId: 1 }
  ).toArray();
  if (!benchmarkWindowsToMove.length) {
    print("Notice: No benchmark windows to move");
  } else {
    benchmarkWindowsToMove.forEach(bmw => {
      const existingDestinationBenchmarkWindow = db.BenchmarkWindows.findOne(
        {
          orgid,
          schoolYear: bmw.schoolYear,
          benchmarkPeriodId: bmw.benchmarkPeriodId,
          siteId: destinationSiteToKeep
        },
        { _id: 1, schoolYear: 1, benchmarkPeriodId: 1, siteId: 1 }
      );
      if (!existingDestinationBenchmarkWindow) {
        if (dryRun) {
          print("WOULD UPDATE BenchmarkWindow with _id:", bmw._id);
        } else {
          const updateOp = db.BenchmarkWindows.updateOne({ _id: bmw._id }, { $set: { siteId: destinationSiteToKeep } });
          print("updating BenchmarkWindow with _id:", bmw._id, "success:", updateOp.modifiedCount ? "Yes!" : "No!");
        }
      } else {
        print(
          `WARNING: Unable to move benchmark window ${bmw._id} due to conflict with ${existingDestinationBenchmarkWindow._id}`
        );
      }
    });
  }

  //ASSESSMENT RESULTS
  const assessmentResultsAffected = db.AssessmentResults.find({
    studentGroupId: { $in: studentGroupIdsToMove }
  }).toArray(); // need to change scores[].siteId
  assessmentResultsAffected.forEach((ar, index) => {
    print("Updating AssessmentResults:", index);
    ar.scores.forEach(score => {
      if (score.siteId === sourceSiteToDelete) {
        score.siteId = destinationSiteToKeep;
      }
    });
    if (dryRun) {
      print("WOULD UPDATE AssessmentResults with _id:", ar._id);
    } else {
      db.AssessmentResults.updateOne({ _id: ar._id }, { $set: { scores: ar.scores } });
    }
  });

  //STUDENTS
  if (dryRun) {
    print("WOULD CHANGE: ", studentsToMove.length, "students");
  } else {
    const stuUpdateRes = db.Students.updateMany(
      { _id: { $in: studentsToMove }, orgid, districtNumber: sourceSchool.stateInformation.districtNumber },
      { $set: { districtNumber: destSchool.stateInformation.districtNumber } }
    );
    print("Changed: ", stuUpdateRes.modifiedCount, "students");
  }

  //StudentsBySkill
  const existingStudentsBySkill = db.StudentsBySkill.find({
    siteId: sourceSiteToDelete
  }).toArray();

  existingStudentsBySkill.forEach(studentsBySkill => {
    if (dryRun) {
      print("WOULD UPDATE/UPSERT StudentsBySkill for assessmentGroupId:", studentsBySkill.assessmentGroupId);
    } else {
      db.StudentsBySkill.update(
        { siteId: destinationSiteToKeep, assessmentGroupId: studentsBySkill.assessmentGroupId },
        {
          $addToSet: {
            studentsBelowInstructionalTarget: {
              $each: studentsBySkill.studentsBelowInstructionalTarget
            },
            studentsBelowMasteryTarget: {
              $each: studentsBySkill.studentsBelowMasteryTarget
            },
            studentsWithoutSkillHistory: {
              $each: studentsBySkill.studentsWithoutSkillHistory
            }
          }
        },
        { upsert: true }
      );
    }
  });

  const studentsBySkillIdsToRemove = existingStudentsBySkill.map(sbs => sbs._id);
  // get rid of unused StudentsBySkill
  if (dryRun) {
    print("WOULD DELETE ", studentsBySkillIdsToRemove.length, "StudentsBySkill records");
  } else {
    db.StudentsBySkill.deleteMany({ _id: { $in: studentsBySkillIdsToRemove } });
  }

  //SITE
  const sourceSchoolDeleted = db.Sites.findOne({ orgid, _id: sourceSiteToDelete });
  print("Source school deleted:");
  printjson(sourceSchoolDeleted);
  if (dryRun) {
    print("WOULD DELETE site with _id:", sourceSiteToDelete);
  } else {
    const deleteOp = db.Sites.deleteOne({ orgid, _id: sourceSiteToDelete });
    print("Deleting site with _id:", sourceSiteToDelete, "success:", deleteOp.deletedCount ? "Yes!" : "No!");
  }
}

function stripLeadingZeros(id) {
  let normalizedId = id;
  while (normalizedId.charAt(0) === "0" && normalizedId.length > 1) {
    normalizedId = normalizedId.substr(1);
  }
  return normalizedId;
}

function getNormalizedId(id) {
  let normalizedId = id;
  // eslint-disable-next-line no-restricted-globals
  while (isNaN(normalizedId.charAt(normalizedId.length - 1)) && normalizedId.length > 1) {
    normalizedId = normalizedId.slice(0, -1);
  }
  normalizedId = stripLeadingZeros(normalizedId);

  return normalizedId;
}

// source: target
const targetIdBySourceId = {
  NABioLbAHTzTpCrFr: "fczMfNXyPKaSyLnhK"
};

// Auto-execution when script is loaded directly (uncomment to run automatically)
/*
Object.entries(targetIdBySourceId).forEach(([sourceId, targetId]) => {
  // TODO(fmazur) - Use proper orgid
  const orgid = "Fu7iRAzeAjGwouJ4P";
  const dryRun = true; // Set to false to actually run the operations
  
  if (!orgid) {
    print("No orgid specified. Exiting...");
    return;
  }
  
  if (dryRun) {
    print("\n***** DRY RUN MODE - Sites normalization *****");
  }
  
  // db.Sites.updateMany({ orgid, schoolYear: 2023 }, { $set: { "stateInformation.districtNumber": "Q7" } });
  db.Sites.find({ orgid })
    .toArray()
    .forEach(site => {
      // NOTE(fmazur) - remove _old_date suffix when merging autorostering schools
      // const schoolNumber = site.stateInformation.schoolNumber.replace(/_old_\d{4}-\d{2}-\d{2}$/, "");
      const schoolNumber = site.stateInformation.schoolNumber;
      if (dryRun) {
        print("WOULD UPDATE Site _id:", site._id, "with normalized district/school numbers");
      } else {
        db.Sites.updateOne(
          { _id: site._id },
          {
            $set: {
              "stateInformation.districtNumber": getNormalizedId(orgid),
              "stateInformation.schoolNumber": schoolNumber,
              "stateInformation.localSchoolNumber": schoolNumber
            }
          }
        );
      }
    });
  mergeSites(sourceId, targetId, orgid, dryRun);
});
*/

// Interactive helper function
function runMergeSitesForOrg(orgid, dryRun = true) {
  if (!orgid) {
    print("No orgid specified. Please provide an orgid.");
    return;
  }

  if (dryRun) {
    print("\n***** DRY RUN MODE - Sites normalization *****");
  }

  // Normalize site information
  db.Sites.find({ orgid })
    .toArray()
    .forEach(site => {
      const schoolNumber = site.stateInformation.schoolNumber;
      if (dryRun) {
        print("WOULD UPDATE Site _id:", site._id, "with normalized district/school numbers");
      } else {
        db.Sites.updateOne(
          { _id: site._id },
          {
            $set: {
              "stateInformation.districtNumber": getNormalizedId(orgid),
              "stateInformation.schoolNumber": schoolNumber,
              "stateInformation.localSchoolNumber": schoolNumber
            }
          }
        );
      }
    });

  // Run merges from the mapping
  Object.entries(targetIdBySourceId).forEach(([sourceId, targetId]) => {
    mergeSites(sourceId, targetId, orgid, dryRun);
  });
}

// Helper function to list available organizations
function listOrganizations() {
  print("Available Organizations:");
  print("======================");
  const orgs = db.Organizations.find({}, { name: 1 }).toArray();
  if (orgs.length === 0) {
    print("No organizations found in database");
    return;
  }
  orgs.forEach(org => {
    print(`- ${org.name} (ID: ${org._id})`);
  });
  print(`\nTotal: ${orgs.length} organizations`);
}

// Helper function to list sites for an organization
function listSitesForOrg(orgid) {
  if (!orgid) {
    print("Please provide an orgid. Use listOrganizations() to see available orgs.");
    return;
  }

  const org = db.Organizations.findOne({ _id: orgid }, { name: 1 });
  if (!org) {
    print(`ERROR: Organization with _id '${orgid}' not found!`);
    return;
  }

  print(`Sites for ${org.name} (${orgid}):`);
  print("=====================================");
  const sites = db.Sites.find({ orgid }, { name: 1, "stateInformation.schoolNumber": 1 }).toArray();
  if (sites.length === 0) {
    print("No sites found for this organization");
    return;
  }
  sites.forEach(site => {
    const schoolNum = site.stateInformation ? site.stateInformation.schoolNumber : "N/A";
    print(`- ${site.name} (ID: ${site._id}, School#: ${schoolNum})`);
  });
  print(`\nTotal: ${sites.length} sites`);
}

print("Functions loaded! Available commands:");
print("- listOrganizations() - Show all organizations");
print("- listSitesForOrg(orgid) - Show sites for an organization");
print("- mergeSites(sourceId, targetId, orgid, dryRun=false)");
print("- runMergeSitesForOrg(orgid, dryRun=true)");
print("- stripLeadingZeros(id)");
print("- getNormalizedId(id)");
