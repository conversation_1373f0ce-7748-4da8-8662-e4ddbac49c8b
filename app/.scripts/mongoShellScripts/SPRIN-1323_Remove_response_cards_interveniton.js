(function() {
  var benchmarkAssessmentId = "cAphxERJMPfAfjuxH";
  var interventionAssessmentId = "bYyN4heB3kaPjvCnn";
  var schoolYear = 2019;
  var query = {
    grade: "01",
    "currentSkill.benchmarkAssessmentId": benchmarkAssessmentId,
    "currentSkill.assessmentId": benchmarkAssessmentId,
    schoolYear: schoolYear,
    "currentSkill.interventions.0": { $exists: true }
  };
  var benchmarkAssessment = db.Assessments.findOne({
    _id: benchmarkAssessmentId
  });
  print("Benchmark Assessment:", benchmarkAssessment.name);
  var interventionAssessment = db.Assessments.findOne({
    _id: interventionAssessmentId
  });
  print("\nIntervention Assessment", interventionAssessment.name);

  var studentsAffected = db.Students.find(query).toArray();

  print("Affected students:", studentsAffected.length);

  var studentIds = studentsAffected.map(function(s) {
    return s._id;
  });

  var enrollments = db.StudentGroupEnrollments.find(
    {
      studentId: { $in: studentIds },
      isActive: true
    },
    { studentId: 1, siteId: 1, studentGroupId: 1 }
  ).toArray();
  var affectedStudentGroupIdsAndSiteIds = enrollments.map(function(e) {
    return { studentGroupId: e.studentGroupId, siteId: e.siteId };
  });
  var studentGroupIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.studentGroupId;
  });
  var studentGroups = db.StudentGroups.find(
    {
      _id: {
        $in: studentGroupIds
      }
    },
    { name: 1, currentAssessmentResultIds: 1 }
  ).toArray();

  var siteIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.siteId;
  });
  var sites = db.Sites.find(
    {
      _id: {
        $in: siteIds
      }
    },
    { name: 1 }
  ).toArray();

  var now = new Date();
  var lastModified = {
    by: "SPRIN_1323_fix_script",
    on: now.valueOf(),
    date: now
  };

  studentsAffected.forEach(function(student, index) {
    var firstName = student.identity.name.firstName;
    var lastName = student.identity.name.lastName;
    var studentId = student._id;
    var enrollment = enrollments.find(function(e) {
      return e.studentId === studentId;
    });

    var siteId = enrollment.siteId;
    var schoolName = sites.find(function(s) {
      return s._id === siteId;
    }).name;
    var groupId = enrollment.studentGroupId;
    var group = studentGroups.find(function(sg) {
      return sg._id === groupId;
    });
    var groupName = group.name;

    print("\n", index, ".");
    print(
      firstName,
      lastName,
      "(_id:",
      studentId,
      ")",
      "\nschool: ",
      schoolName,
      "\ngroup:",
      groupName
    );

    var interventionId = "YAfRn9TocxMptjqkF";
    print("BEFOREnumber of interventions", student.currentSkill.interventions.length);
    student.currentSkill.interventions = student.currentSkill.interventions.filter(function(intervention) {
      return intervention.interventionId !== interventionId;
    });
    print("AFTERnumber of interventions", student.currentSkill.interventions.length);
    var studentUpdateRes = db.Students.update(
      { _id: studentId },
      {
        $set: { lastModified: lastModified, "currentSkill.interventions": student.currentSkill.interventions },
      }
    );
    print("currentSkill updated:", studentUpdateRes.nModified ? "YES" : "NO");
    var assessmentResultId = student.currentSkill.assessmentResultId;
    var currentAssessmentResult = db.AssessmentResults.findOne({_id: assessmentResultId});
    currentAssessmentResult.individualSkills.interventions = currentAssessmentResult.individualSkills.interventions.filter(function(intervention) {
      return intervention.interventionId !== interventionId;
    });
    var assessmentResultUpdateRes = db.AssessmentResults.update(
      { _id: assessmentResultId },
      { $set: currentAssessmentResult }
    );
    print(
      "assessmentResult updated:",
      assessmentResultUpdateRes.nModified ? "YES" : "NO"
    );
  });
})();
