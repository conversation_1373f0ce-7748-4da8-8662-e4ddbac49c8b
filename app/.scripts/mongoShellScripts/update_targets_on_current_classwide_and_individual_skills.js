(function() {
  // ARGUMENTS TO PROVIDE:
  var shouldPrintStudentsAndGroupsData = true; // SET TO FALSE IF YOU DON'T WANT TO SEE THE AFFECTED GROUPS AND STUDENTS DATA
  // END PROVIDING ARGUMENTS

  var assessmentIdsToGrades = {
    oCNYMYX3WE8xhRhZR: ["04", "05", "06"], // SPRIN-1363 and 1365 (the part for grade 06) for Multiplication 0-9
    Smm39q4tPmM9TephC: ["05"], // SPRIN-1366 Add & Subtract Decimals to the Hundredths
    yxBaWDvbNjBJLcaHQ: ["05"], // SPRIN-1366 Sums to 6
    pexzg5K88e3XBHKYi: ["05"], // SPRIN-1366 Subtraction 0-9
    Mj7g2oxvaH9w2fwmS: ["06"], // SPRIN-1370 Simplify Fractions
    "5NiJE3CGhMYpXueaY": ["06", "07"], // SPRIN-1371 (6th grade part) and SPRIN-1376 (7th grade part) Divide 1-Digit into 2-3 Digit w/o Remainders
    gs7NzLtujr94fmfaR: ["06"], // SPRIN-1373 Multiply 1-Digit by 2-3-Digit without Regrouping
    J8aGfDwkaHFN2cCAM: ["07"], // SPRIN-1377 2-Digit Multiplied by 2-Digit with and without Regrouping
    LMG6RNYhvBWsssvGy: ["07"] // SPRIN-1378 Add and Subtract with Integers
  };
  var assessmentIds = Object.keys(assessmentIdsToGrades);

  assessmentIds.forEach(function(assessmentId) {
    var affectedGrades = assessmentIdsToGrades[assessmentId];
    var validationResult = validateArguments(assessmentId, affectedGrades);
    if (!validationResult) {
      return;
    }
    var assessmentDocument = db.Assessments.findOne({ _id: assessmentId });
    if (!assessmentDocument) {
      print("Assessment was not found, make sure to input the correct Assessment ID");
      return;
    }
    print("\n\n************************************");
    print("Assessment: ", assessmentDocument.name);
    print("Affected Grades: ", affectedGrades.join(", "));
    var assessmentTargets = assessmentDocument.strands[0].scores[0].targets;

    affectedGrades.forEach(function(affectedGrade) {
      print("\n\n............ Grade in progress:", affectedGrade, "................");

      var targetsToSet = getTargetsToSetForGrade(assessmentTargets, affectedGrade);
      print("\nTargets to be set:", targetsToSet.join(", "));

      // FIND AFFECTED GROUPS AND STUDENTS
      var affectedGroups = db.StudentGroups.find(
        {
          "currentClasswideSkill.assessmentId": assessmentId,
          schoolYear: 2019,
          isActive: true,
          grade: affectedGrade,
          "currentClasswideSkill.targets": { $ne: targetsToSet }
        },
        { history: 0 }
      ).toArray();
      var affectedGroupIds = affectedGroups.map(function(group) {
        return group._id;
      });

      var affectedStudents = db.Students.find(
        {
          "currentSkill.assessmentId": assessmentId,
          schoolYear: 2019,
          grade: affectedGrade,
          "currentSkill.assessmentTargets": { $ne: targetsToSet }
        },
        { history: 0 }
      ).toArray();
      var affectedStudentIds = affectedStudents.map(function(student) {
        return student._id;
      });

      print("\nNumber of affected Student Groups:", affectedGroups.length);
      print("Number of affected Students:", affectedStudents.length);

      if (shouldPrintStudentsAndGroupsData) {
        printAdditionalData(affectedStudentIds, affectedGroups, affectedStudents);
      }

      var mongoTypeTargets = targetsToSet.map(function(target) {
        return NumberInt(target);
      });
      if (affectedGroupIds.length) {
        var groupUpdateOpResult = db.StudentGroups.updateMany(
          { _id: { $in: affectedGroupIds } },
          {
            $set: {
              "currentClasswideSkill.targets": mongoTypeTargets
            }
          }
        );
        print("\nThere were", groupUpdateOpResult.modifiedCount, "fixed groups.");
      }

      if (affectedStudentIds.length) {
        var assessmentResultsToFix = affectedStudents.map(function(student) {
          return student.currentSkill.assessmentResultId;
        });
        if (shouldPrintStudentsAndGroupsData) {
          print("Assessment Results that will get fixed:");
          printjson(assessmentResultsToFix);
        }
        var studentsAssessmentsOpResult = db.AssessmentResults.updateMany(
          {
            _id: { $in: assessmentResultsToFix }
          },
          {
            $set: { "individualSkills.assessmentTargets": mongoTypeTargets }
          }
        );
        print("\nThere were", studentsAssessmentsOpResult.modifiedCount, "fixed student assessments.");

        var studentsUpdateOpResult = db.Students.updateMany(
          {
            _id: { $in: affectedStudentIds }
          },
          {
            $set: {
              "currentSkill.assessmentTargets": mongoTypeTargets
            }
          }
        );
        print("\nThere were", studentsUpdateOpResult.modifiedCount, "fixed students.");
      }
    });
  });

  function printAdditionalData(affectedStudentIds, affectedGroups, affectedStudents) {
    var activeEnrollments = db.StudentGroupEnrollments.find(
      {
        studentId: { $in: affectedStudentIds },
        schoolYear: 2019,
        isActive: true
      },
      { siteId: 1, studentGroupId: 1, studentId: 1 }
    ).toArray();

    var studentsEnrolledGroupsIds = activeEnrollments.map(function(e) {
      return e.studentGroupId;
    });
    var studentsEnrolledGroups = db.StudentGroups.find(
      { _id: { $in: studentsEnrolledGroupsIds } },
      { name: 1 }
    ).toArray();

    var sitesIdsAffected = affectedGroups.map(function(sg) {
      return sg.siteId;
    });
    var allSiteIdsAffected = sitesIdsAffected.concat(
      activeEnrollments.map(function(e) {
        return e.siteId;
      })
    );
    var sitesData = db.Sites.find({ _id: { $in: allSiteIdsAffected } }, { name: 1, orgid: 1 }).toArray();
    var orgidsAffected = sitesData.map(function(s) {
      return s.orgid;
    });
    var orgsData = db.Organizations.find({ _id: { $in: orgidsAffected } }, { name: 1 }).toArray();
    var groupsToPrint = affectedGroups.map(function(sg) {
      var siteName = sitesData.find(function(s) {
        return s._id === sg.siteId;
      }).name;
      var organizationName = orgsData.find(function(org) {
        return org._id === sg.orgid;
      }).name;
      return {
        groupName: sg.name,
        groupId: sg._id,
        siteName: siteName,
        organizationName: organizationName
      };
    });
    print("\nAffected groups:");
    printjson(groupsToPrint);

    var studentsToPrint = affectedStudents.map(function(student) {
      var organizationName = orgsData.find(function(org) {
        return org._id === student.orgid;
      }).name;
      var matchingEnrollment = activeEnrollments.find(function(e) {
        return e.studentId === student._id;
      });
      var groupId = matchingEnrollment.studentGroupId;
      var siteId = matchingEnrollment.siteId;
      var groupName = studentsEnrolledGroups.find(function(sg) {
        return sg._id === groupId;
      }).name;
      var siteName = sitesData.find(function(s) {
        return s._id === siteId;
      }).name;
      return {
        studentName: "" + student.identity.name.firstName + " " + student.identity.name.lastName,
        studentId: student._id,
        groupName: groupName,
        groupId: groupId,
        siteName: siteName,
        organizationName: organizationName
      };
    });

    print("\nAffected students:");
    printjson(studentsToPrint);
  }

  function getTargetsToSetForGrade(allTargets, grade) {
    var allGradeTargets = allTargets.filter(function(target) {
      return target.grade === grade;
    });
    var defaultTargets = allGradeTargets.filter(function(target) {
      return !target.assessmentType;
    })[0];
    var fallTargets = defaultTargets.periods.find(function(period) {
      return period.name === "Fall";
    });
    return fallTargets.values;
  }

  function validateArguments(assId, grades) {
    if (!grades.length) {
      print("You are supposed to provide at least on grade");
      return;
    }
    if (!assId || !assId.length || typeof assId !== "string") {
      print("assessmentId: ", assId, "is incorrect. Stopping...");
      return;
    }
    var wrongGrades = [];
    grades.forEach(function(grade) {
      if (typeof grade !== "string") {
        wrongGrades.push({ grade: grade, error: "not a string" });
        return;
      }
      if (grade.length !== 2 && grade !== "K") {
        wrongGrades.push({
          grade: grade,
          error: ": should be K or 01,02,03,04 etc...!!!"
        });
        return;
      }
    });
    if (wrongGrades.length) {
      print("Incorrect Grades provided. Stopping....");
      printjson(wrongGrades);
      return;
    }
    return true;
  }
})();
