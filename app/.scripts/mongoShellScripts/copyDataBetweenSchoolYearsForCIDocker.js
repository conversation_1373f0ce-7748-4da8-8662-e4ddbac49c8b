(() => {
  // NOTE(fmazur) - only need to update this constant
  const SCHOOL_YEAR_TO_UPDATE_TO = 2025;

  const schoolYearToUpdateFrom = 2024;
  const schoolYearDifference = SCHOOL_YEAR_TO_UPDATE_TO - schoolYearToUpdateFrom;

  const benchmarkWindows = db
    .getCollection("BenchmarkWindows")
    .find({ schoolYear: schoolYearToUpdateFrom })
    .toArray();

  benchmarkWindows.forEach(benchmarkWindow => {
    benchmarkWindow.schoolYear = NumberInt(SCHOOL_YEAR_TO_UPDATE_TO);
    const newSchoolYear = benchmarkWindow.startDate.getUTCFullYear() + schoolYearDifference;
    benchmarkWindow.startDate.setUTCFullYear(newSchoolYear);
    benchmarkWindow.endDate.setUTCFullYear(newSchoolYear);

    if (!benchmarkWindow._id.includes(schoolYearToUpdateFrom.toString())) {
      benchmarkWindow._id += SCHOOL_YEAR_TO_UPDATE_TO;
    } else {
      benchmarkWindow._id = benchmarkWindow._id.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
    }

    db.getCollection("BenchmarkWindows").insert(benchmarkWindow);
  });

  const users = db
    .getCollection("users")
    .find({
      "profile.siteAccess.schoolYear": schoolYearToUpdateFrom
    })
    .toArray();

  users.forEach(user => {
    const { siteAccess } = user.profile;
    if (!siteAccess.some(e => parseInt(e.schoolYear) === SCHOOL_YEAR_TO_UPDATE_TO)) {
      let newSiteAccess = [];
      siteAccess.forEach(siteAccessElem => {
        if (parseInt(siteAccessElem.schoolYear) === schoolYearToUpdateFrom) {
          let modifiedSiteAccess = Object.assign({}, siteAccessElem);
          modifiedSiteAccess.schoolYear = NumberInt(SCHOOL_YEAR_TO_UPDATE_TO);
          newSiteAccess.push(modifiedSiteAccess);
        }
      });

      db.getCollection("users").update(
        { _id: user._id },
        { $set: { "profile.siteAccess": newSiteAccess.concat(siteAccess) } }
      );
    }
  });

  const students = db
    .getCollection("Students")
    .find({ schoolYear: schoolYearToUpdateFrom })
    .toArray();

  newStudentIds = {};
  students.forEach(student => {
    let newId = "";
    if (!student._id.includes(schoolYearToUpdateFrom.toString())) {
      newId = student._id + SCHOOL_YEAR_TO_UPDATE_TO;
    } else {
      newId = student._id.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
    }
    newStudentIds[student._id] = newId;
    student._id = newId;
    student.schoolYear = SCHOOL_YEAR_TO_UPDATE_TO;
    db.getCollection("Students").insert(student);
  });

  const studentGroups = db
    .getCollection("StudentGroups")
    .find({ schoolYear: schoolYearToUpdateFrom })
    .toArray();

  newStudentGroupsIds = {};
  studentGroups.forEach(studentGroup => {
    let newId = "";
    if (!studentGroup._id.includes(schoolYearToUpdateFrom.toString())) {
      newId = studentGroup._id + SCHOOL_YEAR_TO_UPDATE_TO;
    } else {
      newId = studentGroup._id.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
    }

    newStudentGroupsIds[studentGroup._id] = newId;
    studentGroup._id = newId;
    studentGroup.schoolYear = SCHOOL_YEAR_TO_UPDATE_TO;
    const newHistory = [];
    studentGroup.history &&
      studentGroup.history.forEach(historyElem => {
        if (historyElem.enrolledStudentIds) {
          historyElem.enrolledStudentIds = historyElem.enrolledStudentIds.map(
            oldEnrolledStudentId => newStudentIds[oldEnrolledStudentId]
          );
        }
        historyElem.assessmentResultMeasures &&
          historyElem.assessmentResultMeasures.forEach(assessmentResultMeasure => {
            if (assessmentResultMeasure.studentResults) {
              assessmentResultMeasure.studentResults = assessmentResultMeasure.studentResults.map(oldStudentResult => {
                let modifiedStudentResults = Object.assign({}, oldStudentResult);
                modifiedStudentResults.studentId = newStudentIds[oldStudentResult.studentId];
                return modifiedStudentResults;
              });
            }
          });
        newHistory.push(historyElem);
      });
    studentGroup.history = newHistory;
    if (studentGroup.individualInterventionQueue) {
      studentGroup.individualInterventionQueue = studentGroup.individualInterventionQueue.map(
        studentId => newStudentIds[studentId] || studentId
      );
    }
    db.getCollection("StudentGroups").insert(studentGroup);
  });

  const assessmentResults = db
    .getCollection("AssessmentResults")
    .find({ schoolYear: schoolYearToUpdateFrom })
    .toArray();

  let newAssessmentResultsIds = {};
  assessmentResults.forEach(assessmentResult => {
    let newId = "";
    if (!assessmentResult._id.includes(schoolYearToUpdateFrom.toString())) {
      newId = assessmentResult._id + SCHOOL_YEAR_TO_UPDATE_TO;
    } else {
      newId = assessmentResult._id.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
    }
    assessmentResult.schoolYear = SCHOOL_YEAR_TO_UPDATE_TO;
    let newAssessmentResultsId = newId;
    newAssessmentResultsIds[assessmentResult._id] = newAssessmentResultsId;
    assessmentResult._id = newAssessmentResultsId;
    assessmentResult.studentGroupId = newStudentGroupsIds[assessmentResult.studentGroupId];
    if (assessmentResult.studentId) {
      assessmentResult.studentId = newStudentIds[assessmentResult.studentId];
    }
    if (assessmentResult.previousAssessmentResultId) {
      assessmentResult.previousAssessmentResultId = assessmentResult.previousAssessmentResultId.replace(
        schoolYearToUpdateFrom,
        SCHOOL_YEAR_TO_UPDATE_TO
      );
    }

    if (assessmentResult.nextAssessmentResultId) {
      assessmentResult.nextAssessmentResultId = assessmentResult.nextAssessmentResultId.replace(
        schoolYearToUpdateFrom,
        SCHOOL_YEAR_TO_UPDATE_TO
      );
    }
    if (assessmentResult.ruleResults && assessmentResult.ruleResults.nextAssessmentResultId) {
      assessmentResult.ruleResults.nextAssessmentResultId = assessmentResult.ruleResults.nextAssessmentResultId.replace(
        schoolYearToUpdateFrom,
        SCHOOL_YEAR_TO_UPDATE_TO
      );
    }

    assessmentResult.scores.forEach(score => {
      score.studentId = newStudentIds[score.studentId];
    });

    assessmentResult.measures &&
      assessmentResult.measures.forEach(measure => {
        measure.studentResults.forEach(studentResult => {
          studentResult.studentId = newStudentIds[studentResult.studentId];
        });
      });

    if (assessmentResult.classwideResults) {
      assessmentResult.classwideResults.studentIdsNotMeetingTarget = assessmentResult.classwideResults.studentIdsNotMeetingTarget.map(
        studentId => newStudentIds[studentId]
      );
    }

    if (assessmentResult.individualSkills) {
      assessmentResult.individualSkills.assessmentResultId =
        newAssessmentResultsIds[assessmentResult.individualSkills.assessmentResultId];
    }

    db.getCollection("AssessmentResults").insert(assessmentResult);
  });

  const addedStudents = db
    .getCollection("Students")
    .find({ schoolYear: SCHOOL_YEAR_TO_UPDATE_TO })
    .toArray();

  addedStudents.forEach(student => {
    if (student.currentSkill) {
      db.getCollection("Students").update(
        { _id: student._id },
        {
          $set: {
            "currentSkill.assessmentResultId": newAssessmentResultsIds[student.currentSkill.assessmentResultId]
          }
        }
      );
    }
  });

  const addedStudentGroups = db
    .getCollection("StudentGroups")
    .find({ schoolYear: SCHOOL_YEAR_TO_UPDATE_TO })
    .toArray();

  addedStudentGroups.forEach(studentGroup => {
    const set = {};
    if (studentGroup.currentClasswideSkill) {
      studentGroup.currentClasswideSkill.assessmentResultId =
        newAssessmentResultsIds[studentGroup.currentClasswideSkill.assessmentResultId];
      set["currentClasswideSkill.assessmentResultId"] = studentGroup.currentClasswideSkill.assessmentResultId;
    }
    studentGroup.history.forEach(historyElem => {
      historyElem.assessmentResultId = newAssessmentResultsIds[historyElem.assessmentResultId];
    });
    set.history = studentGroup.history;
    if (studentGroup.currentAssessmentResultIds) {
      studentGroup.currentAssessmentResultIds = studentGroup.currentAssessmentResultIds.map(
        currentAssessmentResultId => newAssessmentResultsIds[currentAssessmentResultId]
      );
      set.currentAssessmentResultIds = studentGroup.currentAssessmentResultIds;
    }

    db.getCollection("StudentGroups").update(
      { _id: studentGroup._id },
      {
        $set: set
      }
    );
  });

  const studentGroupEnrollments = db
    .getCollection("StudentGroupEnrollments")
    .find({ schoolYear: schoolYearToUpdateFrom })
    .toArray();

  studentGroupEnrollments.forEach(studentGroupEnrollment => {
    let newId = "";
    if (!studentGroupEnrollment._id.includes(schoolYearToUpdateFrom.toString())) {
      newId = studentGroupEnrollment._id + SCHOOL_YEAR_TO_UPDATE_TO;
    } else {
      newId = studentGroupEnrollment._id.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
    }
    studentGroupEnrollment._id = newId;
    studentGroupEnrollment.schoolYear = SCHOOL_YEAR_TO_UPDATE_TO;
    studentGroupEnrollment.studentId = newStudentIds[studentGroupEnrollment.studentId];
    studentGroupEnrollment.studentGroupId = newStudentGroupsIds[studentGroupEnrollment.studentGroupId];

    db.getCollection("StudentGroupEnrollments").insert(studentGroupEnrollment);
  });

  const studentsBySkill = db
    .getCollection("StudentsBySkill")
    .find()
    .toArray();

  studentsBySkill.forEach(studentsBySkillElem => {
    studentsBySkillElem.studentsBelowInstructionalTarget = studentsBySkillElem.studentsBelowInstructionalTarget.map(
      studentId => {
        const parsedStudentId = studentId.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
        return newStudentIds[studentId] || parsedStudentId;
      }
    );
    studentsBySkillElem.studentsBelowMasteryTarget = studentsBySkillElem.studentsBelowMasteryTarget.map(studentId => {
      const parsedStudentId = studentId.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
      return newStudentIds[studentId] || parsedStudentId;
    });
    studentsBySkillElem.studentsWithoutSkillHistory = studentsBySkillElem.studentsWithoutSkillHistory.map(studentId => {
      const parsedStudentId = studentId.replace(schoolYearToUpdateFrom, SCHOOL_YEAR_TO_UPDATE_TO);
      return newStudentIds[studentId] || parsedStudentId;
    });
    db.getCollection("StudentsBySkill").update({ _id: studentsBySkillElem._id }, { $set: studentsBySkillElem });
  });

  const sites = db
    .getCollection("Sites")
    .find({})
    .toArray();
  sites.forEach(site => {
    db.getCollection("Sites").update({ _id: site._id }, { $set: { schoolYear: SCHOOL_YEAR_TO_UPDATE_TO } });
  });
})();
