var studentIds = [
  "2b261addc67f45cda",
  "3a0273f07707476a8",
  "701d16b1844441079",
  "97262f9c816b4444b",
  "97c1b8a14c3045e3b",
  "ac1a9e1cc44247588",
  "e9cdd0d53a2a43c39"
];

slicedOrganizations.forEach(function(organizationId) {
  print("orgid", organizationId, "\n\n");
  var activeEnrollments = db.StudentGroupEnrollments.find(
    { orgid: organizationId, schoolYear: 2018, isActive: true },
    { studentId: 1, grade: 1 }
  )
    .sort({ studentId: 1 })
    .toArray();
  var duplicates = [];
  activeEnrollments.forEach(function(enrollment, index) {
    if (index === activeEnrollments.length - 1) return;
    if (enrollment.studentId === activeEnrollments[index + 1].studentId) {
      duplicates.push(enrollment.studentId);
    }
  });
  printjson(duplicates);
});
