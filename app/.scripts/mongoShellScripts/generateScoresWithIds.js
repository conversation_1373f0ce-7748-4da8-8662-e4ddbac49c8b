function generateAssessmentScores(assessmentIds, studentIds, orgid, siteId) {
  const assessments = [];
  assessmentIds.forEach(assessmentId => {
    studentIds.forEach(studentId => {
      assessments.push({
        _id: generateId(),
        assessmentId: assessmentId,
        orgid: orgid,
        siteId: siteId,
        status: "STARTED",
        studentId: studentId
      });
    });
  });
  // copy(assessments);
  return assessments;
}

function generateId() {
  let rtn = "";
  for (let i = 0; i < ID_LENGTH; i++) {
    rtn += ALPHABET.charAt(Math.floor(Math.random() * ALPHABET.length));
  }
  return rtn;
}
