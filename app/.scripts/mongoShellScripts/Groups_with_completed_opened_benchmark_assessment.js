var studentGroupsWithCompletedAssessment = db.AssessmentResults.find(
  { type: "benchmark", grade: "06", benchmarkPeriodId: "cjCMnZKARBJmG8suT", schoolYear: 2018, status: "OPEN" },
  { studentGroupId: 1 }
).map(function(x) {
  return x.studentGroupId;
});
//printjson(studentGroupsWithCompletedAssessment);
var groupData = [];
studentGroupsWithCompletedAssessment.forEach(function(studentGroupId) {
  var group = db.StudentGroups.findOne({ _id: studentGroupId }, { name: 1, orgid: 1, siteId: 1 });
  var site = db.Sites.findOne({ _id: group.siteId }, { name: 1 });
  var organization = db.Organizations.findOne({ _id: group.orgid }, { name: 1 });
  groupData.push({
    groupId: group._id,
    groupName: group.name,
    siteName: site.name,
    orgName: organization.name
  });
});
print("Number of groups with OPEN spring assessment in grade #6:", groupData.length, "\nGroup Data:");
printjson(groupData);
