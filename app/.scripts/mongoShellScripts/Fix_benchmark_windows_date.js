var a = db.BenchmarkWindows.find({
  schoolYear: 2019,
  startDate: { $gte: ISODate("2019-07-31T00:00:00Z") },
  endDate: { $gte: ISODate("2019-12-30T00:00:00Z") }
}).toArray();
print(a.length);

a.forEach(function(bw, index) {
  db.BenchmarkWindows.update(
    { _id: bw._id },
    { $set: { startDate: ISODate("2018-08-01T00:00:00Z"), endDate: ISODate("2018-12-31T00:00:00Z") } }
  );
  if (index === 0 || index === 1) {
    printjson(bw);
    printjson(db.BenchmarkWindows.findOne({ _id: bw._id }));
  }
});
print("copy:\n");
printjson(a);
