const students = db.Students.aggregate([
  {
    $match: {
      schoolYear,
      orgid
    }
  },
  { $sort: { "created.on": -1 } },
  {
    $addFields: {
      localId: { $regexFind: { input: "$identity.identification.localId", regex: /(?<=-)[^-]+(?=-)|^[^-]+$/ } }
    }
  },
  {
    $group: {
      _id: { name: "$identity.name", grade: "$grade", localId: "$localId.match" },
      studentsWithSameName: { $push: "$$ROOT" }
    }
  },
  {
    $match: {
      "studentsWithSameName.1": { $exists: true }
    }
  }
]).toArray();
// print(students);
const studentIdByOldStudentId = {};
const studentsToUpdate = [];
const idsOfStudentsToRemove = [];
students.forEach(studentGrouping => {
  if (studentGrouping.studentsWithSameName.length > 2) {
    print(studentGrouping.studentsWithSameName);
  }
  const [studentToUpdate, studentToRemove] = studentGrouping.studentsWithSameName;
  studentIdByOldStudentId[studentToRemove._id] = studentToUpdate._id;
  studentsToUpdate.push(studentToUpdate);
  idsOfStudentsToRemove.push(studentToRemove._id);
});
print(studentIdByOldStudentId);
