(function() {
  // var studentGroupId = 'TEST_2018_3Uzgl2lfarSd';
  var studentGroupId = "TEST_2018_WgOmA3gYzZkc";
  var schoolYear = 2018;
  var benchmarkPeriodId = ""; // leave empty to use the benchmark period id from the current classwide skill

  var studentGroup = db.StudentGroups.findOne({
    _id: studentGroupId,
    "currentClasswideSkill.benchmarkPeriodId": { $exists: true }
  });

  if (!studentGroup) return print("No student group with a classwide intervention and " + studentGroupId + " id");

  benchmarkPeriodId = benchmarkPeriodId || studentGroup.currentClasswideSkill.benchmarkPeriodId;

  var lastClasswideAssessmentResult = db.AssessmentResults.findOne({
    studentGroupId: studentGroupId,
    status: "OPEN",
    type: "classwide",
    benchmarkPeriodId: benchmarkPeriodId,
    schoolYear: schoolYear
  });

  if (!lastClasswideAssessmentResult)
    return print(
      "No classwide intervention assessment result found for the student group with " +
        studentGroupId +
        " id in the " +
        benchmarkPeriodId +
        " benchmark period and the " +
        schoolYear +
        " school year"
    );

  printjson(lastClasswideAssessmentResult);

  var scores = lastClasswideAssessmentResult.scores.map(function(score) {
    score.status = "STARTED";
    delete score.value;
    return score;
  });

  var now = new Date();
  var lastModified = {
    by: "remove_last_classwide_intervention_scores_script",
    on: now.valueOf(),
    date: now
  };

  db.AssessmentResults.update(
    { _id: lastClasswideAssessmentResult._id },
    {
      $set: { scores: scores, lastModified: lastModified },
      $unset: { classwideResults: "", measures: "", nextAssessmentResultId: "", ruleResults: "" }
    }
  );

  print("Last classwide intervention scores for student group with " + studentGroupId + " id have been removed");
})();
