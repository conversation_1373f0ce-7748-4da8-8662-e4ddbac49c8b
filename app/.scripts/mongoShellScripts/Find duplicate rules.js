
var rules = db.Rules.find({}, {name: 1, attributeValues: 1}).toArray();

rules.forEach(function(rule) {
  if(rule.attributeValues) {
 	var otherRule = db.Rules.find({ 
 	  _id: {$ne: rule._id},
 	  name: rule.name,
 	  "attributeValues.grade": rule.attributeValues.grade,
 	  "attributeValues.benchmarkPeriod": rule.attributeValues.benchmarkPeriod,
 	  "attributeValues.assessmentId": rule.attributeValues.assessmentId,
 	  }).toArray();
 	  if(otherRule.length > 0) {
// 	    print("duplicate rules:", rule._id, otherRule._id);
		print("duplicate rules:", otherRule.length);
 	  } else {
 	  }
  } else {
    
  }
})

