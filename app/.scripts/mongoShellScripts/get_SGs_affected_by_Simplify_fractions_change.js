var affectedGroups = db.StudentGroups.find(
  {
    "currentClasswideSkill.assessmentId": "Mj7g2oxvaH9w2fwmS",
    schoolYear: 2019,
    isActive: true,
    grade: "06",
    "currentClasswideSkill.targets": { $eq: [17, 33, 300] }
  },
  { siteId: 1, name: 1, orgid: 1 }
).toArray();
var sitesIdsAffected = affectedGroups.map(function(sg) {
  return sg.siteId;
});

var sitesData = db.Sites.find(
  { _id: { $in: sitesIdsAffected } },
  { name: 1, orgid: 1 }
).toArray();

var orgidsAffected = sitesData.map(function(s) {
  return s.orgid;
});
var orgsData = db.Organizations.find(
  { _id: { $in: orgidsAffected } },
  { name: 1 }
).toArray();
var groupsWithExtraData = affectedGroups.map(function(sg) {
  var siteName = sitesData.find(function(s) {
    return s._id === sg.siteId;
  }).name;
  var organizationName = orgsData.find(function(org) {
    return org._id === sg.orgid;
  }).name;
  return Object.assign({}, sg, {
    siteName: siteName,
    organizationName: organizationName
  });
});
printjson(groupsWithExtraData);
