var studentsToAr = {
  //
};

var studentGroup = db.StudentGroups.findOne({ _id: "" }, { name: 1, currentAssessmentResultIds: 1 });

Object.keys(studentsToAr).forEach(function(studentId) {
  var assessmentResultIdToBringBackToOpen = studentsToAr[studentId];
  var student = db.Students.findOne({ _id: studentId });
  var firstName = student.identity.name.firstName;
  var lastName = student.identity.name.lastName;
  var currentStudentARId = student.currentSkill.assessmentResultId;
  // pull AR from student group
  var pullCurrentARFromGroupOp = db.StudentGroups.updateOne(
    { _id: studentGroup._id },
    { $pull: { currentAssessmentResultIds: currentStudentARId } }
  );
  print("\npullCurrentARFromGroupOp");
  printjson(pullCurrentARFromGroupOp);
  //

  var indexOfAssessmentToBringBackToOpen = student.history.findIndex(function(item) {
    return item.assessmentResultId === assessmentResultIdToBringBackToOpen;
  });
  var historyItemsToPull = student.history.filter(function(item, index) {
    return index < indexOfAssessmentToBringBackToOpen;
  });
  if (historyItemsToPull.length) {
    var printables = [];
    historyItemsToPull.forEach(function(item) {
      item.assessmentResultMeasures.forEach(function(measure) {
        printables.push({
          studentName: "" + firstName + lastName,
          id: measure.assessmentId,
          name: measure.assessmentName,
          score: measure.studentScores[0],
          endDate: item.whenEnded.date
        });
      });
    });
    print("\nRemoved scores:");
    printjson(printables);
  }
  var assessmentResultIdsToRemove = historyItemsToPull.map(function(item) {
    return item.assessmentResultId;
  });
  print("\nAR Ids to pull from history:");
  var arsToPullFromHistory = assessmentResultIdsToRemove.concat(assessmentResultIdToBringBackToOpen);
  printjson(arsToPullFromHistory);

  // PULL HISTORY ITEMS FROM STUDENT
  var pullHistoryItemsFromStudentOp = db.Students.updateOne(
    { _id: studentId },
    {
      $pull: {
        history: {
          assessmentResultId: { $in: arsToPullFromHistory }
        }
      }
    }
  );
  print("\npullHistoryItemsFromStudentOp");
  printjson(pullHistoryItemsFromStudentOp);
  //

  // REMOVE ASSESSMENT RESULTS FROM DB
  var allAssessmentResultsRemoved = assessmentResultIdsToRemove.concat(currentStudentARId);
  print("\nAR Ids to remove:");
  printjson(allAssessmentResultsRemoved);
  var removeAssessmentResultsOp = db.AssessmentResults.remove({
    _id: { $in: allAssessmentResultsRemoved }
  });
  print("\nremoveAssessmentResultsOp");
  printjson(removeAssessmentResultsOp);
  //

  var assessmentResultToBringBackToOpen = db.AssessmentResults.findOne({ _id: assessmentResultIdToBringBackToOpen });
  // BRING BACK ASSESSMENT TO OPEN
  var assessmentResultToBringBackToOpenOp = db.AssessmentResults.update(
    { _id: assessmentResultIdToBringBackToOpen },
    {
      $set: { status: "OPEN" },
      $unset: {
        classwideResults: "",
        measures: "",
        nextAssessmentResultId: "",
        ruleResults: ""
      }
    }
  );
  print("\nassessmentResultToBringBackToOpenOp");
  printjson(assessmentResultToBringBackToOpenOp);
  //

  var updatedCurrentSkill = {
    benchmarkAssessmentId: assessmentResultToBringBackToOpen.individualSkills.benchmarkAssessmentId,
    benchmarkAssessmentName: assessmentResultToBringBackToOpen.individualSkills.benchmarkAssessmentName,
    benchmarkAssessmentTargets: assessmentResultToBringBackToOpen.individualSkills.benchmarkAssessmentTargets,
    assessmentId: assessmentResultToBringBackToOpen.individualSkills.assessmentId,
    assessmentName: assessmentResultToBringBackToOpen.individualSkills.assessmentName,
    assessmentTargets: assessmentResultToBringBackToOpen.individualSkills.assessmentTargets,
    interventions: assessmentResultToBringBackToOpen.individualSkills.interventions,
    assessmentResultId: assessmentResultToBringBackToOpen.individualSkills.assessmentResultId,
    whenStarted: assessmentResultToBringBackToOpen.created,
    benchmarkPeriodId: assessmentResultToBringBackToOpen.benchmarkPeriodId,
    message: {
      messageCode: "55",
      dismissed: true
    }
  };

  //add current skill
  var studentAddCurrentSkillOp = db.Students.updateOne(
    { _id: studentId },
    { $set: { currentSkill: updatedCurrentSkill } }
  );
  print("\nstudentAddCurrentSkillOp");
  printjson(studentAddCurrentSkillOp);
  //

  //add current ar to student group
  var studentGroupAddNewCurrentArOp = db.StudentGroups.updateOne(
    { _id: studentGroup._id },
    { $addToSet: { currentAssessmentResultIds: assessmentResultIdToBringBackToOpen } }
  );
  print("\nstudentGroupAddNewCurrentArOp");
  printjson(studentGroupAddNewCurrentArOp);
  //
});
