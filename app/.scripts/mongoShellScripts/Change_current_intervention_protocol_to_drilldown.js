(function() {
  var benchmarkAssessmentId = "vBbMt2C7jTeMmha94";
  var interventionAssessmentId = "N4vDf85G4rsQp7FPF";
  var interventionToFind = "56attvmgjtrjMsFif";
  var schoolYear = 2019;

  var benchmarkAssessment = db.Assessments.findOne({
    _id: benchmarkAssessmentId
  });
  print("Benchmark Assessment:", benchmarkAssessment.name);
  var interventionAssessment = db.Assessments.findOne({
    _id: interventionAssessmentId
  });
  print("Intervention Assessment", interventionAssessment.name);

  var studentsAffected = db.Students.find({
    grade: "07",
    "currentSkill.benchmarkAssessmentId": benchmarkAssessmentId,
    schoolYear: schoolYear,
    "currentSkill.assessmentId": interventionAssessmentId,
    "currentSkill.interventions.interventionId": interventionToFind
  }).toArray();

  print("\nAffected students", "(", studentsAffected.length, ") :");

  var studentIds = studentsAffected.map(function(s) {
    return s._id;
  });

  var enrollments = db.StudentGroupEnrollments.find(
    {
      studentId: { $in: studentIds },
      isActive: true
    },
    { studentId: 1, siteId: 1, studentGroupId: 1 }
  ).toArray();
  var affectedStudentGroupIdsAndSiteIds = enrollments.map(function(e) {
    return { studentGroupId: e.studentGroupId, siteId: e.siteId };
  });
  var studentGroupIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.studentGroupId;
  });
  var studentGroups = db.StudentGroups.find(
    {
      _id: {
        $in: studentGroupIds
      }
    },
    { name: 1, currentAssessmentResultIds: 1 }
  ).toArray();

  var siteIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.siteId;
  });
  var sites = db.Sites.find(
    {
      _id: {
        $in: siteIds
      }
    },
    { name: 1 }
  ).toArray();

  var now = new Date();
  var lastModified = {
    by: "SPRIN_1315_fix_script",
    on: now.valueOf(),
    date: now
  };

  studentsAffected.forEach(function(student, index) {
    var firstName = student.identity.name.firstName;
    var lastName = student.identity.name.lastName;
    var studentId = student._id;
    var enrollment = enrollments.find(function(e) {
      return e.studentId === studentId;
    });

    var siteId = enrollment.siteId;
    var schoolName = sites.find(function(s) {
      return s._id === siteId;
    }).name;
    var groupId = enrollment.studentGroupId;
    var group = studentGroups.find(function(sg) {
      return sg._id === groupId;
    });
    var groupName = group.name;

    print("\n", index + 1, ".");
    print(
      "Student Name:",
      firstName,
      lastName,
      "(_id:",
      studentId,
      ")",
      "\nSchool Name: ",
      schoolName,
      "(_id:",
      siteId,
      ")",
      "\nGroup Name:",
      groupName,
      "(_id:",
      groupId,
      ")"
    );

    var studentUpdateRes = db.Students.update(
      { _id: studentId },
      { $set: { "currentSkill.interventions": [], lastModified: lastModified } }
    );
    print("currentSkill updated:", studentUpdateRes.nModified ? "YES" : "NO");
    var assessmentResultId = student.currentSkill.assessmentResultId;
    var assessmentResult = db.AssessmentResults.findOne({ _id: assessmentResultId });
    assessmentResult.individualSkills.interventions = [];
    assessmentResult.scores = assessmentResult.scores.map(function(score) {
      // delete score.value;
      score.status = "STARTED";
      return score;
    });

    var assessmentResultUpdateRes = db.AssessmentResults.update(
      { _id: assessmentResultId },
      { $set: assessmentResult }
    );
    print("assessmentResult updated:", assessmentResultUpdateRes.nModified ? "YES" : "NO");
  });
})();
