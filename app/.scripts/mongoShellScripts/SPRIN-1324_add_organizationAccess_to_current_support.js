var orgCursor = db.Organizations.find({ isSelfEnrollee: { $ne: true } }, { _id: 1 }); // Fetching non SML organizations
print(orgCursor.count());
var organizationIds = orgCursor.toArray().map(function(o) {
  return o._id;
});
var usersCursor = db.users.find(
  {
    "profile.orgid": "allOrgs",
    "profile.siteAccess.role": "arbitraryIdsupport",
    "profile.organizationAccess": { $exists: false }
  },
  { _id: 1 }
);
var count = usersCursor.count();
if (count) {
  print("Found:", count, "support users");
  var userIds = usersCursor.toArray().map(function(u) {
    return u._id;
  });
  var updateManyRes = db.users.updateMany(
    { _id: { $in: userIds } },
    {
      $set: {
        "profile.organizationAccess": organizationIds
      }
    }
  );
  printjson(updateManyRes);
} else {
  print("No action needed");
}
