var enrollments = db.StudentGroupEnrollments.find({
  studentGroupId: "vL7bWgFhkecdmcFrc",
  "lastModified.date": { $gt: ISODate("2019-01-20") }
}).toArray();
var studentIds = enrollments.map(function(e) {
  return e.studentId;
});

var students = db.Students.find({ _id: { $in: studentIds } }, { identity: 1 }).toArray();
students.forEach(function(s) {
  var name =
    "First: " +
    s.identity.name.firstName +
    ",\t Middle: " +
    s.identity.name.middleName +
    ",\t Last: " +
    s.identity.name.lastName;
  print(name, "stateId:\t", s.identity.identification.stateId, "localId:\t", s.identity.identification.localId);
});
