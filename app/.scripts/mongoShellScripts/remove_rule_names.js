var rules = db.Rules.find({}).toArray();
var date = new Date();
rules.forEach(function(rule) {
  var overwrittenSkills = [];
  var setOperation = { lastModified: { by: "RemoveRuleNamesScript", date: date, on: date.valueOf() } };
  if (rule.skills && rule.skills.length && rule.grade) {
    overwrittenSkills = rule.skills.map(function(skill) {
      var skillToBeUpdated = Object.assign({}, skill);
      delete skillToBeUpdated.assessmentName;
      return skillToBeUpdated;
    });
    setOperation.skills = overwrittenSkills;
  }
  db.Rules.update({ _id: rule._id }, { $set: setOperation, $unset: { name: 1, description: 1 } });
});
print("DONE");
