(function() {
  // ARGUMENTS TO PROVIDE:
  var shouldPrintStudentsAndGroupsData = true; // SET TO FALSE IF YOU DON'T WANT TO SEE THE AFFECTED GROUPS AND STUDENTS DATA
  // END PROVIDING ARGUMENTS

  var assessmentIdsToGrades = {
    WkTxuThkN8GD5sTk5: ["08"], //SPRIN-1379 - Mixed Operations
    "5NiJE3CGhMYpXueaY": ["08"] //SPRIN-1379 - Divide 1-Digit into 2-3 Digit w/o Remainders
  };
  var assessmentIds = Object.keys(assessmentIdsToGrades);

  assessmentIds.forEach(function(assessmentId) {
    var affectedGrades = assessmentIdsToGrades[assessmentId];
    var validationResult = validateArguments(assessmentId, affectedGrades);
    if (!validationResult) {
      return;
    }
    var assessmentDocument = db.Assessments.findOne({ _id: assessmentId });
    if (!assessmentDocument) {
      print("Assessment was not found, make sure to input the correct Assessment ID");
      return;
    }
    print("\n\n************************************");
    print("Assessment: ", assessmentDocument.name);
    print("Affected Grades: ", affectedGrades.join(", "));
    var assessmentTargets = assessmentDocument.strands[0].scores[0].targets;

    affectedGrades.forEach(function(affectedGrade) {
      print("\n\n............ Grade in progress:", affectedGrade, "................");

      var targetsToSet = getIndividualTargetsToSetForGrade(assessmentTargets, affectedGrade);
      if (!targetsToSet) {
        print("Could not find individual targets, returning...");
      }
      print("\nTargets to be set:", targetsToSet.join(", "));

      // FIND AFFECTED STUDENTS
      var affectedStudents = db.Students.find(
        {
          "currentSkill.assessmentId": assessmentId,
          schoolYear: 2019,
          grade: affectedGrade,
          "currentSkill.assessmentTargets": { $ne: targetsToSet }
        },
        { history: 0 }
      ).toArray();
      var affectedStudentIds = affectedStudents.map(function(student) {
        return student._id;
      });
      print("Number of affected Students:", affectedStudents.length);

      if (shouldPrintStudentsAndGroupsData) {
        printAdditionalData(affectedStudentIds, affectedStudents);
      }

      var mongoTypeTargets = targetsToSet.map(function(target) {
        return NumberInt(target);
      });

      if (affectedStudentIds.length) {
        var assessmentResultsToFix = affectedStudents.map(function(student) {
          return student.currentSkill.assessmentResultId;
        });
        if (shouldPrintStudentsAndGroupsData) {
          print("Assessment Results that will get fixed:");
          printjson(assessmentResultsToFix);
        }
        var studentsAssessmentsOpResult = db.AssessmentResults.updateMany(
          {
            _id: { $in: assessmentResultsToFix }
          },
          {
            $set: { "individualSkills.assessmentTargets": mongoTypeTargets }
          }
        );
        print("\nThere were", studentsAssessmentsOpResult.modifiedCount, "fixed student assessments.");

        var studentsUpdateOpResult = db.Students.updateMany(
          {
            _id: { $in: affectedStudentIds }
          },
          {
            $set: {
              "currentSkill.assessmentTargets": mongoTypeTargets
            }
          }
        );
        print("\nThere were", studentsUpdateOpResult.modifiedCount, "fixed students.");
      }
    });
  });

  function printAdditionalData(affectedStudentIds, affectedStudents) {
    var activeEnrollments = db.StudentGroupEnrollments.find(
      {
        studentId: { $in: affectedStudentIds },
        schoolYear: 2019,
        isActive: true
      },
      { siteId: 1, studentGroupId: 1, studentId: 1 }
    ).toArray();

    var studentsEnrolledGroupsIds = activeEnrollments.map(function(e) {
      return e.studentGroupId;
    });
    var studentsEnrolledGroups = db.StudentGroups.find(
      { _id: { $in: studentsEnrolledGroupsIds } },
      { name: 1 }
    ).toArray();

    var allSiteIdsAffected = activeEnrollments.map(function(e) {
      return e.siteId;
    });
    var sitesData = db.Sites.find({ _id: { $in: allSiteIdsAffected } }, { name: 1, orgid: 1 }).toArray();
    var orgidsAffected = sitesData.map(function(s) {
      return s.orgid;
    });
    var orgsData = db.Organizations.find({ _id: { $in: orgidsAffected } }, { name: 1 }).toArray();
    var studentsToPrint = affectedStudents.map(function(student) {
      var organizationName = orgsData.find(function(org) {
        return org._id === student.orgid;
      }).name;
      var matchingEnrollment = activeEnrollments.find(function(e) {
        return e.studentId === student._id;
      });
      var groupId = matchingEnrollment.studentGroupId;
      var siteId = matchingEnrollment.siteId;
      var groupName = studentsEnrolledGroups.find(function(sg) {
        return sg._id === groupId;
      }).name;
      var siteName = sitesData.find(function(s) {
        return s._id === siteId;
      }).name;
      return {
        studentName: "" + student.identity.name.firstName + " " + student.identity.name.lastName,
        studentId: student._id,
        groupName: groupName,
        groupId: groupId,
        siteName: siteName,
        organizationName: organizationName
      };
    });

    print("\nAffected students:");
    printjson(studentsToPrint);
  }

  function getIndividualTargetsToSetForGrade(allTargets, grade) {
    var allGradeTargets = allTargets.filter(function(target) {
      return target.grade === grade;
    });
    var individualTargets = allGradeTargets.filter(function(target) {
      return target.assessmentType === "individual";
    });
    var fallTargets =
      individualTargets.length &&
      individualTargets[0].periods.find(function(period) {
        return period.name === "Fall";
      });
    return fallTargets && fallTargets.values;
  }

  function validateArguments(assId, grades) {
    if (!grades.length) {
      print("You are supposed to provide at least on grade");
      return;
    }
    if (!assId || !assId.length || typeof assId !== "string") {
      print("assessmentId: ", assId, "is incorrect. Stopping...");
      return;
    }
    var wrongGrades = [];
    grades.forEach(function(grade) {
      if (typeof grade !== "string") {
        wrongGrades.push({ grade: grade, error: "not a string" });
        return;
      }
      if (grade.length !== 2 && grade !== "K") {
        wrongGrades.push({
          grade: grade,
          error: ": should be K or 01,02,03,04 etc...!!!"
        });
        return;
      }
    });
    if (wrongGrades.length) {
      print("Incorrect Grades provided. Stopping....");
      printjson(wrongGrades);
      return;
    }
    return true;
  }
})();
