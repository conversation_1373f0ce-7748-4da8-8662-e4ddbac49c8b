// FINDS AND FILLS MISSING SCORES FOR ACTIVELY ENROLLED STUDENTS

function generateAssessmentScores(assessmentIds, studentIds, orgid, siteId) {
  var assessmentScores = [];
  assessmentIds.forEach(function(assessmentId) {
    studentIds.forEach(function(studentId) {
      assessmentScores.push({
        _id: generateId(),
        assessmentId: assessmentId,
        orgid: orgid,
        siteId: siteId,
        status: "STARTED",
        studentId: studentId
      });
    });
  });
  // copy(assessments);
  return assessmentScores;
}

function generateId() {
  var rtn = "";
  for (var i = 0; i < ID_LENGTH; i++) {
    rtn += ALPHABET.charAt(Math.floor(Math.random() * ALPHABET.length));
  }
  return rtn;
}

var ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
var ID_LENGTH = 17;

var groups = db.StudentGroups.find(
  { orgid: "evBRRkwmQsj7nWDhF", isActive: true, schoolYear: 2018 },
  { currentAssessmentResultIds: 1, _id: 1 }
).toArray();

groups.forEach(function(group) {
  var activelyEnrolledStudents = db.StudentGroupEnrollments.find(
    { studentGroupId: group._id, isActive: true, schoolYear: 2018 },
    { studentId: 1 }
  )
    .toArray()
    .map(function(s) {
      return s.studentId;
    })
    .sort();
  if (!group.currentAssessmentResultIds) return;
  var activeAssessments = db.AssessmentResults.find(
    { _id: { $in: group.currentAssessmentResultIds }, type: { $ne: "individual" } },
    { scores: 1, assessmentIds: 1 }
  ).toArray();
  activeAssessments.forEach(function(ass) {
    var studentIds = [];
    ass.scores.forEach(function(score) {
      var studentId = score.studentId;
      if (!studentIds.includes(studentId)) {
        studentIds.push(studentId);
      }
    });
    var sortedStudentsInAssessment = studentIds.sort();
    var areEqual =
      sortedStudentsInAssessment.length === activelyEnrolledStudents.length &&
      sortedStudentsInAssessment.every(function(v, i) {
        return v === activelyEnrolledStudents[i];
      });
    if (!areEqual) {
      var missingEnrolledStudents = [];
      activelyEnrolledStudents.forEach(function(s) {
        if (!sortedStudentsInAssessment.includes(s)) missingEnrolledStudents.push(s);
      });
      var unnecessaryStudentsScoresInAssessment = [];
      sortedStudentsInAssessment.forEach(function(s) {
        if (!activelyEnrolledStudents.includes(s)) unnecessaryStudentsScoresInAssessment.push(s);
      });
      print("\n\n************\nWrong Assessment id:", ass._id); //logs incorrect assessment id
      print("missingEnrolledStudents");
      printjson(missingEnrolledStudents);
      if (missingEnrolledStudents.length) {
        var orgid = ass.scores[0].orgid;
        var siteId = ass.scores[0].siteId;
        var assessmentIds = ass.assessmentIds;
        var generatedScores = generateAssessmentScores(assessmentIds, missingEnrolledStudents, orgid, siteId);
        print("Add this to the scores: ");
        printjson(generatedScores);
        print("\n");
        // AssessmentResults.update({_id: ass._id}, {$addToSet: {scores: {$each: generatedScores}}}) NOT TESTED YET!!!!!!!!!!!!!
      }
      print("unnecessaryStudentsScoresInAssessment");
      printjson(unnecessaryStudentsScoresInAssessment);
      print("\n************\n");
    } else {
      print("OK: ", ass._id);
    }
  });
});
