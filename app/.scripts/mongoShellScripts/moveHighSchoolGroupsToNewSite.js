(function() {
  var idToName = {
    // TODO to be provided
  };
  var organizationIds = []; // TODO to be provided
  organizationIds.forEach(function(orgid) {
    print("\n*******************************");
    print("ORG:", idToName[orgid]);

    // get high school student groups
    var studentGroupsToMove = db.StudentGroups.find(
      { orgid: orgid, grade: { $in: ["HS"] }, schoolYear: 2019, isActive: true },
      { _id: 1, name: 1, sectionId: 1, grade: 1, siteId: 1 }
    ).toArray();
    if (!studentGroupsToMove.length) {
      print("NO STUDENT GROUPS TO MOVE, ERROR!");
      return;
    }
    print("Student Groups that get moved:");
    printjson(studentGroupsToMove);
    var studentGroupIdsToMove = studentGroupsToMove.map(function(sg) {
      return sg._id;
    });

    // get new high school
    var newHighSchool = db.Sites.findOne(
      { orgid: orgid, isHighSchool: true },
      { _id: 1, name: 1, stateInformation: 1 }
    );
    if (!newHighSchool) {
      print("NO HIGH SCHOOL AVAILABLE , ERROR!");
      return;
    }
    print("New High school:");
    printjson(newHighSchool);
    var newHighSchoolId = newHighSchool._id;

    // ENROLLMENTS
    var studentEnrollmentsAffectedRes = db.StudentGroupEnrollments.updateMany(
      {
        studentGroupId: { $in: studentGroupIdsToMove }
      },
      { $set: { siteId: newHighSchoolId } }
    );
    print("Changed: ", studentEnrollmentsAffectedRes.modifiedCount, "enrollments");

    var studentGroupsAffected = db.StudentGroups.find({ _id: { $in: studentGroupIdsToMove } }).toArray(); //change siteId
    var sgsUpdateRes = db.StudentGroups.updateMany(
      { _id: { $in: studentGroupIdsToMove } },
      { $set: { siteId: newHighSchoolId } }
    );
    print("Changed: ", sgsUpdateRes.modifiedCount, "groups");

    // USERS
    var userIdsAffected = studentGroupsAffected.reduce(function(acc, group) {
      var groupTeachers = group.ownerIds.concat(group.secondaryTeachers ? group.secondaryTeachers : []);
      return acc.concat(groupTeachers);
    }, []);
    var usersAffected = db.users.find({ _id: { $in: userIdsAffected } }).toArray();
    usersAffected.forEach(function(user) {
      var updatedSiteAccess = user.profile.siteAccess;
      var typeOfAccess = updatedSiteAccess[0].role;
      updatedSiteAccess.unshift({
        role: typeOfAccess,
        siteId: newHighSchoolId,
        schoolYear: NumberInt(2019),
        isActive: true,
        isDefault: true
      });
      var updateOp = db.users.updateOne({ _id: user._id }, { $set: { "profile.siteAccess": updatedSiteAccess } });
      print("updating teacher with _id:", user._id, "success:", updateOp.modifiedCount ? "Yes!" : "No!");
    });

    //BENCHMARK WINDOWS
    var previousWinterBenchmarkWindow = db.BenchmarkWindows.findOne({
      orgid: orgid,
      schoolYear: 2019,
      benchmarkPeriodId: "nEsbWokBWutTZFkTh"
    });
    var newWinterBenchmarkWindow = Object.assign({}, previousWinterBenchmarkWindow, {
      siteId: newHighSchoolId,
      _id: new ObjectId().valueOf()
    }); //change siteId
    var previousFallBenchmarkWindow = db.BenchmarkWindows.findOne({
      orgid: orgid,
      schoolYear: 2019,
      benchmarkPeriodId: "8S52Gz5o85hRkECgq"
    });
    var newFallBenchmarkWindow = Object.assign({}, previousFallBenchmarkWindow, {
      siteId: newHighSchoolId,
      _id: new ObjectId().valueOf()
    }); //change siteId
    var winterBenchmarkInsertOp = db.BenchmarkWindows.insertOne(newWinterBenchmarkWindow);
    print("Inserted winter benchmark to new High School:", !!winterBenchmarkInsertOp.insertedId);
    var fallBenchmarkInsertOp = db.BenchmarkWindows.insertOne(newFallBenchmarkWindow);
    print("Inserted fall benchmark to new High School:", !!fallBenchmarkInsertOp.insertedId);

    //ASSESSMENT RESULTS
    var assessmentResultsAffected = db.AssessmentResults.find({
      studentGroupId: { $in: studentGroupIdsToMove }
    }).toArray(); // need to change scores[].siteId
    assessmentResultsAffected.forEach(function(ar, index) {
      print("Updating AssessmentResults:", index);
      var updatedScores = ar.scores.map(function(score) {
        return Object.assign({}, score, { siteId: newHighSchoolId });
      });
      db.AssessmentResults.updateOne({ _id: ar._id }, { $set: { scores: updatedScores } });
    });
  });
})();
