(function() {
  var studentId = "";
  var studentGroupId = "";
  var benchmarkPeriodId = "";
  var schoolYear = 2018;

  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId });

  if (!studentGroup) return print("No student group with " + studentGroupId + " id");

  var individualAssessmentResults = db.AssessmentResults.find({
    studentId: studentId,
    studentGroupId: studentGroupId,
    type: "individual",
    benchmarkPeriodId: benchmarkPeriodId,
    schoolYear: schoolYear
  }).toArray();

  var individualAssessmentResultIds = individualAssessmentResults.map(function(individualAssessmentResult) {
    return individualAssessmentResult._id;
  });

  if (!individualAssessmentResultIds.length)
    return print(
      "There are no individual interventions for the student with " + studentId + " id in that benchmark period"
    );

  var now = new Date();
  var lastModified = {
    by: "remove_individual_interventions_script",
    on: now.valueOf(),
    date: now
  };

  db.Students.update(
    { _id: studentId },
    {
      $set: { lastModified: lastModified },
      $unset: { currentSkill: "" },
      $pull: { history: { assessmentResultId: { $in: individualAssessmentResultIds } } }
    }
  );

  db.StudentGroups.update(
    { _id: studentGroupId },
    {
      $set: { lastModified: lastModified },
      $push: { individualInterventionQueue: studentId },
      $pullAll: { currentAssessmentResultIds: individualAssessmentResultIds }
    }
  );

  var result = db.AssessmentResults.remove({ _id: { $in: individualAssessmentResultIds } });

  if (result.nRemoved) {
    print("Individual interventions for the student with " + studentId + " id have been removed");
  } else {
    print("There are no individual interventions for the student with " + studentId + " id in that benchmark period");
  }
})();
