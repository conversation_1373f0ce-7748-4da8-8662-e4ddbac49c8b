(function () {
  var studentGroupId = '';
  var assessmentId = '';

  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId });
  if (!studentGroup) return print('No student group with the ' + studentGroupId + ' id');

  var classwideIntervention = db.AssessmentResults.findOne({
    studentGroupId: studentGroupId,
    type: 'classwide',
    assessmentIds: assessmentId,
    status: 'COMPLETED',
  });
  if (!classwideIntervention) return print('No completed classwide intervention with assessment id: ' + assessmentId + ' found for the student group with ' + studentGroupId + ' id');

  var newerClasswideInterventions = db.AssessmentResults.find({
    studentGroupId: studentGroupId,
    type: 'classwide',
    'created.on': { $gt: classwideIntervention.created.on },
  }).toArray();

  print('Newer classwide interventions: ' + newerClasswideInterventions.length);

  var newTargets = classwideIntervention.measures[0].targetScores.map(function (s) {
    return NumberInt(s);
  });

  var currentClasswideSkill = {
    assessmentId: classwideIntervention.assessmentIds[0],
    assessmentName: classwideIntervention.measures[0].assessmentName,
    interventions: [],
    targets: newTargets,
    whenStarted: classwideIntervention.created,
    assessmentResultId: classwideIntervention._id,
    benchmarkPeriodId: classwideIntervention.benchmarkPeriodId,
    message: {
      additionalStudentsAddedToInterventionQueue: false,
      messageCode: '1',
      dismissed: false,
    },
  };

  var newerClasswideInterventionIds = newerClasswideInterventions.map(function (intervention) {
    return intervention._id;
  });
  var idsOfClasswideInterventionToRemoveFromHistory = newerClasswideInterventionIds.slice(0);
  idsOfClasswideInterventionToRemoveFromHistory.push(classwideIntervention._id);
  
  var now = new Date();
  var lastModified = {
    by: 'set_specified_classwide_intervention_as_active',
    on: now.valueOf(),
    date: now,
  };

  var studentGroupUpdate1 = db.StudentGroups.update({ _id: studentGroupId }, {
    $set: { currentClasswideSkill: currentClasswideSkill },
    $pull: {
      currentAssessmentResultIds: { $in: newerClasswideInterventionIds },
      history: { assessmentResultId: { $in: idsOfClasswideInterventionToRemoveFromHistory } },
    },
  });
  printjson(studentGroupUpdate1);

  var studentGroupUpdate2 = db.StudentGroups.update({ _id: studentGroupId }, {
    $addToSet: { currentAssessmentResultIds: classwideIntervention._id },
  });
  printjson(studentGroupUpdate2);

  var scores = classwideIntervention.scores.map(function (score) {
    score.status = 'STARTED';
    delete score.value;
    return score;
  });

  var assessmentsUpdateResult = db.AssessmentResults.update({ _id: classwideIntervention._id }, {
    $set: { lastModified: lastModified, scores: scores, status: 'OPEN' },
    $unset: { classwideResults: '', measures: '', nextAssessmentResultId: '', ruleResults: '' },
  });
  printjson(assessmentsUpdateResult);

  var removeResult = db.AssessmentResults.remove({ _id: { $in: newerClasswideInterventionIds } });
  printjson(removeResult);

  print('Classwide intervention with assessment id: ' + assessmentId + ' for the student group with ' + studentGroupId + ' id has been set as ACTIVE');
})();