const maxSchoolYear = 2023; // remove data from older school years including maxSchoolYear

// const orgIds = db.Organizations.find({ name: { $not: /QA|Aaron/i } }, { name: 1 }).toArray().map(o => o._id);
const orgIds = [
  "wSMdvekNZvyKatBfT"
];
const total = orgIds.length;
orgIds.forEach((orgid, index) => {
    print(`==== [ Orgid: ${orgid} (${index + 1}/${total}) ] ====`)
  removeOrgAndRelatedData(orgid)
});

function removeDocumentsFromCollectionsByQuery(collections, query) {
  let result;
  collections.forEach(collection => {
    result = db[collection]?.deleteMany(query);
    print(collection + ": " + result.deletedCount + " removed");
  })
}

function removeOrgAndRelatedData(orgid) {
  const orgidOnlyQuery = {
    orgid
  }

  const schoolYearQuery = {
    orgid,
    schoolYear: { $lte: maxSchoolYear }
  }

  const createdDateQuery = {
    orgid,
    "created.date": { $lt: ISODate(maxSchoolYear + "-07-01") }
  }

  const startedDateQuery = {
    orgid,
    "started.date": { $lt: ISODate(maxSchoolYear + "-07-01") }
  }

  const siteIds = db.Sites.find(schoolYearQuery).toArray().map(s => s._id);
  const siteIdsQuery = {
    siteId: { $in: [siteIds] }
  }

  const collectionsWithOrgidOnlyQuery = [
    "FutureStudents",
  ];

  const collectionsWithSchoolYearQuery = [
    "AssessmentResults",
    "AssessmentResultsRestorePoint",
    "AssessmentScoresUpload",
    "BenchmarkWindows",
    "BenchmarkWindowsRestorePoint",
    "FileUploadLineItems",
    "FileUploadLineItemsCleansed",
    "FileUploadProcessSummaries",
    "FutureUsers",
    "RosterImportItems",
    "Sites",
    "StudentGroupEnrollments",
    "StudentGroups",
    "StudentGroupsRestorePoint",
    "Students",
    "StudentsRestorePoint",
  ];

  const collectionsWithCreatedDateQuery = [
    "AuditLogs",
    "FileUploads",
  ];

  const collectionsWithStartedDateQuery = [
    "RosterImports",
  ];

  const collectionsWithSiteIdsQuery = [
    "StudentsBySkill",
    "StudentsBySkillRestorePoint",
  ];

  removeDocumentsFromCollectionsByQuery(collectionsWithOrgidOnlyQuery, orgidOnlyQuery);
  removeDocumentsFromCollectionsByQuery(collectionsWithSchoolYearQuery, schoolYearQuery);
  removeDocumentsFromCollectionsByQuery(collectionsWithCreatedDateQuery, createdDateQuery);
  removeDocumentsFromCollectionsByQuery(collectionsWithStartedDateQuery, startedDateQuery);
  removeDocumentsFromCollectionsByQuery(collectionsWithSiteIdsQuery, siteIdsQuery);

  const usersUpdateResult = db.users.updateMany(
    { "profile.orgid": orgid, "profile.siteAccess.siteId": { $in: siteIds } },
    { $pull: { "profile.siteAccess": { siteId: { $in: siteIds } } } }
  );
  print("Users: " + usersUpdateResult.modifiedCount + " updated");

  const usersDeleteResult = db.users.deleteMany({ "profile.orgid": orgid, "profile.siteAccess": [] });
  print("Users: " + usersDeleteResult.deletedCount + " removed");

  const remainingSites = db.Sites.find(orgidOnlyQuery, { _id: 1 }).toArray();
  if (!remainingSites.length) {
    const organizationsDeleteResult = db.Organizations.deleteOne({ _id: orgid });
    print("Organizations: " + organizationsDeleteResult.deletedCount + " removed");
  }
}
