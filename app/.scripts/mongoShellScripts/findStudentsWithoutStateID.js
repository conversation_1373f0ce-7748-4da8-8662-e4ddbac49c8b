var students = db.Students.find(
  {
    $or: [
      { "identity.identification.stateId": "" },
      { "identity.identification.stateId": { $exists: false } },
      { "identity.identification.localId": "" },
      { "identity.identification.localId": { $exists: false } }
    ]
  },
  { _id: 1 }
)
  .toArray()
  .map(function(s) {
    return s._id;
  });
var count = students.length;
//printjson(students)

var activelyEnrolled = db.StudentGroupEnrollments.find({
  studentId: { $in: students },
  isActive: true,
  schoolYear: 2017
})
  .toArray()
  .map(function(s) {
    return s.studentId;
  });
printjson(activelyEnrolled);
