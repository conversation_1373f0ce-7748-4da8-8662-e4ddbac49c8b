{"info": {"_postman_id": "c5d47e7b-970b-4f16-92cb-bc8cfc499168", "name": "SpringMath Ed-fi with OAuth 1.0", "description": "1. Request an OAuth request token from the auth server (GetOAuthCode)\n    1. In authorization tab: Inherit auth from parent (may not matter for this request)\n    1. send the ClientID key supplied by auth provider in an \"oauth/authorize\" GET\n        1. postman creates the signature from the consumer key, timestamp, and generated nonce.\n    2. auth provider returns a request authorization code, value set in collection variable “OAuthCode\".\n2. Request and OAuth token for a session (GetOAuthToken)\n    1. POST an \"oauth/token\" request to the auth server\n    2. In authorization tab\n        1. Authorization: OAuth 1.0.\n        2. Consumer Key: Collection variable “ClientID”. Value supplied by remote auth authority. This identifies us as a registered entity.\n        3. Consumer Secret: Collection variable “Secret”. Value supplied by remote auth authority.  This identifies us as a registered entity\n        4. Access Token: Collection variable “OAuthToken”.  Request authorization code returned by GetOAuthCode.  Grant type is authorization_code.\n        5. The values in the Authorization tab are used to build the body.  Request also uses the environment variables ClientID, Secret, OAuthCode in the request. \n    3. return body contains an access token (sets collection variable “OAuthToken”), expiration and token_type of “bearer\"\n    4. Enter access_token in OAuthCode variable of collection\n3. GetAllSchools\n    1. In authorization tab\n        1. Type: Bearer Token\n        2. Token: Collection variable “OAuthToken” as  received from GetOAuthToken request.\n    2. Send request\n    3. Response is JSON list of schools.\n4. GetLEAs\n    1. Returns list of Local Education Agencies in JSON format\n5. Get10Students \n    1. Returns first 10 students in JSON format.\n6. See Ed-FI API sandbox at https://api.ed-fi.org.\n", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetOAuthToken", "event": [{"listen": "test", "script": {"id": "eb9679f6-79b2-406e-88cb-60a61737c35f", "exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"OAuthToken\", responseData.access_token);", "function b(a){return a?(a^Math.random()*16>>a/4).toString(16):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,b)};", "postman.setEnvironmentVariable(\"ClientUID\", b());"], "type": "text/javascript"}}], "request": {"auth": {"type": "oauth1", "oauth1": [{"key": "token", "value": "{{OAuthCode}}", "type": "string"}, {"key": "consumerSecret", "value": "{{Secret}}", "type": "string"}, {"key": "consumerKey", "value": "{{ClientID}} ", "type": "string"}, {"key": "addParamsToHeader", "value": false, "type": "boolean"}, {"key": "signatureMethod", "value": "HMAC-SHA256", "type": "string"}, {"key": "tokenSecret", "value": "", "type": "string"}, {"key": "timestamp", "value": 1472121255, "type": "number"}, {"key": "nonce", "value": "e5VR16", "type": "string"}, {"key": "version", "value": "1.0", "type": "string"}, {"key": "realm", "value": "", "type": "string"}, {"key": "autoAddParam", "type": "any"}, {"key": "addEmptyParamsToSign", "value": false, "type": "boolean"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "Client_id", "value": "{{ClientID}}", "type": "text"}, {"key": "Client_secret", "value": "{{Secret}}", "type": "text"}, {"key": "Code", "value": "{{OAuthCode}}", "type": "text"}, {"key": "Grant_type", "value": "authorization_code", "type": "text"}]}, "url": {"raw": "{{BaseURL}}/oauth/token", "host": ["{{BaseURL}}"], "path": ["o<PERSON>h", "token"]}}, "response": []}, {"name": "GetOAuthCode", "event": [{"listen": "test", "script": {"id": "0d01faf2-4f88-4123-b441-b3739258d068", "exec": ["var responseData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"OAuthCode\", responseData.code);", "console.log('Returned code: ', postman.getEnvironmentVariable(\"OAuthCode\"));"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BaseURL}}/oauth/authorize?Client_id={{ClientID}}&Response_type=code", "host": ["{{BaseURL}}"], "path": ["o<PERSON>h", "authorize"], "query": [{"key": "Client_id", "value": "{{ClientID}}"}, {"key": "Response_type", "value": "code"}]}}, "response": []}, {"name": "GetAllSchools", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BaseURL}}/{{IDPostAmble}}/{{SchoolYear}}/schools/", "host": ["{{BaseURL}}"], "path": ["{{IDPostAmble}}", "{{SchoolYear}}", "schools", ""]}}, "response": []}, {"name": "GetLEAs", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BaseURL}}/{{IDPostAmble}}/{{SchoolYear}}/localEducationAgencies", "host": ["{{BaseURL}}"], "path": ["{{IDPostAmble}}", "{{SchoolYear}}", "localEducationAgencies"]}}, "response": []}, {"name": "Get10Students", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{BaseURL}}/{{IDPostAmble}}/{{SchoolYear}}/Students", "host": ["{{BaseURL}}"], "path": ["{{IDPostAmble}}", "{{SchoolYear}}", "Students"]}}, "response": []}], "auth": {"type": "oauth1", "oauth1": [{"key": "consumerSecret", "value": "{{Secret}}", "type": "string"}, {"key": "consumerKey", "value": "{{ClientID}} ", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "signatureMethod", "value": "HMAC-SHA1", "type": "string"}, {"key": "version", "value": "1.0", "type": "string"}, {"key": "addParamsToHeader", "value": false, "type": "boolean"}, {"key": "addEmptyParamsToSign", "value": false, "type": "boolean"}]}, "event": [{"listen": "prerequest", "script": {"id": "c2f99aec-b471-469d-bfcb-8277a7844b7e", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "eaee2d00-4cf1-4854-8a93-331b93768ccb", "type": "text/javascript", "exec": [""]}}], "variable": [{"id": "752a8282-fdcb-4464-a42b-169a51c01a01", "key": "BaseURL", "value": "https://api-OS.midatahub.org:443/ODS", "type": "string"}, {"id": "aa7cfc73-7c9f-46ef-ae72-17450fb643b7", "key": "ClientID", "value": " ", "type": "string"}, {"id": "e896c8a6-d4e3-4612-a15f-b5b1311012ca", "key": "Secret", "value": "", "type": "string"}, {"id": "abd8b185-09d9-46de-9af8-493e1edcd286", "key": "IDPostAmble", "value": "api/v2.0", "type": "string"}, {"id": "2c35c38f-b84a-4c5e-8504-ab78cc6743cb", "key": "SchoolYear", "value": "2019", "type": "string"}, {"id": "ffe41cac-da60-4def-8afd-9ec6601d0fb8", "key": "OAuthCode", "value": "", "type": "string"}, {"id": "f2325e6c-7213-44af-8c04-41ff3b0e29cf", "key": "OAuthToken", "value": null, "type": "any"}]}