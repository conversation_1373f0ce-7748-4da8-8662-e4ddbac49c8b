#!/bin/bash
#galaxyDeploy -e {dev|qa|stage} -b {branch} -s {settings}

while getopts "e:b:s:" opt; do
  case $opt in
    e)
      env=$OPTARG
    ;;
    b)
      branch=$OPTARG
    ;;
    s)
      settings=$OPTARG
    ;;
    \? )
      echo "Invalid option: -$OPTARG" >&2
      echo "galaxyDeploy -e {dev|qa|stage} -b {branch} -s {settings}"
      exit 1
    ;;
  esac
done

if [ -z "$env" ]
then
  echo "Environment is required: -e {dev|qa|stage}"
  exit 1
fi
if [ -z "$branch" ]
then
  echo "Branch is required: -b {branch}"
  exit 1
fi

deploy_domain="app.$env.springmath.org"

if [ -z "$settings" ]
then
  settings_arg=''
else
  settings_arg=" --settings "$settings
fi

echo "Deploying "$branch" to "$deploy_domain

echo " DEPLOY_HOSTNAME=ties.galaxy.meteor.com METEOR_SESSION_FILE=deployment_token.json meteor deploy $deploy_domain $settings_arg"
DEPLOY_HOSTNAME=ties.galaxy.meteor.com METEOR_SESSION_FILE=deployment_token.json meteor deploy $deploy_domain $settings_arg
DEPLOY_RESULT=$?

echo "#####################################"
echo "# ASSIGN RESULTS TO DEPLOY_RESULT "$DEPLOY_RESULT" #"
echo "#####################################"

if [ "$DEPLOY_RESULT" == "0" ]; then
  echo "####################################"
  echo "# DEPLOYMENT FINISHED SUCCESSFULLY #"
  echo "####################################"
else
  echo "#####################"
  echo "# DEPLOYMENT FAILED #"
  echo "#####################"
fi

exit $DEPLOY_RESULT
