/*
  INTRODUCTION

  IMPORTANT: USE THIS SCRIPT ONLY IN DEMO

  This is a script for migrating users from from old demo org to new demo org if necessary
  The script moves teacher/admin users between orgs and changes siteAccess to target siteId

   USAGE

   1. Run npm install in the schoolScrubber directory
   2. Set env variables
      a) MONGO_URL -- this is where the migration will occur
   3. Run the script with the following command
      npm run migrateUsers -- --sourceOrgId=ORG_ID_OF_SANDBOX --targetOrgId=ORG_ID_OF_DEMO --targetSiteId=SITE_ID_IN_TARGET_ORG
   4. Users will be able to sign in using their old usernames and passwords

   Note: The script will exit upon error
 */

const minimist = require("minimist");

const MONGO_URL = process.env.MONGO_URL; // mongo url for demo db

const MongoClient = require("mongodb").MongoClient;
const chalk = require("chalk");

async function run() {
  if (!MONGO_URL) {
    console.log(chalk.red("MONGO_URL not set in env"));
    process.exit(0);
  }
  const argv = minimist(process.argv.slice(2));
  const { sourceOrgId, targetOrgId, targetSiteId } = argv;

  if (!sourceOrgId) {
    console.log(chalk.red("Please specify an Organization to copy from, pass sourceOrgId as a parameter"));
    process.exit(0);
  }

  if (!targetOrgId) {
    console.log(
      chalk.red("Please specify a destination organization for this migration script, pass targetOrgId as a parameter")
    );
    process.exit(0);
  }

  if (!targetSiteId) {
    console.log(
      chalk.red("Please specify a destination siteId for this migration script, pass targetSiteId as a parameter")
    );
    process.exit(0);
  }

  const dbConnection = await MongoClient.connect(MONGO_URL);
  console.log(`Connected successfully to target DB`);

  const usersDb = dbConnection.collection("users");
  const users = await usersDb.find({ "profile.orgid": sourceOrgId }).toArray();
  const demoUser = await usersDb.findOne({ _id: "demo_admin_user_id" });
  const teacherUsers = users.filter(user =>
    user.profile.siteAccess.some(sa => sa.role === "arbitraryIdteacher" || sa.role === "arbitraryIdAdmin")
  );
  const updatedUsers = [];
  teacherUsers.forEach(user => {
    const updatedUser = {
      ...user
    };
    updatedUser.profile.orgid = targetOrgId;
    updatedUser.profile.siteAccess = [...demoUser.profile.siteAccess];
    updatedUsers.push(updatedUser);
  });

  const userUpdatePromises = [];
  updatedUsers.forEach(user => {
    userUpdatePromises.push(
      new Promise(async resolve => {
        const userId = user._id;
        delete user._id;
        await usersDb.update({ _id: userId }, { $set: user });
        resolve();
      })
    );
  });

  await Promise.all(userUpdatePromises);

  console.log("Successfully migrated users");

  dbConnection.close();
  process.exit(0);
}

run();
