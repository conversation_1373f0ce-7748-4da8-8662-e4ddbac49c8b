import { DISTRICT_ID, DISTRICT_NAME, SCHOOL_NAME, SCHOOL_NUMBER_OFFSET, USER } from "../constants";
import { keyBy } from "lodash";

const ObjectId = require("mongodb").ObjectId;
const faker = require("faker");
const { cloneDeep } = require("lodash");

const scrubStudentsWithoutAssessmentResultsDependencies = ({ students, newOrgId, shouldAnonymize = true }) => {
  return students.map(student => {
    const currentStudent = cloneDeep(student);

    currentStudent.bak_id = student._id;
    currentStudent._id = new ObjectId().toString();
    currentStudent.orgid = newOrgId;
    if (shouldAnonymize) {
      let grade = student.grade;
      if (grade === "K") {
        grade = 0;
      } else if (grade === "HS") {
        grade = 9;
      }
      const date = new Date(Date.UTC(2019, 1, 1));
      date.setFullYear(date.getFullYear() - 6 - grade);
      currentStudent.demographic.birthDate = date.toISOString().slice(0, 10);
      // NOTE: the db format for the timestamp below includes a floating point 0 like so: 1133568000000.0, the conversion below returns an Int
      currentStudent.demographic.birthDateTimeStamp = parseFloat(date.valueOf());

      currentStudent.identity.name.firstName = faker.name.firstName();
      currentStudent.identity.name.lastName = faker.name.lastName();
      currentStudent.identity.name.middleName = faker.name.firstName();
      currentStudent.identity.identification.localId = `l${faker.random.uuid().slice(0, 5)}`;
      currentStudent.identity.identification.stateId = `s${faker.random.uuid().slice(0, 8)}`;
    }
    return currentStudent;
  });
};

const anonymizeSites = (sites, newOrgId, shouldAnonymize = true) => {
  return sites.map((site, index) => {
    const schoolNo = index + 1 + SCHOOL_NUMBER_OFFSET;
    const currentSite = cloneDeep(site);
    currentSite.bak_id = site._id;
    currentSite.orgid = newOrgId;
    currentSite._id = new ObjectId().toString();
    if (shouldAnonymize) {
      currentSite.stateInformation.districtNumber = DISTRICT_ID;
      currentSite.stateInformation.districtName = DISTRICT_NAME;
      currentSite.stateInformation.schoolNumber = `${schoolNo}`;
      currentSite.stateInformation.localSchoolNumber = `${schoolNo}`;
      currentSite.name = `${SCHOOL_NAME} ${schoolNo}`;
    }
    return currentSite;
  });
};

const anonymizeUsers = ({ users, orgid, schoolYear, anonymizedSites, shouldAnonymize = true }) => {
  const anonymizedSitesByBakId = keyBy(anonymizedSites, "bak_id");
  return users.map(user => {
    let anonymizedUser = cloneDeep(USER);
    anonymizedUser.bak_id = user._id;
    anonymizedUser._id = new ObjectId().toString();
    if (shouldAnonymize) {
      anonymizedUser.profile.name = {
        first: faker.name.firstName(),
        last: faker.name.lastName(),
        middle: faker.name.firstName()
      };
      anonymizedUser.profile.localId = `l${faker.random.uuid().slice(0, 5)}`;
    } else {
      anonymizedUser.profile.name = cloneDeep(user.profile.name);
      anonymizedUser.profile.localId = user.profile.localId;
    }
    anonymizedUser.emails[0].address = `${anonymizedUser.profile.name.first}.${anonymizedUser.profile.name.last}@${orgid}.org`;
    anonymizedUser.profile.orgid = orgid;
    let siteIds = anonymizedSites.map(as => as.bak_id);
    let currentSiteAccess = user.profile.siteAccess;
    anonymizedUser.profile.siteAccess = currentSiteAccess
      .filter(sa => sa.schoolYear === schoolYear && siteIds.includes(sa.siteId))
      .map(sa => ({ ...sa, siteId: anonymizedSitesByBakId[sa.siteId]._id }));
    return anonymizedUser;
  });
};

const scrubBenchmarkWindows = (benchmarkWindows, anonymizedSites) => {
  const relevantBenchmarkWindows = benchmarkWindows.filter(bw =>
    anonymizedSites.find(site => site.bak_id === bw.siteId)
  );
  return relevantBenchmarkWindows.map(bw => {
    const currentBenchmarkWindow = cloneDeep(bw);
    const correspondingSite = anonymizedSites.find(site => site.bak_id === bw.siteId);
    currentBenchmarkWindow._id = new ObjectId().toString();
    currentBenchmarkWindow.siteId = correspondingSite._id;
    currentBenchmarkWindow.orgid = correspondingSite.orgid;

    return currentBenchmarkWindow;
  });
};

const scrubGroupsWithoutAssessmentResultsDependencies = ({
  groups,
  anonymizedSites,
  anonymizedUsers,
  shouldAnonymize = true,
  customUser
}) => {
  const anonymizedGroups = [];
  anonymizedSites.forEach(site => {
    const groupsForSite = groups.filter(group => group.siteId === site.bak_id);
    anonymizedGroups.push(
      ...anonymizeGroupsForSite({
        groups: groupsForSite,
        site: site,
        anonymizedUsers: anonymizedUsers,
        shouldAnonymize: shouldAnonymize,
        customUser
      })
    );
  });
  return anonymizedGroups;
};

function anonymizeGroupsForSite({ groups, site, anonymizedUsers, shouldAnonymize, customUser }) {
  return groups.map(group => {
    const currentGroup = cloneDeep(group);
    currentGroup.bak_id = group._id;
    currentGroup._id = new ObjectId().toString();
    currentGroup.orgid = site.orgid;
    currentGroup.siteId = site._id;
    let teacher;
    if (customUser) {
      teacher = customUser;
      currentGroup.ownerIds = [customUser._id];
    } else {
      const anonymizedUsersById = keyBy(anonymizedUsers, "bak_id");
      teacher = anonymizedUsersById[currentGroup.ownerIds[0]];
      currentGroup.ownerIds = [...currentGroup.ownerIds.map(id => anonymizedUsersById[id]._id)];
    }

    if (shouldAnonymize) {
      currentGroup.sectionId = `sec${faker.random.uuid().slice(0, 8)}`;
      currentGroup.name = `${teacher.profile.name.last}'s grade (${currentGroup.sectionId})`;
    }
    return currentGroup;
  });
}

const scrubStudentGroupEnrollments = (studentGroupEnrollments, anonymizedStudents, anonymizedGroups) => {
  const anonymizedStudentGroupEnrollments = [];
  anonymizedStudents.forEach(student => {
    const studentEnrollments = studentGroupEnrollments.filter(sge => sge.studentId === student.bak_id);
    const relevantGroups = anonymizedGroups.filter(group =>
      studentEnrollments.find(sge => sge.studentGroupId === group.bak_id)
    );
    anonymizedStudentGroupEnrollments.push(
      ...anonymizeEnrollmentsForStudent(studentEnrollments, student, relevantGroups)
    );
  });
  return anonymizedStudentGroupEnrollments;
};

function anonymizeEnrollmentsForStudent(studentEnrollments, anonymizedStudent, anonymizedGroups) {
  const relevantStudentEnrollments = studentEnrollments.filter(se =>
    anonymizedGroups.find(group => se.studentGroupId === group.bak_id)
  );
  return relevantStudentEnrollments.map(enrollment => {
    const currentEnrollment = cloneDeep(enrollment);
    const currentGroup = anonymizedGroups.find(group => enrollment.studentGroupId === group.bak_id);
    currentEnrollment.bak_id = enrollment._id;
    currentEnrollment._id = new ObjectId().toString();
    currentEnrollment.orgid = currentGroup.orgid;
    currentEnrollment.siteId = currentGroup.siteId;
    currentEnrollment.studentGroupId = currentGroup._id;
    currentEnrollment.studentId = anonymizedStudent._id;
    return currentEnrollment;
  });
}

const scrubAssessmentResults = (assessmentResults, anonymizedStudents, anonymizedGroups) => {
  const anonymizedAssessmentResults = [];
  anonymizedGroups.forEach(group => {
    const assessmentResultsForGroup = assessmentResults.filter(ar => ar.studentGroupId === group.bak_id);
    anonymizedAssessmentResults.push(
      ...anonymizeAssessmentResultsForGroup(assessmentResultsForGroup, anonymizedStudents, group)
    );
  });

  return anonymizedAssessmentResults;
};

function anonymizeAssessmentResultsForGroup(assessmentResults, anonymizedStudents, anonymizedGroup) {
  return assessmentResults.map(assessmentResult => {
    const currentAssessmentResult = cloneDeep(assessmentResult);
    currentAssessmentResult.bak_id = assessmentResult._id;
    currentAssessmentResult._id = new ObjectId().toString();
    currentAssessmentResult.studentGroupId = anonymizedGroup._id;
    currentAssessmentResult.orgid = anonymizedGroup.orgid;

    if (assessmentResult.type === "individual") {
      const correspondingStudent = anonymizedStudents.find(student => student.bak_id === assessmentResult.studentId);
      currentAssessmentResult.studentId = correspondingStudent._id;
      currentAssessmentResult.individualSkills.assessmentResultId = currentAssessmentResult._id;
    }

    currentAssessmentResult.scores = assessmentResult.scores.map(score => {
      const correspondingStudent = anonymizedStudents.find(student => student.bak_id === score.studentId);
      const currentScore = cloneDeep(score);
      currentScore._id = new ObjectId().toString();
      currentScore.orgid = anonymizedGroup.orgid;
      currentScore.siteId = anonymizedGroup.siteId;
      currentScore.studentId = correspondingStudent._id;
      return currentScore;
    });

    if (assessmentResult.classwideResults) {
      currentAssessmentResult.classwideResults.studentIdsNotMeetingTarget = assessmentResult.classwideResults.studentIdsNotMeetingTarget.map(
        studentId => {
          const correspondingStudent = anonymizedStudents.find(student => student.bak_id === studentId);
          return correspondingStudent._id;
        }
      );
    }
    if (assessmentResult.measures) {
      currentAssessmentResult.measures = assessmentResult.measures.map(measure => {
        const currentMeasure = cloneDeep(measure);
        currentMeasure.studentResults = measure.studentResults.map(studentResult => {
          const currentStudentResult = cloneDeep(studentResult);
          const correspondingStudent = anonymizedStudents.find(student => student.bak_id === studentResult.studentId);
          currentStudentResult.studentId = correspondingStudent._id;
          currentStudentResult.firstName = correspondingStudent.identity.name.firstName;
          currentStudentResult.lastName = correspondingStudent.identity.name.lastName;
          return currentStudentResult;
        });
        return currentMeasure;
      });
    }

    return currentAssessmentResult;
  });
}

const scrubStudentGroupPropertiesDependentOnAssessmentResults = (
  studentGroups,
  anonymizedAssessmentResults,
  anonymizedStudents
) => {
  return studentGroups.map(group => {
    const groupWithAnonymizedHistory = cloneDeep(group);
    if (group.history) {
      groupWithAnonymizedHistory.history = anonymizeHistory(
        group.history,
        anonymizedAssessmentResults,
        anonymizedStudents
      );
    }

    if (group.currentAssessmentResultIds) {
      groupWithAnonymizedHistory.currentAssessmentResultIds = group.currentAssessmentResultIds.map(
        assessmentResultId => {
          const correspondingAssessmentResult = anonymizedAssessmentResults.find(
            ar => ar.bak_id === assessmentResultId
          );
          return correspondingAssessmentResult._id;
        }
      );
    }

    if (group.currentClasswideSkill && group.currentClasswideSkill.assessmentResultId) {
      const correspondingAssessmentResult = anonymizedAssessmentResults.find(
        ar => ar.bak_id === group.currentClasswideSkill.assessmentResultId
      );
      groupWithAnonymizedHistory.currentClasswideSkill.assessmentResultId = correspondingAssessmentResult._id;
    }
    return groupWithAnonymizedHistory;
  });
};

const scrubStudentPropertiesDependentOnAssessmentResults = (students, anonymizedAssessmentResults) => {
  return students.map(student => {
    const currentStudentWithAnonymizedHistory = cloneDeep(student);
    if (student.history) {
      currentStudentWithAnonymizedHistory.history = anonymizeHistory(student.history, anonymizedAssessmentResults, [
        student
      ]);
    }

    if (student.currentSkill && student.currentSkill.assessmentResultId) {
      const correspondingAssessmentResult = anonymizedAssessmentResults.find(
        ar => ar.bak_id === student.currentSkill.assessmentResultId
      );
      currentStudentWithAnonymizedHistory.currentSkill.assessmentResultId = correspondingAssessmentResult._id;
    }

    return currentStudentWithAnonymizedHistory;
  });
};

function anonymizeHistory(history, anonymizedAssessmentResults, anonymizedStudents) {
  return history.map(historyItem => {
    const currentAssessmentResult = cloneDeep(historyItem);
    const correspondingAssessmentResult = anonymizedAssessmentResults.find(
      ar => ar.bak_id === historyItem.assessmentResultId
    );
    currentAssessmentResult.assessmentResultId = correspondingAssessmentResult._id;

    currentAssessmentResult.assessmentResultMeasures = historyItem.assessmentResultMeasures.map(measure => {
      const currentMeasure = cloneDeep(measure);
      currentMeasure.studentResults = measure.studentResults.map(studentResult => {
        const currentStudentResult = cloneDeep(studentResult);
        const correspondingStudent = anonymizedStudents.find(student => student.bak_id === studentResult.studentId);
        currentStudentResult.studentId = correspondingStudent._id;
        currentStudentResult.firstName = correspondingStudent.identity.name.firstName;
        currentStudentResult.lastName = correspondingStudent.identity.name.lastName;
        return currentStudentResult;
      });
      return currentMeasure;
    });

    if (currentAssessmentResult.enrolledStudentIds) {
      currentAssessmentResult.enrolledStudentIds = historyItem.enrolledStudentIds.map(studentId => {
        const correspondingStudent = anonymizedStudents.find(student => student.bak_id === studentId);
        return correspondingStudent._id;
      });
    }

    return currentAssessmentResult;
  });
}

const removeHelperKeysFor = list => {
  const helperKeys = ["bak_id"];
  return list.map(item => {
    const currentItem = cloneDeep(item);
    helperKeys.forEach(key => delete currentItem[key]);
    return currentItem;
  });
};

export {
  anonymizeSites,
  scrubBenchmarkWindows,
  scrubStudentsWithoutAssessmentResultsDependencies,
  scrubGroupsWithoutAssessmentResultsDependencies,
  scrubStudentGroupEnrollments,
  scrubAssessmentResults,
  scrubStudentGroupPropertiesDependentOnAssessmentResults,
  scrubStudentPropertiesDependentOnAssessmentResults,
  removeHelperKeysFor,
  anonymizeUsers
};
