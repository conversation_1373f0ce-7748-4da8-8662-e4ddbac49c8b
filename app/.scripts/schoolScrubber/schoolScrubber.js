/*
  INTRODUCTION

  This is a script for extracting data from the production database and faking any sensitive data while preserving the relationships between collections for the new dataset.
  This script works for one organization at a time.
  Currently, the following collections and properties are scrubbed:
   - Organization
      - _id
      - name
      - details
   - Sites
      - _id
      - orgid
      - stateInformation
      - name
   - BenchmarkWindows
      - _id
      - siteId
      - orgid
   - Students
      - _id
      - orgid
      - demographic
        - birthDate
        - birthDateTimeStamp
      - identity
        - name
          - firstName
          - lastName
          - middleName
        - identification
          - localId
          - stateId
      - history
      - currentSkill
        - assessmentResultId
   - StudentGroups
      - _id
      - orgid
      - siteId
      - ownerIds
      - courseId
      - sectionId
      - name
      - history
      - currentAssessmentResultIds
      - currentClasswideSkill
        - assessmentResultId
   - StudentGroupEnrollments
      - _id
      - orgid
      - siteId
      - studentGroupId
      - studentId
   - AssessmentResults
      - _id
      - orgid
      - siteId
      - studentId
      - classwideResults
        - studentIdsNotMeetingTargets
      - measures
        - studentResults
          - studentId
          - firstName
          - lastName

   Data is scrubbed using mongo<PERSON><PERSON>'s id generator, fakerjs for personal data and arbitrary values for other properties

   USAGE

   1. Run npm install in the schoolScrubber directory
   2. Set env variables
      a) MONGO_PROD_URL -- this is where data is pulled from
      b) MONGO_TARGET_URL -- this is where scrubbed data is inserted
      c) SCHOOL_YEAR
   3. Run the script with the following command
      npm start -- --orgId=YOUR_ORG_ID --targetOrgId=TARGET_ORG_ID
   4. The data can be accessed with a newly created demo user.
      - Use <NAME_EMAIL>
      - Use the same password as for other users used for development

   Note: The script will exit upon error
 */

import minimist from "minimist";
import { USER } from "./constants";
import {
  scrubAssessmentResults,
  scrubBenchmarkWindows,
  scrubGroupsWithoutAssessmentResultsDependencies,
  anonymizeSites,
  scrubStudentGroupEnrollments,
  scrubStudentGroupPropertiesDependentOnAssessmentResults,
  scrubStudentPropertiesDependentOnAssessmentResults,
  scrubStudentsWithoutAssessmentResultsDependencies,
  removeHelperKeysFor,
  anonymizeUsers
} from "./utils/anonymizationHelpers";
import { cloneDeep, flatMapDepth, uniq } from "lodash";

const MONGO_PROD_URL = process.env.MONGO_PROD_URL; // Provide mongo URL to production DB
const MONGO_TARGET_URL = process.env.MONGO_TARGET_URL; // Provide mongo URL to target DB (demo)

const MongoClient = require("mongodb").MongoClient;
const chalk = require("chalk");
const ObjectId = require("mongodb").ObjectId;

const SCHOOL_YEAR = parseInt(process.env.SCHOOL_YEAR) || 2018;

async function run() {
  if (!MONGO_PROD_URL) {
    console.log(chalk.red("MONGO_PROD_URL not set in env"));
    process.exit(0);
  }
  if (!MONGO_TARGET_URL) {
    console.log(chalk.red("MONGO_TARGET_URL not set in env"));
    process.exit(1);
  }

  const argv = minimist(process.argv.slice(2));
  const {
    orgId,
    targetOrgId,
    siteIds: siteIdsString,
    shouldMergeSites: shouldMergeSitesArg,
    shouldCreateNewSites: shouldCreateNewSitesArg,
    shouldAnonymize: shouldAnonymizeArg = "true"
  } = argv;
  const shouldCreateNewSites = shouldCreateNewSitesArg && JSON.parse(shouldCreateNewSitesArg);
  const shouldMergeSites = shouldMergeSitesArg && JSON.parse(shouldMergeSitesArg);
  const shouldAnonymize = shouldAnonymizeArg && JSON.parse(shouldAnonymizeArg);
  const siteIds = siteIdsString && siteIdsString.split(",");

  if (!orgId) {
    console.log(chalk.red("Please specify Organization to copy, pass organization Id as a parameter"));
    process.exit(0);
  }
  const importDb = await MongoClient.connect(MONGO_PROD_URL);
  console.log(`Connected successfully to production DB`);
  const organizationDb = importDb.collection("Organizations");

  const organization = await organizationDb.findOne({ _id: orgId });
  if (!organization) {
    console.log(chalk.red("No Organization with given orgId found. Please check orgId"));
    process.exit(0);
  }

  const assessmentResultsDb = importDb.collection("AssessmentResults");
  const benchmarkWindowsDb = importDb.collection("BenchmarkWindows");
  const studentGroupEnrollmentsDb = importDb.collection("StudentGroupEnrollments");
  const studentGroupsDb = importDb.collection("StudentGroups");
  const studentsDb = importDb.collection("Students");
  const sitesDb = importDb.collection("Sites");
  const usersDb = importDb.collection("users");

  const query = { orgid: orgId, schoolYear: SCHOOL_YEAR };
  let assessmentResults = await assessmentResultsDb.find(query).toArray();
  let benchmarkWindows = await benchmarkWindowsDb.find(query).toArray();
  let studentGroupEnrollments = await studentGroupEnrollmentsDb
    .find(siteIds ? { ...query, siteId: { $in: siteIds } } : query)
    .toArray();
  let studentGroups = await studentGroupsDb.find(siteIds ? { ...query, siteId: { $in: siteIds } } : query).toArray();

  const owners = uniq(
    flatMapDepth(
      studentGroups,
      sg => {
        return sg.secondaryTeachers ? [sg.ownerIds, sg.secondaryTeachers] : [sg.ownerIds];
      },
      2
    )
  );
  const studentIds = Array.from(new Set(studentGroupEnrollments.map(sge => sge.studentId)));
  let students = await studentsDb.find({ ...query, _id: { $in: studentIds } }).toArray();
  let users = await usersDb
    .find({
      _id: { $in: owners }
    })
    .toArray();

  let sitesFilter = { orgid: orgId };
  if (siteIds && siteIds.length) {
    sitesFilter = { orgid: orgId, _id: { $in: siteIds } };
  }
  let sites = await sitesDb.find(sitesFilter).toArray();
  let oldSiteIds = sites.map(site => site._id);

  if (shouldMergeSites) {
    sites = [sites[0]];
    oldSiteIds = [oldSiteIds[0]];
    assessmentResults = assessmentResults.map(am => ({
      ...am,
      scores: am.scores.map(score => ({ ...score, siteId: oldSiteIds[0] }))
    }));
    benchmarkWindows = benchmarkWindows.map(bw => ({ ...bw, siteId: oldSiteIds[0] }));
    studentGroupEnrollments = studentGroupEnrollments.map(sge => ({ ...sge, siteId: oldSiteIds[0] }));
    studentGroups = studentGroups.map(sg => ({ ...sg, siteId: oldSiteIds[0] }));
  }
  console.log("assessmentResults.length: ", assessmentResults.length);
  console.log("benchmarkWindows.length: ", benchmarkWindows.length);
  console.log("studentGroupEnrollments.length: ", studentGroupEnrollments.length);
  console.log("studentGroups.length: ", studentGroups.length);
  console.log("students.length: ", students.length);
  console.log("users.length: ", users.length);
  importDb.close();

  let uploadDb;
  try {
    uploadDb = await MongoClient.connect(MONGO_TARGET_URL);
  } catch (e) {
    console.log("e:", e);
  }

  const uploadOrganizationDb = uploadDb.collection("Organizations");
  const uploadSitesDb = uploadDb.collection("Sites");

  let targetOrg;
  if (targetOrgId) {
    targetOrg = await uploadOrganizationDb.findOne({ _id: targetOrgId });
    if (!shouldCreateNewSites) {
      sites = await uploadSitesDb.find({ orgid: targetOrgId }).toArray();
    }
  } else {
    targetOrg = createOrg(organization);
  }
  const newOrgId = targetOrg._id;
  let anonymizedSites;
  if (targetOrgId && !shouldCreateNewSites) {
    anonymizedSites = sites.map((site, index) => ({ ...site, bak_id: oldSiteIds[index] }));
  } else {
    console.log("Anonymizing sites...");
    anonymizedSites = anonymizeSites(sites, newOrgId, shouldAnonymize);
  }
  console.log("Scrubbing benchmark windows...");
  const anonymizedBenchmarkWindows = scrubBenchmarkWindows(benchmarkWindows, anonymizedSites);
  console.log("Scrubbing assessments...");
  const anonymizedStudentsWithoutAssessmentResultsDependencies = scrubStudentsWithoutAssessmentResultsDependencies({
    students: students,
    newOrgId: newOrgId,
    shouldAnonymize
  });
  console.log("Anonymizing users...");
  const anonymizedUsers = anonymizeUsers({
    users,
    orgid: newOrgId,
    schoolYear: SCHOOL_YEAR,
    anonymizedSites,
    shouldAnonymize
  });
  console.log("Scrubbing student groups...");
  const anonymizedGroupsWithoutAssessmentResultsDependencies = scrubGroupsWithoutAssessmentResultsDependencies({
    groups: studentGroups,
    anonymizedUsers,
    anonymizedSites,
    shouldAnonymize
  });
  console.log("Scrubbing assessments...");
  const anonymizedStudentGroupEnrollments = scrubStudentGroupEnrollments(
    studentGroupEnrollments,
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedGroupsWithoutAssessmentResultsDependencies
  );
  console.log("Scrubbing assessment results...");
  const anonymizedAssessmentResults = scrubAssessmentResults(
    assessmentResults,
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedGroupsWithoutAssessmentResultsDependencies
  );
  console.log("Finishing scrubbing data...");
  const completelyAnonymizedGroups = scrubStudentGroupPropertiesDependentOnAssessmentResults(
    anonymizedGroupsWithoutAssessmentResultsDependencies,
    anonymizedAssessmentResults,
    anonymizedStudentsWithoutAssessmentResultsDependencies
  );
  const completelyAnonymizedStudents = scrubStudentPropertiesDependentOnAssessmentResults(
    anonymizedStudentsWithoutAssessmentResultsDependencies,
    anonymizedAssessmentResults
  );
  console.log("Removing helper keys...");

  const studentsWithoutHelperKeys = removeHelperKeysFor(completelyAnonymizedStudents);
  const groupsWithoutHelperKeys = removeHelperKeysFor(completelyAnonymizedGroups);
  const assessmentResultsWithoutHelperKeys = removeHelperKeysFor(anonymizedAssessmentResults);
  const sitesWithoutHelperKeys = removeHelperKeysFor(anonymizedSites);
  const studentGroupEnrollmentsWithoutHelperKeys = removeHelperKeysFor(anonymizedStudentGroupEnrollments);
  const usersWithoutHelperKeys = removeHelperKeysFor(anonymizedUsers);
  const uploadAssessmentResultsDb = uploadDb.collection("AssessmentResults");
  const uploadBenchmarkWindowsDb = uploadDb.collection("BenchmarkWindows");
  const uploadStudentGroupEnrollmentsDb = uploadDb.collection("StudentGroupEnrollments");
  const uploadStudentGroupsDb = uploadDb.collection("StudentGroups");
  const uploadStudentsDb = uploadDb.collection("Students");
  const uploadUsersDb = uploadDb.collection("users");
  const uploadStudentsBySkillDb = uploadDb.collection("StudentsBySkill");
  const uploadAssessmentsDb = uploadDb.collection("Assessments");
  const uploadGroupedAssessmentsDb = uploadDb.collection("GroupedAssessments");
  let uploadGroupedAssessments = await uploadGroupedAssessmentsDb.find().toArray();

  console.log("Generating studentsBySkill collection...");

  const studentsBySkill = createStudentsBySkill(sitesWithoutHelperKeys, uploadGroupedAssessments);

  console.log("Creating users...");
  const existingAdminUser = await uploadUsersDb.findOne({
    "profile.orgid": newOrgId,
    "profile.siteAccess.role": "arbitraryIdadmin"
  });
  const user = createUserFor(newOrgId, anonymizedSites, existingAdminUser);
  if (!targetOrgId) {
    await uploadUsersDb.findOneAndDelete({ _id: user._id });
  } else {
    await uploadUsersDb.update(
      { _id: user._id, "profile.orgid": newOrgId },
      { $push: { "profile.siteAccess": { $each: user.profile.siteAccess } } }
    );
  }

  // uploadDb.close();
  // process.exit(0); // prompt doesn't properly work with async/await and causes this function to never return.

  try {
    if (assessmentResultsWithoutHelperKeys.length) {
      console.log("Inserting assessments...");
      await uploadAssessmentResultsDb.insertMany(assessmentResultsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadAssessmentResultsDb failed:", error);
    process.exit(0);
  }
  try {
    if (anonymizedBenchmarkWindows.length) {
      console.log("Inserting benchmark windows...");
      await uploadBenchmarkWindowsDb.insertMany(anonymizedBenchmarkWindows);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadBenchmarkWindowsDb failed:", error);
    process.exit(0);
  }
  try {
    if (studentGroupEnrollmentsWithoutHelperKeys.length) {
      console.log("Inserting student group enrollments...");
      await uploadStudentGroupEnrollmentsDb.insertMany(studentGroupEnrollmentsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentGroupEnrollmentsDb failed:", error);
    process.exit(0);
  }
  try {
    if (groupsWithoutHelperKeys.length) {
      console.log("Inserting student groups...");
      await uploadStudentGroupsDb.insertMany(groupsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentGroupsDb failed:", error);
    process.exit(0);
  }
  try {
    if (studentsWithoutHelperKeys.length) {
      console.log("Inserting students...");
      await uploadStudentsDb.insertMany(studentsWithoutHelperKeys);
    }
  } catch (error) {
    console.log("Inserting scrubbed data to uploadStudentsDb failed:", error);
    process.exit(0);
  }
  if (targetOrgId && shouldCreateNewSites) {
    try {
      if (sitesWithoutHelperKeys.length) {
        console.log("Inserting sites...");
        await uploadSitesDb.insertMany(sitesWithoutHelperKeys);
      }
    } catch (error) {
      console.log("Inserting scrubbed data to uploadSitesDb failed:", error);
      process.exit(0);
    }
    try {
      console.log("Inserting users...");
      await uploadUsersDb.insertMany([...usersWithoutHelperKeys]);
    } catch (error) {
      console.log("Inserting scrubbed data to uploadUsersDb failed:", error);
      process.exit(0);
    }
  }
  if (!targetOrgId) {
    try {
      if (sitesWithoutHelperKeys.length) {
        console.log("Inserting sites...");
        await uploadSitesDb.insertMany(sitesWithoutHelperKeys);
      }
    } catch (error) {
      console.log("Inserting scrubbed data to uploadSitesDb failed:", error);
      process.exit(0);
    }
    try {
      console.log("Inserting users...");
      await uploadUsersDb.insertMany([user, ...usersWithoutHelperKeys]);
    } catch (error) {
      console.log("Inserting scrubbed data to uploadUsersDb failed:", error);
      process.exit(0);
    }
    try {
      console.log("Inserting organizations...");
      await uploadOrganizationDb.insert(targetOrg);
    } catch (error) {
      console.log("Inserting scrubbed data to uploadOrganizationDb failed:", error);
      process.exit(0);
    }
  }
  if (targetOrgId && !shouldCreateNewSites) {
    const targetExistingSites = await uploadSitesDb.find({ orgid: targetOrgId }).toArray();
    const sitesUpdatePromises = [];
    targetExistingSites.forEach(site => {
      if (site.schoolYear < SCHOOL_YEAR) {
        sitesUpdatePromises.push(
          new Promise(resolve => {
            uploadSitesDb.update({ _id: site._id }, { $set: { schoolYear: SCHOOL_YEAR } });
            resolve();
          })
        );
      }
    });

    await Promise.all(sitesUpdatePromises);
  }

  try {
    console.log("Populating students by skill...");
    await uploadStudentsBySkillDb.insert(studentsBySkill);
    const studentsWithIndividualInterventions = await uploadStudentsDb
      .find({
        orgid: newOrgId,
        currentSkill: { $exists: true }
      })
      .toArray();
    await populateStudentsBySkill(
      studentsWithIndividualInterventions,
      uploadAssessmentsDb,
      uploadGroupedAssessmentsDb,
      uploadStudentsBySkillDb,
      uploadStudentGroupEnrollmentsDb
    );
  } catch (error) {
    console.log("Inserting scrubbed data to uploadOrganizationDb failed:", error);
    process.exit(0);
  }

  console.log("Successfully inserted scrubbed data to target DB");

  uploadDb.close();
  process.exit(0); // prompt doesn't properly work with async/await and causes this function to never return.
}

const createUserFor = (orgid, sites, existingUser) => {
  const user = cloneDeep(existingUser || USER);
  user.profile.orgid = orgid;
  user.profile.siteAccess = addSiteAccessFor(sites);
  return user;
};

function addSiteAccessFor(sites, isAdmin = true) {
  return sites.map(site => {
    return {
      role: isAdmin ? "arbitraryIdadmin" : "arbitraryIdteacher",
      siteId: site._id,
      schoolYear: SCHOOL_YEAR,
      isActive: true,
      isDefault: true
    };
  });
}

const createOrg = org => {
  const newOrg = cloneDeep(org);
  newOrg._id = ObjectId().toString();
  newOrg.name = `DEMO_${getDate()}`;
  newOrg.details = {};
  return newOrg;
};

function getDate() {
  const date = new Date();
  return date.getMonth() + 1 + "/" + date.getDate() + "/" + date.getFullYear();
}

function createStudentsBySkill(sites, groupedAssessments) {
  const studentsBySiteTemplate = {
    studentsBelowInstructionalTarget: [],
    studentsBelowMasteryTarget: [],
    studentsWithoutSkillHistory: []
  };
  const documentsToInsert = [];
  sites.forEach(function(site) {
    groupedAssessments.forEach(function(ga) {
      const studentBySite = Object.assign({}, studentsBySiteTemplate, {
        _id: new ObjectId().toString(),
        skillName: ga.skillName,
        assessmentGroupId: ga._id,
        siteId: site._id
      });
      documentsToInsert.push(studentBySite);
    });
  });
  return documentsToInsert;
}

async function populateStudentsBySkill(
  studentsWithIndividualInterventions,
  assessmentsDb,
  groupedAssessmentsDb,
  studentsBySkillDb, // This handle is for db that you want to upload to
  studentGroupEnrollmentsDb
) {
  for (const student of studentsWithIndividualInterventions) {
    if (student.currentSkill.assessmentId) {
      const sge = await studentGroupEnrollmentsDb.findOne({
        studentId: student._id,
        schoolYear: SCHOOL_YEAR,
        isActive: true
      });
      const assessment = await assessmentsDb.findOne({ _id: student.currentSkill.assessmentId });
      if (assessment && sge) {
        const assessmentGroup = await groupedAssessmentsDb.findOne({
          assessmentMeasures: assessment.monitorAssessmentMeasure
        });
        if (assessmentGroup) {
          if (!student.history || !student.history.length) {
            await studentsBySkillDb.update(
              {
                siteId: sge.siteId,
                assessmentGroupId: assessmentGroup._id
              },
              { $addToSet: { studentsWithoutSkillHistory: student._id } }
            );
          } else {
            const latestHistoryEntry = student.history[0];
            const updateQuery = { assessmentGroupId: assessmentGroup._id, siteId: sge.siteId };
            if (latestHistoryEntry.assessmentId === student.currentSkill.assessmentId) {
              const latestScore = parseInt(latestHistoryEntry.assessmentResultMeasures[0].studentResults[0].score);
              const scoreTargets = latestHistoryEntry.assessmentResultMeasures[0].targetScores;
              if (latestScore < scoreTargets[0]) {
                await studentsBySkillDb.update(updateQuery, {
                  $addToSet: { studentsBelowInstructionalTarget: student._id }
                });
              } else if (latestScore < scoreTargets[1]) {
                await studentsBySkillDb.update(updateQuery, {
                  $addToSet: { studentsBelowMasteryTarget: student._id }
                });
              }
            } else {
              await studentsBySkillDb.update(updateQuery, { $addToSet: { studentsWithoutSkillHistory: student._id } });
            }
          }
        }
      }
    }
  }
}

run();
