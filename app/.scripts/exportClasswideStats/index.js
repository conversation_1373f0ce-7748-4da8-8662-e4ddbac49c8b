const fs = require("fs");
const _flatten = require("lodash/flatten");
const _get = require("lodash/get");
const Json2csvParser = require("json2csv").Parser;
const MongoClient = require("mongodb").MongoClient;
const Excel = require("exceljs");
const getClasswideInterventionStats = require("./lib/getClasswideInterventionStats").getClasswideInterventionStats;

const targetUrl = process.env.TARGET_URL || "mongodb://localhost:3001"; // local app mongodb connection string is default
const targetDBName = process.env.TARGET_DB || "meteor"; // local app db is default

const args = process.argv.slice(2);
let shouldAnonymize = false;
if (args.includes("anonymize")) {
  console.log("ANONYMIZE SET!");
  shouldAnonymize = true;
}

let saveAsExcel = false;
if (args.includes("saveAsExcel")) {
  console.log("EXCEL OUTPUT SET");
  saveAsExcel = true;
}

// I wasn't able to find more than 15 scores for a single assessment and we need to hard-code the headers here
const fields = [
  "grade",
  "Local Student ID",
  "Classwide Skill Name",
  "Classwide Sequence Position",
  "First Skill score",
  "Final Skill score",
  "ROI",
  "Teacher ID",
  "Class Section ID",
  "School ID",
  "Number of scores",
  "Number of weeks spent",
  "Score #1",
  "Score #2",
  "Score #3",
  "Score #4",
  "Score #5",
  "Score #6",
  "Score #7",
  "Score #8",
  "Score #9",
  "Score #10",
  "Score #11",
  "Score #12",
  "Score #13",
  "Score #14",
  "Score #15",
  "Score #16"
];

const grades = ["K", "01", "02", "03", "04", "05", "06", "07", "08"];
const schoolYear = 2019;

let targetConnection;

async function run() {
  targetConnection = await MongoClient.connect(targetUrl);
  console.log(`\nConnected successfully to Target DB`);
  const targetDB = targetConnection.db(targetDBName);
  const studentGroupsCollection = targetDB.collection("StudentGroups");
  const studentGroupEnrollmentsCollection = targetDB.collection("StudentGroupEnrollments");
  const studentsCollection = targetDB.collection("Students");
  const rulesCollection = targetDB.collection("Rules");
  const usersCollection = targetDB.collection("users");
  const sitesCollection = targetDB.collection("Sites");
  for (const grade of grades) {
    const groupsWithClasswideIntervention = await getGroupsWithClasswideHistory(studentGroupsCollection, grade);
    const studentGroupIds = groupsWithClasswideIntervention.map(g => g._id);
    const studentIdsToLocalIds = await getStudentIdsToLocalIdsMap({
      studentGroupEnrollmentsCollection,
      studentGroupIds,
      studentsCollection,
      grade
    });
    const gradeClasswideSequence = await getGradeClasswideSequence(rulesCollection, grade);
    const users = await getGroupOwnerIds(groupsWithClasswideIntervention, usersCollection);
    const sites = await getSitesData(groupsWithClasswideIntervention, sitesCollection);
    const jsonCollection = getCsvStats({
      studentGroups: groupsWithClasswideIntervention,
      users,
      sites,
      studentIdsToLocalIds,
      classwideSequence: gradeClasswideSequence
    });

    await writeFile(jsonCollection, grade);
  }
  await targetConnection.close();
  console.log("Connection to DB closed.");
}

run().catch(async error => {
  if (error.err) {
    console.log("e", error.err);
  } else {
    console.log("e", error);
  }
  console.log("Closing connections...");
  await targetConnection.close();
});

async function getGroupsWithClasswideHistory(studentGroupsCollection, grade) {
  const query = {
    isActive: true,
    schoolYear: schoolYear,
    "history.type": "classwide",
    grade
  };

  const sgsCursor = await studentGroupsCollection.find(query, {
    projection: {
      history: 1,
      grade: 1,
      sectionId: 1,
      ownerIds: 1,
      siteId: 1
    }
  });
  const totalCount = await sgsCursor.count();
  console.log(`Found ${totalCount} Student Groups with classwide history in grade ${grade}`);
  const studentGroups = await sgsCursor.toArray();
  if (shouldAnonymize) {
    return studentGroups.map(sg => ({ ...sg, anonID: getRandomInt() }));
  }
  return studentGroups;
}

async function getStudentIdsToLocalIdsMap({
  studentGroupEnrollmentsCollection,
  studentGroupIds,
  studentsCollection,
  grade
}) {
  const studentGroupEnrollments = await studentGroupEnrollmentsCollection
    .find({ studentGroupId: { $in: studentGroupIds }, schoolYear: schoolYear }, { projection: { studentId: 1 } })
    .toArray();
  const studentIds = studentGroupEnrollments.map(e => e.studentId);
  const students = await studentsCollection
    .find({ _id: { $in: studentIds } }, { projection: { "identity.identification": 1 } })
    .toArray();
  const studentIdsToLocalIds = {};
  console.log(`Found ${studentIds.length} students with enrollment in grade: ${grade}`);
  if (shouldAnonymize) {
    students.forEach(s => (studentIdsToLocalIds[s._id] = getRandomInt()));
  } else {
    students.forEach(s => (studentIdsToLocalIds[s._id] = s.identity.identification.localId));
  }
  return studentIdsToLocalIds;
}

async function getGradeClasswideSequence(rulesCollection, grade) {
  const gradeClasswideRule = await rulesCollection.findOne({ grade }, { projection: { skills: 1 } });
  return gradeClasswideRule.skills.map(s => s.assessmentName);
}

async function getGroupOwnerIds(studentGroups, usersCollection) {
  const ownerIds = studentGroups.map(g => (g.ownerIds && g.ownerIds.length ? g.ownerIds[0] : "N/A"));
  const users = await usersCollection
    .find({ _id: { $in: ownerIds } }, { projection: { "profile.localId": 1 } })
    .toArray();
  if (shouldAnonymize) {
    return users.map(u => ({
      ...u,
      anonID: getRandomInt()
    }));
  }
  return users;
}

async function getSitesData(studentGroups, sitesCollection) {
  const siteIds = studentGroups.map(g => g.siteId);
  const sites = await sitesCollection
    .find({ _id: { $in: siteIds } }, { projection: { stateInformation: 1 } })
    .toArray();
  if (shouldAnonymize) {
    return sites.map(s => ({ ...s, anonID: getRandomInt() }));
  }
  return sites;
}

function getCsvStats({ studentGroups, users, sites, studentIdsToLocalIds, classwideSequence }) {
  const jsonCollection = [];
  studentGroups.forEach(studentGroup => {
    const classwideHistory = studentGroup.history.filter(item => item.type === "classwide");
    let teacherLocalId = "N/A";
    if (studentGroup.ownerIds && studentGroup.ownerIds.length) {
      const ownerId = studentGroup.ownerIds[0];
      const owner = users.find(u => u._id === ownerId);
      if (shouldAnonymize) {
        teacherLocalId = _get(owner, "anonID", "N/A");
      } else {
        teacherLocalId = _get(owner, "profile.localId", "N/A");
      }
    }
    const school = sites.find(s => s._id === studentGroup.siteId);
    let schoolId = "N/A";
    if (shouldAnonymize) {
      schoolId = _get(school, "anonID", "N/A");
    } else {
      if (school && school.stateInformation) {
        schoolId = school.stateInformation.schoolNumber || school.stateInformation.localSchoolNumber;
      } else if (school && school._id) {
        schoolId = school._id;
      }
    }

    const classwideStats = getClasswideInterventionStats({
      history: classwideHistory,
      grade: studentGroup.grade,
      studentIdsToLocalIds,
      gradeClasswideSequence: classwideSequence,
      teacherLocalId,
      classSectionId: shouldAnonymize ? studentGroup.anonID : studentGroup.sectionId,
      schoolId
    });
    jsonCollection.push(classwideStats);
  });
  return jsonCollection;
}

async function writeFile(jsonCollection, grade) {
  const parser = new Json2csvParser({ fields });
  console.log("Parsing...", jsonCollection.length);
  const flatCollection = _flatten(jsonCollection);
  const outputPath = `./${shouldAnonymize ? "anonymous" : "real"}_classwide_stats_export_grade_${grade}.${
    saveAsExcel ? "xls" : "csv"
  }`;
  console.log("Saving to file...");
  if (saveAsExcel) {
    const workbook = new Excel.Workbook();
    const sheetName = `grade: ${grade}`;
    const worksheet = workbook.addWorksheet(sheetName);
    worksheet.columns = fields.map(f => ({ header: f, key: f }));
    worksheet.addRows(flatCollection);
    await workbook.xlsx.writeFile(outputPath);
  } else {
    const csv = parser.parse(flatCollection);
    console.log("Parsing complete!");
    await fs.writeFile(outputPath, csv, "utf8");
  }
  console.log(`\nGrade: ${grade} saved!\n`);
}

function getRandomInt(min = 1, max = 100000) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
