##### **Use with node version >=10.12.0**

#### The script exports a CSV file containing students statistics in classwide interventions. The script saves classwide_stats_export.csv file in project's directory.

To start working with the script run:
`npm install`

Tests!
- `npm test`

To run in local environment with Meteor and MongoDB on port 3001:
- `npm start`

To run in Production export the following variables in terminal before running the script:
- TARGET_URL - mongo url to DB (e.g. "mongodb://localhost:3001" for local env)
- TARGET_DB - Database name (e.g. "meteor" for local or "springmathapp" for production)

Script Options:
- The script provides possibility to anonymize included IDs by setting them to a random number. To use that feature run the script with:
    - `npm start anonymize`

- The script saves the file to CSV format. If you want to save it to XLS format run the script with:
    - `npm start saveAsExcel`

- Both options can be combined:
    - `npm start anonymize saveAsExcel`
