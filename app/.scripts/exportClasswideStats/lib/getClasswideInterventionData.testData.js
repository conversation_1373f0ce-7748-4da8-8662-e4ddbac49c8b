const FIRST_STUDENT_ID = "firstStudentId";
const SECOND_STUDENT_ID = "secondStudentId";
const FIRST_STUDENT_LOCAL_ID = "123";
const SECOND_STUDENT_LOCAL_ID = "456";

const studentIdsToLocalIds = {
  [FIRST_STUDENT_ID]: FIRST_STUDENT_LOCAL_ID,
  [SECOND_STUDENT_ID]: SECOND_STUDENT_LOCAL_ID
};

const gradeClasswideSequence = [
  "Fact Families: Multiplication/Division 0-12",
  "Find the Least Common Denominator",
  "Simplify Fractions"
];

const groupWithSimplifiedHistory = {
  history: [
    {
      assessmentName: "Simplify Fractions",
      whenEnded: {
        on: 1550675770306.0
      },
      whenStarted: {
        on: 1549898112661.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "22"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "23"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Simplify Fractions",
      whenEnded: {
        on: 1549898112672.0
      },
      whenStarted: {
        on: 1549373718465.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "20"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "18"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Simplify Fractions",
      whenEnded: {
        on: 1549373718475.0
      },
      whenStarted: {
        on: 1547828221835.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "16"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "13"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Simplify Fractions",
      whenStarted: {
        on: 1547046899064.0
      },
      whenEnded: {
        on: 1547828221921.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "14"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "12"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Find the Least Common Denominator",
      whenEnded: {
        on: 1547046899155.0
      },
      whenStarted: {
        on: 1545318655001.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "12"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "18"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Find the Least Common Denominator",
      whenEnded: {
        on: 1545318655012.0
      },
      whenStarted: {
        on: 1544213271219.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "8"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "13"
            }
          ]
        }
      ]
    },
    {
      assessmentName: "Fact Families: Multiplication/Division 0-12",
      whenStarted: {
        on: 1541691419487.0
      },
      whenEnded: {
        on: 1542212231200.0
      },
      assessmentResultMeasures: [
        {
          studentResults: [
            {
              studentId: FIRST_STUDENT_ID,
              score: "47"
            },
            {
              studentId: SECOND_STUDENT_ID,
              score: "72"
            }
          ]
        }
      ]
    }
  ]
};

const singleAssessmentItemsWithAbsentStudents = [
  {
    assessmentName: "Simplify Fractions",
    whenEnded: {
      on: 1550675770306.0
    },
    whenStarted: {
      on: 1549898112661.0
    },
    assessmentResultMeasures: [
      {
        assessmentName: "Simplify Fractions",
        studentResults: [
          {
            studentId: SECOND_STUDENT_ID,
            score: "23"
          }
        ]
      }
    ]
  },
  {
    assessmentName: "Simplify Fractions",
    whenEnded: {
      on: 1549898112672.0
    },
    whenStarted: {
      on: 1549373718465.0
    },
    assessmentResultMeasures: [
      {
        assessmentName: "Simplify Fractions",
        studentResults: [
          {
            studentId: FIRST_STUDENT_ID,
            score: "20"
          }
        ]
      }
    ]
  },
  {
    assessmentName: "Simplify Fractions",
    whenEnded: {
      on: 1549373718475.0
    },
    whenStarted: {
      on: 1547828221835.0
    },
    assessmentResultMeasures: [
      {
        assessmentName: "Simplify Fractions",
        studentResults: [
          {
            studentId: FIRST_STUDENT_ID,
            score: "16"
          },
          {
            studentId: SECOND_STUDENT_ID,
            score: "13"
          }
        ]
      }
    ]
  },
  {
    assessmentName: "Simplify Fractions",
    whenStarted: {
      on: 1547046899064.0
    },
    whenEnded: {
      on: 1547828221921.0
    },
    assessmentResultMeasures: [
      {
        assessmentName: "Simplify Fractions",
        studentResults: [
          {
            studentId: SECOND_STUDENT_ID,
            score: "12"
          }
        ]
      }
    ]
  }
];

module.exports = {
  groupWithSimplifiedHistory,
  singleAssessmentItemsWithAbsentStudents,
  studentIdsToLocalIds,
  FIRST_STUDENT_ID,
  SECOND_STUDENT_ID,
  FIRST_STUDENT_LOCAL_ID,
  SECOND_STUDENT_LOCAL_ID,
  gradeClasswideSequence
};
