# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SpringMath is a Response to Intervention (RTI) Math assessment and intervention tool built with Meteor.js. The application helps teachers and administrators track student math progress, manage interventions, and analyze assessment data.

## Development Commands

### Running the Application
```bash
# Start the application (port 3000)
npm run start

# Start with bootstrap data (first time setup)
npm run start:bootstrap

# Start on port 4000
npm run start4000

# Run with Docker
npm run start:docker
```

### Testing
```bash
# Run unit tests
npm run unit
npm run tests:unit

# Run E2E tests with Cypress
npm run cypress:open  # Interactive mode
npm run cypress:run:local  # Headless mode
npm run tests:e2e  # Full E2E test suite
```

### Linting and Code Quality
```bash
# Run ESLint
npm run lint

# Auto-fix linting issues
npm run lint:fix
```

## Architecture

### Technology Stack
- **Frontend**: React 16, React Router, React Bootstrap, Highcharts
- **Backend**: Meteor.js with MongoDB
- **Testing**: Jest (unit), Cypress (E2E), Enzyme (React components)
- **Styling**: SCSS with Bootstrap 5
- **Build**: Babel, SWC for Jest transforms

### Project Structure
```
app/
├── client/           # Client entry point and main files
├── imports/
│   ├── api/         # MongoDB collections and methods
│   │   ├── assessmentResults/
│   │   ├── students/
│   │   ├── users/
│   │   └── ...
│   ├── ui/
│   │   ├── components/  # React components
│   │   ├── pages/       # Page-level components
│   │   ├── layouts/     # Layout components
│   │   └── routing/     # React Router setup
│   └── startup/     # Meteor startup code
├── server/          # Server entry point
├── public/          # Static assets
└── tests/           # Test configuration and fixtures
```

### Key Architectural Patterns

1. **Collections**: MongoDB collections are defined in `imports/api/*/` with:
   - Collection definition (`*.js`)
   - Methods (`methods.js`)
   - Publications (`server/publications.js`)
   - Tests (`*.tests.js`)

2. **Routing**: React Router v5 with role-based routes:
   - `TeacherRoutes.jsx` - Teacher dashboard and classroom views
   - `AdminRoutes.jsx` - School/district administrator views
   - `DataAdminRoutes.jsx` - Data management interfaces

3. **Authentication**: Multiple authentication methods:
   - Standard Meteor accounts
   - Azure AD B2C SSO integration
   - MFA support for special roles

4. **Data Flow**:
   - Meteor Methods for data mutations
   - Publications/Subscriptions for reactive data
   - React Context for app-wide state

### Environment Configuration

The application uses `settings.json` for configuration:
- Environment variables (LOCAL, DEV, QA, STAGE, PROD)
- API keys and secrets
- Feature flags
- S3 bucket configuration

### Key Features

1. **Assessment Management**: Screening, progress monitoring, individual interventions
2. **Rostering**: Student/teacher management, CSV imports, external API integration
3. **Reporting**: School/district-wide analytics, growth charts, intervention tracking
4. **Print Materials**: PDF generation for assessments and intervention materials

### Testing Strategy

- **Unit Tests**: Jest with Enzyme for React components, test files alongside source
- **E2E Tests**: Cypress tests in `tests/cypress/integration/`
- **CI/CD**: CircleCI for continuous integration (referenced in README)

### Important Notes

- Always check for existing patterns in neighboring files before implementing new features
- The app uses Meteor's built-in accounts system with custom extensions
- File uploads use Bytescale (formerly FilePicker) for storage
- PDF generation uses a separate service (`PDF_GENERATOR_ENDPOINT`)
- Multi-tenancy is handled through organizations/sites hierarchy