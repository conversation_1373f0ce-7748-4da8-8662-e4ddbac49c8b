name: Build and deploy springmath demo to OVHCloud
on:
  push:
    branches:
      - demo
      - demo-3
jobs:
  build-and-deploy-to-demo:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2

      - name: Login to OVH Harbor Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Kubernetes set context
        uses: Azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVHCLOUD_TEST_KUBE_CONFIG }}
      - name: Docker Build and Push, Kubernetes apply
        run: |
          # Get the current version from the version.js file
          CURRENT_VERSION=$(grep -o '"[0-9]\+\.[0-9]\+\.[0-9]\+"' app/imports/startup/client/version.js | tr -d '"')
          echo "Current version: $CURRENT_VERSION"

          # Use git commit short hash as build number for uniqueness
          BUILD_NUMBER=$(git rev-parse --short HEAD)
          echo "Build number: $BUILD_NUMBER"

          # Create release tag with build number for DEMO environment
          export RELEASE="${CURRENT_VERSION}-${BUILD_NUMBER}-demo"
          echo "Release tag: $RELEASE"

          # split the pieces
          REGISTRY_REPO="${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/springmath-demo"

          docker build -t "$REGISTRY_REPO:$RELEASE" .
          docker push  "$REGISTRY_REPO:$RELEASE"

          # correct way to add 'latest'
          docker tag  "$REGISTRY_REPO:$RELEASE" "$REGISTRY_REPO:latest"
          docker push "$REGISTRY_REPO:latest"

          # set up sm secrets - encode plain text vars to base64
          echo 'Setting up Kubernetes secrets...'
          DEMO_ROOT_URL_B64=$(echo -n "${{ vars.DEMO_ROOT_URL }}" | base64 -w 0)
          DEMO_PORT_B64=$(echo -n "${{ vars.METEOR_PORT }}" | base64 -w 0)

          sed -i'' -e "s|DEMO_ROOT_URL_SECRET|${DEMO_ROOT_URL_B64}|g" \
            -e "s|DEMO_MONGO_URL_SECRET|${{ secrets.DEMO_MONGO_URL }}|g" \
            -e "s|DEMO_MONGO_OPLOG_URL_SECRET|${{ secrets.DEMO_MONGO_OPLOG_URL }}|g" \
            -e "s|DEMO_PORT_SECRET|${DEMO_PORT_B64}|g" \
            -e "s|DEMO_METEOR_SETTINGS_SECRET|${{ secrets.DEMO_METEOR_SETTINGS }}|g" \
            ./k8/demo.yml

          # set up docker creds
          export DOCKER_CONFIG=$(cat ~/.docker/config.json | base64 -w 0)
          sed -i'' -e "s|IMAGE_FULL_TAG|$REGISTRY_REPO:$RELEASE|g" \
            -e "s|DOCKER_CONFIG|$DOCKER_CONFIG|g" \
            ./k8/demo.yml

          echo 'applying yml file'

          # apply demo
          kubectl apply -f ./k8/demo.yml

           # Wait for deployment to complete
          kubectl rollout status deployment/springmath-demo --timeout=600s

      - name: Purge Cloudflare cache
        uses: jakejarvis/cloudflare-purge-action@master
        env:
          CLOUDFLARE_ZONE: ${{ vars.SPRINGMATH_CLOUDFLARE_ZONE_ID }}
          CLOUDFLARE_TOKEN: ${{ secrets.SPRINGMATH_CLOUDFLARE_PURGE_TOKEN }}
          PURGE_URLS: '["https://app.demo2.springmath.org/*"]'
