# SpringMath Autorostering System Documentation

## Overview

The SpringMath autorostering system automatically imports student, teacher, and class data from external systems (OneRoster and EdFi) on scheduled intervals. This document comprehensively covers the system architecture, configuration, and troubleshooting based on production findings.

## Table of Contents
- [Architecture](#architecture)
- [Cron Job Management](#cron-job-management)
- [Roster Import Process](#roster-import-process)
- [Email Notifications](#email-notifications)
- [Configuration](#configuration)
- [Troubleshooting Guide](#troubleshooting-guide)
- [Meteor 3 Migration Notes](#meteor-3-migration-notes)

## Architecture

### Core Components

1. **CronManager** (`app/imports/api/cron/cronManager.js`)
   - Manages all scheduled roster import jobs
   - Handles job lifecycle (creation, execution, removal)
   - Monitors school year boundaries
   - Coordinates with external rostering APIs

2. **Roster Import Pipeline** (`app/imports/api/rostering/fetchData.js`)
   - Fetches data from OneRoster/EdFi APIs
   - Validates and transforms data
   - Handles conflicts and errors
   - Persists to MongoDB collections

3. **Email Service** (`app/imports/api/utilities/methods.js`)
   - Sends failure notifications to Data Admins
   - Uses Mailgun for production email delivery
   - Includes detailed error reports

### Data Flow

```
External API → fetchData.js → Validation → MongoDB → Email Notifications
                    ↑
            CronManager (scheduler)
```

## Cron Job Management

### How Cron Jobs Work

The system uses `cron-job-manager` library to schedule recurring imports:

```javascript
// Cron job creation in cronManager.js
this.manager.add(orgid, cronTime, this.cronJobFn(orgid, isInFuture), {
  start: true,
  timeZone  // CRITICAL: Must be camelCase, not "timezone"
});
```

### Cron Expression Format

**Production Environment:**
```
Minutes Hours DayOfMonth Month DayOfWeek
Example: "0 2 * * *" = Daily at 2:00 AM
```

**Local Development:**
```javascript
// Runs 5 seconds after configuration for testing
const seconds = new Date().getSeconds() + 5;
```

### Key Functions

1. **`setRosteringJob()`** - Creates/updates a cron job for an organization
2. **`runRosterImport()`** - Executes the import process
3. **`removeRosteringJob()`** - Removes jobs when expired or no longer needed
4. **`getCronTime()`** - Generates cron expressions based on sync schedule

### School Year Boundary Check

The system automatically removes cron jobs at school year end:

```javascript
const schoolYearEndDate = moment.tz({ 
  ...schoolYearBoundary,  // {month, day} only - no year stored!
  year: schoolYear        // Year from syncSchedule
}, timeZone).endOf("day");

if (today.valueOf() > schoolYearEndDate.valueOf()) {
  this.removeRosteringJob(orgid);
}
```

**Important:** `schoolYearBoundary` only stores month/day, not year. The year comes from `syncSchedule.schoolYear`.

## Roster Import Process

### Import Flow

1. **Initialization** (`initRosterDocument`)
   - Creates RosterImports document
   - Sets status to "started"

2. **Data Fetching** (`validateAndImportRowData`)
   - Calls EdFi or OneRoster APIs
   - Handles pagination and retries
   - Transforms to SpringMath format

3. **Validation**
   - Checks for duplicate students in classes
   - Validates email addresses
   - Ensures required fields present

4. **Import** (`insertRoster`)
   - Updates Students, Teachers, Sites, StudentGroups
   - Tracks additions, updates, removals
   - Handles conflicts

5. **Completion**
   - Updates RosterImports status
   - Sends email notifications if errors

### Error Handling

Errors are categorized as:
- **Validation Errors** - Data quality issues (invalid emails, duplicates)
- **API Errors** - Connection/authentication failures
- **Conflict Errors** - Require manual intervention

## Email Notifications

### When Emails Are Sent

Emails are triggered when:
1. Validation errors occur during import
2. API connection fails
3. Large data changes detected (safety check)
4. Manual intervention required

### Email Recipients

The system sends to all Data Admins in the organization:
```javascript
{ 
  "profile.orgid": orgid, 
  "profile.siteAccess.role": "arbitraryIddataAdmin" 
}
```

If no Data Admins exist, a warning is logged but no error occurs.

### Email Configuration

- **Production**: Uses Mailgun (configured via METEOR_SETTINGS)
- **From**: <EMAIL>
- **Subject**: "Action required: [Source] import failed for [Organization]"

**Note:** Successful email sends are NOT logged. Only failures appear in logs.

## Configuration

### MongoDB Sync Schedule Structure

```javascript
{
  _id: "orgid",
  rosteringSettings: {
    syncSchedule: {
      startDate: "2025-09-01",      // ISO date string
      endDate: "2026-06-30",        // Optional end date
      time: "02:00",                // 24-hour format
      frequency: "daily",           // daily|weekly|monthly
      schoolYear: 2025,             // School year
      timeZone: "America/Chicago"   // MUST be camelCase!
    },
    filters: {
      schools: [...],  // Required for cron to run
      teachers: [...], // Required for cron to run  
      classes: [...]   // Required for cron to run
    }
  },
  schoolYearBoundary: {
    month: 7,  // July (0-indexed)
    day: 31    // No year field!
  },
  rostering: "rosterEdFi" // or "rosterOR"
}
```

### Critical Field Names

⚠️ **IMPORTANT**: Field naming is case-sensitive:
- ✅ `timeZone` (camelCase) - CORRECT
- ❌ `timezone` (lowercase) - WILL FAIL SILENTLY

### The `isInFuture` Logic

When `startDate > today`:
- `isInFuture = true`
- Returns Date object instead of cron pattern
- Job runs once at start date/time, then updates itself
- **Issue**: May not create recurring schedule properly

**Best Practice**: Keep `startDate` as today or past for recurring jobs.

## Troubleshooting Guide

### Testing Cron Execution

1. **Update MongoDB for immediate test:**
```javascript
db.Organizations.updateOne(
  {_id: "orgid"},
  {$set: {
    "rosteringSettings.syncSchedule": {
      startDate: "2025-09-11",
      endDate: "2026-06-30",
      time: "21:45",  // Set 5+ minutes in future
      frequency: "daily",
      schoolYear: 2025,
      timeZone: "America/Chicago"  // camelCase!
    }
  }}
)
```

2. **Restart pods to pick up changes:**
```bash
# Delete one pod (faster than full restart)
kubectl delete pod springmath-prod-xxxxx

# Monitor logs
kubectl logs -f -l app=springmath-prod | grep CronManager
```

3. **Verify cron registered:**
Look for: `===[ Cronjobs ]===` in logs showing your orgid

### Common Issues and Solutions

#### Issue: Cron removes itself after execution
**Cause**: School year boundary in the past
**Solution**: Update `schoolYearBoundary` or `syncSchedule.schoolYear`

#### Issue: Cron never executes
**Causes**:
- Missing required filters (schools, teachers, classes must have items)
- Incorrect `timeZone` field (must be camelCase)
- `isInFuture: true` with Date object instead of cron pattern

#### Issue: No email notifications
**Check**:
1. Organization has Data Admins: 
```javascript
db.users.find({
  "profile.orgid": "orgid",
  "profile.siteAccess.role": "arbitraryIddataAdmin"
})
```
2. Check Mailgun logs (emails send successfully without logging)
3. Look for ninjalog errors in Kubernetes logs

### Useful MongoDB Queries

**Check today's imports:**
```javascript
var today = new Date(); 
today.setHours(0,0,0,0);
db.RosterImports.find({
  "started.date": {$gte: today}
}, {
  orgid: 1, 
  status: 1, 
  error: 1
})
```

**Find organizations with sync schedules:**
```javascript
db.Organizations.find({
  "rosteringSettings.syncSchedule": {$exists: true},
  rostering: {$in: ["rosterEdFi", "rosterOR"]}
}, {
  name: 1,
  "rosteringSettings.syncSchedule": 1
})
```

**Check for validation errors:**
```javascript
db.RosterImports.find({
  error: {$exists: true},
  "started.date": {$gte: new Date(Date.now() - 24*60*60*1000)}
})
```

### Kubernetes Commands

**Monitor cron execution:**
```bash
kubectl logs -f -l app=springmath-prod --since=1m | grep -E "CronManager|runRosterImport"
```

**Check for email errors:**
```bash
kubectl logs -l app=springmath-prod --since=12h | grep -E "Error while sending an email|ninjalog.*error.*roster"
```

**Watch specific organization:**
```bash
kubectl logs -f -l app=springmath-prod | grep "orgid"
```

## Meteor 3 Migration Notes

### Key Changes

1. **Removed `Meteor.bindEnvironment`**
   - No longer needed with Meteor 3's native async/await
   - Cron callbacks now use standard async functions

2. **Async/Await Patterns**
   - All database operations use `*Async()` methods
   - Proper error handling with try/catch
   - No more Fiber-based synchronous code

3. **Working Example:**
```javascript
// Meteor 3 pattern (current)
cronJobFn = (orgid) => async () => {
  await this.runRosterImport(orgid);
};

// Old Meteor 2 pattern (removed)
cronJobFn = (orgid) => Meteor.bindEnvironment(() => {
  this.runRosterImport(orgid);
});
```

### Testing After Meteor 3 Upgrade

The autorostering system was successfully tested post-migration:
- Cron jobs execute on schedule
- Async operations complete properly
- Email notifications send correctly
- No Fiber-related errors

## Environment-Specific Behaviors

### LOCAL
- Cron runs 5 seconds after configuration for immediate testing
- Limited API retries (1 vs 9 in production)
- No access reminder emails

### DEV/QA
- Access reminder emails enabled
- Standard cron patterns
- Full retry logic

### PROD
- Full functionality
- Mailgun email delivery
- Maximum retries for API calls
- All cron jobs active

## Best Practices

1. **Always use camelCase** for MongoDB field names (`timeZone` not `timezone`)
2. **Set startDate to today or past** for recurring jobs
3. **Ensure filters are populated** before enabling sync schedule
4. **Monitor school year boundaries** to prevent premature job removal
5. **Check for Data Admins** when organizations report missing emails
6. **Use 5+ minute buffer** when testing cron changes in production
7. **Delete single pods** instead of full deployment restart when testing

## Related Files

- `/app/imports/api/cron/cronManager.js` - Core cron management
- `/app/imports/api/rostering/fetchData.js` - Import logic
- `/app/imports/api/rosterImports/methods.js` - Database operations
- `/app/imports/api/utilities/methods.js` - Email functions
- `/app/imports/api/rostering/utils.js` - API utilities

## Support

For issues with autorostering:
1. Check this documentation first
2. Review recent RosterImports in MongoDB
3. Check Kubernetes logs for errors
4. Verify Mailgun for email delivery
5. Ensure organization configuration is complete