# SpringMath Test MongoDB Setup Instructions

## Overview
This Docker image contains a pre-configured MongoDB instance with SpringMath test data for local development and testing.

## Setup Instructions

### 1. Load the Docker Image
After receiving the `springmath-test-mongodb.tar` file (approximately 1.1GB), load it into Docker:

```bash
docker load -i springmath-test-mongodb.tar
```

This will load the image as `springmath/test-mongodb:latest`

### 2. Run the MongoDB Container

Run the container with the test data:

```bash
docker run -d \
  --name springmath-test-mongo \
  -p 27017:27017 \
  springmath/test-mongodb:latest \
  bash -c "/initializeMongo.sh && tail -f /var/log/mongodb.log"
```

### 3. Verify It's Running

Check that MongoDB is running:
```bash
docker logs springmath-test-mongo
```

You should see messages about MongoDB starting and test data being restored.

### 4. Connect Your Application

Configure your Meteor application to connect to the test database:

```bash
export MONGO_URL="mongodb://localhost:27017/meteor?replicaSet=edspring"
export MONGO_OPLOG_URL="mongodb://localhost:27017/local?replicaSet=edspring"
meteor --settings settings.json --port 3000
```

## Useful Commands

### Stop the container:
```bash
docker stop springmath-test-mongo
```

### Start it again:
```bash
docker start springmath-test-mongo
```

### Remove the container (when done):
```bash
docker rm springmath-test-mongo
```

### Connect with mongosh to explore the data:
```bash
mongosh mongodb://localhost:27017/meteor?replicaSet=edspring
```

## What's Included

- MongoDB 8.0 with replica set configuration
- Pre-loaded SpringMath test data in the `meteor` database
- All necessary collections for running E2E tests
- Test users, organizations, students, and assessment data

## Troubleshooting

If the container doesn't start properly:
1. Check if port 27017 is already in use: `lsof -i :27017`
2. Check Docker logs: `docker logs springmath-test-mongo`
3. Ensure you have enough disk space (needs at least 2GB free)

## Notes

- This is test data only - do not use for production
- The MongoDB replica set is named "edspring"
- The database name is "meteor"
- Data is automatically restored when the container starts